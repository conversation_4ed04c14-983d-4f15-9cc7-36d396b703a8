"use client";

import { useState, useEffect } from 'react';
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { createBrowserClient } from "@/lib/supabase/client";
import { useUser } from '@/contexts/user-context';
import { Music2 as SongIconMain, PlusCircle, LayoutGrid, ListFilter, Search as SearchIcon, Loader2, ListMusic as ListMusicIcon, ZoomIn, ZoomOut, Rows3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Slider } from "@/components/ui/slider";
import { SongCard } from '@/components/songs/song-card'; 
import { SongListItem } from '@/components/songs/song-list-item'; // Import real component
import { SongCardCompact } from '@/components/songs/song-card-compact'; // Import real component
import type { Song as SongType, UserProfile, Album } from "@/types";
import { toast } from '@/hooks/use-toast';

import { cn } from '@/lib/utils';

// Define a more specific type for songs fetched for this page
// Type for data as it comes directly from the Supabase query
interface RawSongDataItem {
  id: string;
  created_at: string; // Consider Date type if parsing later
  title: string;
  creator_user_id: string;
  cover_art_url: string | null;
  is_public?: boolean | null;
  slug?: string | null;
  album_id?: string | null;
  duration_ms?: number | null;
  artist?: string | null;
  genre?: string | null; // In DB: text
  subgenre?: string | null; // In DB: text
  moods?: string | null; // In DB: text (actually TEXT[] after form changes)
  themes?: string | null; // In DB: text (actually TEXT[] after form changes)
  bpm?: number | null;
  musical_key?: string | null;
  tags?: string[] | null; // In DB: ARRAY
  audio_url?: string | null;
  lyrics?: string | null;
  description?: string | null;
  release_date?: string | null; // In DB: date
  credits?: any | null; // In DB: jsonb
  visibility?: string | null;
  plays?: number | null;
  dislike_count?: number | null;
  like_count?: number | null;
  updated_at?: string | null; // Consider Date type
  is_explicit?: boolean | null;
  instrumentation?: string[] | null; // In DB: ARRAY
  is_archived?: boolean | null;
  view_count?: number | null;
  band_id?: string | null;

  // Joined data
  profiles: {
    id: string;
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  }[] | null;
  albums: {
    id: string;
    title: string | null;
    cover_url: string | null;
  }[] | null;
}

// FetchedSongForPage uses SongType, which is an alias for Song from @/types
type FetchedSongForPage = SongType;

export default function UserSongsPage() {
  const supabase = createBrowserClient();
  const { user } = useUser();
  const router = useRouter();

  const [allSongs, setAllSongs] = useState<FetchedSongForPage[]>([]);
  // displayedSongs should also use FetchedSongForPage, which now has profiles as an object
  const [displayedSongs, setDisplayedSongs] = useState<FetchedSongForPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('created_at_desc'); 
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // Removed 'grid-compact'
  const [gridCols, setGridCols] = useState(5); // Default for 'grid', adjust as needed
  const [listItemDensity, setListItemDensity] = useState(1); // 0: compact, 1: default, 2: spacious

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    const fetchSongs = async () => {
      // Debug log: start fetch
      if (process.env.NODE_ENV !== 'production') {
        console.log('[manage-songs] Fetching songs for user', user?.id);
      }
      setIsLoading(true);
      const { data, error } = await supabase
        .from("songs")
        .select(`
          id, title, created_at, creator_user_id, cover_art_url, is_public, slug, album_id, duration_ms, artist, genre, subgenre, moods, themes, bpm, musical_key, tags, audio_url, lyrics, description, release_date, credits, visibility, plays, like_count, dislike_count, updated_at, is_explicit, instrumentation, is_archived, view_count, band_id,
          profiles:creator_user_id (id, username, display_name, avatar_url),
          albums:album_id (id, title, cover_url)
        `)
        .eq("creator_user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching songs:", error);
        toast({ title: "Erreur", description: "Impossible de charger les morceaux.", variant: "destructive" });
        setAllSongs([]);
      } else {
        let transformedData: FetchedSongForPage[] = [];
        try {
          if (process.env.NODE_ENV !== 'production') {
            console.log('[manage-songs] Raw data from Supabase:', data);
          }
          transformedData = (data as RawSongDataItem[] || []).map((rawSong): FetchedSongForPage => {
          const slugValue: string | null = rawSong.slug ?? null;
          const profileObject = rawSong.profiles && rawSong.profiles.length > 0 ? rawSong.profiles[0] : null;
          const albumObject = rawSong.albums && rawSong.albums.length > 0 ? rawSong.albums[0] : null;

          const stringToArrayOrNull = (value: string | null | undefined): string[] | null | undefined => {
            if (typeof value === 'string') return [value];
            return value; // Handles null or undefined
          };

          const songForState: FetchedSongForPage = {
            id: rawSong.id,
            cover_url: rawSong.cover_art_url ?? null,
            created_at: rawSong.created_at,
            updated_at: rawSong.updated_at ?? undefined,
            title: rawSong.title,
            artist_name: profileObject ? (profileObject.display_name ?? profileObject.username ?? undefined) : (rawSong.artist ?? undefined),
            album_title: albumObject?.title && albumObject.title !== null ? albumObject.title : undefined,
            duration: rawSong.duration_ms != null ? parseFloat((rawSong.duration_ms / 1000).toFixed(2)) : null,
            duration_ms: rawSong.duration_ms ?? null,
            genre: rawSong.genre, // Keep raw genre if needed for other purposes
            genres: typeof rawSong.genre === 'string' ? [rawSong.genre] : null,
            subgenre: stringToArrayOrNull(rawSong.subgenre) ?? null,
            mood: stringToArrayOrNull(rawSong.moods) ?? null, // Changed from rawSong.mood
            theme: stringToArrayOrNull(rawSong.themes) ?? null, // Changed from rawSong.theme
            tags: rawSong.tags,
            bpm: rawSong.bpm,
            musical_key: rawSong.musical_key,
            cover_art_url: rawSong.cover_art_url ?? null,
            audio_url: rawSong.audio_url ?? null,
            lyrics: rawSong.lyrics,
            description: rawSong.description,
            credits: rawSong.credits,
            is_public: typeof rawSong.is_public === 'boolean' ? rawSong.is_public : false,
            slug: slugValue,
            plays: rawSong.plays ?? 0,
            like_count: rawSong.like_count ?? 0,
            dislike_count: rawSong.dislike_count ?? 0,
            creator_user_id: rawSong.creator_user_id,
            album_id: rawSong.album_id,
            band_id: rawSong.band_id,
            profiles: profileObject,
            albums: albumObject ? { id: albumObject.id, title: albumObject.title ?? undefined, cover_url: albumObject.cover_url ?? null } : null,
            release_date: rawSong.release_date,
            visibility: rawSong.visibility,
            is_explicit: rawSong.is_explicit,
            instrumentation: rawSong.instrumentation,
            is_archived: rawSong.is_archived,
            view_count: rawSong.view_count
          };
          return songForState;
        });
        if (process.env.NODE_ENV !== 'production') {
            console.log('[manage-songs] Transformed songs:', transformedData);
          }
          setAllSongs(transformedData);
        } catch (err) {
          console.error('[manage-songs] Error during songs mapping:', err);
          toast({ title: 'Erreur', description: 'Erreur lors du mapping des morceaux.', variant: 'destructive' });
          setAllSongs([]);
        }
      }
      setIsLoading(false);
    };

    fetchSongs();
  }, [user, supabase, router]);

  useEffect(() => {
    let processedSongs = [...allSongs];
    if (searchTerm) {
      processedSongs = processedSongs.filter(s => 
        s.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (s.artist_name && s.artist_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (s.profiles?.display_name && s.profiles.display_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (s.profiles?.username && s.profiles.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (s.albums?.title && s.albums.title.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // TODO: Implement sorting logic for songs (by title, artist, album, date, etc.)
    switch (sortOption) {
      case 'title_asc':
        processedSongs.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'title_desc':
        processedSongs.sort((a, b) => b.title.localeCompare(a.title));
        break;
      case 'created_at_asc':
      default: // Also for created_at_desc initially
        processedSongs.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime());
        if (sortOption === 'created_at_asc') processedSongs.reverse();
        break;
    }
    setDisplayedSongs(processedSongs);
  }, [allSongs, searchTerm, sortOption]);

  const handleUpdateSongStatus = (songId: string, newStatus: { is_public: boolean; slug: string | null }) => {
    console.log(`Updating status for song ${songId} to:`, newStatus); // DEBUG
    const updateInList = (list: FetchedSongForPage[]) =>
      list.map(s =>
        s.id === songId
          ? { ...s, is_public: newStatus.is_public, slug: newStatus.slug }
          : s
      );
    setAllSongs(prev => {
      const newList = updateInList(prev);
      console.log(`New allSongs list after status update for ${songId}:`, newList.find(s=>s.id === songId)); // DEBUG
      return newList;
    });
  };

  const handleSongStatsChange = (songId: string, newStats: { 
    like_count: number; 
    dislike_count: number; 
    is_liked_by_user: boolean; 
    is_disliked_by_user: boolean; 
  }) => {
    setAllSongs(prevSongs => 
      prevSongs.map(s => 
        s.id === songId 
          ? { 
              ...s, 
              like_count: newStats.like_count, 
              dislike_count: newStats.dislike_count,
              // Note: is_liked_by_user and is_disliked_by_user are not typically part of the main song object
              // in the list, as they are user-specific. The SongCard handles its own UI for this.
              // However, if these counts are used for sorting or display directly from allSongs, update them.
            } 
          : s
      )
    );
  };

  const handleDeleteSong = async (songId: string) => {
    if (!user) return;
    // TODO: Add confirmation dialog
    const { error } = await supabase
      .from('songs')
      .delete()
      .eq('id', songId)
      .eq('creator_user_id', user.id);

    if (error) {
      toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
    } else {
      toast({ title: "Morceau supprimé", description: "Le morceau a été supprimé avec succès." });
      setAllSongs(prev => prev.filter(s => s.id !== songId));
    }
  };
  
  // useEffect for gridCols based on viewMode is no longer needed if 'grid-compact' is removed
  // or can be simplified if 'grid' has a fixed default or remembers its state.
  // For now, let's assume gridCols is managed directly.

  const handleGridColsChange = (newCols: number) => {
    const minCols = 2; // Define for 'grid'
    const maxCols = 6; // Define for 'grid'
    setGridCols(Math.max(minCols, Math.min(maxCols, newCols)));
  };
  
  const getGridClass = () => {
    let classes = "grid gap-4 "; // Default gap for 'grid'
    if (gridCols <= 1) classes += "grid-cols-1";
    else if (gridCols === 2) classes += "grid-cols-1 xs:grid-cols-2";
    else if (gridCols === 3) classes += "grid-cols-2 sm:grid-cols-3";
    else if (gridCols === 4) classes += "grid-cols-2 sm:grid-cols-3 md:grid-cols-4";
    else if (gridCols === 5) classes += "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5";
    else if (gridCols >= 6) classes += "grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6";
    
    if (!classes.includes("grid-cols-")) {
        classes += "lg:grid-cols-5"; // Default fallback for grid
    }
    return classes;
  };

  if (isLoading) {
    return <div className="w-full px-4 md:px-6 lg:px-8 py-8 flex justify-center items-center min-h-[calc(100vh-200px)]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }

  return (
    <div className="w-full px-4 md:px-6 lg:px-8 py-8">
      <div className="flex flex-col md:flex-row items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <SongIconMain className="mr-3 h-8 w-8 text-primary" />
            Mes Morceaux
          </h1>
          <p className="text-muted-foreground text-sm">Gérez votre catalogue de morceaux.</p>
        </div>
        <div className="flex items-center gap-2 flex-wrap">
            {/* Search, Sort, View Mode, Density Sliders */}
            <div className="relative">
                <SearchIcon className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input placeholder="Rechercher morceaux..." className="pl-8 w-40 md:w-auto" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            </div>
            <TooltipProvider>
              <Tooltip>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild><TooltipTrigger asChild><Button variant="outline" size="icon" className="h-9 w-9"><ListFilter className="h-4 w-4" /><span className="sr-only">Trier</span></Button></TooltipTrigger></DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSortOption('created_at_desc')}>Date (Plus Récents)</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('created_at_asc')}>Date (Plus Anciens)</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('title_asc')}>Titre (A-Z)</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('title_desc')}>Titre (Z-A)</DropdownMenuItem>
                    {/* TODO: Add more sort options (artist, album) */}
                  </DropdownMenuContent>
                </DropdownMenu>
                <TooltipContent><p>Trier par: {sortOption.replace('_', ' ').replace('created at', 'Date')}</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Button variant={viewMode === 'grid' ? "secondary" : "outline"} size="icon" onClick={() => setViewMode('grid')} title="Vue Grille"><LayoutGrid className="h-4 w-4" /></Button>
            {/* Removed grid-compact button */}
            <Button variant={viewMode === 'list' ? "secondary" : "outline"} size="icon" onClick={() => setViewMode('list')} title="Vue Liste"><ListMusicIcon className="h-4 w-4" /></Button>
            
            {viewMode === 'grid' && (
              <div className="flex items-center gap-1.5 ml-1" title="Densité Grille">
                {/* Bouton GAUCHE (-) => Éléments PLUS PETITS (plus de colonnes) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGridColsChange(gridCols + 1)} title="Éléments plus petits"><ZoomOut className="h-4 w-4" /></Button> 
                <Slider 
                  value={[ 6 - gridCols ]} // Inverted value: maxCols (6 for grid) - currentCols
                  min={0} // Slider visually goes from 0 (left) to max (right)
                  max={6 - 2} // maxSlider = maxCols (6) - minCols (2) = 4
                  step={1} 
                  className="w-[80px]" 
                  onValueChange={(v) => handleGridColsChange(6 - v[0])} // Convert back: newCols = maxCols (6) - sliderValue
                  dir="ltr" 
                />
                {/* Bouton DROITE (+) => Éléments PLUS GRANDS (moins de colonnes) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGridColsChange(gridCols - 1)} title="Éléments plus grands"><ZoomIn className="h-4 w-4" /></Button>
              </div>
            )}
            {viewMode === 'list' && (
              <div className="flex items-center gap-1.5 ml-1" title="Densité Liste">
                {/* Bouton GAUCHE (-) => Éléments PLUS PETITS (compact, density 0) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setListItemDensity(p => Math.max(0, p - 1))} title="Plus compact"><ZoomOut className="h-4 w-4" /></Button>
                <Slider 
                  value={[listItemDensity]} // Direct value: 0 (compact) to 2 (spacious)
                  min={0} 
                  max={2} 
                  step={1} 
                  className="w-[80px]" 
                  onValueChange={(v) => setListItemDensity(v[0])} // Slider left (0) = compact, Slider right (2) = spacious
                  dir="ltr" 
                />
                {/* Bouton DROITE (+) => Éléments PLUS GRANDS (spacious, density 2) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setListItemDensity(p => Math.min(2, p + 1))} title="Plus espacé"><ZoomIn className="h-4 w-4" /></Button>
              </div>
            )}
            <Button asChild><Link href="/manage-songs/create"><PlusCircle className="mr-2 h-4 w-4" /> Créer</Link></Button>
        </div>
      </div>

      {viewMode === 'grid' && (
        <div className={cn(getGridClass())}> 
          {displayedSongs.map((song) => (
            <SongCard 
              key={song.id} 
              song={song} 
              onDelete={handleDeleteSong} 
              onUpdateStatus={handleUpdateSongStatus}
              onStatsChange={handleSongStatsChange} // Pass the handler
            />
          ))}
        </div>
      )}
      {/* Removed grid-compact rendering section */}
      {viewMode === 'list' && ( 
        <div className="space-y-3">
          {displayedSongs.map((song) => (
            <SongListItem 
              key={song.id} 
              song={song} 
              onDelete={handleDeleteSong} 
              onUpdateStatus={handleUpdateSongStatus} 
              density={listItemDensity}
              // onStatsChange={handleSongStatsChange} // If SongListItem also needs to update stats
            />
          ))}
        </div>
      )}
      {displayedSongs.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <ListMusicIcon className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-sm font-medium text-foreground">Aucun morceau trouvé</h3>
          <p className="mt-1 text-sm text-muted-foreground">Commencez par créer votre premier morceau.</p>
          <div className="mt-6">
            <Button asChild><Link href="/manage-songs/create"><PlusCircle className="mr-2 h-4 w-4" /> Créer un morceau</Link></Button>
          </div>
        </div>
      )}
    </div>
  );
}
