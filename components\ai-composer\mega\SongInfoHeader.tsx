'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { MultiSelect } from '@/components/ui/multi-select';
import { 
  Music, User, Clock, Hash, Guitar, Volume2, 
  Edit3, Save, X, Plus, Settings, Info
} from 'lucide-react';
import { GENRES_OPTIONS, MOODS_OPTIONS, INSTRUMENTS_OPTIONS, MUSICAL_KEY_OPTIONS, TIME_SIGNATURE_OPTIONS } from '@/components/songs/song-options';

interface SongInfoHeaderProps {
  currentSong: any;
  setCurrentSong: (song: any) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  onSave: () => Promise<void>;
}

// Utilise les mêmes options que SongForm pour la cohérence

export const SongInfoHeader: React.FC<SongInfoHeaderProps> = ({
  currentSong,
  setCurrentSong,
  styleConfig,
  setStyleConfig,
  onSave
}) => {
  
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    title: currentSong.title || '',
    artist: currentSong.artist || '',
    bpm: styleConfig.bpm || 120,
    key: styleConfig.key || 'C',
    capo: styleConfig.capo || 0,
    timeSignature: styleConfig.timeSignature || '4/4',
    genres: styleConfig.genres || [],
    moods: styleConfig.moods || [],
    instrumentation: styleConfig.instrumentation || [],
    description: currentSong.description || ''
  });

  const handleSave = async () => {
    // Mettre à jour currentSong
    setCurrentSong({
      ...currentSong,
      title: editData.title,
      artist: editData.artist,
      description: editData.description
    });

    // Mettre à jour styleConfig
    setStyleConfig({
      ...styleConfig,
      bpm: editData.bpm,
      key: editData.key,
      capo: editData.capo,
      timeSignature: editData.timeSignature,
      genres: editData.genres,
      moods: editData.moods,
      instrumentation: editData.instrumentation
    });

    setIsEditing(false);
    await onSave();
  };

  const handleCancel = () => {
    setEditData({
      title: currentSong.title || '',
      artist: currentSong.artist || '',
      bpm: styleConfig.bpm || 120,
      key: styleConfig.key || 'C',
      capo: styleConfig.capo || 0,
      timeSignature: styleConfig.timeSignature || '4/4',
      genres: styleConfig.genres || [],
      moods: styleConfig.moods || [],
      instrumentation: styleConfig.instrumentation || [],
      description: currentSong.description || ''
    });
    setIsEditing(false);
  };

  // Les fonctions addGenre et removeGenre ne sont plus nécessaires avec MultiSelect

  if (isEditing) {
    return (
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Informations du morceau
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4" />
                Annuler
              </Button>
              <Button variant="default" size="sm" onClick={handleSave}>
                <Save className="h-4 w-4" />
                Sauvegarder
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Ligne 1: Titre et Artiste */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title" className="text-white">Titre</Label>
              <Input
                id="title"
                value={editData.title}
                onChange={(e) => setEditData({...editData, title: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Titre de la chanson"
              />
            </div>
            <div>
              <Label htmlFor="artist" className="text-white">Artiste</Label>
              <Input
                id="artist"
                value={editData.artist}
                onChange={(e) => setEditData({...editData, artist: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Nom de l'artiste"
              />
            </div>
          </div>

          {/* Ligne 2: Paramètres musicaux */}
          <div className="grid grid-cols-4 gap-4">
            <div>
              <Label htmlFor="bpm" className="text-white">BPM</Label>
              <Input
                id="bpm"
                type="number"
                value={editData.bpm}
                onChange={(e) => setEditData({...editData, bpm: parseInt(e.target.value) || 120})}
                className="bg-slate-700 border-slate-600 text-white"
                min="60"
                max="200"
              />
            </div>
            <div>
              <Label htmlFor="key" className="text-white">Tonalité</Label>
              <Select value={editData.key} onValueChange={(value) => setEditData({...editData, key: value})}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MUSICAL_KEY_OPTIONS.map(key => (
                    <SelectItem key={key.value} value={key.value}>{key.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="capo" className="text-white">Capo</Label>
              <Input
                id="capo"
                type="number"
                value={editData.capo}
                onChange={(e) => setEditData({...editData, capo: parseInt(e.target.value) || 0})}
                className="bg-slate-700 border-slate-600 text-white"
                min="0"
                max="12"
              />
            </div>
            <div>
              <Label htmlFor="timeSignature" className="text-white">Mesure</Label>
              <Select value={editData.timeSignature} onValueChange={(value) => setEditData({...editData, timeSignature: value})}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIME_SIGNATURE_OPTIONS.map(sig => (
                    <SelectItem key={sig.value} value={sig.value}>{sig.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Ligne 3: Genres, Moods et Instrumentation */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label className="text-white">Genres</Label>
              <MultiSelect
                options={GENRES_OPTIONS}
                value={editData.genres}
                onChange={(value) => setEditData({...editData, genres: value})}
                placeholder="Choisir des genres..."
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <Label htmlFor="moods" className="text-white">Ambiances</Label>
              <MultiSelect
                options={MOODS_OPTIONS}
                value={editData.moods}
                onChange={(value) => setEditData({...editData, moods: value})}
                placeholder="Choisir des ambiances..."
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <Label className="text-white">Instrumentation</Label>
              <MultiSelect
                options={INSTRUMENTS_OPTIONS}
                value={editData.instrumentation}
                onChange={(value) => setEditData({...editData, instrumentation: value})}
                placeholder="Choisir des instruments..."
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </div>

          {/* Ligne 4: Description */}
          <div>
            <Label htmlFor="description" className="text-white">Description</Label>
            <Textarea
              id="description"
              value={editData.description}
              onChange={(e) => setEditData({...editData, description: e.target.value})}
              className="bg-slate-700 border-slate-600 text-white"
              placeholder="Description du morceau, inspiration, notes..."
              rows={2}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Infos principales */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                <Music className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white">
                  {currentSong.title || 'Nouvelle chanson'}
                </h2>
                <p className="text-sm text-slate-400 flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {currentSong.artist || 'Artiste inconnu'}
                </p>
              </div>
            </div>

            {/* Paramètres musicaux */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1 text-slate-300">
                <Clock className="h-4 w-4" />
                <span>{styleConfig.bpm || 120} BPM</span>
              </div>
              <div className="flex items-center gap-1 text-slate-300">
                <Hash className="h-4 w-4" />
                <span>{styleConfig.key || 'C'} {styleConfig.timeSignature || '4/4'}</span>
              </div>
              {styleConfig.capo > 0 && (
                <div className="flex items-center gap-1 text-slate-300">
                  <Guitar className="h-4 w-4" />
                  <span>Capo {styleConfig.capo}</span>
                </div>
              )}
              {styleConfig.moods && styleConfig.moods.length > 0 && (
                <div className="flex items-center gap-1 text-slate-300">
                  <Volume2 className="h-4 w-4" />
                  <span>{styleConfig.moods.slice(0, 2).join(', ')}{styleConfig.moods.length > 2 ? '...' : ''}</span>
                </div>
              )}
            </div>

            {/* Genres */}
            <div className="flex items-center gap-2">
              {(styleConfig.genres || []).slice(0, 3).map(genre => (
                <Badge key={genre} variant="outline" className="text-xs">
                  {genre}
                </Badge>
              ))}
              {(styleConfig.genres || []).length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{(styleConfig.genres || []).length - 3}
                </Badge>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="gap-1"
            >
              <Edit3 className="h-4 w-4" />
              Modifier
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              title="Informations détaillées"
            >
              <Info className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Description si présente */}
        {currentSong.description && (
          <div className="mt-3 pt-3 border-t border-slate-700">
            <p className="text-sm text-slate-300">{currentSong.description}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
