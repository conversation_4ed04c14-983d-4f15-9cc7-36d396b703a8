"use client";

import Link from 'next/link';
import Image from 'next/image';
import type { Song } from '@/components/songs/song-schema';
import type { UserProfile, Album } from '@/types';
import { PlayCircle, UserCircle, Loader2, Music2, MoreHorizontal, Edit3, Trash2, ListPlus, Copy, Thum<PERSON>Up, ThumbsDown, Eye, Play as PlayIconSmall } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { PlayButton } from '@/components/audio/play-button';
import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button';
import { LikeButton } from '@/components/social/like-button';
import { DislikeButton } from '@/components/social/dislike-button';
import { AddToPlaylistModal } from '@/components/playlists/add-to-playlist-modal';
import { useResourceInteractionStore, getResourceInteractionStoreState, type ResourceInteractionStoreState, getResourceKey } from '@/lib/stores/resource-interaction-store';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore';

interface SongForCompactCard extends Song {
  profiles?: UserProfile | null;
  albums?: Pick<Album, 'id' | 'title' | 'cover_url'> | null;
  duration_ms: number | null;
  plays?: number | null;
}

interface SongCardCompactProps {
  song: SongForCompactCard;
  onUpdateStatus?: (songId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  onDelete?: (songId: string) => void;
  onStatsChange?: (songId: string, newStats: { 
    like_count: number; 
    dislike_count: number; 
    is_liked_by_user: boolean; 
    is_disliked_by_user: boolean; 
  }) => void;
  onInfoClick?: (song: SongForCompactCard) => void;
}

export function SongCardCompact({ song, onUpdateStatus, onDelete, onStatsChange, onInfoClick }: SongCardCompactProps) {
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const { toast } = useToast();
  const router = useRouter();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);
  const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] = useState(false);
  const playSpecificSong = useAudioPlayerStore(state => state.playSong);
  const artistDisplay = song.profiles?.display_name || song.profiles?.username || song.artist_name || "Artiste inconnu";
  
  const initialImageSrc = song.cover_art_url || song.albums?.cover_url;
  const fallbackImageSrc = '/images/covers/mouvk.png';
  const [currentImageSrc, setCurrentImageSrc] = useState(initialImageSrc || fallbackImageSrc);
  const [hasLoadError, setHasLoadError] = useState(false);

  useEffect(() => {
    setCurrentImageSrc(initialImageSrc || fallbackImageSrc);
    setHasLoadError(false);
  }, [initialImageSrc, fallbackImageSrc]);

  useEffect(() => {
    if (song && song.id) {
      getResourceInteractionStoreState().setResourceStatus('song', song.id, {
        isLiked: song.is_liked_by_current_user ?? false,
        likeCount: song.like_count ?? 0,
        isDisliked: song.is_disliked_by_current_user ?? false,
        dislikeCount: song.dislike_count ?? 0,
      });
    }
  }, [song]);

  const { 
    likeCount: currentLikeCount,
    dislikeCount: currentDislikeCount
  } = useResourceInteractionStore(
    (state: ResourceInteractionStoreState) => 
      state.resourceStates[getResourceKey('song', song.id)] || { 
        likeCount: song.like_count ?? 0, 
        dislikeCount: song.dislike_count ?? 0 
      }
  );

  const viewPageUrl = song.slug ? `/songs/${song.slug}` : `/songs/${song.id}`;
  
  const handleImageError = () => {
    if (!hasLoadError) {
      setCurrentImageSrc(fallbackImageSrc);
      setHasLoadError(true);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le morceau "${song.title}" ?`)) {
      if (onDelete) onDelete(song.id);
      else toast({ title: "Suppression (Placeholder)"});
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(`/manage-songs/${song.id}/edit`);
  };

  const handleAddToQueue = () => {
    playSpecificSong({ ...song, artist_name: artistDisplay });
    toast({ title: "Lecture demandée", description: `"${song.title}" devrait commencer à jouer.` });
  };

  const handleAddToPlaylist = () => {
    if (!user) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour ajouter à une playlist.", variant: "destructive"});
      return;
    }
    setIsAddToPlaylistModalOpen(true);
  };

  const handleDuplicate = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour dupliquer un morceau.", variant: "destructive" });
      return;
    }
    try {
      const { data, error } = await supabase.rpc('duplicate_song', { p_song_id: song.id, p_user_id: user.id });
      if (error) throw error;
      toast({ title: "Morceau dupliqué", description: `"${song.title}" a été dupliqué avec succès.` });
      router.refresh();
    } catch (err: any) {
      toast({ title: "Erreur de duplication", description: err.message || "Impossible de dupliquer le morceau.", variant: "destructive" });
    }
  };

  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault(); e.stopPropagation();
    if (!user || song.creator_user_id !== user.id || isTogglingStatus || song.is_public === undefined) return;
    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_song_public_status', { p_song_id: song.id, p_user_id: user.id });
      if (error) throw error;
      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0];
        toast({ title: "Statut du morceau mis à jour", description: `"${song.title}" est ${new_is_public ? 'public' : 'privé'}.`});
        if (onUpdateStatus) onUpdateStatus(song.id, { is_public: new_is_public, slug: new_slug });
      } else { throw new Error("Réponse RPC invalide."); }
    } catch (err: any) { toast({ title: "Erreur", description: err.message || "Impossible de changer le statut.", variant: "destructive" });
    } finally { setIsTogglingStatus(false); }
  };

  return (
    <>
      <Card className="overflow-hidden flex flex-col group/card transition-all duration-200 hover:shadow-md">
      <CardHeader className="p-0 relative">
        {song.is_public !== undefined && (
            <div 
                title={song.is_public ? "Public (cliquer pour changer)" : "Privé (cliquer pour changer)"}
                onClick={togglePublicStatus}
                className={cn(
                    "absolute top-1.5 left-1.5 z-10 w-3 h-3 rounded-full cursor-pointer flex items-center justify-center transition-all",
                    "ring-1 ring-offset-1 ring-offset-card",
                    isTogglingStatus 
                      ? "bg-yellow-500 ring-yellow-500/50 animate-pulse" 
                      : song.is_public 
                        ? "bg-green-500 ring-green-500/50 hover:bg-green-400" 
                        : "bg-red-500 ring-red-500/50 hover:bg-red-400" 
                )}
            >
                {isTogglingStatus && <Loader2 className="h-1.5 w-1.5 animate-spin text-white" />} 
            </div>
        )}
        <Link href={viewPageUrl} className="block aspect-square relative group">
          <Image 
            src={currentImageSrc} 
            alt={song.title || 'Song cover'} 
            width={200} 
            height={200} 
            className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            onError={handleImageError}
          />
          {/* Actions Overlay */}
          <div className="absolute inset-0 opacity-0 group-hover/card:opacity-100 transition-opacity duration-300 pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
            
            {/* Top-right actions */}
            <div className="absolute top-1.5 right-1.5 flex flex-col space-y-1 pointer-events-auto">
              <Button title="Modifier" onClick={handleEdit} variant="ghost" size="sm" className="bg-black/70 hover:bg-black/90 text-white backdrop-blur-sm p-1 h-auto w-auto">
                <Edit3 className="h-3 w-3" />
              </Button>
              <Button title="Dupliquer" onClick={handleDuplicate} variant="ghost" size="sm" className="bg-black/70 hover:bg-black/90 text-white backdrop-blur-sm p-1 h-auto w-auto">
                <Copy className="h-3 w-3" />
              </Button>
              {onDelete && (
                <Button title="Supprimer" onClick={handleDelete} variant="ghost" size="sm" className="bg-red-600/80 hover:bg-red-500/90 text-white backdrop-blur-sm p-1 h-auto w-auto">
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Bottom-left actions */}
            <div className="absolute bottom-1.5 left-1.5 flex items-center space-x-1 pointer-events-auto">
              <PlayButton song={{ ...song, artist_name: artistDisplay }} size="sm" className="bg-black/70 hover:bg-black/90 text-white backdrop-blur-sm p-1 h-auto w-auto" />
              <AddToPlaylistButton songId={song.id} buttonSize="sm" buttonClassName="bg-black/70 hover:bg-black/90 text-white backdrop-blur-sm p-1 h-auto w-auto" />
            </div>
          </div>
          {/* Info visible au survol et au zoom - NOW INSIDE LINK */}
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover/card:opacity-100 transition-opacity duration-300 pointer-events-none">
            <Link href={viewPageUrl}>
              <CardTitle className="text-xs font-medium text-white hover:underline mb-0.5 truncate leading-tight" title={song.title}>
                {song.title}
              </CardTitle>
            </Link>
            <div className="text-xs text-white/80 truncate mb-0.5" title={artistDisplay}>
              <Link href={`/artists/${song.profiles?.username || song.creator_user_id}`} className="hover:underline flex items-center gap-1">
                {song.profiles?.avatar_url ? (
                  <Image src={song.profiles.avatar_url} alt={artistDisplay} width={12} height={12} className="rounded-full" />
                ) : (
                  <UserCircle className="w-2.5 h-2.5" />
                )}
                <span className="truncate">{artistDisplay}</span>
              </Link>
            </div>
            <div className="text-xs text-white/80 flex items-center gap-2 flex-wrap">
              {song.plays !== undefined && (
                <span className="flex items-center gap-0.5" title="Écoutes">
                  <PlayIconSmall className="w-2.5 h-2.5" /> {song.plays}
                </span>
              )}
              <span title={`${currentLikeCount ?? 0} likes`} className="flex items-center gap-0.5">
                  <ThumbsUp className="w-2.5 h-2.5" /> {currentLikeCount ?? 0}
                </span>
                <span title={`${currentDislikeCount ?? 0} dislikes`} className="flex items-center gap-1">
                  <ThumbsDown className="w-2.5 h-2.5" /> {currentDislikeCount ?? 0}
                </span>
              </div>
            </div>
        </Link>
      </CardHeader>
      <CardFooter className="p-2 pt-1 flex justify-between items-center">
        <div className="flex items-center gap-0.5">
          <PlayButton song={{ ...song, artist_name: artistDisplay }} size="sm" variant="ghost" className="h-6 w-6" />
          {onInfoClick && (
            <Button
              title="Voir les détails"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onInfoClick(song);
              }}
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-muted-foreground hover:text-primary p-0"
            >
              <Eye className="h-3.5 w-3.5" />
            </Button>
          )}
          <Button onClick={handleAddToQueue} size="icon" variant="ghost" className="h-6 w-6" title="Ajouter à la file d'attente">
            <ListPlus className="h-3 w-3" />
          </Button>

          {user && <AddToPlaylistButton songId={song.id} buttonSize="sm" buttonClassName="h-6 w-6" />}
          
          <div className="flex items-center">
            <LikeButton resourceId={song.id} resourceType="song" size="sm" variant="ghost" className="h-6 w-6" />
          </div>
          <div className="flex items-center">
            <DislikeButton resourceId={song.id} resourceType="song" size="sm" variant="ghost" className="h-6 w-6" />
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <MoreHorizontal className="h-3 w-3" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleAddToPlaylist}><ListPlus className="mr-2 h-3 w-3" /> Ajouter à une playlist</DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push(`/manage-songs/${song.id}/edit`)}><Edit3 className="mr-2 h-3 w-3" /> Modifier</DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}><Copy className="mr-2 h-3 w-3" /> Dupliquer</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-3 w-3" /> Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>

      {isAddToPlaylistModalOpen && user && (
        <AddToPlaylistModal
          songId={song.id}
          isOpen={isAddToPlaylistModalOpen}
          onClose={() => setIsAddToPlaylistModalOpen(false)}
        />
      )}
    </>
  );
}
