'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Type, Music, Eye, EyeOff, Wand2, RotateCcw, 
  Copy, Paste, Search, Replace, BookOpen, Mic,
  Volume2, Play, Pause, SkipForward, Hash
} from 'lucide-react';

interface LyricsStudioEditorProps {
  content: string;
  onContentChange: (content: string) => void;
  selectedSection: string;
  sections: any[];
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
  styleConfig: any;
}

type EditorMode = 'write' | 'edit' | 'review';
type ViewMode = 'text' | 'chords' | 'phonetic';

export const LyricsStudioEditor: React.FC<LyricsStudioEditorProps> = ({
  content,
  onContentChange,
  selectedSection,
  sections,
  onAIGenerate,
  styleConfig
}) => {
  
  const [editorMode, setEditorMode] = useState<EditorMode>('write');
  const [viewMode, setViewMode] = useState<ViewMode>('text');
  const [showMetrics, setShowMetrics] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [lineCount, setLineCount] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const currentSection = sections.find(s => s.id === selectedSection);

  // Calcul des métriques en temps réel
  React.useEffect(() => {
    const words = content.trim().split(/\s+/).filter(Boolean).length;
    const lines = content.split('\n').length;
    setWordCount(words);
    setLineCount(lines);
  }, [content]);

  // Modes d'édition
  const editorModes = [
    {
      id: 'write' as EditorMode,
      label: 'Écrire',
      icon: Type,
      color: 'text-blue-400',
      description: 'Mode création libre'
    },
    {
      id: 'edit' as EditorMode,
      label: 'Éditer',
      icon: Search,
      color: 'text-green-400',
      description: 'Mode révision et correction'
    },
    {
      id: 'review' as EditorMode,
      label: 'Réviser',
      icon: Eye,
      color: 'text-purple-400',
      description: 'Mode lecture et analyse'
    }
  ];

  // Modes de visualisation
  const viewModes = [
    {
      id: 'text' as ViewMode,
      label: 'Texte',
      icon: Type,
      description: 'Texte brut'
    },
    {
      id: 'chords' as ViewMode,
      label: 'Accords',
      icon: Music,
      description: 'Avec accords'
    },
    {
      id: 'phonetic' as ViewMode,
      label: 'Phonétique',
      icon: Volume2,
      description: 'Aide à la prononciation'
    }
  ];

  // Actions IA spécialisées pour les paroles
  const lyricsAIActions = [
    {
      id: 'improve',
      label: 'Améliorer',
      icon: Wand2,
      prompt: `Améliore ces paroles en gardant le sens mais en améliorant le style, la fluidité et les rimes:\n\n${content}`,
      color: 'bg-blue-500'
    },
    {
      id: 'rhyme',
      label: 'Rimes',
      icon: Hash,
      prompt: `Suggère des mots qui riment et s'intègrent bien dans ce contexte musical:\n\n${content}`,
      color: 'bg-green-500'
    },
    {
      id: 'extend',
      label: 'Étendre',
      icon: SkipForward,
      prompt: `Continue ces paroles en gardant le même style et thème:\n\n${content}`,
      color: 'bg-purple-500'
    },
    {
      id: 'rewrite',
      label: 'Réécrire',
      icon: RotateCcw,
      prompt: `Réécris ces paroles avec un style différent mais le même message:\n\n${content}`,
      color: 'bg-orange-500'
    }
  ];

  // Gestionnaire pour les actions IA
  const handleAIAction = useCallback(async (action: typeof lyricsAIActions[0]) => {
    if (!content.trim()) {
      // Si pas de contenu, générer des paroles pour la section
      const sectionPrompt = `Écris des paroles pour un ${currentSection?.type || 'couplet'} dans le style ${styleConfig.genres?.[0] || 'pop'}. Thème: ${styleConfig.theme || 'libre'}`;
      await onAIGenerate(sectionPrompt, 'lyrics');
    } else {
      await onAIGenerate(action.prompt, 'lyrics');
    }
  }, [content, currentSection, styleConfig, onAIGenerate]);

  // Gestionnaire pour l'enregistrement vocal
  const handleVoiceRecording = useCallback(() => {
    setIsRecording(!isRecording);
    // TODO: Implémenter l'enregistrement vocal
  }, [isRecording]);

  // Gestionnaire pour insérer du texte à la position du curseur
  const insertTextAtCursor = useCallback((text: string) => {
    if (!textareaRef.current) return;
    
    const start = textareaRef.current.selectionStart;
    const end = textareaRef.current.selectionEnd;
    const newContent = content.substring(0, start) + text + content.substring(end);
    
    onContentChange(newContent);
    
    // Repositionner le curseur
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.selectionStart = start + text.length;
        textareaRef.current.selectionEnd = start + text.length;
        textareaRef.current.focus();
      }
    }, 0);
  }, [content, onContentChange]);

  return (
    <div className="h-full flex flex-col bg-slate-900/30">
      {/* Barre d'outils */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-3">
        <div className="flex items-center justify-between">
          {/* Modes d'édition */}
          <div className="flex items-center gap-1 bg-slate-700/50 rounded-lg p-1">
            {editorModes.map((mode) => {
              const Icon = mode.icon;
              return (
                <Button
                  key={mode.id}
                  variant={editorMode === mode.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setEditorMode(mode.id)}
                  className={`gap-1 ${editorMode === mode.id ? 'bg-slate-600' : ''}`}
                  title={mode.description}
                >
                  <Icon className={`h-4 w-4 ${mode.color}`} />
                  {mode.label}
                </Button>
              );
            })}
          </div>

          {/* Modes de visualisation */}
          <div className="flex items-center gap-1 bg-slate-700/50 rounded-lg p-1">
            {viewModes.map((mode) => {
              const Icon = mode.icon;
              return (
                <Button
                  key={mode.id}
                  variant={viewMode === mode.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode(mode.id)}
                  className="gap-1"
                  title={mode.description}
                >
                  <Icon className="h-4 w-4" />
                  {mode.label}
                </Button>
              );
            })}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant={isRecording ? "default" : "outline"}
              size="sm"
              onClick={handleVoiceRecording}
              className={`gap-1 ${isRecording ? 'bg-red-500 animate-pulse' : ''}`}
            >
              <Mic className="h-4 w-4" />
              {isRecording ? 'Arrêter' : 'Dicter'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMetrics(!showMetrics)}
              className="gap-1"
            >
              {showMetrics ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              Métriques
            </Button>
          </div>
        </div>
      </div>

      {/* Zone d'édition principale */}
      <div className="flex-1 flex overflow-hidden">
        {/* Éditeur */}
        <div className="flex-1 flex flex-col">
          {/* Zone de texte */}
          <div className="flex-1 p-4">
            <Textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => onContentChange(e.target.value)}
              placeholder={`Écrivez les paroles pour ${currentSection?.title || 'cette section'}...\n\nMode ${editorMode} • Vue ${viewMode}\n\nUtilisez les outils IA pour vous aider dans la création.`}
              className="w-full h-full resize-none bg-slate-800/50 border-slate-600 text-white placeholder-slate-400 font-mono text-base leading-relaxed"
              style={{ minHeight: '400px' }}
            />
          </div>

          {/* Actions IA rapides */}
          <div className="border-t border-slate-700 bg-slate-800/30 p-3">
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-400 mr-2">Actions IA:</span>
              {lyricsAIActions.map((action) => {
                const Icon = action.icon;
                return (
                  <Button
                    key={action.id}
                    variant="outline"
                    size="sm"
                    onClick={() => handleAIAction(action)}
                    className={`gap-1 ${action.color} text-white border-slate-600 hover:border-slate-500`}
                  >
                    <Icon className="h-4 w-4" />
                    {action.label}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Panneau métriques */}
        {showMetrics && (
          <div className="w-80 border-l border-slate-700 bg-slate-800/30">
            <div className="p-4">
              <h3 className="text-sm font-medium text-white mb-3">Métriques & Analyse</h3>
              
              {/* Statistiques de base */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardContent className="p-3 text-center">
                    <div className="text-lg font-bold text-white">{wordCount}</div>
                    <div className="text-xs text-slate-400">Mots</div>
                  </CardContent>
                </Card>
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardContent className="p-3 text-center">
                    <div className="text-lg font-bold text-white">{lineCount}</div>
                    <div className="text-xs text-slate-400">Lignes</div>
                  </CardContent>
                </Card>
              </div>

              {/* Analyse de la section */}
              <Card className="bg-slate-700/50 border-slate-600 mb-4">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-white">Section Actuelle</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">Type:</span>
                      <Badge variant="outline" className="text-xs capitalize">
                        {currentSection?.type || 'N/A'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Durée:</span>
                      <span className="text-white">{currentSection?.duration || 0}s</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Accords:</span>
                      <span className="text-white">{currentSection?.chords?.length || 0}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Suggestions contextuelles */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-white">Suggestions</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2 text-xs text-slate-400">
                    {wordCount < 20 && (
                      <div className="flex items-center gap-2 text-yellow-400">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                        Section courte - Ajoutez du contenu
                      </div>
                    )}
                    {wordCount > 100 && (
                      <div className="flex items-center gap-2 text-orange-400">
                        <div className="w-2 h-2 bg-orange-400 rounded-full" />
                        Section longue - Considérez diviser
                      </div>
                    )}
                    {!content.trim() && (
                      <div className="flex items-center gap-2 text-blue-400">
                        <div className="w-2 h-2 bg-blue-400 rounded-full" />
                        Utilisez l'IA pour commencer
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* Barre d'état */}
      <div className="border-t border-slate-700 bg-slate-800/50 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-4">
            <span>Mode: {editorModes.find(m => m.id === editorMode)?.label}</span>
            <span>Vue: {viewModes.find(m => m.id === viewMode)?.label}</span>
            <span>Position: Ligne {lineCount}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>{wordCount} mots • {lineCount} lignes</span>
            {isRecording && (
              <Badge variant="destructive" className="animate-pulse">
                Enregistrement...
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
