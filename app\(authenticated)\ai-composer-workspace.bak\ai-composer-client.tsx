'use client';

import React from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { AppSidebar, type UserProfileForSidebar } from "@/components/sidebar";
import MobileMenuButton from "@/app/(authenticated)/mobile-menu-button";
import { Button } from '@/components/ui/button';
import { Music, ArrowRight, PlusCircle } from 'lucide-react';
import { useSong } from "@/hooks/useSong";
import { useUser } from "@/contexts/user-context";

interface AIComposerClientPageProps {
  userObjForSidebar: UserProfileForSidebar;
}

export function AIComposerClientPage({ userObjForSidebar }: AIComposerClientPageProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const songId = searchParams.get('songId') || undefined;
  const { user } = useUser();
  const { createNewSong, isLoading: isCreating } = useSong({ userId: user?.id });

  // Si un songId est fourni, rediriger vers la page spécifique du morceau
  React.useEffect(() => {
    if (songId) {
      router.push(`/ai-composer-workspace/${songId}`);
    }
  }, [songId, router]);

  // Gérer la création d'une nouvelle chanson
  const handleCreateNewSong = async () => {
    if (user?.id) {
      await createNewSong();
    } else {
      router.push('/login');
    }
  };

  return (
    <div className="flex h-screen main-layout-container bg-zinc-950">
      <MobileMenuButton />
      <AppSidebar user={userObjForSidebar} />
      <div className="flex-1 flex flex-col bg-gradient-to-br from-zinc-900 via-zinc-950 to-black">
        <main className="flex-1 overflow-y-auto p-8 flex flex-col items-center justify-center">
          <div className="max-w-2xl w-full text-center text-white">
            <Music className="w-16 h-16 mx-auto mb-6 text-blue-400" />
            <h1 className="text-3xl font-bold mb-4">Espace de travail AI Composer</h1>
            <p className="mb-8 text-zinc-300">Bienvenue dans votre espace de création musicale assistée par IA.</p>
            
            <div className="flex flex-col items-center gap-4">
              <Button 
                size="lg" 
                onClick={handleCreateNewSong} 
                disabled={isCreating}
                className="px-8 py-6 text-lg bg-blue-600 hover:bg-blue-700"
              >
                <PlusCircle className="mr-3 h-6 w-6" />
                {isCreating ? 'Création...' : 'Créer une nouvelle chanson'}
              </Button>
              
              <p className="text-zinc-400 mt-4">Ou accédez à vos chansons existantes depuis le tableau de bord.</p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}