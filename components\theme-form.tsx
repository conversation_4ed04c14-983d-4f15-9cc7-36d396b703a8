"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";

const defaultBg = "/BG3.png";

export function ThemeForm() {
  const [theme, setTheme] = useState("auto");
  const [bg, setBg] = useState(defaultBg);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Thème</h2>
        <div className="flex gap-3">
          <Button variant={theme === "light" ? "default" : "outline"} onClick={() => setTheme("light")}>Clair</Button>
          <Button variant={theme === "dark" ? "default" : "outline"} onClick={() => setTheme("dark")}>Sombre</Button>
          <Button variant={theme === "auto" ? "default" : "outline"} onClick={() => setTheme("auto")}>Auto</Button>
        </div>
      </div>
      <div>
        <h2 className="text-xl font-semibold mb-2">Fond d'écran</h2>
        <div className="flex items-center gap-3">
          <img src={bg} alt="Fond d'écran" className="h-16 w-28 object-cover rounded border" />
          <input type="file" accept="image/*" onChange={e => {
            if (e.target.files && e.target.files[0]) {
              const url = URL.createObjectURL(e.target.files[0]);
              setBg(url);
            }
          }} />
          <Button variant="outline" onClick={() => setBg(defaultBg)}>Réinitialiser</Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">Le fond d'écran s'applique à tout le site. BG3.png par défaut.</p>
      </div>
      <div className="mt-6">
        <div className="rounded-lg border p-4" style={{ background: `url('${bg}') center/cover` }}>
          <span className="text-xs text-white drop-shadow">Aperçu en temps réel</span>
        </div>
      </div>
    </div>
  );
}
