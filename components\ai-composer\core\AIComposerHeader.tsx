'use client';

import React, { useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, Music, Play, Pause, Download, Edit3, ArrowRight, Save
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import type { CurrentSong, StyleConfig, LyricsSection } from './AIComposerCore';

interface AIComposerHeaderProps {
  songId?: string;
  currentSong: CurrentSong;
  styleConfig: StyleConfig;
  songSections: LyricsSection[];
  lyricsContent: string;
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
  saving: boolean;
  setSaving: (saving: boolean) => void;
  router: any;
  supabase: any;
  user: any;
  extractChordProgressions: () => string[];
  extractSongStructure: () => any[];
}

export const AIComposerHeader: React.FC<AIComposerHeaderProps> = ({
  songId,
  currentSong,
  styleConfig,
  songSections,
  lyricsContent,
  isPlaying,
  setIsPlaying,
  saving,
  setSaving,
  router,
  supabase,
  user,
  extractChordProgressions,
  extractSongStructure
}) => {

  // Fonction pour sauvegarder la chanson actuelle
  const handleSaveSong = useCallback(async () => {
    if (!user) {
      toast({
        title: 'Erreur',
        description: 'Vous devez être connecté pour sauvegarder.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSaving(true);
      
      // Préparer les données de la chanson avec tous les champs corrects
      const songData = {
        title: currentSong.title,
        artist: currentSong.artist,
        lyrics: lyricsContent,
        key: currentSong.key,
        musical_key: currentSong.key, // Ajouter musical_key pour compatibilité
        bpm: currentSong.tempo,
        time_signature: currentSong.timeSignature,
        genre: styleConfig.genres?.[0] || null,
        moods: styleConfig.moods || [],
        instruments: styleConfig.instrumentation || [],
        structure: songSections.map(s => s.type).join(', '),
        chords: extractChordProgressions().join(', '),
        // Métadonnées AI Composer dans editor_data
        editor_data: {
          ai_composer: {
            sections: songSections,
            styleConfig: styleConfig,
            chordProgressions: extractChordProgressions(),
            structure: extractSongStructure(),
            lastModified: new Date().toISOString()
          }
        },
        creator_user_id: user.user?.id || '',
        updated_at: new Date().toISOString(),
        is_incomplete: false, // Marquer comme complète lors de la sauvegarde
        ai_assistance_level: 'high' // Indiquer l'utilisation de l'IA
      };

      let result;
      if (songId) {
        // Mettre à jour la chanson existante
        result = await supabase
          .from('songs')
          .update(songData)
          .eq('id', songId)
          .select()
          .single();
      } else {
        // Créer une nouvelle chanson
        result = await supabase
          .from('songs')
          .insert(songData)
          .select()
          .single();
      }

      if (result.error) throw result.error;

      toast({
        title: 'Succès',
        description: songId ? 'Chanson mise à jour avec succès!' : 'Chanson sauvegardée avec succès!'
      });
      
      // Si c'est une nouvelle chanson, mettre à jour l'URL
      if (!songId && result.data) {
        const newSongId = result.data.id;
        router.push(`/ai-composer-workspace?songId=${newSongId}`);
        
        // Proposer de naviguer vers l'éditeur
        setTimeout(() => {
          toast({
            title: 'Succès',
            description: 'Chanson créée! Vous pouvez maintenant l\'éditer.'
          });
        }, 1000);
      }
      
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de la sauvegarde.',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  }, [
    user, currentSong, lyricsContent, styleConfig, songSections, songId, 
    supabase, router, extractChordProgressions, extractSongStructure, setSaving
  ]);

  // Fonction pour créer une nouvelle chanson
  const handleSaveAsSong = useCallback(async () => {
    if (!user) {
      toast({
        title: 'Erreur',
        description: 'Vous devez être connecté pour sauvegarder une chanson.',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      setSaving(true);
      
      // Formater toutes les sections avec accords
      let fullLyrics = '';
      songSections.forEach((section, index) => {
        if (index > 0) fullLyrics += '\n\n';
        fullLyrics += `[${section.title}]\n`;
        
        const lines = section.content.split('\n');
        lines.forEach((line, lineIndex) => {
          // Vérifier s'il y a des accords pour cette ligne
          const chordsForLine = section.chords?.filter(chord => 
            chord.position !== undefined
          ) || [];
          
          if (chordsForLine.length > 0) {
            // Créer une ligne d'accords positionnés
            let chordLine = '';
            let lastPos = 0;
            
            // Trier les accords par position
            const sortedChords = chordsForLine.sort((a, b) => 
              (a.position || 0) - (b.position || 0)
            );
            
            sortedChords.forEach(chord => {
              const pos = chord.position || 0;
              chordLine += ' '.repeat(Math.max(0, pos - lastPos)) + chord.chord;
              lastPos = pos + chord.chord.length;
            });
            
            fullLyrics += '  ' + chordLine + '\n';
          }
          
          fullLyrics += ' ' + line + '\n';
        });
      });
      
      // Créer les données de la chanson
      const songData = {
        title: currentSong.title || 'Nouvelle composition AI',
        artist: currentSong.artist || 'Artiste',
        lyrics: fullLyrics,
        key: currentSong.key,
        musical_key: currentSong.key,
        tempo: currentSong.tempo,
        time_signature: currentSong.timeSignature,
        bpm: currentSong.tempo,
        genre: styleConfig.genres?.[0] || '',
        moods: styleConfig.moods || [],
        instruments: styleConfig.instrumentation || [],
        structure: songSections.map(s => s.type).join(', '),
        chords: extractChordProgressions().join(', '),
        editor_data: {
          ai_composer: {
            sections: songSections,
            styleConfig: styleConfig,
            chordProgressions: extractChordProgressions(),
            structure: extractSongStructure(),
            lastModified: new Date().toISOString()
          }
        },
        creator_user_id: user.user?.id || '',
        is_incomplete: false,
        is_public: false,
        ai_assistance_level: 'high' as const
      };
      
      // Appeler l'API pour créer la chanson
      const { data: newSong, error } = await supabase
        .from('songs')
        .insert([songData])
        .select()
        .single();
      
      if (error) throw error;
      
      toast({
        title: 'Succès',
        description: 'Chanson sauvegardée avec succès!'
      });
      
      // Naviguer vers l'éditeur de la nouvelle chanson
      router.push(`/manage-songs/${newSong.id}/edit`);
      
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de la sauvegarde de la chanson.',
        variant: 'destructive'
      });
    }
  }, [songSections, currentSong, styleConfig, user, supabase, router]);

  const handleNavigateToEditor = useCallback(() => {
    if (songId) {
      router.push(`/manage-songs/${songId}/edit`);
    } else {
      // Créer une nouvelle chanson et naviguer vers l'éditeur
      router.push('/manage-songs/new');
    }
  }, [songId, router]);

  return (
    <div className="border-b bg-card">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
            <Sparkles className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI Composer
            </h1>
            <div className="flex items-center gap-2">
              <p className="text-sm text-muted-foreground">
                {songId ? `Édition: ${currentSong.title}` : 'Créez et composez avec l\'intelligence artificielle'}
              </p>
              {songId && (
                <Badge variant="outline" className="text-xs">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1" />
                  Synchronisé
                </Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="gap-1">
            <Music className="h-3 w-3" />
            {currentSong.key} • {currentSong.tempo} BPM
          </Badge>
          
          <Button
            variant={isPlaying ? "default" : "outline"}
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className="gap-1"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            {isPlaying ? 'Pause' : 'Jouer'}
          </Button>
          
          <Button variant="outline" size="sm" className="gap-1">
            <Download className="h-4 w-4" />
            Exporter
          </Button>
          
          <div className="flex items-center gap-2">
            <Button 
              onClick={songId ? handleSaveSong : handleSaveAsSong}
              variant="outline" 
              size="sm" 
              className="gap-1 border-green-500 text-green-600 hover:bg-green-50"
              disabled={saving}
            >
              <Save className="h-4 w-4" />
              {saving ? 'Sauvegarde...' : songId ? 'Sauvegarder' : 'Sauvegarder comme nouvelle'}
            </Button>
            
            {songId && (
              <Button 
                onClick={() => router.push(`/manage-songs/${songId}/edit`)}
                variant="outline" 
                size="sm" 
                className="gap-1 border-blue-500 text-blue-600 hover:bg-blue-50"
              >
                <Edit3 className="h-4 w-4" />
                Éditer la chanson
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <Button 
            onClick={handleNavigateToEditor} 
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 gap-1" 
            size="sm"
          >
            <Edit3 className="h-4 w-4" />
            Éditeur Complet
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
