"use client";

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormDescription, FormMessage } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { MultiSelect } from '@/components/ui/multi-select';
import { type ProfileFormValues } from './profile-form-schema';

const aiToolOptions = [
  { value: 'chatgpt', label: 'ChatGPT' },
  { value: 'suno', label: 'Suno' },
  { value: 'udio', label: 'Udio' },
  { value: 'stable-audio', label: 'Stable Audio' },
  { value: 'aiva', label: 'AIVA' },
  { value: 'soundraw', label: 'Soundraw' },
  { value: 'autre', label: 'Autre' },
];

export function ProfileSettingsTab() {
  const { control, watch } = useFormContext<ProfileFormValues>();

  const aiUsageLevel = watch('ai_usage_level');

  return (
    <Card>
      <CardHeader>
        <CardTitle>Paramètres & Confidentialité</CardTitle>
        <CardDescription>Gérez la visibilité de votre profil et vos préférences de collaboration.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        <FormField
          control={control}
          name="is_profile_public"
          render={({ field }) => (
            <FormItem className="rounded-lg border p-4">
              <FormLabel className="text-base font-medium">Profil Public</FormLabel>
              <FormDescription className="mb-3">
                Votre profil sera visible par tous les utilisateurs et visiteurs du site.
              </FormDescription>
              <FormControl>
                <RadioGroup
                  onValueChange={(value) => field.onChange(value === 'true')}
                  defaultValue={String(field.value)}
                  className="flex flex-col space-y-1 md:flex-row md:space-x-4 md:space-y-0"
                >
                  <FormItem className="flex items-center space-x-2 rounded-md border p-2 pl-3 pr-4 shadow-sm">
                    <FormControl>
                      <RadioGroupItem value="true" />
                    </FormControl>
                    <FormLabel className="font-normal cursor-pointer">Oui, rendre mon profil public</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 rounded-md border p-2 pl-3 pr-4 shadow-sm">
                    <FormControl>
                      <RadioGroupItem value="false" />
                    </FormControl>
                    <FormLabel className="font-normal cursor-pointer">Non, garder mon profil privé</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="open_to_collab"
          render={({ field }) => (
            <FormItem className="rounded-lg border p-4">
              <FormLabel className="text-base font-medium">Ouvert aux collaborations</FormLabel>
              <FormDescription className="mb-3">
                Indiquez aux autres artistes que vous êtes intéressé(e) par des projets communs.
              </FormDescription>
              <FormControl>
                <RadioGroup
                  onValueChange={(value) => field.onChange(value === 'true')}
                  defaultValue={String(field.value)}
                  className="flex flex-col space-y-1 md:flex-row md:space-x-4 md:space-y-0"
                >
                  <FormItem className="flex items-center space-x-2 rounded-md border p-2 pl-3 pr-4 shadow-sm">
                    <FormControl>
                      <RadioGroupItem value="true" />
                    </FormControl>
                    <FormLabel className="font-normal cursor-pointer">Oui, je suis ouvert aux collaborations</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 rounded-md border p-2 pl-3 pr-4 shadow-sm">
                    <FormControl>
                      <RadioGroupItem value="false" />
                    </FormControl>
                    <FormLabel className="font-normal cursor-pointer">Non, pas pour le moment</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />

        <div className="rounded-lg border p-4">
            <div className="space-y-0.5 mb-4">
                <FormLabel className="text-base">Utilisation de l'Intelligence Artificielle</FormLabel>
                <FormDescription>
                    Comment utilisez-vous l'IA dans votre processus créatif ?
                </FormDescription>
            </div>
            <FormField
              control={control}
              name="ai_usage_level"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl><RadioGroupItem value="none" /></FormControl>
                        <FormLabel className="font-normal">Aucune</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl><RadioGroupItem value="light" /></FormControl>
                        <FormLabel className="font-normal">Légère (inspiration, idées)</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl><RadioGroupItem value="moderate" /></FormControl>
                        <FormLabel className="font-normal">Modérée (aide à la composition, arrangements)</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl><RadioGroupItem value="heavy" /></FormControl>
                        <FormLabel className="font-normal">Intensive (génération d'éléments principaux, mixage)</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {aiUsageLevel && aiUsageLevel !== 'none' && (
                <div className="mt-4">
                    <FormField
                      control={control}
                      name="ai_tools"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Outils IA utilisés</FormLabel>
                          <MultiSelect
                            options={aiToolOptions}
                            selected={field.value ?? []}
                            onChange={field.onChange}
                            placeholder="Sélectionnez les outils que vous utilisez..."
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                </div>
            )}
        </div>

      </CardContent>
    </Card>
  );
}
