import { createSupabaseServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { MessageList } from "@/components/messages/message-list"
import { ConversationView } from "@/components/messages/conversation-view"

export default async function MessagesPage({
  searchParams,
}: {
  searchParams: { conversation?: string }
}) {
  const supabase = createSupabaseServerClient()
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login")
  }

  const { data: conversations } = await supabase
    .from("conversations")
    .select(`
      id,
      updated_at,
      conversation_participants!inner (
        user_id
      ),
      messages (
        id,
        content,
        created_at,
        sender_id
      )
    `)
    .eq("conversation_participants.user_id", session.user.id)
    .order("updated_at", { ascending: false })

  // Récupérer les informations des participants pour chaque conversation
  const enhancedConversations = await Promise.all(
    (conversations || []).map(async (conversation) => {
      const { data: participants } = await supabase
        .from("conversation_participants")
        .select(`
        user_id,
        users:user_id (
          id,
          email,
          user_metadata
        )
      `)
        .eq("conversation_id", conversation.id)
        .neq("user_id", session.user.id)

      // Récupérer le dernier message
      const lastMessage =
        conversation.messages && conversation.messages.length > 0
          ? conversation.messages.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
          : null

      return {
        ...conversation,
        participants: participants || [],
        lastMessage,
      }
    }),
  )

  const selectedConversationId = searchParams.conversation || null

  return (
    <div className="container mx-auto py-6">
      <h1 className="mb-6 text-2xl font-bold">Messages</h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="md:col-span-1">
          <MessageList
            conversations={enhancedConversations}
            currentUserId={session.user.id}
            selectedConversationId={selectedConversationId}
          />
        </div>

        <div className="md:col-span-2">
          {selectedConversationId ? (
            <ConversationView conversationId={selectedConversationId} currentUserId={session.user.id} />
          ) : (
            <div className="flex h-[500px] items-center justify-center rounded-lg border border-dashed">
              <p className="text-center text-gray-500">Sélectionnez une conversation pour afficher les messages</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
