import { notFound } from 'next/navigation';
import { type Song } from '@/components/songs/song-schema';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getUserProfileForSidebar } from '@/lib/supabase/queries/user'; 
import { ComposerLayout } from '@/components/layouts/ComposerLayout';
import ComposerStudioWrapper from '@/components/ai-composer/mega/ComposerStudioWrapper';



interface SongWorkspacePageProps {
  params: {
    songId: string;
  };
}

export default async function SongWorkspacePage({ params }: SongWorkspacePageProps) {
  try {
  const { songId } = params;
  const supabase = await createSupabaseServerClient();

  // 1. Fetch user and song data in parallel
  const [userResponse, songResponse] = await Promise.all([
    supabase.auth.getUser(),
    supabase.from('songs').select<'*', Song>('*').eq('id', songId).single(),
  ]);

  const { data: { user }, error: userError } = userResponse;
  const { data: song, error: songError } = songResponse;

  // 2. Validate data and user profile
  if (userError || !user) {
    console.error('[SongWorkspacePage] User not found or error fetching user:', userError);
    return notFound();
  }

  if (songError || !song) {
    console.error(`[SongWorkspacePage] Song with ID ${songId} not found or error fetching song:`, songError);
    return notFound();
  }

  const userProfile = await getUserProfileForSidebar(supabase, user.id, user);
  if (!userProfile) {
    console.error(`[SongWorkspacePage] User profile not found for user ${user.id}`);
    return notFound();
  }

  // 3. Render the layout with the fetched data
  return (
    <ComposerLayout userProfile={userProfile}>
      <ComposerStudioWrapper
        songId={songId}
        initialSongData={song}
        userProfile={userProfile}
      />
    </ComposerLayout>
  );
  } catch (error) {
    console.error('[SongWorkspacePage] Caught a critical error:', error);
    if (error instanceof Error) {
        return <div>An unexpected error occurred: {error.message}</div>;
    }
    return <div>An unexpected error occurred.</div>;
  }
}