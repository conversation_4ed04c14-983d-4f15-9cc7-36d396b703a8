# Module d'Analyse IA - Plan de Refactoring

Ce document décrit le plan d'action pour extraire les fonctionnalités d'analyse IA en composants réutilisables, comme défini dans la documentation technique `ai-profile-analysis-module.md`.

## Objectif

L'objectif est de rendre la logique d'analyse et l'interface utilisateur (UI) d'affichage des résultats complètement réutilisables pour d'autres contextes d'analyse (morceaux, albums, statistiques).

## Tâches

### 1. Création d'un Hook Réutilisable : `useAiAnalysis`

-   **Créer un nouveau fichier** : `hooks/useAiAnalysis.ts`
-   **Extraire la logique d'état** de `advanced-settings-tab.tsx` :
    -   `analysisKeywords`, `setAnalysisKeywords`
    -   `analysisPrompt`, `setAnalysisPrompt`
    -   `profileAnalysis`, `setProfileAnalysis` (sera renommé en `analysis`, `setAnalysis`)
    -   `isGeneratingProfile`, `setIsGeneratingProfile` (sera renommé en `isGenerating`, `setIsGenerating`)
    -   `profileAnalysisError`, `setProfileAnalysisError` (sera renommé en `error`, `setError`)
-   **Extraire la logique de persistance** :
    -   Le `useEffect` qui charge les données depuis `localStorage`.
    -   Rendre la clé `localStorage` dynamique en fonction du contexte (ex: `mouvik_profile_analysis`, `mouvik_song_analysis_123`).
-   **Extraire la logique de génération** :
    -   La fonction `generateProfileAnalysis` sera transformée en une fonction `generateAnalysis` plus générique.
    -   La construction du prompt sera adaptée en fonction du type de contexte (`profile`, `song`, `album`, `stats`).
-   **Le hook retournera** :
    -   `analysis`
    -   `isGenerating`
    -   `error`
    -   `analysisKeywords`, `setAnalysisKeywords`
    -   `analysisPrompt`, `setAnalysisPrompt`
    -   `generateAnalysis` (fonction pour lancer l'analyse)
    -   `clearAnalysis` (fonction pour effacer l'analyse)

### 2. Création d'un Composant d'Affichage Réutilisable : `AnalysisDisplay`

-   **Créer un nouveau fichier** : `components/ai/AnalysisDisplay.tsx`
-   **Extraire le JSX d'affichage** de `advanced-settings-tab.tsx`.
-   **Le composant acceptera des props** :
    -   `analysis`: L'objet d'analyse à afficher.
    -   `title`: Un titre personnalisable (ex: "Analyse de Profil", "Analyse du Morceau").
    -   `icon`: Une icône personnalisable.
-   **Le composant gérera** :
    -   L'affichage du titre, du fournisseur IA, des mots-clés et du mini-prompt.
    -   Le rendu du contenu HTML de l'analyse.
    -   L'affichage du timestamp.

### 3. Refactoring du Composant `advanced-settings-tab.tsx`

-   **Supprimer** toute la logique d'état et les fonctions qui ont été déplacées dans le hook `useAiAnalysis`.
-   **Importer et utiliser** le hook `useAiAnalysis` pour gérer l'état et les actions.
-   **Importer et utiliser** le composant `AnalysisDisplay` pour afficher les résultats.
-   **Conserver** uniquement les éléments d'interface spécifiques à la page des paramètres (champs de saisie pour les mots-clés et le mini-prompt, bouton de génération).

## Prochaines Étapes (Implémentation)

1.  Créer le répertoire `hooks` s'il n'existe pas.
2.  Créer le fichier `hooks/useAiAnalysis.ts`.
3.  Créer le répertoire `components/ai` s'il n'existe pas.
4.  Créer le fichier `components/ai/AnalysisDisplay.tsx`.
5.  Implémenter le hook `useAiAnalysis` en déplaçant le code depuis `advanced-settings-tab.tsx`.
6.  Implémenter le composant `AnalysisDisplay` en déplaçant le JSX depuis `advanced-settings-tab.tsx`.
7.  Mettre à jour `advanced-settings-tab.tsx` pour utiliser le nouveau hook et le nouveau composant.
