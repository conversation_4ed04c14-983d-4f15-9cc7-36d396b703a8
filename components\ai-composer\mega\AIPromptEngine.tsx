'use client';

import React from 'react';

// Types pour les prompts
export interface PromptContext {
  // Contexte musical
  genre: string;
  key: string;
  bpm: number;
  timeSignature: string;
  mood: string;
  capo?: number;
  
  // Contexte du projet
  generalPrompt: string;
  songTitle: string;
  artist: string;
  description: string;
  
  // Contexte de la section
  currentSection: {
    id: string;
    type: string;
    title: string;
    content: string;
    chords: any[];
    duration: number;
  };
  
  // Contexte global
  allSections: any[];
  totalWords: number;
  totalSections: number;
  completionScore: number;
  
  // Historique IA
  recentInteractions: string[];
}

export class AIPromptEngine {
  
  // Générateur de contexte musical complet
  static generateMusicContext(context: PromptContext): string {
    return `
🎵 CONTEXTE MUSICAL COMPLET :

📊 PARAMÈTRES TECHNIQUES :
• Genre principal : ${context.genre}
• Tonalité : ${context.key} majeur
• Tempo : ${context.bpm} BPM
• Mesure : ${context.timeSignature}
• Ambiance : ${context.mood}
${context.capo ? `• Capo : Case ${context.capo}` : ''}

🎯 VISION ARTISTIQUE :
${context.generalPrompt}

🎼 INFORMATIONS DU MORCEAU :
• Titre : "${context.songTitle}"
• Artiste : ${context.artist}
• Description : ${context.description}

📈 ÉTAT DU PROJET :
• Progression : ${context.completionScore}%
• Sections totales : ${context.totalSections}
• Mots totaux : ${context.totalWords}
• Structure actuelle : ${context.allSections.map(s => s.type).join(' → ')}

🎵 SECTION ACTUELLE :
• Type : ${context.currentSection.type}
• Titre : "${context.currentSection.title}"
• Durée : ${context.currentSection.duration}s
• Contenu actuel : "${context.currentSection.content.substring(0, 200)}${context.currentSection.content.length > 200 ? '...' : ''}"
• Accords : ${context.currentSection.chords.map(c => c.name).join(' - ') || 'Aucun'}

${context.recentInteractions.length > 0 ? `
🔄 INTERACTIONS RÉCENTES :
${context.recentInteractions.slice(-3).map((interaction, i) => `${i + 1}. ${interaction.substring(0, 100)}...`).join('\n')}
` : ''}
`;
  }

  // Prompts pour suggestions intelligentes
  static getSuggestionPrompts(context: PromptContext) {
    const musicContext = this.generateMusicContext(context);
    
    return {
      // Suggestions créatives
      continueWriting: `${musicContext}

🎯 MISSION : CONTINUER L'ÉCRITURE

Analyse le contenu actuel et continue l'écriture de cette section de ${context.currentSection.type} en respectant :

1. COHÉRENCE STYLISTIQUE :
   • Maintenir le style ${context.genre}
   • Respecter l'ambiance ${context.mood}
   • Garder la cohérence avec la vision artistique

2. STRUCTURE MUSICALE :
   • Adapter au tempo ${context.bpm} BPM
   • Respecter la mesure ${context.timeSignature}
   • Créer des phrases musicales équilibrées

3. PROGRESSION NARRATIVE :
   • Développer le message existant
   • Maintenir la cohérence émotionnelle
   • Créer une progression logique

4. TECHNIQUES D'ÉCRITURE :
   • Utiliser des rimes riches et variées
   • Créer des images poétiques fortes
   • Adapter le vocabulaire au genre

CONSIGNE : Écris 4-8 lignes qui continuent naturellement le contenu existant, en gardant le même niveau d'énergie et la même perspective narrative.`,

      improveContent: `${musicContext}

🎯 MISSION : AMÉLIORER LE CONTENU EXISTANT

Analyse le contenu actuel et propose une version améliorée qui optimise :

1. QUALITÉ POÉTIQUE :
   • Enrichir les rimes (riches, embrassées, croisées)
   • Améliorer les images et métaphores
   • Renforcer l'impact émotionnel
   • Éliminer les répétitions faibles

2. MUSICALITÉ :
   • Optimiser le flow pour ${context.bpm} BPM
   • Améliorer la prosodie
   • Créer des accents naturels
   • Faciliter la mémorisation

3. COHÉRENCE NARRATIVE :
   • Clarifier le message principal
   • Renforcer la progression logique
   • Améliorer les transitions
   • Éliminer les incohérences

4. ADAPTATION AU GENRE ${context.genre.toUpperCase()} :
   • Respecter les codes stylistiques
   • Utiliser le vocabulaire approprié
   • Adapter le registre de langue
   • Créer l'émotion attendue

CONSIGNE : Propose une version améliorée du contenu existant, en expliquant les changements apportés et leurs bénéfices.`,

      generateRhymes: `${musicContext}

🎯 MISSION : SUGGESTIONS DE RIMES CRÉATIVES

Analyse le contenu et propose des suggestions de rimes pour enrichir cette section :

1. ANALYSE DES RIMES EXISTANTES :
   • Identifier les schémas de rimes actuels
   • Repérer les opportunités d'amélioration
   • Analyser la richesse rimique

2. SUGGESTIONS DE MOTS RIMANTS :
   • Proposer des rimes riches et originales
   • Éviter les rimes banales ou clichés
   • Adapter au contexte sémantique
   • Maintenir la cohérence du message

3. ALTERNATIVES CRÉATIVES :
   • Rimes internes et assonances
   • Allitérations et jeux sonores
   • Rimes approximatives créatives
   • Effets rythmiques

4. INTÉGRATION CONTEXTUELLE :
   • Respecter le thème de la chanson
   • Maintenir l'ambiance ${context.mood}
   • Adapter au style ${context.genre}
   • Préserver le sens global

CONSIGNE : Propose 10-15 mots ou expressions qui riment avec les fins de lignes actuelles, en expliquant comment les intégrer créativement.`,

      addChords: `${musicContext}

🎯 MISSION : SUGGESTIONS D'ACCORDS HARMONIQUES

Analyse le contenu et propose une progression d'accords parfaite pour cette section :

1. ANALYSE HARMONIQUE :
   • Tonalité : ${context.key} majeur
   • Type de section : ${context.currentSection.type}
   • Durée : ${context.currentSection.duration}s
   • Ambiance recherchée : ${context.mood}

2. PROGRESSION RECOMMANDÉE :
   • Accords principaux adaptés au ${context.genre}
   • Fonctions harmoniques (I, IV, V, vi, etc.)
   • Couleurs harmoniques pour l'émotion
   • Transitions fluides

3. ADAPTATION AU CONTENU :
   • Synchronisation avec les paroles
   • Soutien de l'émotion du texte
   • Accents sur les mots importants
   • Respiration musicale

4. VARIANTES CRÉATIVES :
   • Extensions d'accords (7ème, 9ème)
   • Accords de passage
   • Modulations possibles
   • Effets harmoniques spéciaux

CONSIGNE : Propose une progression de 4-8 accords avec leur position dans la section, en expliquant les choix harmoniques et leur effet émotionnel.`,

      analyzeStructure: `${musicContext}

🎯 MISSION : ANALYSE STRUCTURELLE COMPLÈTE

Analyse la structure actuelle et propose des optimisations :

1. ARCHITECTURE GLOBALE :
   • Équilibre des sections
   • Progression dramatique
   • Points culminants
   • Cohérence du parcours

2. ANALYSE DE LA SECTION ACTUELLE :
   • Fonction dans la chanson
   • Niveau d'énergie approprié
   • Durée optimale
   • Transitions entrante/sortante

3. COMPARAISON AVEC LES STANDARDS ${context.genre.toUpperCase()} :
   • Structure typique du genre
   • Innovations acceptables
   • Risques et opportunités
   • Potentiel commercial

4. RECOMMANDATIONS D'OPTIMISATION :
   • Réorganisation possible
   • Ajouts/suppressions
   • Variations créatives
   • Améliorations des transitions

CONSIGNE : Fournis une analyse détaillée avec des recommandations concrètes pour optimiser la structure globale et la section actuelle.`
    };
  }

  // Prompts pour analyses multi-dimensionnelles
  static getAnalysisPrompts(context: PromptContext, dimension: string, level: string) {
    const musicContext = this.generateMusicContext(context);
    
    const prompts = {
      structure: {
        section: `${musicContext}

🏗️ ANALYSE STRUCTURELLE DE SECTION

Effectue une analyse approfondie de la structure de cette section :

1. ORGANISATION INTERNE :
   • Découpage en phrases musicales (A/B/A'/B')
   • Équilibre des parties (nombre de mesures)
   • Points de tension et résolution
   • Cohérence métrique et rythmique

2. FONCTION DANS LA CHANSON :
   • Rôle par rapport aux autres sections
   • Niveau d'énergie approprié (1-10)
   • Transition entrante (comment on arrive)
   • Transition sortante (comment on repart)
   • Mémorabilité et impact

3. OPTIMISATIONS STRUCTURELLES :
   • Réorganisation des phrases
   • Ajouts ou suppressions recommandés
   • Variations rythmiques possibles
   • Points d'accroche à renforcer

4. BENCHMARKING GENRE ${context.genre.toUpperCase()} :
   • Comparaison avec les standards
   • Innovations créatives possibles
   • Respect des attentes du public
   • Potentiel de différenciation

CONSIGNE : Fournis une analyse détaillée avec des recommandations concrètes et applicables immédiatement.`,

        song: `${musicContext}

🏗️ ANALYSE STRUCTURELLE GLOBALE

Analyse la structure complète de cette chanson :

SECTIONS ACTUELLES : ${context.allSections.map(s => `${s.type} (${s.duration}s)`).join(' → ')}

1. ARCHITECTURE GÉNÉRALE :
   • Équilibre des sections (durées et intensités)
   • Progression dramatique (courbe d'énergie)
   • Points culminants et respirations
   • Cohérence du parcours musical
   • Durée totale et format commercial

2. COMPARAISON AVEC LES STANDARDS ${context.genre.toUpperCase()} :
   • Structure typique du genre
   • Écarts créatifs justifiés
   • Opportunités d'innovation
   • Risques commerciaux identifiés

3. ANALYSE DES TRANSITIONS :
   • Fluidité entre les sections
   • Contrastes et similitudes
   • Éléments de liaison
   • Surprises et attentes

4. OPTIMISATIONS RECOMMANDÉES :
   • Réorganisation des sections
   • Ajout/suppression de parties
   • Variations et reprises
   • Améliorations des transitions

CONSIGNE : Propose un plan de restructuration détaillé avec justifications artistiques et commerciales.`
      },

      emotion: {
        section: `${musicContext}

❤️ ANALYSE ÉMOTIONNELLE DE SECTION

Analyse l'impact émotionnel de cette section :

1. PALETTE ÉMOTIONNELLE ACTUELLE :
   • Émotions primaires exprimées
   • Nuances et subtilités
   • Intensité émotionnelle (1-10)
   • Authenticité du ressenti
   • Universalité vs spécificité

2. TECHNIQUES EXPRESSIVES UTILISÉES :
   • Choix lexicaux et leur impact
   • Rythme et prosodie émotionnelle
   • Images et métaphores évocatrices
   • Progression émotionnelle interne
   • Contrastes et climax

3. OPTIMISATIONS ÉMOTIONNELLES :
   • Mots plus puissants à utiliser
   • Contrastes émotionnels à créer
   • Moments de vulnérabilité à placer
   • Pics d'intensité à positionner
   • Techniques de connexion au public

4. COHÉRENCE AVEC L'AMBIANCE ${context.mood.toUpperCase()} :
   • Adéquation avec l'ambiance cible
   • Écarts créatifs justifiés
   • Renforcement de l'émotion principale
   • Évitement des contradictions

CONSIGNE : Propose des modifications concrètes pour maximiser l'impact émotionnel tout en gardant l'authenticité.`,

        song: `${musicContext}

❤️ PARCOURS ÉMOTIONNEL GLOBAL

Analyse le voyage émotionnel de la chanson complète :

1. CARTOGRAPHIE ÉMOTIONNELLE :
   • Émotion dominante de chaque section
   • Transitions émotionnelles entre sections
   • Cohérence du parcours global
   • Points de rupture ou surprise
   • Résolution émotionnelle finale

2. DYNAMIQUE ÉMOTIONNELLE :
   • Montée en puissance progressive
   • Moments de répit et respiration
   • Climax émotionnel principal
   • Résolution satisfaisante
   • Mémorabilité émotionnelle

3. STRATÉGIE D'IMPACT :
   • Émotions cibles du public ${context.genre}
   • Techniques de connexion utilisées
   • Universalité vs spécificité du message
   • Potentiel d'identification personnelle
   • Mémorabilité à long terme

4. OPTIMISATION DU PARCOURS :
   • Renforcement des moments forts
   • Amélioration des transitions
   • Équilibrage des intensités
   • Création de surprises contrôlées

CONSIGNE : Dessine un parcours émotionnel optimisé avec des recommandations précises pour chaque section.`
      }
    };

    return prompts[dimension]?.[level] || `Analyse ${dimension} niveau ${level} pour cette section.`;
  }

  // Prompts pour le chat contextuel
  static getChatPrompt(context: PromptContext, userMessage: string): string {
    const musicContext = this.generateMusicContext(context);
    
    return `${musicContext}

🎯 ASSISTANT IA MUSICAL EXPERT

Tu es un expert en composition musicale, production et écriture de chansons. L'utilisateur travaille sur le projet décrit ci-dessus et te pose une question.

CONSIGNES POUR TES RÉPONSES :

1. EXPERTISE MUSICALE :
   • Utilise tes connaissances approfondies en théorie musicale
   • Adapte tes conseils au genre ${context.genre}
   • Considère le contexte technique (${context.key}, ${context.bpm} BPM, ${context.timeSignature})
   • Respecte l'ambiance ${context.mood}

2. CONTEXTUALISATION :
   • Tiens compte de la vision artistique du projet
   • Considère l'état d'avancement (${context.completionScore}%)
   • Adapte tes suggestions à la section actuelle (${context.currentSection.type})
   • Utilise les informations du morceau

3. RÉPONSES PRATIQUES :
   • Donne des conseils concrets et applicables
   • Propose des exemples spécifiques
   • Suggère des techniques précises
   • Évite les généralités

4. CRÉATIVITÉ ET INNOVATION :
   • Encourage l'originalité dans le respect du genre
   • Propose des alternatives créatives
   • Suggère des expérimentations intéressantes
   • Inspire de nouvelles idées

QUESTION DE L'UTILISATEUR :
"${userMessage}"

CONSIGNE : Réponds de manière experte, créative et pratique, en tenant compte de tout le contexte du projet.`;
  }
}

export default AIPromptEngine;
