"use client";

import React, { createContext, useContext, ReactNode, useMemo } from 'react';
import type { UserProfileForSidebar } from '@/components/sidebar'; // Assuming UserProfileForSidebar is exported

interface UserContextType {
  user: UserProfileForSidebar | null;
  isLoading: boolean;
  error: string | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
  value: {
    user: UserProfileForSidebar | null;
    isLoading: boolean;
    error: string | null;
  };
}

export const UserProvider = ({ children, value }: UserProviderProps) => {
  // This ensures contextValue only changes if user, isLoading, or error change.
  const contextValue = useMemo(() => {
    return {
      user: value.user,
      isLoading: value.isLoading,
      error: value.error,
    };
  }, [value.user, value.isLoading, value.error]); // Depend on individual properties

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
