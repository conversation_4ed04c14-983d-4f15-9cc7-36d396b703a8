'use client';

import React from 'react';
import { UnifiedAIWorkspace } from './UnifiedAIWorkspace';
// import { useComposerCallbacks } from '@/components/composer/ComposerCallbacks'; // Removed

// Assuming UserProfileForSidebar and AIHistoryItem types are available or will be imported
// For now, let's define them structurally for clarity if not imported from a central types file.
interface UserProfileForSidebar {
  email?: string | null | undefined; // Make email property optional and allow null/undefined
  username?: string | null | undefined; // Allow null for username
  avatarUrl?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  id?: string;
}

interface AIHistoryItem {
  id: string;
  prompt: string;
  result: string;
  type: string;
  timestamp: Date;
  applied: boolean;
}

interface AIWorkspaceSidebarProps {
  onAIGenerate: (prompt: string, type: string) => Promise<string>;
  onInsertText: (text: string) => void;
  onInsertChord: (chord: string) => void;
  userProfile?: UserProfileForSidebar; // As per checkpoint requirements
  aiHistory?: AIHistoryItem[]; // Passed to UnifiedAIWorkspace
  isLoading?: boolean; // Passed to UnifiedAIWorkspace
  currentSection?: string; // Passed to UnifiedAIWorkspace
  selectedText?: string; // Passed to UnifiedAIWorkspace
  // Add onHistoryItemApply and onHistoryItemDelete if the sidebar's UnifiedAIWorkspace needs them
  // and if the page level will manage history application/deletion for the sidebar instance.
  // For now, keeping it aligned with the direct props UnifiedAIWorkspace takes for generation/insertion.
}

export const AIWorkspaceSidebar: React.FC<AIWorkspaceSidebarProps> = ({
  onAIGenerate,
  onInsertText,
  onInsertChord,
  userProfile, // Not directly used by UnifiedAIWorkspace by default, but good to have if needed
  aiHistory,
  isLoading,
  currentSection,
  selectedText
}) => {
  // const callbacks = useComposerCallbacks(); // Removed
  return (
    <UnifiedAIWorkspace 
      onAIGenerate={onAIGenerate}
      onInsertText={onInsertText}
      onInsertChord={onInsertChord}
      aiHistory={aiHistory}
      isLoading={isLoading}
      currentSection={currentSection}
      selectedText={selectedText}
      // If UnifiedAIWorkspace in sidebar needs its own history management independent of MegaLayout,
      // then onHistoryItemApply/Delete would be needed here from page.tsx
    />
  );
};
