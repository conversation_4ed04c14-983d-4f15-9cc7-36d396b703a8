import React from "react";
import { But<PERSON> } from "@/components/ui/button";

export function SubscriptionForm() {
  // Placeholder data
  const currentTier = "Free";
  const mouviksBalance = 2500;

  return (
    <div className="space-y-6">
      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Abonnement actuel</h2>
        <div className="flex items-center gap-2">
          <span className="px-3 py-1 rounded-full bg-primary/20 text-primary font-bold">
            {currentTier}
          </span>
        </div>
      </div>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-1">Souscrire à un plan</h3>
        <div className="flex gap-3">
          <Button variant="secondary">Passer Pro</Button>
          <Button variant="secondary">Passer Studio</Button>
        </div>
      </div>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-1">Acheter des MOUVIKS</h3>
        <div className="flex gap-3">
          <Button variant="outline">Pack 1 000 MOUVIKS</Button>
          <Button variant="outline">Pack 5 000 MOUVIKS</Button>
          <Button variant="outline">Pack 10 000 MOUVIKS</Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">Les MOUVIKS servent à booster vos contenus et accéder à des options avancées.</p>
      </div>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-1">Solde actuel</h3>
        <span className="font-bold text-amber-500">{mouviksBalance.toLocaleString()} MOUVIKS</span>
      </div>
      {/* Historique fictif */}
      <div>
        <h3 className="text-lg font-medium mb-1">Historique des achats</h3>
        <ul className="text-xs text-muted-foreground list-disc ml-5">
          <li>+1 000 MOUVIKS (12/05/2025)</li>
          <li>Abonnement Pro activé (01/05/2025)</li>
        </ul>
      </div>
    </div>
  );
}
