console.log('[supabase/client] Singleton client module loaded');

import { createBrowserClient as _createBrowserClient } from '@supabase/ssr'
import { type SupabaseClient } from '@supabase/supabase-js'

// Use a singleton pattern to ensure the same client instance is used across the app.
// This is crucial for maintaining a consistent authentication state.
let browserClient: SupabaseClient | undefined

function createBrowserClientSingleton() {
  if (!browserClient) {
    console.log('[supabase/client] createBrowserClient called, client:');
    browserClient = _createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        db: {
          schema: 'public',
        },
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: true
        },
        global: {
          headers: {
            'x-client-info': 'supabase-js-web'
          }
        },
        realtime: {
          params: {
            eventsPerSecond: 10
          }
        }
      }
    )
    console.log('[supabase/client] browserClient instance created:', typeof browserClient, browserClient);
  }
  return browserClient
}

// Main export for creating the browser client (now a singleton).
export const createBrowserClient = createBrowserClientSingleton;

// Keep this for compatibility with any existing code that uses the old function name.
export const getSupabaseClient = () => {
  return createBrowserClient()
}

