export type NoteName = 'A' | 'B' | 'C' | 'D' | 'E' | 'F' | 'G';

export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

export type InstrumentType = 'guitar' | 'piano' | 'ukulele' | 'bass';
export type Accidental = '#' | 'b' | '##' | 'bb' | '';

export type ChordQuality = 
  | 'maj' | 'min' | 'dim' | 'aug' | 'sus2' | 'sus4' 
  | 'maj7' | 'min7' | '7' | 'dim7' | 'hdim7' | 'minmaj7' | 'aug7' | 'maj6' | 'min6';

export interface Chord {
  root: NoteName;
  accidental?: Accidental;
  quality?: ChordQuality;
  bass?: NoteName;
  bassAccidental?: Accidental;
}

export type ChordSuggestion = {
  chord: Chord;
  reasoning: string;
  confidence: number;
};
