"use client";

import { useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea"; // Added Textarea import
import { toast } from "@/hooks/use-toast";
import { Share2, Link2, Mail, Code } from "lucide-react";

interface SharePopoverProps {
  resourceType: 'album' | 'song' | 'playlist' | 'band' | 'profile'; // Added 'profile'
  resourceSlug: string; // Assuming slug is always available for public shareable resources
  resourceTitle: string;
  triggerButton?: React.ReactNode;
}

export function SharePopover({
  resourceType,
  resourceSlug,
  resourceTitle,
  triggerButton,
}: SharePopoverProps) {
  const [popoverOpen, setPopoverOpen] = useState(false);

  // Ensure window is defined before accessing window.location.origin
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  const shareUrl = `${baseUrl}/${resourceType}/${resourceSlug}`;
  const embedUrl = `${baseUrl}/${resourceType}/${resourceSlug}/embed`; // Basic embed, can be enhanced later

  const copyToClipboard = (text: string, type: string) => {
    if (typeof window !== 'undefined') {
      navigator.clipboard.writeText(text)
        .then(() => {
          toast({ title: "Copié!", description: `${type} copié dans le presse-papiers.` });
          setPopoverOpen(false); // Close popover on copy
        })
        .catch(err => {
          console.error(`Erreur de copie ${type}:`, err);
          toast({ title: "Erreur", description: `Impossible de copier le ${type}.`, variant: "destructive" });
        });
    }
  };

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild>
        {triggerButton || (
          <Button variant="outline" size="lg" title="Partager">
            <Share2 className="mr-2 h-4 w-4" /> Partager
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="space-y-3">
          <h4 className="font-medium leading-none">Partager {resourceTitle}</h4>
          <p className="text-sm text-muted-foreground">
            Partagez cette ressource avec d'autres personnes.
          </p>
          
          <div className="space-y-2">
            <Label htmlFor={`share-link-${resourceId}`}>Lien direct</Label>
            <div className="flex items-center space-x-2">
              <Input
                id={`share-link-${resourceId}`}
                value={shareUrl}
                readOnly
                className="h-8 flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => copyToClipboard(shareUrl, "Lien")}
                title="Copier le lien"
              >
                <Link2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Button variant="outline" size="sm" className="w-full" asChild>
            <a href={`mailto:?subject=Découvrez: ${resourceTitle}&body=Je voulais partager ceci avec vous: ${shareUrl}`}>
              <Mail className="mr-2 h-4 w-4" /> Partager par email
            </a>
          </Button>

          <div className="space-y-2">
            <Label htmlFor={`embed-code-${resourceId}`}>Code d'intégration (iframe)</Label>
            <div className="flex items-center space-x-2">
              <Textarea
                id={`embed-code-${resourceId}`}
                value={`<iframe src="${embedUrl}" width="100%" height="450" frameborder="0" allowfullscreen loading="lazy"></iframe>`}
                readOnly
                className="h-20 text-xs flex-1"
                rows={3}
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                className="h-8 w-8 self-start mt-1" // Align button nicely with textarea
                onClick={() => copyToClipboard(`<iframe src="${embedUrl}" width="100%" height="450" frameborder="0" allowfullscreen loading="lazy"></iframe>`, "Code d'intégration")}
                title="Copier le code"
              >
                <Code className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Intégrez cette ressource sur votre site web.
            </p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

// Helper to generate a unique ID for inputs if needed, though slug should be unique enough for popover context
// For simplicity, I'm using a fixed string part of the ID. If multiple SharePopovers are on one page for the same resource,
// this might need a more robust unique ID generation (e.g., using a hook like useId from React 18).
// However, resourceId should be unique per instance of the popover.
const resourceId = Math.random().toString(36).substring(2, 9); // Simple unique suffix for demo
