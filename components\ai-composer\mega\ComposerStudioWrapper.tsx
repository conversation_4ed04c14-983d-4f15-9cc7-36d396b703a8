'use client';

import React from 'react';
import { useSong } from '@/hooks/useSong';
import { useComposerStore } from '@/stores/composer';
import dynamic from 'next/dynamic';
import { Song, SongFormData } from '@/components/songs/song-schema';
import { UserProfile } from '@/types/auth';

// Dynamic import with proper typing
const ComposerStudioLayout = dynamic(
  () => import('@/components/ai-composer/mega/ComposerStudioLayout'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center w-full h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }
);

interface ComposerStudioWrapperProps {
  songId: string;
  initialSongData?: Partial<Song>;
  userProfile: UserProfile | null;
}

export const ComposerStudioWrapper: React.FC<ComposerStudioWrapperProps> = ({
  songId,
  initialSongData,
  userProfile,
}) => {
  const {
    currentSong,
    isLoading,
    handleFieldChange: updateSongFields, // Renaming for clarity
    saveSongVersion,
  } = useSong({
    songId,
    userId: userProfile?.id || null,
    initialData: initialSongData as Song,
  });

  const unsavedChanges = useComposerStore((state) => state.unsavedChanges);

  // Adapter function to match the expected signature
  const handleFieldChangeAdapter = (fieldName: keyof SongFormData, value: any) => {
    updateSongFields({ [fieldName]: value });
  };

  if (!songId) {
    return null;
  }

  return (
    <ComposerStudioLayout
      song={currentSong}
      userProfile={userProfile}
      isLoading={isLoading}
      handleFieldChange={handleFieldChangeAdapter} // Pass the adapter
      saveSongVersion={saveSongVersion}
      unsavedChanges={unsavedChanges}
    />
  );
};

export default ComposerStudioWrapper;
