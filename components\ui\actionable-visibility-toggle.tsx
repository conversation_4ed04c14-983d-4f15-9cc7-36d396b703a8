"use client";

import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ActionableVisibilityToggleProps {
  isPublic: boolean;
  isUpdating: boolean;
  onToggle: () => void;
  className?: string;
}

export function ActionableVisibilityToggle({
  isPublic,
  isUpdating,
  onToggle,
  className,
}: ActionableVisibilityToggleProps) {
  const label = isPublic ? "Rendre privé" : "Rendre public";

  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={onToggle}
            disabled={isUpdating}
            className={cn(
              "p-2 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center", // Ensure flex properties for centering LED
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
              isPublic ? "hover:bg-red-500/10" : "hover:bg-green-500/10", // Subtle hover based on next state
              className
            )}
            aria-label={label}
          >
            {isUpdating ? (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            ) : (
              <div
                className={cn(
                  "h-3 w-3 rounded-full",
                  isPublic ? "bg-green-500" : "bg-red-500"
                )}
              />
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isUpdating ? "Mise à jour..." : (isPublic ? "Visible (Public) - Cliquer pour rendre privé" : "Caché (Privé) - Cliquer pour rendre public")}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
