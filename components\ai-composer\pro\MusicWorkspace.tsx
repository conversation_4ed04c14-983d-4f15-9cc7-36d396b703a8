'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, Music, Layers, Waveform, Sparkles, 
  Guitar, Piano, Drum, Mic, Volume2, Settings,
  Plus, Edit3, Copy, Trash2, MoreHorizontal
} from 'lucide-react';

// Import des éditeurs spécialisés
import { LyricsStudioEditor } from './LyricsStudioEditor';
import { ArrangementStudio } from './ArrangementStudio';
import { MixingStudio } from './MixingStudio';
import { MasteringStudio } from './MasteringStudio';

interface MusicWorkspaceProps {
  studioMode: 'compose' | 'arrange' | 'mix' | 'master';
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  handleLyricsChange: (content: string) => void;
  handleAIGenerate: (prompt: string, type: string) => Promise<void>;
  availableInstruments: any[];
}

export const MusicWorkspace: React.FC<MusicWorkspaceProps> = ({
  studioMode,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  handleLyricsChange,
  handleAIGenerate,
  availableInstruments
}) => {
  
  const [activeSubTab, setActiveSubTab] = useState('main');

  // Configuration des modes avec leurs outils spécifiques
  const studioModeConfig = {
    compose: {
      title: 'Composition',
      icon: FileText,
      color: 'text-blue-400',
      tabs: [
        { id: 'lyrics', label: 'Paroles', icon: FileText },
        { id: 'melody', label: 'Mélodie', icon: Music },
        { id: 'chords', label: 'Accords', icon: Guitar }
      ]
    },
    arrange: {
      title: 'Arrangement',
      icon: Layers,
      color: 'text-green-400',
      tabs: [
        { id: 'structure', label: 'Structure', icon: Layers },
        { id: 'instruments', label: 'Instruments', icon: Piano },
        { id: 'rhythm', label: 'Rythme', icon: Drum }
      ]
    },
    mix: {
      title: 'Mixage',
      icon: Waveform,
      color: 'text-purple-400',
      tabs: [
        { id: 'levels', label: 'Niveaux', icon: Volume2 },
        { id: 'effects', label: 'Effets', icon: Sparkles },
        { id: 'eq', label: 'EQ', icon: Settings }
      ]
    },
    master: {
      title: 'Mastering',
      icon: Sparkles,
      color: 'text-orange-400',
      tabs: [
        { id: 'dynamics', label: 'Dynamique', icon: Waveform },
        { id: 'stereo', label: 'Stéréo', icon: Volume2 },
        { id: 'final', label: 'Final', icon: Sparkles }
      ]
    }
  };

  const currentConfig = studioModeConfig[studioMode];
  const currentSection = songSections.find(s => s.id === selectedSection);

  // Gestionnaire pour ajouter une nouvelle section
  const handleAddSection = useCallback(() => {
    const newSection = {
      id: `section-${Date.now()}`,
      type: 'verse',
      title: `Section ${songSections.length + 1}`,
      content: '',
      chords: [],
      duration: 16,
      startTime: songSections.reduce((total, s) => total + (s.duration || 16), 0)
    };
    
    setSongSections([...songSections, newSection]);
    setSelectedSection(newSection.id);
  }, [songSections, setSongSections, setSelectedSection]);

  // Gestionnaire pour dupliquer une section
  const handleDuplicateSection = useCallback((sectionId: string) => {
    const sectionToDuplicate = songSections.find(s => s.id === sectionId);
    if (!sectionToDuplicate) return;
    
    const newSection = {
      ...sectionToDuplicate,
      id: `section-${Date.now()}`,
      title: `${sectionToDuplicate.title} (Copie)`
    };
    
    setSongSections([...songSections, newSection]);
  }, [songSections, setSongSections]);

  // Gestionnaire pour supprimer une section
  const handleDeleteSection = useCallback((sectionId: string) => {
    if (songSections.length <= 1) return; // Garder au moins une section
    
    const filteredSections = songSections.filter(s => s.id !== sectionId);
    setSongSections(filteredSections);
    
    if (selectedSection === sectionId && filteredSections.length > 0) {
      setSelectedSection(filteredSections[0].id);
    }
  }, [songSections, setSongSections, selectedSection, setSelectedSection]);

  return (
    <div className="h-full flex flex-col bg-slate-900/50">
      {/* En-tête du workspace */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <currentConfig.icon className={`h-6 w-6 ${currentConfig.color}`} />
            <div>
              <h2 className="text-lg font-semibold text-white">{currentConfig.title}</h2>
              <p className="text-sm text-slate-400">
                {currentSection ? `${currentSection.title} • ${currentSection.type}` : 'Aucune section sélectionnée'}
              </p>
            </div>
          </div>
          
          {/* Actions rapides */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleAddSection} className="gap-1">
              <Plus className="h-4 w-4" />
              Nouvelle section
            </Button>
            {currentSection && (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleDuplicateSection(currentSection.id)}
                  className="gap-1"
                >
                  <Copy className="h-4 w-4" />
                  Dupliquer
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleDeleteSection(currentSection.id)}
                  className="gap-1 text-red-400 hover:text-red-300"
                  disabled={songSections.length <= 1}
                >
                  <Trash2 className="h-4 w-4" />
                  Supprimer
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Navigation des sections */}
      <div className="border-b border-slate-700 bg-slate-800/30 p-3">
        <ScrollArea className="w-full">
          <div className="flex items-center gap-2">
            {songSections.map((section, index) => (
              <Button
                key={section.id}
                variant={selectedSection === section.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedSection(section.id)}
                className={`gap-2 min-w-fit ${
                  selectedSection === section.id 
                    ? 'bg-blue-600 hover:bg-blue-700' 
                    : 'bg-slate-700 hover:bg-slate-600'
                }`}
              >
                <span className="text-xs opacity-70">#{index + 1}</span>
                <span>{section.title}</span>
                <Badge variant="secondary" className="text-xs capitalize">
                  {section.type}
                </Badge>
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Sous-navigation par mode */}
      <div className="border-b border-slate-700 bg-slate-800/20">
        <Tabs value={activeSubTab} onValueChange={setActiveSubTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-transparent border-0">
            {currentConfig.tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger 
                  key={tab.id} 
                  value={tab.id}
                  className="gap-2 data-[state=active]:bg-slate-700 data-[state=active]:text-white"
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </div>

      {/* Contenu principal selon le mode */}
      <div className="flex-1 overflow-hidden">
        {studioMode === 'compose' && (
          <Tabs value={activeSubTab} className="h-full">
            <TabsContent value="lyrics" className="h-full m-0">
              <LyricsStudioEditor
                content={lyricsContent}
                onContentChange={handleLyricsChange}
                selectedSection={selectedSection}
                sections={songSections}
                onAIGenerate={handleAIGenerate}
                styleConfig={styleConfig}
              />
            </TabsContent>
            <TabsContent value="melody" className="h-full m-0">
              <div className="h-full p-6 flex items-center justify-center">
                <div className="text-center">
                  <Music className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">Éditeur de Mélodie</h3>
                  <p className="text-slate-400">Fonctionnalité en développement</p>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="chords" className="h-full m-0">
              <div className="h-full p-6 flex items-center justify-center">
                <div className="text-center">
                  <Guitar className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">Éditeur d'Accords</h3>
                  <p className="text-slate-400">Intégration Enhanced Chord System en cours</p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}

        {studioMode === 'arrange' && (
          <ArrangementStudio
            activeTab={activeSubTab}
            songSections={songSections}
            setSongSections={setSongSections}
            selectedSection={selectedSection}
            setSelectedSection={setSelectedSection}
            styleConfig={styleConfig}
            setStyleConfig={setStyleConfig}
            availableInstruments={availableInstruments}
          />
        )}

        {studioMode === 'mix' && (
          <MixingStudio
            activeTab={activeSubTab}
            songSections={songSections}
            selectedSection={selectedSection}
            styleConfig={styleConfig}
            setStyleConfig={setStyleConfig}
          />
        )}

        {studioMode === 'master' && (
          <MasteringStudio
            activeTab={activeSubTab}
            songSections={songSections}
            styleConfig={styleConfig}
            setStyleConfig={setStyleConfig}
          />
        )}
      </div>

      {/* Barre d'état du workspace */}
      <div className="border-t border-slate-700 bg-slate-800/50 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-4">
            <span>Section: {currentSection?.title || 'Aucune'}</span>
            <span>•</span>
            <span>Durée: {currentSection?.duration || 0}s</span>
            <span>•</span>
            <span>Type: {currentSection?.type || 'N/A'}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Mode: {currentConfig.title}</span>
            <span>•</span>
            <span>Outil: {currentConfig.tabs.find(t => t.id === activeSubTab)?.label}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
