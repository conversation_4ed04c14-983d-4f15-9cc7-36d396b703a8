"use client"

import React, { <PERSON>actN<PERSON>, CSSProperties, ForwardedRef } from "react"
import Link from "next/link"
import { Slot } from "@radix-ui/react-slot"
import { VariantProps, cva } from "class-variance-authority"
import { PanelLeft } from "lucide-react"

import { useIsMobile } from "@/hooks/use-mobile";
import { useHydrated } from "@/hooks/useHydrated";
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import { Skeleton } from "@/components/ui/skeleton"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const SIDEBAR_COOKIE_NAME = "sidebar:state"
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7
const SIDEBAR_WIDTH = "16rem"
const SIDEBAR_WIDTH_MOBILE = "18rem"
const SIDEBAR_WIDTH_ICON = "3rem"
const SIDEBAR_KEYBOARD_SHORTCUT = "b"

type SidebarContext = {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
}

const SidebarContext = React.createContext<SidebarContext | null>(null)

function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }

  return context
}

const SidebarProvider = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    defaultOpen?: boolean
    open?: boolean
    onOpenChange?: (open: boolean) => void
    collapsed?: boolean
    children?: ReactNode
    className?: string
    style?: CSSProperties
  }
>(
  (
    {
      defaultOpen = true,
      open: openProp,
      onOpenChange: setOpenProp,
      collapsed,
      children,
      className,
      style,
      ...props
    },
    ref
  ) => {
    console.log('--- DEBUG: React object in sidebar-engine ---', React);
    const isMobile = useIsMobile()
    const [open, setOpen] = React.useState(defaultOpen)
    const [openMobile, setOpenMobile] = React.useState(false)

    const state = collapsed ? "collapsed" : "expanded"

    const toggleSidebar = () => {
      if (setOpenProp) {
        setOpenProp(!openProp)
      } else {
        setOpen(!open)
      }
    }

    return (
      <SidebarContext.Provider
        value={{
          state,
          open,
          setOpen,
          openMobile,
          setOpenMobile,
          isMobile,
          toggleSidebar,
        }}
      >
        <div ref={ref} className={className} style={style} {...props}>
          {children}
        </div>
      </SidebarContext.Provider>
    )
  }
)
SidebarProvider.displayName = "SidebarProvider"

const sidebarVariants = cva(
  "top-0 z-30 flex h-full shrink-0 flex-col transition-all duration-300 ease-in-out",
  {
    variants: {
      state: {
        expanded: "w-[var(--sidebar-width)]",
        collapsed: "w-[var(--sidebar-width-icon)]",
      },
    },
    defaultVariants: {
      state: "expanded",
    },
  }
)

const Sidebar = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & VariantProps<typeof sidebarVariants>
>(({ className, state, style, ...props }, ref) => {
  const { isMobile } = useSidebar()

  if (isMobile) {
    return null
  }

  return (
    <div
      ref={ref}
      data-sidebar="sidebar"
      className={cn(sidebarVariants({ state }), className)}
      style={
        {
          "--sidebar-width": `var(--sidebar-width, ${SIDEBAR_WIDTH})`,
          "--sidebar-width-icon": `var(--sidebar-width-icon, ${SIDEBAR_WIDTH_ICON})`,
          ...style,
        } as CSSProperties
      }
      {...props}
    />
  )
})
Sidebar.displayName = "Sidebar"

const SidebarRail = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>((
  { className, ...props },
  ref
) => {
  const { isMobile, openMobile, setOpenMobile } = useSidebar()

  if (!isMobile) {
    return null
  }

  return (
    <Sheet open={openMobile} onOpenChange={setOpenMobile}>
      <SheetContent
        ref={ref}
        side="left"
        className={cn("w-[var(--sidebar-width-mobile)] p-0", className)}
        style={
          {
            "--sidebar-width-mobile": `var(--sidebar-width-mobile, ${SIDEBAR_WIDTH_MOBILE})`,
          } as CSSProperties
        }
        {...props}
      />
    </Sheet>
  )
})
SidebarRail.displayName = "SidebarRail"

const SidebarHeader = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & { asChild?: boolean }
>(({ asChild = false, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "div"
  const { state } = useSidebar()

  return (
    <Comp
      ref={ref}
      data-sidebar="header"
      data-state={state}
      className={cn(
        "flex h-12 shrink-0 items-center gap-2 border-b px-2",
        "data-[state=collapsed]:justify-center",
        className
      )}
      {...props}
    />
  )
})
SidebarHeader.displayName = "SidebarHeader"

const SidebarContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & { asChild?: boolean }
>(({ asChild = false, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "div"

  return (
    <Comp
      ref={ref}
      data-sidebar="content"
      className={cn("flex-1 overflow-y-auto", className)}
      {...props}
    />
  )
})
SidebarContent.displayName = "SidebarContent"

const SidebarFooter = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & { asChild?: boolean }
>(({ asChild = false, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "div"

  return (
    <Comp
      ref={ref}
      data-sidebar="footer"
      className={cn("mt-auto shrink-0 border-t p-2", className)}
      {...props}
    />
  )
})
SidebarFooter.displayName = "SidebarFooter"

const SidebarTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button">
>(({ className, ...props }, ref) => {
  const { toggleSidebar } = useSidebar()
  return (
    <Button
      ref={ref}
      variant="ghost"
      size="icon"
      className={cn("size-8 shrink-0", className)}
      onClick={() => toggleSidebar()}
      {...props}
    >
      <PanelLeft className="size-4" />
    </Button>
  )
})
SidebarTrigger.displayName = "SidebarTrigger"

const SidebarSeparator = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>((props, ref) => <Separator ref={ref} {...props} />)
SidebarSeparator.displayName = "SidebarSeparator"

const SidebarInput = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<"input">
>((props, ref) => <Input ref={ref} {...props} />)
SidebarInput.displayName = "SidebarInput"

const SidebarInset = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-2", className)} {...props} />
))
SidebarInset.displayName = "SidebarInset"

const SidebarGroup = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-2", className)} {...props} />
))
SidebarGroup.displayName = "SidebarGroup"

const SidebarGroupLabel = React.forwardRef<
  HTMLSpanElement,
  React.ComponentProps<"span">
>(({ className, ...props }, ref) => {
  const { state } = useSidebar()
  return (
    <span
      ref={ref}
      data-sidebar="group-label"
      data-state={state}
      className={cn(
        "truncate text-xs font-semibold uppercase text-muted-foreground",
        "data-[state=collapsed]:hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarGroupLabel.displayName = "SidebarGroupLabel"

const SidebarGroupContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>((props, ref) => <div ref={ref} {...props} />)
SidebarGroupContent.displayName = "SidebarGroupContent"

const SidebarGroupAction = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => {
  const { state } = useSidebar()
  return (
    <div
      ref={ref}
      data-sidebar="group-action"
      data-state={state}
      className={cn(
        "absolute right-2 top-1/2 -translate-y-1/2",
        "data-[state=collapsed]:hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarGroupAction.displayName = "SidebarGroupAction"

const SidebarMenu = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul ref={ref} className={cn("space-y-px", className)} {...props} />
))
SidebarMenu.displayName = "SidebarMenu"

const SidebarMenuItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("relative", className)} {...props} />
))
SidebarMenuItem.displayName = "SidebarMenuItem"

const sidebarMenuButtonVariants = cva(
  "flex min-w-0 items-center gap-2 overflow-hidden rounded-md p-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50",
  {
    variants: {
      isActive: {
        true: "bg-sidebar-accent text-sidebar-accent-foreground",
      },
      collapsible: {
        icon: "justify-center",
        text: "",
      },
      size: {
        sm: "text-xs",
        md: "text-sm",
      },
    },
    defaultVariants: {
      isActive: false,
      collapsible: "text",
      size: "md",
    },
  }
)

const SidebarMenuButton = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentProps<"a"> & {
    asChild?: boolean
    size?: "sm" | "md"
    isActive?: boolean
  }
>(({ asChild = false, size = "md", isActive, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "a"
  const { state } = useSidebar()

  return (
    <Comp
      ref={ref}
      data-sidebar="menu-button"
      className={cn(
        sidebarMenuButtonVariants({
          isActive,
          size,
          collapsible: state === "collapsed" ? "icon" : "text",
        }),
        className
      )}
      {...props}
    />
  )
})
SidebarMenuButton.displayName = "SidebarMenuButton"

const SidebarMenuBadge = React.forwardRef<
  HTMLSpanElement,
  React.ComponentProps<"span">
>(({ className, ...props }, ref) => {
  const { state } = useSidebar()
  return (
    <span
      ref={ref}
      data-sidebar="menu-badge"
      data-state={state}
      className={cn(
        "ml-auto rounded-full bg-sidebar-badge px-2 py-px text-xs text-sidebar-badge-foreground",
        "data-[state=collapsed]:hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuBadge.displayName = "SidebarMenuBadge"

const SidebarMenuAction = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & { showOnHover?: boolean }
>(({ showOnHover = false, className, ...props }, ref) => {
  const { state } = useSidebar()
  return (
    <div
      ref={ref}
      data-sidebar="menu-action"
      data-state={state}
      data-show-on-hover={showOnHover}
      className={cn(
        "absolute right-2 top-1/2 -translate-y-1/2",
        "data-[state=collapsed]:hidden",
        "group-hover:data-[show-on-hover=true]:opacity-100",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuAction.displayName = "SidebarMenuAction"

const SidebarMenuSkeleton = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => {
  const { state } = useSidebar()
  const hydrated = useHydrated()

  if (!hydrated) {
    return null
  }

  return (
    <li ref={ref} className={cn("relative", className)} {...props}>
      <div
        className={cn(
          sidebarMenuButtonVariants({
            collapsible: state === "collapsed" ? "icon" : "text",
          })
        )}
      >
        <Skeleton className="size-4 shrink-0 rounded-full" />
        <Skeleton
          className={cn(
            "h-4 w-full",
            state === "collapsed" && "hidden"
          )}
        />
      </div>
    </li>
  )
})
SidebarMenuSkeleton.displayName = "SidebarMenuSkeleton"

const SidebarMenuSub = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    data-sidebar="menu-sub"
    className={cn(
      "list-none space-y-px bg-transparent ps-6",
      "group-data-[collapsible=icon]:hidden",
      className
    )}
    {...props}
  />
))
SidebarMenuSub.displayName = "SidebarMenuSub"

const SidebarMenuSubItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ ...props }, ref) => <li ref={ref} {...props} />)
SidebarMenuSubItem.displayName = "SidebarMenuSubItem"

const SidebarMenuSubButton = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentProps<"a"> & {
    asChild?: boolean
    size?: "sm" | "md"
    isActive?: boolean
  }
>(({ asChild = false, size = "md", isActive, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "a"

  return (
    <Comp
      ref={ref}
      data-sidebar="menu-sub-button"
      data-size={size}
      data-active={isActive}
      className={cn(
        "flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground",
        "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",
        size === "sm" && "text-xs",
        size === "md" && "text-sm",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuSubButton.displayName = "SidebarMenuSubButton"

export {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarInset,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar,
}
