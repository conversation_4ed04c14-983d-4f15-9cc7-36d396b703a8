/**
 * Exemple de configuration locale pour les migrations
 * 
 * Copiez ce fichier en tant que `config.local.js` et ajustez les valeurs
 * selon votre environnement de développement.
 */

module.exports = {
  // Configuration de la base de données locale
  database: {
    host: 'localhost',
    port: '5432',
    name: 'mouvik_dev',
    user: 'postgres',
    password: 'votre_mot_de_passe',
    ssl: false,
  },
  
  // Options de test
  test: {
    // Activer/désactiver les tests spécifiques pour le développement
    enableRollbackTest: false, // Désactiver le test de rollback en développement
  },
  
  // Journalisation plus détaillée en développement
  logging: {
    level: 'debug',
  },
};
