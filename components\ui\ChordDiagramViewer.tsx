"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Guitar, Music, Volume2, Plus, Download, Eye, Edit } from "lucide-react";

// Types pour les accords
export interface ChordDiagram {
  name: string;
  frets: number[];
  fingers: number[];
  instrument: 'guitar' | 'ukulele' | 'bass' | 'mandolin';
  difficulty?: 'easy' | 'medium' | 'hard';
  alternative?: boolean;
  barrePosition?: number;
}

export interface ChordLibrary {
  [key: string]: ChordDiagram[];
}

// Bibliothèque d'accords étendue
const EXTENDED_CHORD_LIBRARY: ChordLibrary = {
  // Accords majeurs
  "C": [
    { name: "C", frets: [0, 1, 0, 2, 3, 0], fingers: [0, 1, 0, 2, 3, 0], instrument: "guitar", difficulty: "easy" },
    { name: "C", frets: [0, 0, 0, 3], fingers: [0, 0, 0, 3], instrument: "ukulele", difficulty: "easy" },
    { name: "C", frets: [0, 0, 1, 3], fingers: [0, 0, 1, 3], instrument: "bass", difficulty: "easy" }
  ],
  "D": [
    { name: "D", frets: [-1, -1, 0, 2, 3, 2], fingers: [0, 0, 0, 1, 3, 2], instrument: "guitar", difficulty: "easy" },
    { name: "D", frets: [2, 2, 2, 0], fingers: [1, 1, 1, 0], instrument: "ukulele", difficulty: "medium" },
    { name: "D", frets: [0, 0, 0, 2], fingers: [0, 0, 0, 2], instrument: "bass", difficulty: "easy" }
  ],
  "E": [
    { name: "E", frets: [0, 2, 2, 1, 0, 0], fingers: [0, 2, 3, 1, 0, 0], instrument: "guitar", difficulty: "easy" },
    { name: "E", frets: [1, 4, 0, 2], fingers: [1, 4, 0, 2], instrument: "ukulele", difficulty: "medium" },
    { name: "E", frets: [0, 2, 2, 1], fingers: [0, 2, 3, 1], instrument: "bass", difficulty: "medium" }
  ],
  "F": [
    { name: "F", frets: [1, 1, 3, 3, 2, 1], fingers: [1, 1, 3, 4, 2, 1], instrument: "guitar", difficulty: "hard", barrePosition: 1 },
    { name: "F", frets: [2, 0, 1, 0], fingers: [2, 0, 1, 0], instrument: "ukulele", difficulty: "easy" },
    { name: "F", frets: [1, 3, 3, 2], fingers: [1, 3, 4, 2], instrument: "bass", difficulty: "medium" }
  ],
  "G": [
    { name: "G", frets: [3, 2, 0, 0, 3, 3], fingers: [3, 2, 0, 0, 4, 4], instrument: "guitar", difficulty: "easy" },
    { name: "G", frets: [0, 2, 3, 2], fingers: [0, 1, 3, 2], instrument: "ukulele", difficulty: "easy" },
    { name: "G", frets: [3, 5, 5, 4], fingers: [1, 3, 4, 2], instrument: "bass", difficulty: "medium" }
  ],
  "A": [
    { name: "A", frets: [-1, 0, 2, 2, 2, 0], fingers: [0, 0, 1, 2, 3, 0], instrument: "guitar", difficulty: "easy" },
    { name: "A", frets: [2, 1, 0, 0], fingers: [2, 1, 0, 0], instrument: "ukulele", difficulty: "easy" },
    { name: "A", frets: [0, 0, 2, 2], fingers: [0, 0, 1, 2], instrument: "bass", difficulty: "easy" }
  ],
  "B": [
    { name: "B", frets: [-1, 2, 4, 4, 4, 2], fingers: [0, 1, 2, 3, 4, 1], instrument: "guitar", difficulty: "hard", barrePosition: 2 },
    { name: "B", frets: [4, 3, 2, 2], fingers: [4, 3, 1, 2], instrument: "ukulele", difficulty: "hard" },
    { name: "B", frets: [2, 4, 4, 3], fingers: [1, 3, 4, 2], instrument: "bass", difficulty: "medium" }
  ],
  
  // Accords mineurs
  "Am": [
    { name: "Am", frets: [-1, 0, 2, 2, 1, 0], fingers: [0, 0, 2, 3, 1, 0], instrument: "guitar", difficulty: "easy" },
    { name: "Am", frets: [2, 0, 0, 0], fingers: [2, 0, 0, 0], instrument: "ukulele", difficulty: "easy" },
    { name: "Am", frets: [0, 0, 2, 2], fingers: [0, 0, 1, 2], instrument: "bass", difficulty: "easy" }
  ],
  "Dm": [
    { name: "Dm", frets: [-1, -1, 0, 2, 3, 1], fingers: [0, 0, 0, 2, 3, 1], instrument: "guitar", difficulty: "easy" },
    { name: "Dm", frets: [2, 2, 1, 0], fingers: [2, 3, 1, 0], instrument: "ukulele", difficulty: "medium" },
    { name: "Dm", frets: [0, 0, 0, 1], fingers: [0, 0, 0, 1], instrument: "bass", difficulty: "easy" }
  ],
  "Em": [
    { name: "Em", frets: [0, 2, 2, 0, 0, 0], fingers: [0, 2, 3, 0, 0, 0], instrument: "guitar", difficulty: "easy" },
    { name: "Em", frets: [0, 4, 3, 2], fingers: [0, 4, 3, 2], instrument: "ukulele", difficulty: "medium" },
    { name: "Em", frets: [0, 2, 2, 0], fingers: [0, 1, 2, 0], instrument: "bass", difficulty: "easy" }
  ],
  "Fm": [
    { name: "Fm", frets: [1, 1, 3, 3, 1, 1], fingers: [1, 1, 3, 4, 1, 1], instrument: "guitar", difficulty: "hard", barrePosition: 1 },
    { name: "Fm", frets: [1, 0, 1, 3], fingers: [1, 0, 2, 4], instrument: "ukulele", difficulty: "medium" },
    { name: "Fm", frets: [1, 3, 3, 1], fingers: [1, 3, 4, 1], instrument: "bass", difficulty: "medium" }
  ],
  "Gm": [
    { name: "Gm", frets: [3, 1, 0, 0, 3, 3], fingers: [3, 1, 0, 0, 4, 4], instrument: "guitar", difficulty: "medium" },
    { name: "Gm", frets: [0, 2, 3, 1], fingers: [0, 2, 3, 1], instrument: "ukulele", difficulty: "medium" },
    { name: "Gm", frets: [3, 5, 5, 3], fingers: [1, 3, 4, 1], instrument: "bass", difficulty: "medium" }
  ],
  
  // Accords 7ème
  "C7": [
    { name: "C7", frets: [-1, 3, 2, 3, 1, 0], fingers: [0, 3, 2, 4, 1, 0], instrument: "guitar", difficulty: "medium" },
    { name: "C7", frets: [0, 0, 0, 1], fingers: [0, 0, 0, 1], instrument: "ukulele", difficulty: "easy" },
    { name: "C7", frets: [0, 0, 1, 1], fingers: [0, 0, 1, 2], instrument: "bass", difficulty: "easy" }
  ],
  "G7": [
    { name: "G7", frets: [3, 2, 0, 0, 0, 1], fingers: [3, 2, 0, 0, 0, 1], instrument: "guitar", difficulty: "easy" },
    { name: "G7", frets: [0, 2, 1, 2], fingers: [0, 2, 1, 3], instrument: "ukulele", difficulty: "medium" },
    { name: "G7", frets: [3, 5, 3, 4], fingers: [1, 4, 2, 3], instrument: "bass", difficulty: "medium" }
  ]
};

interface ChordDiagramViewerProps {
  className?: string;
  onChordSelect?: (chordName: string) => void;
}

export function ChordDiagramViewer({
  className,
  onChordSelect
}: ChordDiagramViewerProps) {
  const [selectedInstrument, setSelectedInstrument] = useState<'guitar' | 'ukulele' | 'bass' | 'mandolin'>('guitar');
  const [selectedChordType, setSelectedChordType] = useState<'major' | 'minor' | 'seventh' | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedChord, setSelectedChord] = useState<string | null>(null);
  
  // Filtrer les accords selon le type et la recherche
  const filteredChords = Object.entries(EXTENDED_CHORD_LIBRARY).filter(([chordName, variations]) => {
    // Filtrer par recherche
    if (searchTerm && !chordName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    // Filtrer par type d'accord
    if (selectedChordType !== 'all') {
      if (selectedChordType === 'major' && (chordName.includes('m') || chordName.includes('7'))) {
        return false;
      }
      if (selectedChordType === 'minor' && (!chordName.includes('m') || chordName.includes('7'))) {
        return false;
      }
      if (selectedChordType === 'seventh' && !chordName.includes('7')) {
        return false;
      }
    }
    
    // Vérifier qu'il y a une variation pour l'instrument sélectionné
    return variations.some(v => v.instrument === selectedInstrument);
  });
  
  // Fonction pour rendre un diagramme d'accord
  const renderChordDiagram = (chord: ChordDiagram, size: 'small' | 'medium' | 'large' = 'medium') => {
    const strings = chord.instrument === 'guitar' ? 6 : chord.instrument === 'bass' ? 4 : 4;
    const frets = 5;
    const width = size === 'small' ? 60 : size === 'medium' ? 80 : 120;
    const height = size === 'small' ? 75 : size === 'medium' ? 100 : 150;
    const stringSpacing = (width - 20) / (strings - 1);
    const fretSpacing = (height - 20) / frets;
    
    return (
      <div className="chord-diagram-container">
        <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`} className="border rounded">
          {/* Fond */}
          <rect width={width} height={height} fill="white" />
          
          {/* Cordes */}
          {Array.from({ length: strings }, (_, i) => (
            <line
              key={`string-${i}`}
              x1={10 + i * stringSpacing}
              y1="10"
              x2={10 + i * stringSpacing}
              y2={height - 10}
              stroke="#666"
              strokeWidth="1"
            />
          ))}
          
          {/* Frettes */}
          {Array.from({ length: frets + 1 }, (_, i) => (
            <line
              key={`fret-${i}`}
              x1="10"
              y1={10 + i * fretSpacing}
              x2={width - 10}
              y2={10 + i * fretSpacing}
              stroke="#666"
              strokeWidth={i === 0 ? "3" : "1"}
            />
          ))}
          
          {/* Barré si présent */}
          {chord.barrePosition && (
            <rect
              x="8"
              y={10 + (chord.barrePosition - 0.3) * fretSpacing}
              width={width - 16}
              height="6"
              fill="#333"
              rx="3"
            />
          )}
          
          {/* Doigtés */}
          {chord.frets.map((fret, stringIndex) => {
            if (fret === 0) {
              // Corde à vide - cercle ouvert
              return (
                <circle
                  key={`open-${stringIndex}`}
                  cx={10 + stringIndex * stringSpacing}
                  cy="5"
                  r="3"
                  fill="none"
                  stroke="#333"
                  strokeWidth="2"
                />
              );
            } else if (fret === -1) {
              // Corde non jouée - X
              return (
                <g key={`muted-${stringIndex}`}>
                  <line
                    x1={10 + stringIndex * stringSpacing - 3}
                    y1="2"
                    x2={10 + stringIndex * stringSpacing + 3}
                    y2="8"
                    stroke="#333"
                    strokeWidth="2"
                  />
                  <line
                    x1={10 + stringIndex * stringSpacing + 3}
                    y1="2"
                    x2={10 + stringIndex * stringSpacing - 3}
                    y2="8"
                    stroke="#333"
                    strokeWidth="2"
                  />
                </g>
              );
            } else {
              // Doigt sur frette
              const x = 10 + stringIndex * stringSpacing;
              const y = 10 + (fret - 0.5) * fretSpacing;
              const radius = size === 'small' ? 3 : size === 'medium' ? 4 : 6;
              
              return (
                <g key={`finger-${stringIndex}`}>
                  <circle cx={x} cy={y} r={radius} fill="#333" />
                  {size !== 'small' && chord.fingers[stringIndex] > 0 && (
                    <text
                      x={x}
                      y={y + 1}
                      textAnchor="middle"
                      fontSize={size === 'medium' ? "8" : "10"}
                      fill="white"
                      fontWeight="bold"
                    >
                      {chord.fingers[stringIndex]}
                    </text>
                  )}
                </g>
              );
            }
          })}
        </svg>
        
        {/* Informations sur l'accord */}
        <div className="mt-2 text-center">
          <div className="font-semibold text-sm">{chord.name}</div>
          <div className="flex justify-center gap-1 mt-1">
            <Badge 
              variant={chord.difficulty === 'easy' ? 'default' : chord.difficulty === 'medium' ? 'secondary' : 'destructive'}
              className="text-xs"
            >
              {chord.difficulty === 'easy' ? 'Facile' : chord.difficulty === 'medium' ? 'Moyen' : 'Difficile'}
            </Badge>
            {chord.alternative && (
              <Badge variant="outline" className="text-xs">Alt</Badge>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  const getInstrumentIcon = (instrument: string) => {
    switch (instrument) {
      case 'guitar': return <Guitar className="w-4 h-4" />;
      case 'ukulele': return <Music className="w-4 h-4" />;
      case 'bass': return <Volume2 className="w-4 h-4" />;
      case 'mandolin': return <Music className="w-4 h-4" />;
      default: return <Music className="w-4 h-4" />;
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            {getInstrumentIcon(selectedInstrument)}
            <span className="ml-2">Diagrammes d'Accords</span>
          </div>
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              <Download className="w-4 h-4 mr-1" /> Export
            </Button>
            <Button size="sm" variant="outline">
              <Plus className="w-4 h-4 mr-1" /> Créer
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Contrôles */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label className="text-sm">Instrument</Label>
            <Select value={selectedInstrument} onValueChange={(value: 'guitar' | 'ukulele' | 'bass' | 'mandolin') => setSelectedInstrument(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="guitar">
                  <div className="flex items-center">
                    <Guitar className="w-4 h-4 mr-2" />
                    Guitare
                  </div>
                </SelectItem>
                <SelectItem value="ukulele">
                  <div className="flex items-center">
                    <Music className="w-4 h-4 mr-2" />
                    Ukulélé
                  </div>
                </SelectItem>
                <SelectItem value="bass">
                  <div className="flex items-center">
                    <Volume2 className="w-4 h-4 mr-2" />
                    Basse
                  </div>
                </SelectItem>
                <SelectItem value="mandolin">
                  <div className="flex items-center">
                    <Music className="w-4 h-4 mr-2" />
                    Mandoline
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label className="text-sm">Type d'accord</Label>
            <Select value={selectedChordType} onValueChange={(value: any) => setSelectedChordType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous</SelectItem>
                <SelectItem value="major">Majeurs</SelectItem>
                <SelectItem value="minor">Mineurs</SelectItem>
                <SelectItem value="seventh">7ème</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label className="text-sm">Rechercher</Label>
            <Input
              placeholder="Ex: C, Am, G7..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        {/* Grille d'accords */}
        <ScrollArea className="h-[400px]">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-2">
            {filteredChords.map(([chordName, variations]) => {
              const chordForInstrument = variations.find(v => v.instrument === selectedInstrument);
              if (!chordForInstrument) return null;
              
              return (
                <div
                  key={`${chordName}-${selectedInstrument}`}
                  className={`cursor-pointer p-2 rounded-lg border transition-all hover:shadow-md ${
                    selectedChord === chordName ? 'border-primary bg-primary/5' : 'border-border'
                  }`}
                  onClick={() => {
                    setSelectedChord(chordName);
                    onChordSelect?.(chordName);
                  }}
                >
                  {renderChordDiagram(chordForInstrument, 'medium')}
                </div>
              );
            })}
          </div>
        </ScrollArea>
        
        {filteredChords.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            Aucun accord trouvé pour les critères sélectionnés.
          </div>
        )}
      </CardContent>
    </Card>
  );
}