<#
.SYNOPSIS
    Configuration pour les scripts de migration
.DESCRIPTION
    Ce fichier contient les paramètres de configuration pour les scripts de migration.
    Copiez ce fichier en tant que 'config.local.ps1' et modifiez les valeurs selon votre environnement.
#>

# Configuration de la base de données
$config = @{
    Database = @{
        Server = "localhost"
        Port = 5432
        Name = "mouvik"
        User = "postgres"
        # Pour des raisons de sécurité, définissez le mot de passe dans config.local.ps1
        # ou utilisez des variables d'environnement
        Password = if ($env:DB_PASSWORD) { $env:DB_PASSWORD } else { "postgres" }
    }
    
    # Chemins
    Paths = @{
        Migrations = "$PSScriptRoot\..\db\migrations"
        Backups = "$PSScriptRoot\..\backups"
    }
    
    # Options de migration
    Migration = @{
        # Activer/désactiver la sauvegarde automatique
        EnableBackup = $true
        
        # Nombre de sauvegardes à conserver (0 pour illimité)
        KeepBackups = 5
        
        # Niveau de journalisation (DEBUG, INFO, WARN, ERROR)
        LogLevel = "INFO"
    }
}

# Charger la configuration locale si elle existe
$localConfigPath = Join-Path $PSScriptRoot "config.local.ps1"
if (Test-Path $localConfigPath) {
    try {
        $localConfig = . $localConfigPath
        
        # Fusionner les configurations
        if ($localConfig -is [hashtable]) {
            $localConfig.Keys | ForEach-Object {
                if ($localConfig[$_] -is [hashtable] -and $config.ContainsKey($_)) {
                    $localConfig[$_].Keys | ForEach-Object {
                        $config[$_][$_] = $localConfig[$_][$_]
                    }
                } else {
                    $config[$_] = $localConfig[$_]
                }
            }
        }
    } catch {
        Write-Warning "Erreur lors du chargement de la configuration locale: $_"
    }
}

# Exporter la configuration
$config

<#
Exemple de fichier config.local.ps1 :

@{
    Database = @{
        Server = "localhost"
        Port = 5432
        Name = "mouvik_prod"
        User = "admin"
        # Utilisez une variable d'environnement pour le mot de passe en production
        # ou utilisez un gestionnaire de secrets
        Password = $env:DB_PASSWORD
    }
    
    Migration = @{
        EnableBackup = $true
        KeepBackups = 10
        LogLevel = "DEBUG"
    }
}
#>
