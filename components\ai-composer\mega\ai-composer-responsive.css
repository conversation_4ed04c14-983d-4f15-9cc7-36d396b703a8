/* AI Composer Responsive Styles */

/* Variables CSS pour la cohérence */
:root {
  --ai-composer-primary: #3b82f6;
  --ai-composer-secondary: #8b5cf6;
  --ai-composer-accent: #10b981;
  --ai-composer-warning: #f59e0b;
  --ai-composer-error: #ef4444;
  --ai-composer-success: #22c55e;
  
  --ai-composer-bg-primary: #0f172a;
  --ai-composer-bg-secondary: #1e293b;
  --ai-composer-bg-tertiary: #334155;
  
  --ai-composer-text-primary: #f8fafc;
  --ai-composer-text-secondary: #cbd5e1;
  --ai-composer-text-muted: #64748b;
  
  --ai-composer-border: #475569;
  --ai-composer-border-light: #64748b;
  
  --ai-composer-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --ai-composer-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  --ai-composer-radius: 0.5rem;
  --ai-composer-radius-lg: 0.75rem;
  
  --ai-composer-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --ai-composer-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Breakpoints */
@media (max-width: 767px) {
  .ai-composer-mobile-only { display: block !important; }
  .ai-composer-desktop-only { display: none !important; }
  .ai-composer-tablet-only { display: none !important; }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .ai-composer-mobile-only { display: none !important; }
  .ai-composer-desktop-only { display: none !important; }
  .ai-composer-tablet-only { display: block !important; }
}

@media (min-width: 1024px) {
  .ai-composer-mobile-only { display: none !important; }
  .ai-composer-desktop-only { display: block !important; }
  .ai-composer-tablet-only { display: none !important; }
}

/* Layout responsive */
.ai-composer-workspace {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--ai-composer-bg-primary);
  color: var(--ai-composer-text-primary);
  transition: var(--ai-composer-transition);
  overflow: hidden;
}

.ai-composer-workspace.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* Header responsive */
.ai-composer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--ai-composer-border);
  background: var(--ai-composer-bg-secondary);
  flex-shrink: 0;
  min-height: 4rem;
}

@media (max-width: 767px) {
  .ai-composer-header {
    padding: 0.5rem;
    flex-direction: column;
    gap: 0.5rem;
    min-height: auto;
  }
}

/* Workspace content */
.ai-composer-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

@media (max-width: 767px) {
  .ai-composer-content {
    flex-direction: column;
  }
}

/* Main editor area */
.ai-composer-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

/* Side panels */
.ai-composer-panel {
  background: var(--ai-composer-bg-secondary);
  border-left: 1px solid var(--ai-composer-border);
  transition: var(--ai-composer-transition);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-composer-panel.collapsed {
  width: 4rem;
}

.ai-composer-panel.expanded {
  width: 20rem;
}

.ai-composer-panel.hidden {
  width: 0;
  border: none;
  overflow: hidden;
}

@media (max-width: 767px) {
  .ai-composer-panel {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 100;
    transform: translateX(100%);
    border-left: 1px solid var(--ai-composer-border);
    box-shadow: var(--ai-composer-shadow-lg);
  }
  
  .ai-composer-panel.expanded {
    transform: translateX(0);
    width: 80vw;
    max-width: 20rem;
  }
  
  .ai-composer-panel.collapsed {
    transform: translateX(100%);
  }
}

/* Buttons responsive */
.ai-composer-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--ai-composer-radius);
  font-weight: 500;
  transition: var(--ai-composer-transition-fast);
  cursor: pointer;
  border: 1px solid transparent;
  min-height: 2.75rem; /* 44px pour le tactile */
  min-width: 2.75rem;
}

.ai-composer-btn:focus {
  outline: 2px solid var(--ai-composer-primary);
  outline-offset: 2px;
}

.ai-composer-btn.primary {
  background: var(--ai-composer-primary);
  color: white;
}

.ai-composer-btn.primary:hover {
  background: #2563eb;
}

.ai-composer-btn.secondary {
  background: var(--ai-composer-bg-tertiary);
  color: var(--ai-composer-text-primary);
  border-color: var(--ai-composer-border);
}

.ai-composer-btn.secondary:hover {
  background: var(--ai-composer-border);
}

.ai-composer-btn.ghost {
  background: transparent;
  color: var(--ai-composer-text-secondary);
}

.ai-composer-btn.ghost:hover {
  background: var(--ai-composer-bg-tertiary);
  color: var(--ai-composer-text-primary);
}

@media (max-width: 767px) {
  .ai-composer-btn {
    padding: 0.75rem 1rem;
    min-height: 3rem; /* 48px pour mobile */
  }
}

/* Input fields responsive */
.ai-composer-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--ai-composer-border);
  border-radius: var(--ai-composer-radius);
  background: var(--ai-composer-bg-tertiary);
  color: var(--ai-composer-text-primary);
  transition: var(--ai-composer-transition-fast);
  font-size: 1rem;
}

.ai-composer-input:focus {
  outline: none;
  border-color: var(--ai-composer-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ai-composer-textarea {
  resize: vertical;
  min-height: 6rem;
  font-family: inherit;
}

@media (max-width: 767px) {
  .ai-composer-input {
    padding: 1rem;
    font-size: 1rem; /* Évite le zoom sur iOS */
  }
  
  .ai-composer-textarea {
    min-height: 5rem;
  }
}

/* Cards responsive */
.ai-composer-card {
  background: var(--ai-composer-bg-secondary);
  border: 1px solid var(--ai-composer-border);
  border-radius: var(--ai-composer-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--ai-composer-shadow);
  transition: var(--ai-composer-transition);
}

.ai-composer-card:hover {
  box-shadow: var(--ai-composer-shadow-lg);
  border-color: var(--ai-composer-border-light);
}

@media (max-width: 767px) {
  .ai-composer-card {
    padding: 1rem;
    border-radius: var(--ai-composer-radius);
  }
}

/* Typography responsive */
.ai-composer-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--ai-composer-text-primary);
  line-height: 1.2;
}

.ai-composer-subtitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--ai-composer-text-secondary);
  line-height: 1.3;
}

.ai-composer-body {
  font-size: 1rem;
  color: var(--ai-composer-text-secondary);
  line-height: 1.5;
}

.ai-composer-caption {
  font-size: 0.875rem;
  color: var(--ai-composer-text-muted);
  line-height: 1.4;
}

@media (max-width: 767px) {
  .ai-composer-title {
    font-size: 1.25rem;
  }
  
  .ai-composer-subtitle {
    font-size: 1rem;
  }
  
  .ai-composer-body {
    font-size: 0.875rem;
  }
  
  .ai-composer-caption {
    font-size: 0.75rem;
  }
}

/* Loading states */
.ai-composer-loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--ai-composer-text-muted);
}

.ai-composer-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--ai-composer-border);
  border-top: 2px solid var(--ai-composer-primary);
  border-radius: 50%;
  animation: ai-composer-spin 1s linear infinite;
}

@keyframes ai-composer-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status indicators */
.ai-composer-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.ai-composer-status.success {
  background: rgba(34, 197, 94, 0.1);
  color: var(--ai-composer-success);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.ai-composer-status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--ai-composer-warning);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.ai-composer-status.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--ai-composer-error);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.ai-composer-status.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--ai-composer-primary);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Animations */
.ai-composer-fade-in {
  animation: ai-composer-fadeIn 0.3s ease-out;
}

.ai-composer-slide-in-right {
  animation: ai-composer-slideInRight 0.3s ease-out;
}

.ai-composer-slide-out-right {
  animation: ai-composer-slideOutRight 0.3s ease-in;
}

@keyframes ai-composer-fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes ai-composer-slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes ai-composer-slideOutRight {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

/* Utilities */
.ai-composer-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.ai-composer-focus-trap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Scrollbar styling */
.ai-composer-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.ai-composer-scrollbar::-webkit-scrollbar-track {
  background: var(--ai-composer-bg-tertiary);
  border-radius: 4px;
}

.ai-composer-scrollbar::-webkit-scrollbar-thumb {
  background: var(--ai-composer-border);
  border-radius: 4px;
}

.ai-composer-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--ai-composer-border-light);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --ai-composer-border: #ffffff;
    --ai-composer-text-secondary: #ffffff;
    --ai-composer-text-muted: #cccccc;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --ai-composer-bg-primary: #000000;
    --ai-composer-bg-secondary: #111111;
    --ai-composer-bg-tertiary: #222222;
  }
}

/* Print styles */
@media print {
  .ai-composer-workspace {
    background: white !important;
    color: black !important;
  }
  
  .ai-composer-panel {
    display: none !important;
  }
  
  .ai-composer-btn {
    display: none !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .ai-composer-btn {
    min-height: 3rem;
    min-width: 3rem;
  }
  
  .ai-composer-input {
    padding: 1rem;
  }
}

/* Landscape mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .ai-composer-header {
    padding: 0.25rem 0.5rem;
    min-height: 3rem;
  }
  
  .ai-composer-panel.expanded {
    width: 50vw;
  }
}

/* Large screens */
@media (min-width: 1440px) {
  .ai-composer-panel.expanded {
    width: 24rem;
  }
  
  .ai-composer-card {
    padding: 2rem;
  }
}

/* Ultra-wide screens */
@media (min-width: 1920px) {
  .ai-composer-workspace {
    max-width: 1920px;
    margin: 0 auto;
  }
}

/* Styles responsifs pour AI Composer Workspace */

/* Variables CSS pour la cohérence */
:root {
  --header-height: 4rem;
  --footer-height: 3rem;
  --sidebar-width-collapsed: 4rem;
  --sidebar-width-expanded: 20rem;
  --mobile-panel-width: 20rem;
  --transition-duration: 300ms;
}

/* Container principal */
.ai-composer-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header responsive */
.ai-composer-header {
  height: var(--header-height);
  flex-shrink: 0;
  border-bottom: 1px solid rgb(51 65 85);
  background: rgb(15 23 42 / 0.8);
  backdrop-filter: blur(8px);
}

/* Zone de contenu principal */
.ai-composer-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 0;
  height: calc(100vh - var(--header-height) - var(--footer-height));
}

/* Éditeur de paroles */
.lyrics-editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
  height: 100%;
  min-height: 500px;
}

/* Amélioration de l'éditeur de texte */
.ai-composer-textarea {
  min-height: 400px !important;
  height: 100% !important;
  resize: none;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
  padding: 16px;
  border-radius: 8px;
  border: 2px solid var(--ai-composer-border);
  background: var(--ai-composer-bg-secondary);
  color: var(--ai-composer-text-primary);
  transition: var(--ai-composer-transition);
}

.ai-composer-textarea:focus {
  border-color: var(--ai-composer-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.ai-composer-textarea::placeholder {
  color: var(--ai-composer-text-muted);
  font-style: italic;
}

/* Panneaux latéraux */
.side-panel {
  background: rgb(15 23 42 / 0.3);
  border-left: 1px solid rgb(51 65 85);
  transition: all var(--transition-duration) ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.side-panel.collapsed {
  width: var(--sidebar-width-collapsed);
}

.side-panel.expanded {
  width: var(--sidebar-width-expanded);
}

/* Footer responsive */
.ai-composer-footer {
  height: var(--footer-height);
  flex-shrink: 0;
  border-top: 1px solid rgb(51 65 85);
  background: rgb(15 23 42 / 0.8);
}

/* Responsive breakpoints */

/* Mobile (< 768px) */
@media (max-width: 767px) {
  .ai-composer-main {
    flex-direction: column;
    height: calc(100vh - var(--header-height) - var(--footer-height));
  }
  
  .side-panel.expanded {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: var(--mobile-panel-width);
    z-index: 40;
    background: rgb(15 23 42);
    box-shadow: -4px 0 16px rgba(0, 0, 0, 0.3);
    height: 100vh;
  }
  
  .side-panel.collapsed {
    display: none;
  }
  
  .lyrics-editor-container {
    height: 100%;
    flex: 1;
  }
}

/* Tablet (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .ai-composer-main {
    height: calc(100vh - var(--header-height) - var(--footer-height));
  }
  
  .side-panel.expanded {
    width: 16rem;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 40;
    background: rgb(15 23 42);
    box-shadow: -4px 0 16px rgba(0, 0, 0, 0.3);
    height: 100vh;
  }
  
  .side-panel.collapsed {
    width: 3rem;
  }
  
  .lyrics-editor-container {
    height: 100%;
    flex: 1;
  }
}

/* Desktop (>= 1024px) */
@media (min-width: 1024px) {
  .ai-composer-main {
    flex-direction: row;
    height: calc(100vh - var(--header-height) - var(--footer-height));
  }
  
  .side-panel.expanded {
    width: var(--sidebar-width-expanded);
    position: relative;
  }
  
  .side-panel.collapsed {
    width: var(--sidebar-width-collapsed);
  }
  
  .lyrics-editor-container {
    height: 100%;
    flex: 1;
  }
}

/* Animations et transitions */
.panel-transition {
  transition: all var(--transition-duration) cubic-bezier(0.4, 0, 0.2, 1);
}

/* Overlay pour mobile */
.mobile-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  opacity: 0;
  transition: opacity var(--transition-duration) ease-in-out;
}

.mobile-overlay.active {
  opacity: 1;
}

/* Styles pour les boutons de toggle */
.panel-toggle-btn {
  transition: all 200ms ease-in-out;
}

.panel-toggle-btn:hover {
  background: rgb(51 65 85 / 0.5);
}

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}

/* Styles pour le mode plein écran */
.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  background: rgb(15 23 42);
}

.fullscreen-mode .ai-composer-header,
.fullscreen-mode .ai-composer-footer {
  display: none;
}

.fullscreen-mode .ai-composer-main {
  height: 100vh;
}

/* Optimisations pour les performances */
.side-panel {
  will-change: width, transform;
}

.mobile-overlay {
  will-change: opacity;
}

/* Styles pour les états de chargement */
.loading-state {
  opacity: 0.7;
  pointer-events: none;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
  .side-panel {
    border-left-width: 2px;
  }
  
  .ai-composer-header,
  .ai-composer-footer {
    border-width: 2px;
  }
}

/* Corrections spécifiques pour la hauteur complète */
.h-full {
  height: 100% !important;
}

.min-h-screen {
  min-height: 100vh !important;
}

/* Assurer que les conteneurs flex utilisent toute la hauteur */
.flex-1 {
  flex: 1 1 0% !important;
}

/* Corrections pour l'overflow des panneaux */
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
}

/* Styles pour les panneaux mobiles avec overlay */
@media (max-width: 1023px) {
  .side-panel.expanded::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}