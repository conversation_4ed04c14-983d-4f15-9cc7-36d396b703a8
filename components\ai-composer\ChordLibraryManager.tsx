'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { MidiChordPlayer, type ChordPosition as MidiChordPosition, type ArpeggioPattern } from '@/lib/chords/midi-chord-player';
import { useChordLibrary, AVAILABLE_INSTRUMENTS, type ChordPosition } from '@/hooks/useChordLibrary';
import { Music, Play, Square, Volume2, Search, Filter, Zap, Star } from 'lucide-react';

interface ChordLibraryManagerProps {
  selectedInstrument?: string;
  onChordSelect?: (chord: string, position: ChordPosition) => void;
  onPlayChord?: (midiNotes: number[]) => void;
  currentKey?: string;
}

const ChordLibraryManager: React.FC<ChordLibraryManagerProps> = ({
  selectedInstrument = 'guitar',
  onChordSelect,
  onPlayChord
}) => {
  const { chordData, loading, error, instrumentConfig, searchChords, getChordsByDifficulty } = useChordLibrary(selectedInstrument);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChord, setSelectedChord] = useState<string>('');
  const [selectedPosition, setSelectedPosition] = useState<number>(0);
  const [midiPlayer] = useState(() => new MidiChordPlayer());
  const [isPlaying, setIsPlaying] = useState(false);
  const [playMode, setPlayMode] = useState<'block' | 'arpeggio'>('block');
  const [arpeggioPattern, setArpeggioPattern] = useState<string>('ascending');
  const [difficultyFilter, setDifficultyFilter] = useState<number>(5);
  const [showFavorites, setShowFavorites] = useState(false);

  // Filtrer les accords selon le terme de recherche et la difficulté
  const filteredChords = useMemo(() => {
    let chords = searchTerm ? searchChords(searchTerm) : (chordData ? Object.entries(chordData.chords) : []);
    
    // Filtrer par difficulté
    if (difficultyFilter < 5) {
      chords = chords.filter(([_, variations]) => 
        variations.some(variation => 
          variation.positions.some(position => position.difficulty <= difficultyFilter)
        )
      );
    }
    
    return chords;
  }, [chordData, searchTerm, difficultyFilter, searchChords]);

  // Jouer un accord
  const playChord = async (position: ChordPosition) => {
    if (!midiPlayer || isPlaying) {
      return;
    }

    setIsPlaying(true);
    
    try {
      // Convertir le ChordPosition du hook vers le format attendu par midi-chord-player
      const midiPosition = {
        frets: position.frets.map(f => typeof f === 'string' ? parseInt(f) || 0 : f),
        fingers: position.fingers,
        barres: position.barres,
        midi: position.midi,
        difficulty: position.difficulty.toString(),
        baseFret: position.baseFret
      };
      
      if (playMode === 'block') {
        await midiPlayer.playChord(midiPosition);
      } else {
        await midiPlayer.playArpeggio(midiPosition, arpeggioPattern);
      }
      
      onPlayChord?.(position.midi);
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  // Sélectionner un accord
  const handleChordSelect = (chordName: string, position: ChordPosition) => {
    setSelectedChord(chordName);
    onChordSelect?.(chordName, position);
  };

  // Rendu d'un diagramme d'accord simplifié
  const renderChordDiagram = (position: ChordPosition) => {
    return (
      <div className="chord-diagram bg-muted/20 p-2 rounded border">
        <div className="grid grid-cols-6 gap-1 text-xs font-mono">
          {position.frets.map((fret, index) => (
            <div key={index} className="text-center">
              {fret === 'x' ? '×' : fret}
            </div>
          ))}
        </div>
        <div className="text-xs text-muted-foreground mt-1">
          Difficulté: {position.difficulty}/5
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Chargement des accords...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <p>{error}</p>
            <p className="text-sm mt-2">Utilisation des accords de base...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Bibliothèque d'Accords
        </CardTitle>
        
        {/* Informations sur l'instrument */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Music className="h-4 w-4" />
          <span>{instrumentConfig.label}</span>
          {instrumentConfig.tuning.length > 0 && (
            <span className="ml-2">Accordage: {instrumentConfig.tuning.join('-')}</span>
          )}
        </div>
        
        {/* Contrôles */}
        <div className="flex gap-2 flex-wrap">
          {/* Filtre de difficulté */}
          <Select value={difficultyFilter.toString()} onValueChange={(value) => setDifficultyFilter(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Facile (1)</SelectItem>
              <SelectItem value="2">Facile (1-2)</SelectItem>
              <SelectItem value="3">Moyen (1-3)</SelectItem>
              <SelectItem value="4">Difficile (1-4)</SelectItem>
              <SelectItem value="5">Tous</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Mode de lecture */}
          <Select value={playMode} onValueChange={(value: 'block' | 'arpeggio') => setPlayMode(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="block">Bloc</SelectItem>
              <SelectItem value="arpeggio">Arpège</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Pattern d'arpège */}
          {playMode === 'arpeggio' && (
            <Select value={arpeggioPattern} onValueChange={setArpeggioPattern}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(MidiChordPlayer.ARPEGGIO_PATTERNS).map(([key, pattern]) => (
                  <SelectItem key={key} value={key}>
                    {pattern.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        {/* Recherche */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un accord..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {filteredChords.map(([chordName, variations]) => (
              <div key={chordName} className="border rounded-lg p-3">
                <h3 className="font-semibold mb-2">{chordName}</h3>
                
                <Tabs defaultValue="0" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    {variations.slice(0, 3).map((variation, index) => (
                      <TabsTrigger key={index} value={index.toString()}>
                        {variation.suffix}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  
                  {variations.slice(0, 3).map((variation, variationIndex) => (
                    <TabsContent key={variationIndex} value={variationIndex.toString()}>
                      <div className="space-y-2">
                        {variation.positions
                          .filter(position => position.difficulty <= difficultyFilter)
                          .slice(0, 3)
                          .map((position, positionIndex) => (
                          <div key={positionIndex} className="flex items-center gap-3 p-2 border rounded hover:bg-muted/50 transition-colors">
                            {renderChordDiagram(position)}
                            
                            <div className="flex-1">
                              <div className="flex gap-2 mb-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => playChord(position)}
                                  disabled={isPlaying}
                                  className="flex items-center gap-1"
                                >
                                  {isPlaying ? <Square className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                                  {playMode === 'arpeggio' ? 'Arpège' : 'Bloc'}
                                </Button>
                                
                                <Button
                                  size="sm"
                                  onClick={() => handleChordSelect(`${chordName}${variation.suffix !== 'major' ? variation.suffix : ''}`, position)}
                                  className="flex items-center gap-1"
                                >
                                  <Zap className="h-3 w-3" />
                                  Ajouter
                                </Button>
                              </div>
                              
                              <div className="flex gap-1 flex-wrap">
                                <Badge variant="secondary" className="text-xs">
                                  Frette {position.baseFret}
                                </Badge>
                                {position.barres.length > 0 && (
                                  <Badge variant="outline" className="text-xs">
                                    Barré
                                  </Badge>
                                )}
                                <Badge 
                                  variant={position.difficulty <= 2 ? 'default' : position.difficulty <= 3 ? 'secondary' : 'destructive'} 
                                  className="text-xs"
                                >
                                  <Star className="h-2 w-2 mr-1" />
                                  {position.difficulty}/5
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default ChordLibraryManager;