'use client';

import { useRouter } from 'next/navigation';
import { useTransition } from 'react';
import { createNewSongAction } from './actions';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { type Song } from '@/components/songs/song-schema';

interface ComposerActionsProps {
  songs: Pick<Song, 'id' | 'title'>[];
}

export default function ComposerActions({ songs }: ComposerActionsProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const handleCreateSong = () => {
    startTransition(async () => {
      try {
        await createNewSongAction();
      } catch (error) {
        console.error('Failed to create song:', error);
        toast({
          title: 'Error',
          description: 'Could not create a new song. Please try again.',
          variant: 'destructive',
        });
      }
    });
  };

  const handleLoadSong = (songId: string) => {
    if (songId) {
      router.push(`/ai-composer-workspace/${songId}`);
    }
  };

  return (
    <div className="flex flex-col items-center gap-4 md:flex-row">
      <Button onClick={handleCreateSong} disabled={isPending} size="lg" className="px-8 py-6 text-lg">
        <PlusCircle className="mr-3 h-6 w-6" />
        {isPending ? 'Création...' : 'Créer une nouvelle chanson'}
      </Button>
      
      {songs && songs.length > 0 && (
        <>
          <p className="text-muted-foreground font-medium">OU</p>
          <Select onValueChange={handleLoadSong}>
            <SelectTrigger className="w-[280px] h-[58px] text-lg">
              <SelectValue placeholder="Charger une chanson" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Vos chansons</SelectLabel>
                {songs.filter(song => song.id).map((song) => (
                  <SelectItem key={song.id!} value={song.id!}>
                    {song.title || 'Chanson sans titre'}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </>
      )}
    </div>
  );
}