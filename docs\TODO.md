# TODO - Débogage Audio et Fonctionnalités des Versions

## Problème Principal Actuel: Erreur "Invalid URI"

- **Description :** Une erreur "Invalid URI. Load of media resource failed." survient lors du chargement d'une version de morceau sauvegardée qui contient une URL audio.
- **URL problématique (exemple) :** `https://pmzpzeeuyahozsmfdfyr.supabase.co/storage/v1/object/public/audio/1748977888543-0lfnsvn.wav` (Cette URL est valide en accès direct).
- **Contexte de l'erreur :**
    - L'erreur apparaît dans la console du navigateur peu après que `useLocalFileManagement` (dans `SongForm.tsx`) ait défini l'URL audio.
    - Le log personnalisé `[AudioWaveformPreview] Attempting to load URL...` ajouté dans `AudioWaveformPreview.tsx` **n'est pas apparu** dans les derniers logs fournis par l'utilisateur. Cela suggère que l'erreur pourrait survenir *avant* que WaveSurfer ne tente explicitement de charger l'URL via `wavesurfer.current.load()`, ou qu'elle provient d'une autre source.
- **Hypothèse actuelle :** Un autre élément (potentiellement un tag `<audio>` HTML natif caché ou mal configuré dans `SongForm.tsx` ou un de ses composants enfants) pourrait tenter de charger l'URL audio dès sa mise à jour et échouer, déclenchant l'erreur générique du navigateur avant que `AudioWaveformPreview` n'agisse.

### Prochaines étapes d'investigation pour "Invalid URI":

1.  **Vérification des logs console complets :**
    *   Reproduire l'erreur.
    *   Fournir les logs **complets** de la console du navigateur pour confirmer définitivement si le log `[AudioWaveformPreview] Attempting to load URL...` apparaît ou non.
    *   Rechercher tout autre message d'erreur pertinent.

2.  **Analyse de la requête réseau :**
    *   Ouvrir l'onglet "Réseau" des outils de développement **avant** de reproduire l'erreur.
    *   Filtrer la requête pour le fichier audio (ex: `0lfnsvn.wav`).
    *   Fournir les détails complets de cette requête :
        *   URL complète et méthode.
        *   Code de statut HTTP.
        *   En-têtes de réponse complets (surtout `Content-Type`, `Access-Control-Allow-Origin`).
        *   Aperçu de la réponse (pour vérifier si c'est bien le fichier audio ou une erreur HTML).

3.  **Inspection du DOM :**
    *   Inspecter le DOM de la page où l'erreur se produit.
    *   Rechercher activement des éléments `<audio>` (visibles ou cachés) et vérifier leurs attributs `src` avant et après le chargement de la version.

4.  **Revue du code (`SongForm.tsx` et enfants) :**
    *   Continuer l'analyse des résultats de la recherche `codebase_search` pour `<audio`.
    *   Identifier tout usage direct d'un tag `<audio>` qui pourrait être lié à `audioUrl` ou une variable dérivée, en dehors de `AudioWaveformPreview`.

## Fonctionnalité d'édition des versions (Complétée en grande partie)

- L'édition du nom et des notes des versions a été implémentée (UI dans `SongVault.tsx`, logique dans `useSongVersioning.ts`).
- **À vérifier :** Le bon fonctionnement de cette édition une fois le problème audio résolu, pour s'assurer qu'il n'y a pas d'effets de bord.

## Améliorations `AudioWaveformPreview.tsx`

- Ajout de logs pour tracer la valeur et le type de `audioUrl` avant l'appel à `wavesurfer.current.load()`.
- Renforcement de la validation de `audioUrl` pour éviter les chaînes vides ou composées uniquement d'espaces.

---

## Module Compositeur/Éditeur de Morceaux (SongForm & related)

### Fonctionnalité du Bouton "Enregistrer" (Song Editor Save Button)

- **[X] CORRIGÉ - Bouton "Enregistrer" :** Le bouton "Enregistrer" sur la page d'édition de morceau est maintenant fonctionnel.
    - **Problème initial :** Le clic sur "Enregistrer" ne déclenchait pas les gestionnaires de soumission de React Hook Form (`handleSubmit`).
    - **Cause identifiée :** Une erreur se produisait silencieusement durant la validation Zod, car des champs de type tableau (ex: `subgenre`) étaient `null` dans les données du formulaire (provenant de la BDD) alors que le schéma Zod attendait un tableau (même vide `[]`).
    - **Correctifs apportés :**
        1.  Ajout d'un `useEffect` de débogage dans `SongForm.tsx` pour tracer les erreurs de parsing Zod en temps réel sur les valeurs du formulaire (`methods.watch()`).
        2.  Modification de la logique de `processedInitialValues` (dans `useMemo` de `SongForm.tsx`) pour transformer les champs de type tableau `null` en `[]` avant de les passer à `methods.reset()` de React Hook Form.
    - **Résultat :** La validation Zod passe, `handleSubmit` appelle `handleRHFSubmitWrapper`, qui à son tour appelle `handleFormSubmit` (dans `page.tsx`), et les données sont sauvegardées dans Supabase.

### Améliorations Générales et Fonctionnalités Manquantes (Suite)

- **[ ] Éditeur IA :**
    - **[ ] Complétude :** Évaluer et implémenter les fonctionnalités manquantes ou à améliorer dans l'éditeur IA (`LyricsEditorWithAI.tsx`).
        - Ex: Ajouter des actions IA comme "Résumer", "Expliquer la structure", etc.
        - Ex: Améliorer l'UI/UX pour l'affichage des résultats IA.
- **[ ] Diagrammes d'Accords & JSON associé :**
        - **[ ] Intégration et Correction :** Intégrer et corriger l'affichage des diagrammes d'accords.
        - **[ ] Gestion des fichiers JSON d'accords :**
            - Discuter de la localisation et du chargement des fichiers JSON d'accords (chemin à spécifier par l'utilisateur ou autre méthode).
            - Définir/améliorer la structure du JSON pour les diagrammes d'accords si nécessaire.
        - **[ ] Gestion des accords manquants :** Implémenter une stratégie pour les accords non trouvés dans les fichiers JSON (ex: placeholder, ajout dynamique, notification).
        - **[ ] Optimisation :** Assurer une performance et une maintenabilité professionnelles de l'intégration.
- **[ ] Revue Générale du Module :**
    - **[ ] Identifier et lister d'autres améliorations** pour le module de composition et d'édition de morceaux.
    - **[ ] S'assurer de la robustesse et de la convivialité** de toutes les fonctionnalités.

---