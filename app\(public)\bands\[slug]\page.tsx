import { createSupabaseServerClient } from "@/lib/supabase/server";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Music, Calendar, MessageSquare as MessageSquareIcon, Share2, Edit } from "lucide-react"; // Added Share2, Edit
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { CommentSection } from "@/components/comments/comment-section";
import { ResourceViewTracker } from "@/components/stats/resource-view-tracker";
import Image from "next/image"; 
import { SharePopover } from '@/components/shared/share-popover'; 
import { Button } from "@/components/ui/button"; 
import { ResourceHeaderBanner } from '@/components/shared/resource-header-banner'; 
import { ResourceStatsDisplay } from '@/components/shared/ResourceStatsDisplay';
import { LikeButton } from '@/components/social/like-button'; // Added
import { DislikeButton } from '@/components/social/dislike-button'; // Added
import { FollowBandButton } from '@/components/social/FollowBandButton'; // Added

interface PublicBandData {
  id: string;
  name: string;
  description: string | null;
  avatar_url: string | null;
  cover_url: string | null;
  creator_id: string; 
  created_at: string;
  genres?: string[] | null;
  moods?: string[] | null;
  instrumentation?: string[] | null;
  location?: string | null;
  are_comments_public?: boolean; 
  is_public?: boolean; 
  band_members_with_profiles?: Array<{ 
    user_id: string; 
    profile: { username: string | null; avatar_url: string | null } | null 
  }>;
  like_count?: number;
  dislike_count?: number;
  view_count?: number;
  follower_count?: number;
}

async function getPublicBandBySlug(slug: string, supabaseClient: any): Promise<Omit<PublicBandData, 'band_members_with_profiles' | 'like_count' | 'view_count'> & { band_members: Array<{user_id: string}> | null } | null> {
  const { data, error } = await supabaseClient
    .from("bands")
    .select(`
      id, name, description, avatar_url, cover_url, creator_id, created_at, 
      genres, moods, instrumentation, location, are_comments_public, is_public,
      dislike_count, follower_count,
      band_members ( user_id )
    `)
    .eq("slug", slug)
    .single();

  if (error) {
    console.error("Error fetching public band by slug:", slug, error);
    return null;
  }
  return data; 
}

export default async function PublicBandPage({ params }: { params: { slug: string } }) {
  const supabase = createSupabaseServerClient(); 
  const bandBasicData = await getPublicBandBySlug(params.slug, supabase);

  if (!bandBasicData) {
    notFound();
  }

  // Fetch like_count and view_count via RPC
  let likeCount = 0;
  let viewCount = 0;

  const { data: likeCountData } = await supabase
    .rpc('get_like_count', { resource_id_param: bandBasicData.id, resource_type_param: 'band' });
  if (typeof likeCountData === 'number') likeCount = likeCountData;

  const { data: viewCountData } = await supabase
    .rpc('get_view_count', { resource_id_param: bandBasicData.id, resource_type_param: 'band' });
  if (typeof viewCountData === 'number') viewCount = viewCountData;

  let bandMembersWithProfiles: PublicBandData['band_members_with_profiles'] = [];
  if (bandBasicData.band_members && bandBasicData.band_members.length > 0) {
    const memberUserIds = bandBasicData.band_members.map(m => m.user_id);
    const { data: profilesData, error: profilesError } = await supabase
      .from("profiles")
      .select("id, username, avatar_url")
      .in("id", memberUserIds);

    if (profilesError) {
      console.error("Error fetching member profiles for public band page:", profilesError);
    } else if (profilesData) {
      const profilesMap = new Map(profilesData.map(p => [p.id, p]));
      bandMembersWithProfiles = bandBasicData.band_members.map(m => ({
        user_id: m.user_id,
        profile: profilesMap.get(m.user_id) || null
      }));
    }
  }
  
  const band: PublicBandData = { 
    ...bandBasicData, 
    like_count: likeCount,
    view_count: viewCount,
    // dislike_count and follower_count are assumed to be on bandBasicData if they exist on the table
    band_members_with_profiles: bandMembersWithProfiles 
  };

  const { data: { user: currentUser } } = await supabase.auth.getUser();
  const isModerator = currentUser?.id === band.creator_id;

  let isCurrentUserMember = false;
  let initialIsLiked = false;
  let initialIsDisliked = false;
  let initialIsFollowed = false;

  if (currentUser && band.id) { 
    const { data: membership } = await supabase
      .from("band_members")
      .select("user_id")
      .eq("band_id", band.id)
      .eq("user_id", currentUser.id)
      .maybeSingle();
    isCurrentUserMember = !!membership;

    const { data: likeData } = await supabase.from('likes').select('id').eq('resource_id', band.id).eq('resource_type', 'band').eq('user_id', currentUser.id).maybeSingle();
    initialIsLiked = !!likeData;

    const { data: dislikeData } = await supabase.from('dislikes').select('id').eq('resource_id', band.id).eq('resource_type', 'band').eq('user_id', currentUser.id).maybeSingle();
    initialIsDisliked = !!dislikeData;
    
    const { data: followData } = await supabase.from('band_followers').select('user_id').eq('band_id', band.id).eq('user_id', currentUser.id).maybeSingle();
    initialIsFollowed = !!followData;
  }
  
  console.log(`PublicBandPage: slug=${params.slug}, band.is_public=${band.is_public}, currentUser.id=${currentUser?.id}, band.creator_id=${band.creator_id}, isCurrentUserMember=${isCurrentUserMember}`);
  if (!band.is_public && currentUser?.id !== band.creator_id && !isCurrentUserMember) {
    console.log(`PublicBandPage: Calling notFound() because band is not public and user is not creator or member.`);
    notFound();
  }

  return (
    <div className="pb-8">
      <ResourceViewTracker resourceId={band.id} resourceType="band" />
      
      <ResourceHeaderBanner
        coverUrl={band.cover_url}
        avatarUrl={band.avatar_url}
        defaultIcon={<Users className="w-20 h-20 text-muted-foreground" />}
        resourceTypeLabel="Groupe"
        title={band.name}
        description={band.description}
        details={
          <>
            {band.genres?.map(g => <Badge key={g} variant="secondary" className="text-xs">{g}</Badge>)}
            {band.location && <Badge variant="outline" className="text-xs">{band.location}</Badge>}
            {band.created_at && (
              <span className="text-xs text-muted-foreground">
                Formé le {new Date(band.created_at).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' })}
              </span>
            )}
          </>
        }
        actionButtons={
          <>
            {currentUser && (
              <>
                <LikeButton
                  resourceId={band.id}
                  resourceType="band"
                  initialLikes={band.like_count || 0}
                  initialIsLiked={initialIsLiked}
                  userId={currentUser.id}
                  size="default"
                />
                <DislikeButton
                  resourceId={band.id}
                  resourceType="band"
                  initialDislikes={band.dislike_count || 0}
                  initialIsDisliked={initialIsDisliked}
                  userId={currentUser.id}
                  size="default"
                />
                <FollowBandButton
                  bandId={band.id}
                  initialIsFollowed={initialIsFollowed}
                  initialFollowerCount={band.follower_count || 0}
                  size="default"
                />
              </>
            )}
            <SharePopover
              resourceType="band"
              resourceSlug={params.slug} 
              resourceTitle={band.name}
              triggerButton={
                <Button variant="outline" size="default" title="Partager">
                  <Share2 className="mr-2 h-4 w-4" /> Partager
                </Button>
              }
            />
            {currentUser?.id === band.creator_id && (
              <Button variant="outline" asChild title="Gérer le groupe" size="default">
                <Link href={`/manage-bands/${band.id}/edit`}><Edit className="mr-2 h-4 w-4" /> Gérer</Link>
              </Button>
            )}
          </>
        }
        stats={
          <ResourceStatsDisplay
            resourceType="band"
            likeCount={band.like_count}
            dislikeCount={band.dislike_count}
            viewCount={band.view_count}
            followerCount={band.follower_count}
          />
        }
      />

      <div className="container mx-auto max-w-7xl py-8 px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main content column */}
          <div className="lg:col-span-2 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>À propos de {band.name}</CardTitle>
              </CardHeader>
              <CardContent>
                {band.description ? (
                  <p className="text-muted-foreground whitespace-pre-wrap">{band.description}</p>
                ) : (
                  <p className="text-muted-foreground">Aucune description fournie pour ce groupe.</p>
                )}
                <div className="mt-4 space-y-2">
                  {band.moods && band.moods.length > 0 && <p><strong>Ambiances:</strong> {band.moods.join(', ')}</p>}
                  {band.instrumentation && band.instrumentation.length > 0 && <p><strong>Instrumentation:</strong> {band.instrumentation.join(', ')}</p>}
                </div>
              </CardContent>
            </Card>

            {/* TODO: Placeholder for Band's Songs / Albums list */}
            <Card>
              <CardHeader><CardTitle>Discographie (Prochainement)</CardTitle></CardHeader>
              <CardContent><p className="text-muted-foreground">Les albums et morceaux du groupe seront affichés ici.</p></CardContent>
            </Card>
            
            <CommentSection 
              resourceId={band.id} 
              resourceType="band"
              resourceCreatorId={band.creator_id} 
              isModeratorView={isModerator} 
              areCommentsPublic={band.are_comments_public ?? false}
              isCurrentUserMember={isCurrentUserMember}
            />
          </div>

          {/* Sidebar column */}
          <aside className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader><CardTitle>Membres du groupe</CardTitle></CardHeader>
              <CardContent>
                {band.band_members_with_profiles && band.band_members_with_profiles.length > 0 ? (
                  <ul className="space-y-3">
                    {band.band_members_with_profiles.map(member => (
                      member.profile && member.profile.username && ( 
                        <li key={member.user_id} className="flex items-center gap-3">
                          <Link href={`/artists/${member.profile.username}`} className="flex items-center gap-3 hover:bg-muted/50 p-1 rounded-md transition-colors">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={member.profile.avatar_url || undefined} alt={member.profile.username} />
                              <AvatarFallback>{member.profile.username.charAt(0).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <span>{member.profile.username}</span>
                          </Link>
                        </li>
                      )
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-muted-foreground">Aucun membre à afficher.</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader><CardTitle>Activité (Prochainement)</CardTitle></CardHeader>
              <CardContent><p className="text-sm text-muted-foreground">L'activité récente du groupe sera affichée ici.</p></CardContent>
            </Card>

            <Card>
              <CardHeader><CardTitle>Mur (Prochainement)</CardTitle></CardHeader>
              <CardContent><p className="text-sm text-muted-foreground">Le mur de discussion ou les posts du groupe apparaîtront ici.</p></CardContent>
            </Card>

            <Card>
              <CardHeader><CardTitle>Artistes Similaires (Prochainement)</CardTitle></CardHeader>
              <CardContent><p className="text-sm text-muted-foreground">Des suggestions d'artistes similaires seront affichées ici.</p></CardContent>
            </Card>

            <Card>
              <CardHeader><CardTitle>Groupes Similaires (Prochainement)</CardTitle></CardHeader>
              <CardContent><p className="text-sm text-muted-foreground">Des suggestions de groupes similaires seront affichées ici.</p></CardContent>
            </Card>
          </aside>
        </div>
      </div>
    </div>
  );
}
