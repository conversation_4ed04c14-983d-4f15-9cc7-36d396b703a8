CREATE OR REPLACE FUNCTION public.get_playlist_details_for_view(p_playlist_id uuid, p_requesting_user_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    is_public boolean,
    cover_art_url text,
    banner_url text,
    user_id uuid,
    created_at timestamptz,
    updated_at timestamptz,
    slug text,
    creator_profile json,
    songs json,
    song_count bigint,
    total_duration_ms numeric,
    like_count bigint,
    view_count bigint,
    is_liked boolean,
    is_bookmarked boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.description,
        p.is_public,
        p.cover_art_url,
        p.banner_url,
        p.user_id,
        p.created_at,
        p.updated_at,
        p.slug,
        json_build_object(
            'id', u.id,
            'username', u.username,
            'avatar_url', u.avatar_url
        ) AS creator_profile,
        (
            SELECT json_agg(
                json_build_object(
                    'id', s.id, 
                    'title', s.title,
                    'duration_ms', s.duration_ms,
                    'cover_image_url', s.cover_image_url,
                    'profiles', s.profiles
                )
            ) FILTER (WHERE s.id IS NOT NULL)
            FROM playlist_songs ps
            JOIN songs_with_profiles s ON ps.song_id = s.id
            WHERE ps.playlist_id = p.id
        ) AS songs,
        COUNT(DISTINCT ps.song_id) AS song_count,
        SUM(s.duration_ms) AS total_duration_ms,
        (SELECT COUNT(*) FROM playlist_likes pl WHERE pl.playlist_id = p.id) AS like_count,
        p.view_count,
        (CASE WHEN p_requesting_user_id IS NOT NULL THEN
            EXISTS (SELECT 1 FROM playlist_likes pl WHERE pl.playlist_id = p.id AND pl.user_id = p_requesting_user_id)
        ELSE FALSE END) AS is_liked,
        (CASE WHEN p_requesting_user_id IS NOT NULL THEN
            EXISTS (SELECT 1 FROM user_playlist_bookmarks upb WHERE upb.playlist_id = p.id AND upb.user_id = p_requesting_user_id)
        ELSE FALSE END) AS is_bookmarked
    FROM
        playlists p
    JOIN
        profiles u ON p.user_id = u.id
    LEFT JOIN
        playlist_songs ps ON p.id = ps.playlist_id
    LEFT JOIN
        songs s ON ps.song_id = s.id
    WHERE
        p.id = p_playlist_id
    GROUP BY
        p.id, u.id;
END;
$$ LANGUAGE plpgsql STABLE;
