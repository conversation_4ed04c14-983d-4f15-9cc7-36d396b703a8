"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { UserPlus, UserCheck, Loader2 } from 'lucide-react';
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';

interface FollowProfileButtonProps {
  profileId: string;
  initialIsFollowed: boolean;
  initialFollowerCount: number;
  onFollowToggle?: (isFollowing: boolean, newFollowerCount: number) => void;
  size?: "sm" | "default" | "lg" | "icon";
  className?: string;
}

export function FollowProfileButton({
  profileId,
  initialIsFollowed,
  initialFollowerCount,
  onFollowToggle,
  size = "default",
  className,
}: FollowProfileButtonProps) {
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isFollowed, setIsFollowed] = useState(initialIsFollowed);
  const [followerCount, setFollowerCount] = useState(initialFollowerCount);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsFollowed(initialIsFollowed);
    setFollowerCount(initialFollowerCount);
  }, [initialIsFollowed, initialFollowerCount]);

  const handleFollow = async () => {
    if (!user) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour suivre un profil.", variant: "destructive" });
      return;
    }
    if (user.id === profileId) {
      toast({ title: "Action impossible", description: "Vous ne pouvez pas vous suivre vous-même.", variant: "default" });
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('toggle_profile_follow', {
        p_profile_id: profileId,
        p_user_id: user.id,
      });

      if (error) throw error;

      if (data && data.length > 0) {
        const result = data[0];
        setIsFollowed(result.is_following);
        setFollowerCount(result.new_follower_count);
        if (onFollowToggle) {
          onFollowToggle(result.is_following, result.new_follower_count);
        }
        toast({
          title: result.is_following ? "Suivi !" : "Ne plus suivi",
          description: result.is_following ? `Vous suivez maintenant ce profil.` : `Vous ne suivez plus ce profil.`,
        });
      }
    } catch (error: any) {
      console.error("Error toggling profile follow:", error);
      toast({ title: "Erreur", description: error.message || "Une erreur est survenue.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user || user.id === profileId) { // Don't show button if not logged in or it's user's own profile
    return null;
  }

  return (
    <Button
      variant={isFollowed ? "secondary" : "outline"}
      size={size}
      onClick={handleFollow}
      disabled={isLoading}
      className={className}
      title={isFollowed ? "Ne plus suivre ce profil" : "Suivre ce profil"}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : isFollowed ? (
        <UserCheck className="mr-2 h-4 w-4" />
      ) : (
        <UserPlus className="mr-2 h-4 w-4" />
      )}
      {isFollowed ? 'Suivi' : 'Suivre'}
      {/* Optionally display follower count, though typically not on the button itself */}
      {/* <span className="ml-2 text-xs">({followerCount})</span> */}
    </Button>
  );
}
