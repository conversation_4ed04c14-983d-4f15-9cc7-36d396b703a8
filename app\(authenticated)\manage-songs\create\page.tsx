"use client";

import { useEffect, useState, useRef } from 'react'; // Removed useCallback, added useRef
import { useRouter } from 'next/navigation'; 
import { createBrowserClient } from '@supabase/ssr'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
// Removed useForm, SubmitHandler, zodResolver, z as SongForm handles this
import { toast } from "@/hooks/use-toast"; 

import { SongForm, SongFormHandle } from '@/components/songs/SongForm';
import { createSong } from '@/lib/actions/song.actions';
import { SongFormValues } from '@/components/songs/song-schema'; // Import SongFormValues from song-schema
import { Button } from "@/components/ui/button"; 
import { Loader2 as LoadingSpinner } from 'lucide-react'; 

// Removed musicalKeys, timeSignatures, songFormSchema, and local SongFormValues interface

interface Album {
  id: string;
  title: string;
}

// Removed constants for localStorage keys related to AI config, as SongForm and its children handle this.
// Removed NO_ALBUM_SELECTED_VALUE if SongForm handles this logic internally (it does)

export default function CreateSongPage() {
  const songFormRef = useRef<SongFormHandle>(null); // Ref for SongForm component
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  const router = useRouter();

  // Removed stripHtml utility if not used elsewhere; SongForm handles lyrics stripping.

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [albums, setAlbums] = useState<Album[]>([]);
  const [isLoadingAlbums, setIsLoadingAlbums] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null); // State to store userId

  // Removed states: uploadingAudio, uploadedAudioUrl, uploadingImage, uploadedImageUrl, lyricsContent, 
  // aiConfig, aiLoading, aiLastResult, aiError, aiHistory, aiMaxOutputTokens, aiGeneralPrompt
  // Removed refs: audioFileInputRef, imageFileInputRef
  // Removed form hook: const form = useForm(...)
  // Removed handleLyricsChange and other specific field handlers

  // Fetch albums and user on component mount
  useEffect(() => {
    const fetchData = async () => {
      // Fetch user
      const { data: userSessionData, error: sessionError } = await supabase.auth.getUser();
      if (sessionError || !userSessionData?.user) {
        console.error('Authentication error:', sessionError);
        toast({ title: "Erreur d'authentification", description: "Impossible de récupérer l'utilisateur. Veuillez vous reconnecter.", variant: "destructive" });
        router.push('/login');
        return;
      }
      setCurrentUserId(userSessionData.user.id);

      // Fetch albums
      setIsLoadingAlbums(true);
      const { data, error } = await supabase.from('albums').select('id, title');
      if (error) {
        console.error('Error fetching albums:', error);
        toast({
          title: "Erreur lors du chargement des albums",
          description: `Erreur lors du chargement des albums: ${error.message}`,
          variant: "destructive",
        });
      } else {
        setAlbums(data || []);
      }
      setIsLoadingAlbums(false);
    };
    fetchData();
  }, [supabase, router]);

  const onFormSubmit = async (data: SongFormValues) => {
    setIsSubmitting(true);
    try {
      if (!currentUserId) {
        toast({ title: "Erreur d'authentification", description: "ID utilisateur non disponible. Veuillez réessayer.", variant: "destructive" });
        router.push('/login');
        return;
      }

      const result = await createSong(data);

      if (result.error || !result.success || !result.data) {
        console.error('Error creating song:', result.error, (result as any).details);
        toast({
          title: "Erreur lors de la création",
          description: result.error || "Une erreur inconnue est survenue.",
          variant: "destructive",
        });
        return; 
      }

      const newSong = result.data;
      toast({ title: "Chanson Créée!", description: `\"${newSong.title}\" a été ajoutée.` });

      if (songFormRef.current) {
        const vaultActions = songFormRef.current.getVaultActions();
        if (vaultActions && newSong.id) {
          try {
            await vaultActions.savePendingItems(newSong.id, currentUserId);
          } catch (vaultError: any) {
            console.error("Error saving vault items:", vaultError);
            toast({ title: "Erreur Vault", description: `Les fichiers du vault n'ont pas pu être sauvegardés: ${vaultError.message}`, variant: "destructive"});
          }
        }
      }

      router.push(`/manage-songs/${newSong.id}/edit`);

    } catch (e: any) {
      console.error("An unexpected error occurred during form submission:", e);
      toast({
        title: "Erreur inattendue",
        description: "Une erreur inattendue est survenue lors de la soumission.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingAlbums) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner className="h-8 w-8 animate-spin" />
        <p className="ml-2">Chargement des albums...</p>
      </div>
    );
  }
  
  return (
    <main className="w-full min-h-screen bg-background flex flex-col items-stretch">
      {/* Removed max-w-[1600px] and mx-auto for full width */}
      <div className="w-full px-2 sm:px-6 lg:px-12 py-6">
        <h1 className="text-3xl sm:text-4xl font-bold tracking-tight text-gray-900 dark:text-white mb-2">
          Créer un morceau
        </h1>
        <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-8">
          Partagez votre musique avec le monde.
        </p>
        <SongForm
          mode="create"
          onFormSubmit={onFormSubmit}
          supabaseClient={supabase}
          ref={songFormRef}
          albumsData={albums}
          isLoadingAlbums={isLoadingAlbums}
          currentUserId={currentUserId || ''}
        />
      </div>
    </main>
  );
}
