import { createSupabaseServerClient } from "@/lib/supabase/server";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { History, Music, Play, ArrowLeft } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { AddToPlaylistButton } from "@/components/playlists/add-to-playlist-button";

// Simplified Song type for this page
interface RecentlyPlayedSong {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  profiles: {
    username: string | null;
    display_name: string | null;
  } | null;
  last_played_at: string; 
}

interface SupabaseSongProfile {
  username: string | null;
  display_name: string | null;
}

interface SupabaseSong {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  profiles: SupabaseSongProfile | null; 
}

interface PlayWithSongData {
  song_id: string;
  created_at: string;
  songs: SupabaseSong | null; 
}


export default async function RecentlyPlayedPage() {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return <p className="w-full px-4 md:px-6 lg:px-8 py-8">Veuillez vous connecter pour voir vos écoutes récentes.</p>;
  }

  // Fetch recently played distinct songs by the user
  // This is a bit more complex: get distinct song_ids from 'plays' ordered by last play time, then fetch song details.
  // Using an RPC function would be cleaner for this, but for now, a direct query approach.
  
  // Step 1: Get distinct song_ids and their last play time
  const { data: recentPlays, error: recentPlaysError } = await supabase
    .from('plays')
    .select('song_id, created_at')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
    // .limit(200); // Limit initial fetch to avoid too much processing client-side for distinct
    // This query gets all plays, then we'll process distinct songs.
    // A better query would use a window function or distinct on, but that's harder with Supabase JS client directly for complex cases.
    // Let's try an RPC or a view for this in the future if performance is an issue.
    // For now, simple fetch and client-side distinct.
    // Alternative: A view `user_recent_song_plays` (song_id, user_id, last_played_at)

  // For now, let's use a simpler approach that might show duplicates if played multiple times recently,
  // or fetch more and then make distinct.
  // A better approach would be an RPC:
  // CREATE OR REPLACE FUNCTION get_user_recently_played_songs(p_user_id UUID, p_limit INT)
  // RETURNS TABLE(song_id UUID, last_played_at TIMESTAMPTZ) AS $$
  // BEGIN
  //   RETURN QUERY
  //   SELECT s.song_id, MAX(s.created_at) as last_played_at
  //   FROM public.plays s
  //   WHERE s.user_id = p_user_id
  //   GROUP BY s.song_id
  //   ORDER BY last_played_at DESC
  //   LIMIT p_limit;
  // END;
  // $$ LANGUAGE plpgsql;
  // For now, I'll proceed with a client-side distinct after fetching song details.

  const { data: songPlaysData, error: songPlaysError } = await supabase
    .from('plays')
    .select('song_id, created_at, songs(*, profiles:creator_user_id(username, display_name))')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
    .limit(50); 

  if (songPlaysError) {
    console.error("Error fetching recently played songs:", songPlaysError);
  }
  
  const typedSongPlays = songPlaysData as PlayWithSongData[] | null;

  const distinctSongsMap = new Map<string, RecentlyPlayedSong>();
  if (typedSongPlays) {
    for (const play of typedSongPlays) {
      if (play.songs && !distinctSongsMap.has(play.songs.id)) { // play.songs is now correctly typed as SupabaseSong | null
        distinctSongsMap.set(play.songs.id, {
          id: play.songs.id,
          title: play.songs.title,
          duration: play.songs.duration,
          cover_url: play.songs.cover_url,
          profiles: play.songs.profiles, 
          last_played_at: play.created_at, 
        });
      }
    }
  }
  const songs: RecentlyPlayedSong[] = Array.from(distinctSongsMap.values());


  const formatDuration = (seconds: number | null) => {
    if (seconds === null) return '-:--';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${String(remainingSeconds).padStart(2, '0')}`;
  };
  
  return (
    <div className="w-full px-4 md:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold flex items-center">
          <History className="mr-3 h-8 w-8 text-sky-500" />
          Écoutés Récemment
        </h1>
        <Button variant="outline" size="sm" asChild>
          <Link href="/playlists"><ArrowLeft className="mr-2 h-4 w-4" />Retour aux Playlists</Link>
        </Button>
      </div>

      {songs.length > 0 ? (
        <div className="space-y-3">
          {songs.map((song, index) => (
            <Card key={song.id} className="flex items-center p-3 gap-4 hover:bg-muted/50">
              <span className="text-sm text-muted-foreground w-6 text-center">{index + 1}</span>
              {song.cover_url ? (
                <img src={song.cover_url} alt={song.title} className="h-12 w-12 object-cover rounded-md" />
              ) : (
                <div className="h-12 w-12 bg-muted rounded-md flex items-center justify-center">
                  <Music className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <Link href={`/songs/${song.id}`} className="font-medium hover:underline truncate block" title={song.title}>
                  {song.title}
                </Link>
                <p className="text-xs text-muted-foreground truncate" title={song.profiles?.display_name || song.profiles?.username || "Artiste inconnu"}>
                  {song.profiles?.display_name || song.profiles?.username || "Artiste inconnu"}
                </p>
                <p className="text-xs text-muted-foreground">
                  Écouté {formatDistanceToNow(new Date(song.last_played_at), { locale: fr, addSuffix: true })}
                </p>
              </div>
              <span className="text-sm text-muted-foreground hidden sm:inline">{formatDuration(song.duration)}</span>
              <div className="flex items-center gap-2">
                <AddToPlaylistButton songId={song.id} />
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <History className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Aucune écoute récente</h3>
          <p className="text-muted-foreground">Votre historique d'écoute apparaîtra ici.</p>
        </div>
      )}
    </div>
  );
}
