import type React from "react"
import { redirect } from "next/navigation"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { AppSidebar, type UserProfileForSidebar } from "@/components/sidebar";
import { getUserProfileForSidebar } from "@/lib/supabase/queries/user";
import MobileMenuButton from "./mobile-menu-button";
// UserProvider is no longer needed here as it's global in ClientLayout
// import { UserProvider } from "@/contexts/user-context"; 

interface AuthenticatedLayoutProps {
  children: React.ReactNode
}

interface ProfileDataForLayout {
  display_name?: string | null;
  username?: string | null;
  avatar_url?: string | null;
  role_primary?: string | null;
  subscription_tier?: string | null; 
  user_role?: string | null; 
  custom_uploads_per_month?: number | null;
  custom_vault_space_gb?: number | null;
  custom_vault_max_files?: number | null; // Added
  custom_ia_credits_month?: number | null;
  custom_coins_month?: number | null;
  custom_max_playlists?: number | null;
  custom_max_friends?: number | null;
  ia_credits?: number | null; 
  coins_balance?: number | null; 
}

export default async function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const supabase = createSupabaseServerClient()

  const {
    data: { user }, // This is AuthUser
    error: userError
  } = await supabase.auth.getUser()

  if (!user || userError) {
    redirect("/") 
  }

  // Fetch profile data to construct userObj for AppSidebar
  // UserProvider is now in ClientLayout, so this layout doesn't need to provide it.
  const userForSidebar = await getUserProfileForSidebar(supabase, user.id, user);
  if (!userForSidebar) {
    console.warn(`AuthenticatedLayout: Could not fetch sidebar profile for user ${user.id}. User might see a degraded experience.`);
    // Even if the profile is missing, we must provide a minimal object to avoid crashing the sidebar
    const minimalProfile: UserProfileForSidebar = {
      id: user.id,
      email: user.email,
      name: user.email?.split('@')[0] || 'User',
      username: user.email?.split('@')[0] || 'user',
      avatar_url: null,
      role_primary: 'user',
      subscription_tier: 'free',
      user_role: 'user',
      custom_uploads_per_month: null,
      custom_vault_space_gb: null,
      custom_vault_max_files: null,
      custom_ia_credits_month: null,
      custom_coins_month: null,
      custom_max_playlists: null,
      custom_max_friends: null,
      ia_credits: 0,
      coins_balance: 0,
    };
    return (
      <div className="flex h-screen bg-background">
        <AppSidebar user={minimalProfile} />
        <main className="flex-1 p-4 sm:p-6 md:p-8 overflow-y-auto">
          {children}
        </main>
      </div>
    );
  }
  
  // ClientLayout (via app/layout.tsx) will provide UserProvider globally.
  // This AuthenticatedLayout just renders its specific structure.
  return (
    <div className="flex h-screen main-layout-container"> 
      <MobileMenuButton />
      <AppSidebar user={userForSidebar} /> 
      <div className="flex-1 flex flex-col">
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
