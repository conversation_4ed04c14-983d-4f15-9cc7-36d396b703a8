// Utiliser l'API Web standard pour le streaming
// Pas besoin d'importer StreamingTextResponse
import { BytesOutputParser } from '@langchain/core/output_parsers';
import { ChatOllama } from '@langchain/community/chat_models/ollama';
import { ChatOpenAI } from '@langchain/openai';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    // Vercel AI SDK's useChat hook sends the messages array directly
    const messages = body.messages ?? [];
    const model = body.model; // Get model from the request
    const provider = body.provider || 'ollama'; // Default to ollama if not specified
    
    console.log('[API Route /api/chat] Received request:', { provider, model, messagesCount: messages.length });

    if (!messages.length) {
      return new Response('Missing messages in request body', { status: 400 });
    }

    if (!model) {
      return new Response('Missing model in request body', { status: 400 });
    }

    // Transform the Vercel AI SDK message format to LangChain's format
    const processedMessages = messages.map((message: any) => {
      if (message.role === 'user') {
        return new HumanMessage(message.content);
      } else {
        // Assumes other roles like 'assistant' or 'system'
        return new AIMessage(message.content);
      }
    });

    const parser = new BytesOutputParser();
    let langchainStream;

    // Choisir le fournisseur d'IA en fonction du paramètre provider
    if (provider === 'ollama') {
      const ollama = new ChatOllama({
        baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
        model: model,
        temperature: body.temperature || 0.7, // Use provided temp or a default
      });
      
      // Create a stream from the LangChain model piped through the parser
      langchainStream = await ollama
        .pipe(parser)
        .stream(processedMessages)
        .catch((e: any) => {
          console.error('[API Route /api/chat] Error during Ollama stream:', e);
          throw e;
        });
    } else if (provider === 'openai') {
      const openai = new ChatOpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        model: model,
        temperature: body.temperature || 0.7,
        streaming: true,
      });
      
      // Create a stream from the LangChain model piped through the parser
      langchainStream = await openai
        .pipe(parser)
        .stream(processedMessages)
        .catch((e: any) => {
          console.error('[API Route /api/chat] Error during OpenAI stream:', e);
          throw e;
        });
    } else {
      return new Response(`Unsupported provider: ${provider}`, { status: 400 });
    }

    // Version simplifiée pour ai v4.3.16 - format de streaming plus simple
    const encoder = new TextEncoder();
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          let text = "";
          for await (const chunk of langchainStream) {
            // S'assurer que le chunk est bien une chaîne de caractères et non un tableau de bytes
            let textChunk;
            if (typeof chunk === 'string') {
              textChunk = chunk;
            } else if (chunk instanceof Uint8Array) {
              textChunk = new TextDecoder().decode(chunk);
            } else {
              textChunk = String(chunk);
            }
            
            text += textChunk;
            // Encoder en UTF-8 pour le streaming
            controller.enqueue(encoder.encode(textChunk));
          }
          controller.close();
        } catch (error) {
          console.error('[API Route /api/chat] Error in stream processing:', error);
          controller.error(error);
        }
      },
    });
    
    // Retourner la réponse en streaming avec le bon Content-Type
    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        'X-Content-Type-Options': 'nosniff',
      },
    });

  } catch (error: any) {
    console.error('[API Route /api/chat] Error:', error);
    return new Response(`Error processing request: ${error.message}`,
      {
        status: 500,
        headers: {
          'Content-Type': 'text/plain',
        },
      }
    );
  }
}
