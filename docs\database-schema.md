# Schéma de la Base de Données Mouvik

## Table: `albums`

Cette table contient les informations sur les albums.

| Colonne | Type | Description |
| --- | --- | --- |
| id | uuid | Identifiant unique de l'album (clé primaire) |
| user_id | uuid | Identifiant de l'utilisateur créateur (clé étrangère vers `profiles.id`) |
| band_id | uuid | Identifiant du groupe (clé étrangère vers `bands.id`) |
| title | text | Titre de l'album |
| description | text | Description de l'album |
| cover_url | text | URL de l'image de couverture |
| header_banner_url | text | URL de l'image de bannière |
| release_date | date | Date de sortie de l'album |
| genre | text[] | Tableau des genres musicaux de l'album |
| moods | text[] | Tableau des ambiances/moods de l'album |
| instrumentation | text[] | Tableau des instruments utilisés |
| album_type | text | Type d'album (ex: 'album', 'EP', 'single') |
| is_public | boolean | Si l'album est visible publiquement |
| status | varchar | Statut de l'album (ex: 'draft', 'published', 'archived') |
| slug | text | Slug unique pour l'URL de l'album |
| view_count | integer | Nombre de vues |
| dislike_count | integer | Nombre de dislikes |
| are_comments_public | boolean | Si les commentaires sont publics |
| is_gallery_public | boolean | Si la galerie d'images est publique |
| gallery_image_urls | jsonb | URLs des images de la galerie |
| created_at | timestamptz | Date de création |
| updated_at | timestamptz | Date de dernière mise à jour |


Ce document décrit la structure des tables principales de la base de données Mouvik.

## Table: `profiles`

Stocke les informations des profils utilisateurs.

| Colonne              | Type                               | Nullable | Défaut            | Description                                          |
|----------------------|------------------------------------|----------|-------------------|------------------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)                 | `UUID`                             | NON      |                   | Identifiant unique (PK, référence `auth.users.id`)   |
| `name`               | `TEXT`                             | NON      |                   | Nom d'affichage (utilisé pour le trigger)            |
| `username`           | `TEXT`                             | OUI      |                   | Nom d'utilisateur public (unique)                    |
| `full_name`          | `TEXT`                             | OUI      |                   | Nom complet de l'utilisateur                         |
| `display_name`       | `TEXT`                             | OUI      |                   | Nom affiché publiquement si différent de username    |
| `email`              | `TEXT`                             | OUI      |                   | Email de l'utilisateur (peut dupliquer `auth.users.email`) |
| `avatar_url`         | `TEXT`                             | OUI      |                   | URL de l'avatar                                      |
| `bio`                | `TEXT`                             | OUI      |                   | Biographie de l'utilisateur                          |
| `website`            | `TEXT`                             | OUI      |                   | Site web de l'utilisateur                            |
| `location`           | `TEXT`                             | OUI      |                   | Localisation de l'utilisateur                        |
| `genres`             | `TEXT[]`                           | OUI      |                   | Genres musicaux associés au profil                   |
| `tags`               | `TEXT[]`                           | OUI      |                   | Compétences, centres d'intérêt, tags libres        |
| `instruments_played` | `JSONB`                            | OUI      |                   | Instruments joués (format JSONB)                     |
| `follower_count`     | `INTEGER`                          | OUI      | `0`               | Nombre de followers                                  |
| `is_artist`          | `BOOLEAN`                          | OUI      | `false`           | Indique si l'utilisateur est un artiste              |
| `subscription_tier`  | `TEXT`                             | OUI      | `'free'`          | Niveau d'abonnement de l'utilisateur (à confirmer)   |
| `created_at`         | `TIMESTAMPTZ`                      | OUI      | `now()`           | Date de création du profil                           |
| `updated_at`         | `TIMESTAMPTZ`                      | OUI      | `now()`           | Date de dernière mise à jour                         |

## Table: `songs`

Contient les informations sur les morceaux de musique.

| Colonne            | Type          | Nullable | Défaut            | Description                                          |
|--------------------|---------------|----------|-------------------|------------------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)               | `UUID`        | NON      | `gen_random_uuid()`| Identifiant unique de la chanson (PK)                |
| `creator_user_id`  | `UUID`        | NON      |                   | ID du créateur (FK vers `profiles.id`)               |
| `album_id`         | `UUID`        | OUI      |                   | ID de l'album associé (FK vers `albums.id`)          |
| `title`            | `TEXT`        | NON      |                   | Titre de la chanson                                  |
| `genre`            | `TEXT[]`      | OUI      |                   | Genres principaux (ex: {'Rock', 'Pop'})              |
| `sub_genres`       | `TEXT[]`      | OUI      |                   | Sous-genres (ex: {'Progressive Rock', 'Art Rock'})   |
| `moods`            | `TEXT[]`      | OUI      |                   | Humeurs associées (ex: {'Énergique', 'Mélancolique'})|
| `tags`             | `TEXT[]`      | OUI      |                   | Tags libres                                          |
| `plays`            | `INTEGER`     | OUI      | `0`               | Nombre total d'écoutes                               |
| `duration_ms`      | `INTEGER`     | OUI      |                   | Durée en millisecondes                               |
| `bpm`              | `INTEGER`     | OUI      |                   | Battements par minute                                |
| `musical_key`      | `TEXT`        | OUI      |                   | Tonalité musicale (ex: "C#m", "Fmaj")                |
| `release_date`     | `DATE`        | OUI      |                   | Date de sortie                                       |
| `cover_art_url`    | `TEXT`        | OUI      |                   | URL de la pochette                                   |
| `audio_file_url`   | `TEXT`        | OUI      |                   | URL du fichier audio (stockage Supabase)             |
| `lyrics`           | `TEXT`        | OUI      |                   | Paroles de la chanson                                |
| `is_public`        | `BOOLEAN`     | OUI      | `true`            | Visibilité publique                                  |
| `created_at`       | `TIMESTAMPTZ` | OUI      | `now()`           | Date de création                                     |
| `updated_at`       | `TIMESTAMPTZ` | OUI      | `now()`           | Date de dernière mise à jour                         |

## Table: `albums`

Regroupe les chansons en albums.

| Colonne         | Type          | Nullable | Défaut            | Description                                  |
|-----------------|---------------|----------|-------------------|----------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)            | `UUID`        | NON      | `gen_random_uuid()`| Identifiant unique de l'album (PK)           |
| `user_id`       | `UUID`        | NON      |                   | ID du créateur (FK vers `profiles.id`)       |
| `title`         | `TEXT`        | NON      |                   | Titre de l'album                             |
| `description`   | `TEXT`        | OUI      |                   | Description de l'album                       |
| `release_date`  | `DATE`        | OUI      |                   | Date de sortie de l'album                    |
| `is_public`     | `BOOLEAN`     | OUI      | `true`            | Visibilité publique                          |
| `created_at`    | `TIMESTAMPTZ` | OUI      | `now()`           | Date de création                             |
| `updated_at`    | `TIMESTAMPTZ` | OUI      | `now()`           | Date de dernière mise à jour                 |

## Table: `audio_analysis`

Stocke les résultats de l'analyse audio des chansons.

| Colonne            | Type          | Nullable | Défaut  | Description                                                                 |
|--------------------|---------------|----------|---------|-----------------------------------------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)               | `UUID`        | NON      | `gen_random_uuid()` | Identifiant unique de l'analyse (PK)                                        |
| `song_id`          | `UUID`        | NON      |         | ID de la chanson (FK vers `songs.id`)                                       |
| `tempo`            | `REAL`        | OUI      |         | Tempo estimé en BPM                                                         |
| `key`              | `TEXT`        | OUI      |         | Tonalité estimée (ex: "C", "Db")                                            |
| `mode`             | `TEXT`        | OUI      |         | Mode ("Major", "Minor")                                                     |
| `time_signature`   | `TEXT`        | OUI      |         | Signature rythmique (ex: "4/4")                                             |
| `loudness`         | `REAL`        | OUI      |         | Niveau sonore global moyen en dB                                            |
| `energy`           | `REAL`        | OUI      |         | Mesure perceptive de l'intensité (0.0-1.0)                                  |
| `danceability`     | `REAL`        | OUI      |         | Adaptabilité à la danse (0.0-1.0)                                           |
| `acousticness`     | `REAL`        | OUI      |         | Mesure de l'acousticité (0.0-1.0)                                           |
| `instrumentalness` | `REAL`        | OUI      |         | Prédiction de l'absence de voix (0.0-1.0)                                   |
| `liveness`         | `REAL`        | OUI      |         | Présence d'un public (0.0-1.0)                                              |
| `speechiness`      | `REAL`        | OUI      |         | Présence de mots parlés (0.0-1.0)                                           |
| `valence`          | `REAL`        | OUI      |         | Positivité musicale (0.0-1.0)                                               |
| `duration_ms`      | `INTEGER`     | OUI      |         | Durée de la piste en millisecondes                                          |
| `segments`         | `JSONB`       | OUI      |         | Segments audio (timbre, hauteur)                                            |
| `sections`         | `JSONB`       | OUI      |         | Sections structurelles (intro, couplet)                                     |
| `beats`            | `JSONB`       | OUI      |         | Positions des temps                                                         |
| `tatums`           | `JSONB`       | OUI      |         | Plus petite unité de temps perceptuelle                                     |
| `bars`             | `JSONB`       | OUI      |         | Positions des mesures                                                       |
| `created_at`       | `TIMESTAMPTZ` | OUI      | `now()` | Date de création de l'analyse                                               |

## Table: `activities`

Journalise les activités des utilisateurs sur la plateforme.

| Colonne       | Type          | Nullable | Défaut            | Description                                               |
|---------------|---------------|----------|-------------------|-----------------------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)          | `UUID`        | NON      | `gen_random_uuid()`| Identifiant unique de l'activité (PK)                     |
| `user_id`     | `UUID`        | NON      |                   | ID de l'utilisateur (FK vers `profiles.id`)               |
| `type`        | `TEXT`        | NON      |                   | Type d'activité (ex: 'song_created', 'song_played')       |
| `target_id`   | `UUID`        | OUI      |                   | ID de l'entité cible (ex: song_id, album_id)              |
| `target_type` | `TEXT`        | OUI      |                   | Type de l'entité cible (ex: 'song', 'album')              |
| `content`     | `JSONB`       | OUI      |                   | Détails additionnels sur l'activité                       |
| `created_at`  | `TIMESTAMPTZ` | OUI      | `now()`           | Date de création de l'activité                            |

## Table: `song_versions`

Historique des versions des chansons.

| Colonne          | Type          | Nullable | Défaut            | Description                                  |
|------------------|---------------|----------|-------------------|----------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)             | `UUID`        | NON      | `gen_random_uuid()`| Identifiant unique de la version (PK)        |
| `song_id`        | `UUID`        | NON      |                   | ID de la chanson (FK vers `songs.id`)        |
| `version_number` | `INTEGER`     | NON      |                   | Numéro de version (auto-incrémenté par RPC)  |
| `version_name`   | `TEXT`        | OUI      |                   | Nom de la version (donné par l'utilisateur)  |
| `song_data`      | `JSONB`       | NON      |                   | Snapshot des données de la chanson           |
| `notes`          | `TEXT`        | OUI      |                   | Notes sur cette version                      |
| `created_at`     | `TIMESTAMPTZ` | OUI      | `now()`           | Date de création de la version               |

## Table: `playlists`

Permet aux utilisateurs de créer des listes de lecture.

| Colonne         | Type          | Nullable | Défaut            | Description                                  |
|-----------------|---------------|----------|-------------------|----------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)            | `UUID`        | NON      | `gen_random_uuid()`| Identifiant unique de la playlist (PK)       |
| `user_id`       | `UUID`        | NON      |                   | ID du créateur (FK vers `profiles.id`)       |
| `name`          | `TEXT`        | NON      |                   | Nom de la playlist                           |
| `description`   | `TEXT`        | OUI      |                   | Description de la playlist                   |
| `is_public`     | `BOOLEAN`     | OUI      | `true`            | Visibilité publique                          |
| `created_at`    | `TIMESTAMPTZ` | OUI      | `now()`           | Date de création                             |
| `updated_at`    | `TIMESTAMPTZ` | OUI      | `now()`           | Date de dernière mise à jour                 |

## Table: `playlist_songs`

Table de jonction pour lier les chansons aux playlists.

| Colonne       | Type          | Nullable | Défaut  | Description                                  |
|---------------|---------------|----------|---------|----------------------------------------------|
| [id](cci:2://file:///c:/_DEV_projects/TOOL/mouvik-5v0%20-%20Copie%20%282%29/components/ia/ai-config-menu.tsx:38:0-38:67)          | `BIGSERIAL`   | NON      |         | Identifiant unique de la liaison (PK)        |
| `playlist_id` | `UUID`        | NON      |         | ID de la playlist (FK vers `playlists.id`)   |
| `song_id`     | `UUID`        | NON      |         | ID de la chanson (FK vers `songs.id`)        |

## Table: `app_settings`

Configuration globale de l'application.

| Colonne      | Type          | Nullable | Défaut  | Description                          |
|--------------|---------------|----------|---------|--------------------------------------|
| `key`        | `TEXT`        | NON      |         | Clé de configuration (PK)            |
| `value`      | `TEXT`        | OUI      |         | Valeur de configuration              |
| `created_at` | `TIMESTAMPTZ` | OUI      | `now()` | Date de création                     |
| `updated_at` | `TIMESTAMPTZ` | OUI      | `now()` | Date de dernière mise à jour         |
