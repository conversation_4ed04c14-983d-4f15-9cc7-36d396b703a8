"use client";

import React from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { type ProfileFormValues } from './profile-form-schema';

const socialPlatformOptions = [
  { value: "spotify", label: "Spotify" },
  { value: "youtube", label: "YouTube" },
  { value: "suno", label: "Suno" },
  { value: "udio", label: "Udio" },
  { value: "soundcloud", label: "SoundCloud" },
  { value: "bandcamp", label: "Bandcamp" },
  { value: "instagram", label: "Instagram" },
  { value: "tiktok", label: "TikTok" },
  { value: "facebook", label: "Facebook" },
  { value: "twitter", label: "Twitter / X" },
  { value: "github", label: "GitHub" },
  { value: "website", label: "Autre (site web)" },
];

export function ProfileSocialTab() {
  const { control } = useFormContext<ProfileFormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "social_links",
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Réseaux Sociaux & Liens</CardTitle>
        <CardDescription>Connectez vos autres plateformes pour que les visiteurs puissent découvrir tout votre univers.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          {fields.map((item, index) => (
            <div key={item.id} className="flex items-end gap-2 p-2 rounded-md hover:bg-slate-50/5">
              <FormField
                control={control}
                name={`social_links.${index}.platform`}
                render={({ field }) => (
                  <FormItem className="w-1/3">
                    {index === 0 && <FormLabel>Plateforme</FormLabel>}
                    <Select onValueChange={field.onChange} defaultValue={field.value ?? ''}>
                      <FormControl>
                        <SelectTrigger><SelectValue placeholder="Sélectionnez..." /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {socialPlatformOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name={`social_links.${index}.url`}
                render={({ field }) => (
                  <FormItem className="flex-grow">
                    {index === 0 && <FormLabel>URL</FormLabel>}
                    <FormControl>
                      <Input placeholder="https://..." {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="button" variant="ghost" size="icon" onClick={() => remove(index)} className="shrink-0">
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2"
          onClick={() => append({ platform: '', url: '' })}
        >
          Ajouter un lien
        </Button>
      </CardContent>
    </Card>
  );
}
