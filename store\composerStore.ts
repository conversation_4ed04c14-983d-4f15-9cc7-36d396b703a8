import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { Song } from '@/components/songs/song-schema';

// ------------ Types ------------ //
export type PanelState = 'collapsed' | 'normal' | 'expanded';
export type ViewMode = 'compose' | 'arrange' | 'analyze' | 'structure';
export type LayoutMode = 'focus' | 'split' | 'full' | 'studio';

export interface LayoutState {
  viewMode: ViewMode;
  layoutMode: LayoutMode;
  leftPanel: PanelState;
  rightPanel: PanelState;
  bottomPanel: PanelState;
  showAI: boolean;
  showInsights: boolean;
  showStructure: boolean;
  showChords: boolean;
}

const DEFAULT_LAYOUT_STATE: LayoutState = {
  viewMode: 'compose',
  layoutMode: 'split',
  leftPanel: 'normal',
  rightPanel: 'normal',
  bottomPanel: 'collapsed',
  showAI: true,
  showInsights: true,
  showStructure: true,
  showChords: true,
};

// ------------ Store ------------ //
interface ComposerStore {
  currentSong: Song | null;
  hasUnsavedChanges: boolean;
  layoutState: LayoutState;
  // Actions
  setCurrentSong: (song: Song | null) => void;
  updateSongFields: (partial: Partial<Song>) => void;
  setUnsavedChanges: (dirty: boolean) => void;
  setLayoutState: (partial: Partial<LayoutState>) => void;
  resetSong: () => void;
}

export const useComposerStore = create<ComposerStore>()(
  persist(
    (set) => ({
      currentSong: null,
      hasUnsavedChanges: false,
      layoutState: DEFAULT_LAYOUT_STATE,
      setCurrentSong: (song) => set({ currentSong: song }),
      updateSongFields: (partial) =>
        set((state) => {
          const newSong = state.currentSong ? { ...state.currentSong, ...partial } : null;
          const hasChanges = JSON.stringify(newSong) !== JSON.stringify(state.currentSong);
          return {
            currentSong: newSong,
            hasUnsavedChanges: hasChanges,
          };
        }),
      setUnsavedChanges: (dirty) => set({ hasUnsavedChanges: dirty }),
      setLayoutState: (partial) =>
        set((state) => ({ layoutState: { ...state.layoutState, ...partial } })),
      resetSong: () => set({ currentSong: null, hasUnsavedChanges: false }),
    }),
    {
      name: 'composer-store',
      partialize: (state) => ({ layoutState: state.layoutState }), // persist only UI prefs
    }
  )
);
