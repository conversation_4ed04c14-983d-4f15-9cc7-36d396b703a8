"use client";

import { useState, useEffect, ReactNode } from 'react';
import { createBrowserClient } from '../../lib/supabase/client';

// NOUVELLES Définitions de types alignées sur database-schema.md

interface ProfileInfo {
  id: string; // UUID from profiles.id;
  follower_count?: number | null;
  username?: string | null;
  full_name?: string | null;
  avatar_url?: string | null;
  bio?: string | null;
  website?: string | null;
  genres?: string[] | null; // Was favorite_genres
  favorite_artists?: string[] | null;
  instruments_played?: string[] | null;
  tags?: string[] | null; // Was skills
  learning_goals?: string[] | null;
  subscription_tier?: string | null;
  created_at?: string | null; // TIMESTAMPTZ
  updated_at?: string | null; // TIMESTAMPTZ
}

interface AudioAnalysisInfo {
  id: string; // UUID from audio_analysis.id
  song_id: string; // UUID
  tempo?: number | null; // REAL
  key?: string | null;
  mode?: string | null;
  time_signature?: string | null;
  loudness?: number | null; // REAL
  energy?: number | null; // REAL
  danceability?: number | null; // REAL
  acousticness?: number | null; // REAL
  instrumentalness?: number | null; // REAL
  liveness?: number | null; // REAL
  speechiness?: number | null; // REAL
  valence?: number | null; // REAL
  duration_ms?: number | null; // INTEGER
  created_at?: string | null; // TIMESTAMPTZ
  // Note: segments, sections, beats, etc. (JSONB) sont omis pour la simplicité initiale
}

interface SongInfo {
  id: string; // UUID from songs.id;
  genre?: string[] | null; // Champ TEXT[] de la table 'songs' (updated from TEXT)
  creator_user_id: string; // UUID
  album_id?: string | null; // UUID
  title: string;

  sub_genres?: string[] | null; // Champ TEXT[] de la table 'songs'
  moods?: string[] | null; // Champ TEXT[] de la table 'songs'
  tags?: string[] | null; // Champ TEXT[] de la table 'songs'
  plays?: number | null;
  duration_seconds?: number | null;
  bpm?: number | null; // De songs.bpm
  key?: string | null; // De songs.key
  release_date?: string | null; // DATE
  cover_art_url?: string | null;
  audio_file_url?: string | null;
  lyrics?: string | null;
  is_public?: boolean | null;
  created_at?: string | null; // TIMESTAMPTZ
  updated_at?: string | null; // TIMESTAMPTZ
  
  audio_analysis?: AudioAnalysisInfo | null; // Données enrichies optionnelles

  // Anciens champs de SongStats nécessitant une logique d'agrégation/source de données spécifique (omis pour l'instant) :
  // uniqueListeners?: number | null;
  // averageRating?: number | null;
  // completionRate?: number | null;
  // skipRate?: number | null;
  // addedToPlaylists?: number | null;
  // shares?: number | null;
  // comments?: number | null;
  // lastListenedAt?: string | null;
}

interface PlaylistInfo {
  id: string; // UUID
  user_id: string; // UUID
  name: string;
  description?: string | null;
  cover_art_url?: string | null;
  is_public?: boolean | null;
  created_at?: string | null; // TIMESTAMPTZ
  updated_at?: string | null; // TIMESTAMPTZ
  songs?: SongInfo[]; // Optionnel: si on charge les chansons de la playlist
  trackCount?: number; // Dérivé
}

interface OverallStats {
  totalPlaysAllSongs?: number | null; 
  totalSongsCreated?: number | null;
  // D'autres statistiques nécessiteront des agrégations (ex: depuis la table 'activities')
}

interface EngagementMetrics {
  lastActive?: string | null; // Peut être dérivé de profiles.updated_at ou activities
  totalSongs?: number | null; // Nombre de chansons créées par l'utilisateur
  totalAlbums?: number | null; // Nombre d'albums créés
  // D'autres métriques nécessiteront des agrégations
}

interface UserData {
  profile: ProfileInfo | null;
  songs: SongInfo[];
  playlists: PlaylistInfo[];
  overallStats: OverallStats | null;
  userEngagement: EngagementMetrics | null;
}

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangle, Download, Save, Trash2, Upload, Brain, RefreshCw, CheckCircle, Loader2, FileText, Settings2, Palette, Database, SlidersHorizontal, Info, ChevronDown, Music, BarChart4, TrendingUp, Sparkles, Zap } from 'lucide-react';
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { AiConfigMenu } from '@/components/ia/ai-config-menu';
import { useChat } from 'ai/react';
const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik"; // Key used by AiConfigMenu
const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";

export function AdvancedSettingsTab() {
  // const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik"; // Moved to module level
  // LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY is defined at module level

  const [autoSaveInterval, setAutoSaveInterval] = useState(5);
  const [audioQuality, setAudioQuality] = useState("high");
  const [isExportingData, setIsExportingData] = useState(false);
  const [isImportingData, setIsImportingData] = useState(false);
  
  // États pour la configuration IA
  const [aiProvider, setAiProvider] = useState("openai");
  const [apiKey, setApiKey] = useState("");
  const [ollamaEndpoint, setOllamaEndpoint] = useState("http://localhost:11434");
  const [selectedModel, setSelectedModel] = useState("llama3");
  const [isSavingAiConfig, setIsSavingAiConfig] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  
  // États pour le test de connexion IA et l'analyse de profil
  const [aiConnectionStatus, setAiConnectionStatus] = useState<'idle' | 'testing' | 'loading' | 'connected' | 'error'>('idle');
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isGeneratingProfile, setIsGeneratingProfile] = useState(false);
  const [profileAnalysis, setProfileAnalysis] = useState<React.ReactNode | null>(null);
  const [profileAnalysisError, setProfileAnalysisError] = useState<string | null>(null);
  const [connectionProgress, setConnectionProgress] = useState(0);
  // États pour gérer le streaming de texte brut
  const [streamedText, setStreamedText] = useState<string>("");
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState<boolean>(false);
  const [chatError, setChatError] = useState<Error | null>(null);
  // Mot-clés et questions d'orientation pour l'analyse
  const [analysisKeywords, setAnalysisKeywords] = useState<string>("");
  const [analysisPrompt, setAnalysisPrompt] = useState<string>("");
  // Interval pour la progression
  const [progressIntervalId, setProgressIntervalId] = useState<NodeJS.Timeout | null>(null);
  // Paramètres pour l'analyse IA
  const [aiTemperature, setAiTemperature] = useState<number>(0.7);
  const [aiTokenLimit, setAiTokenLimit] = useState<string>("4000");
  const [analysisDepth, setAnalysisDepth] = useState<'basic' | 'standard' | 'deep'>('standard');
  const [wordLimit, setWordLimit] = useState<number>(500);
  
  // Chargement de l'analyse sauvegardée au démarrage
  useEffect(() => {
    const savedAnalysis = localStorage.getItem('mouvik_profile_analysis');
    if (savedAnalysis) {
      try {
        const parsedData = JSON.parse(savedAnalysis);
        
        // Restaurer les mots-clés et le mini-prompt s'ils existent
        if (parsedData.keywords) setAnalysisKeywords(parsedData.keywords);
        if (parsedData.prompt) setAnalysisPrompt(parsedData.prompt);
        
        // Recréer le JSX à partir des données sauvegardées
        setProfileAnalysis(
          <div className="prose prose-sm dark:prose-invert max-w-none p-4 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 rounded-md border shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-primary flex items-center m-0">
                <Sparkles size={20} className="mr-2 text-yellow-500" />
                Analyse de Profil Musical
              </h4>
              <Badge variant="outline" className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-800">
                {parsedData.provider}
              </Badge>
            </div>
            {(parsedData.keywords || parsedData.prompt) && (
              <div className="mb-3 text-xs bg-slate-100 dark:bg-slate-800 p-2 rounded-md">
                {parsedData.keywords && (
                  <p className="mb-1"><span className="font-medium">Mots-clés :</span> {parsedData.keywords}</p>
                )}
                {parsedData.prompt && (
                  <p className="mb-0"><span className="font-medium">Question/prompt :</span> {parsedData.prompt}</p>
                )}
              </div>
            )}
            <div dangerouslySetInnerHTML={{ __html: parsedData.content }} />
          </div>
        );
        console.log('[AdvancedSettingsTab] Loaded saved profile analysis');
      } catch (error) {
        console.error('[AdvancedSettingsTab] Error loading saved profile analysis:', error);
      }
    }
  }, []);
  
  // Fonction pour envoyer une requête et gérer le streaming de texte brut
  const fetchStreamingText = async (messages: any[]) => {
    setIsLoadingAnalysis(true);
    setChatError(null);
    setStreamedText("");
    setIsGeneratingProfile(true);
    setConnectionProgress(0);
    setProfileAnalysisError(null);
    
    try {
      // Récupérer le fournisseur et le modèle sélectionnés
      const currentSelectedProvider = localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY) || 'openai';
      let selectedModel = '';
      
      if (currentSelectedProvider === 'ollama') {
        selectedModel = localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY) || 'llama3';
      } else {
        // Pour OpenAI, utiliser un modèle par défaut
        selectedModel = 'gpt-3.5-turbo';
      }
      
      console.log(`[fetchStreamingText] Using provider: ${currentSelectedProvider}, model: ${selectedModel}`);
      
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          messages,
          model: selectedModel,
          provider: currentSelectedProvider
        }),
      });
      
      console.log('[fetchStreamingText] Response status:', response.status);
      
      if (!response.ok) {
        console.error('[fetchStreamingText] Error response', response);
        const errorText = await response.text();
        console.error('[fetchStreamingText] Error body:', errorText);
        toast.error("Erreur de réponse du serveur IA", { 
          description: `Status: ${response.status}. ${errorText ? `Détail: ${errorText.substring(0,100)}...` : ''}` 
        });
        throw new Error(`Erreur serveur: ${response.status}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) throw new Error("Impossible de lire la réponse");
      
      let accumulatedText = "";
      const decoder = new TextDecoder();
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunkText = decoder.decode(value, { stream: true });
        accumulatedText += chunkText;
        setStreamedText(accumulatedText);
      }
      
      setConnectionProgress(100);
      console.log('[fetchStreamingText] Stream complete');
      
      const containsAsciiCodes = /^[\d,\s]+$/.test(accumulatedText.trim());
      
      let formattedText = accumulatedText;
      
      if (containsAsciiCodes) {
        console.log('[fetchStreamingText] Detected ASCII codes, attempting to convert');
        try {
          const asciiCodes = accumulatedText.split(',').map(code => parseInt(code.trim()));
          formattedText = String.fromCharCode(...asciiCodes);
        } catch (error) {
          console.error('[fetchStreamingText] Failed to convert ASCII codes:', error);
        }
      }
      
      formattedText = formattedText
        .replace(/\n/g, '<br/>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
      
      // Sauvegarder l'analyse dans le localStorage pour la persistance
      const currentProvider = localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY) || 'IA';
      const analysisData = {
        content: formattedText,
        provider: currentProvider === 'openai' ? 'OpenAI' : 'Ollama',
        timestamp: new Date().toISOString(),
        keywords: analysisKeywords || '',
        prompt: analysisPrompt || ''
      };
      
      // Sauvegarder dans le localStorage
      localStorage.setItem('mouvik_profile_analysis', JSON.stringify(analysisData));
      console.log('[fetchStreamingText] Analysis saved to localStorage');
      
      // Mettre à jour le texte formaté avec un conteneur JSX
      setProfileAnalysis(
        <div className="prose prose-sm dark:prose-invert max-w-none p-4 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 rounded-md border shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-primary flex items-center m-0">
              <Sparkles size={20} className="mr-2 text-yellow-500" />
              Analyse de Profil Musical
            </h4>
            <Badge variant="outline" className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-800">
              {currentProvider === 'openai' ? 'OpenAI' : 'Ollama'}
            </Badge>
          </div>
          {(analysisKeywords || analysisPrompt) && (
            <div className="mb-3 text-xs bg-slate-100 dark:bg-slate-800 p-2 rounded-md">
              {analysisKeywords && (
                <p className="mb-1"><span className="font-medium">Mots-clés :</span> {analysisKeywords}</p>
              )}
              {analysisPrompt && (
                <p className="mb-0"><span className="font-medium">Question/prompt :</span> {analysisPrompt}</p>
              )}
            </div>
          )}
          <div dangerouslySetInnerHTML={{ __html: formattedText }} />
        </div>
      );
      setIsGeneratingProfile(false);
      setIsLoadingAnalysis(false);
    } catch (error: any) {
      console.error('[fetchStreamingText] Error:', error);
      setChatError(error.message);
      setProfileAnalysisError(error.message);
      toast.error("Erreur lors de la génération", { description: error.message });
    } finally {
      setIsLoadingAnalysis(false);
      setIsGeneratingProfile(false);
      setConnectionProgress(100);
    }
  };

  const handleExportData = async () => {
    setIsExportingData(true);
    try {
      // Simulation d'un export de données
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Dans une implémentation réelle, cela déclencherait un téléchargement
      toast.success("Données exportées avec succès", {
        description: "Un fichier JSON contenant vos données a été téléchargé."
      });
    } catch (error) {
      toast.error("Erreur lors de l'exportation", {
        description: "Une erreur est survenue. Veuillez réessayer."
      });
    } finally {
      setIsExportingData(false);
    }
  };

  // Fonction pour tester la connexion IA
  const testAiConnection = async () => {
    setIsTestingConnection(true);
    setAiConnectionStatus('testing');
    setConnectionError(null);
    setProgressIntervalId(null);

    const duration = 3000; // Corresponds to the simulated API call duration
    const steps = 60; // Update ~every 50ms for a smooth progress
    const stepDuration = duration / steps;
    let currentStep = 0;

    const newIntervalId = setInterval(() => {
      currentStep++;
      const newProgress = Math.min((currentStep / steps) * 100, 100);
      setConnectionProgress(newProgress);

      if (currentStep >= steps) {
        clearInterval(newIntervalId);
        setProgressIntervalId(null);
      }
    }, stepDuration);
    setProgressIntervalId(newIntervalId);

    const selectedProvider = localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY);
  console.log('[testAiConnection] Selected provider from localStorage:', selectedProvider);

  setTimeout(async () => {
    try {
      console.log('[testAiConnection] Entering try block, provider:', selectedProvider);
      if (selectedProvider === 'ollama') {
        const apiEndpointForTest = ollamaEndpoint || "http://localhost:11434";
        const OLLAMA_VERSION_URL = `${apiEndpointForTest.replace(/\/api$/, '')}/api/version`;
        console.log(`[testAiConnection] Attempting to fetch ${OLLAMA_VERSION_URL} for Ollama`);
        const response = await fetch(OLLAMA_VERSION_URL, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        });
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Erreur API Ollama (${response.status}): ${errorText || 'Réponse vide'}`);
        }
        const versionData = await response.json();
        if (versionData && versionData.version) {
          setAiConnectionStatus('connected');
          toast.success("Connexion IA (Ollama) réussie!", {
            description: `Connecté à Ollama version ${versionData.version}.`
          });
        } else {
          throw new Error("Réponse de version Ollama invalide.");
        }
      } else if (selectedProvider === 'openai') {
        const currentApiKey = localStorage.getItem('openai_api_key_mouvik') || apiKey;
        if (!currentApiKey) {
          throw new Error("Clé API OpenAI non configurée. Veuillez l'ajouter dans les paramètres IA.");
        }
        console.log('[testAiConnection] Attempting to fetch OpenAI models list');
        const response = await fetch('https://api.openai.com/v1/models', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${currentApiKey}`,
          },
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Réponse non-JSON de l\'API OpenAI.'}));
          throw new Error(`Erreur API OpenAI (${response.status}): ${errorData.error?.message || errorData.message || 'Erreur inconnue'}`);
        }
        // We don't strictly need the model list data, just a successful response
        await response.json(); 
        setAiConnectionStatus('connected');
        toast.success("Connexion IA (OpenAI) réussie!", {
          description: "La clé API OpenAI est valide et la connexion a été établie."
        });
      } else {
         setAiConnectionStatus('error');
         toast.error("Fournisseur IA non supporté ou non sélectionné", {
            description: "Veuillez sélectionner Ollama ou OpenAI et configurer les paramètres si nécessaire."
         });
      }
    } catch (error: any) {
      console.error('[testAiConnection] Error caught:', error);
      setAiConnectionStatus('error');
      const providerName = selectedProvider === 'ollama' ? 'Ollama' : selectedProvider === 'openai' ? 'OpenAI' : 'IA';
      toast.error(`Échec de la connexion IA (${providerName})`, {
        description: error.message || `Impossible de joindre le service ${providerName}. Vérifiez la configuration et l'état du service.`
      });
    } finally {
      console.log('[testAiConnection] Entering finally block');
      if (progressIntervalId === newIntervalId) { 
        clearInterval(newIntervalId);
        setProgressIntervalId(null);
      }
      setConnectionProgress(100); // Ensure progress bar completes visually
    }
  }, duration);
  };
  
  // Fonction pour générer une analyse de profil
  const generateProfileAnalysis = async () => {
    // Clear previous analysis and error before starting a new one
    setProfileAnalysis(null);
    setProfileAnalysisError(null);
    console.log('[AdvancedSettingsTab] generateProfileAnalysis CALLED.');
    const currentSelectedProvider = localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY);
    console.log(`[AdvancedSettingsTab] generateProfileAnalysis: AI provider from localStorage: ${currentSelectedProvider}`);
    const ollamaModelFromStorage = localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY);
    console.log(`[AdvancedSettingsTab] generateProfileAnalysis: Ollama model from localStorage: ${ollamaModelFromStorage}`);
    
    // Nous n'avons plus besoin de gérer manuellement l'intervalle de progression
    // car fetchStreamingText le fait pour nous
    
    try {
      if (currentSelectedProvider === 'ollama') {
        if (aiConnectionStatus !== 'connected') {
          toast.error("Connexion Ollama non établie", {
            description: "Veuillez d'abord tester la connexion à Ollama dans les paramètres IA."
          });
          throw new Error("Ollama not connected");
        }
        if (!ollamaModelFromStorage) {
          toast.error("Aucun modèle Ollama sélectionné", {
            description: "Veuillez sélectionner un modèle Ollama pour générer l'analyse."
          });
          throw new Error("Ollama model not selected");
        }
      } else if (currentSelectedProvider === 'openai') {
        const currentApiKey = localStorage.getItem('openai_api_key_mouvik') || apiKey;
        if (aiConnectionStatus !== 'connected') {
          toast.error("Connexion OpenAI non établie", {
            description: "Veuillez d'abord tester la connexion à OpenAI dans les paramètres IA."
          });
          throw new Error("OpenAI not connected");
        }
        if (!currentApiKey) {
          toast.error("Clé API OpenAI manquante", {
            description: "Veuillez configurer votre clé API OpenAI dans les paramètres IA."
          });
          throw new Error("OpenAI API key missing");
        }
      } else {
        toast.info("Analyse de profil non disponible", {
          description: `La génération de profil n'est pas supportée pour le fournisseur: ${currentSelectedProvider || 'Non défini'}.`
        });
        throw new Error("Unsupported provider");
      }

      const supabase = createBrowserClient();
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Utilisateur non authentifié", { description: "Veuillez vous connecter pour générer une analyse." });
        throw new Error("User not authenticated");
      }

      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      const { data: songsData, error: songsError } = await supabase
        .from('songs')
        .select('*')
        .eq('creator_user_id', user.id);

      const userData: UserData = {
        profile: profileData as ProfileInfo || null,
        songs: songsData as SongInfo[] || [],
        playlists: [], // Placeholder
        overallStats: null, // Placeholder
        userEngagement: null, // Placeholder
      };

      if (profileError || songsError) {
        console.error('Error fetching user data:', profileError, songsError);
        toast.error("Erreur de récupération des données utilisateur");
        throw new Error("Failed to fetch user data");
      }
      
      if (!userData.profile && userData.songs.length === 0) {
        toast.info("Données utilisateur insuffisantes", { description: "Impossible de générer une analyse sans données de profil ou de chansons." });
        throw new Error("Insufficient user data");
      }

      let prompt = `En tant qu'expert en musique et coach artistique IA pour la plateforme Mouvik, analyse le profil musical suivant. Fournis une analyse constructive et inspirante en français. Limite ta réponse à environ ${wordLimit} mots. \n`;
      prompt += `Profondeur d'analyse demandée: ${analysisDepth}.\n\n`;
        
      if (userData.profile) {
        prompt += `Profil Utilisateur:\n`;
        prompt += `  Nom d'artiste: ${userData.profile.username || 'Non spécifié'}\n`;
        if (userData.profile.bio) prompt += `  Bio: ${userData.profile.bio}\n`;
        if (userData.profile.genres && userData.profile.genres.length > 0) prompt += `  Genres musicaux: ${userData.profile.genres.join(', ')}\n`;
        if (userData.profile.tags && userData.profile.tags.length > 0) prompt += `  Tags/Compétences: ${userData.profile.tags.join(', ')}\n`;
        if (userData.profile.instruments_played) {
            try {
                const instruments = userData.profile.instruments_played && Array.isArray(userData.profile.instruments_played) && userData.profile.instruments_played.length > 0 ? userData.profile.instruments_played.map((instrument: any) => instrument.name || 'Unknown Instrument').join(', ') : 'N/A';
                prompt += `  Instruments joués: ${instruments}\n\n`;
            } catch (e) { console.warn("Could not parse instruments_played", e); }
        }
        prompt += `\n`;
      }
      
      if (userData.songs && userData.songs.length > 0) {
        prompt += `Chansons créées par l'utilisateur:\n`;
        prompt += `${userData.songs.map(song => `- Titre: ${song.title}\n  Genre: ${song.genre?.join(', ') || 'N/A'}\n  Sous-genres: ${song.sub_genres?.join(', ') || 'N/A'}\n  Ambiance: ${song.moods?.join(', ') || 'N/A'}\n  Tags: ${song.tags?.join(', ') || 'N/A'}\n  Clé musicale: ${song.key || 'N/A'}\n  Durée: ${song.duration_seconds ? (song.duration_seconds / 60).toFixed(2) + ' min' : 'N/A'}\n  Popularité: ${song.plays || 0} écoutes`).join('\n\n') || 'Aucune chanson détaillée.'}\n`;
      }
              
      prompt += `Objectifs pour l'analyse:\n`;
      prompt += `1. Identifier les points forts et le style unique de l'artiste.\n`;
      prompt += `2. Suggérer des pistes d'amélioration ou d'exploration musicale.\n`;
      prompt += `3. Fournir des conseils pour accroître l'engagement et la visibilité.\n`;
      
      // Ajouter les mots-clés s'ils sont fournis
      if (analysisKeywords && analysisKeywords.trim() !== '') {
        prompt += `\nMots-clés/thèmes à mettre en avant dans l'analyse: ${analysisKeywords}\n`;
      }
      
      // Ajouter le mini-prompt ou la question s'ils sont fournis
      if (analysisPrompt && analysisPrompt.trim() !== '') {
        prompt += `\nQuestion/instruction spécifique: ${analysisPrompt}\n`;
        prompt += `Assure-toi de répondre directement à cette question/instruction dans ton analyse.\n`;
      }
      
      prompt += `Sois positif, encourageant et offre des perspectives concrètes. Termine par une note d'encouragement.\n`;
      prompt += `\nRéponse attendue (en français, format texte brut) :`;

      console.log('[generateProfileAnalysis] Constructed prompt:', prompt);

      // Utiliser fetchStreamingText pour tous les fournisseurs d'IA
      // Notre backend /api/chat gère déjà les différents fournisseurs
      await fetchStreamingText([
        { role: "user", content: prompt }
      ]);
      
      // Le formatage et l'affichage sont gérés par fetchStreamingText
      toast.success(`Analyse de profil IA (${localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY) || 'IA'}) générée avec succès!`);
      
    } catch (error: any) {
      console.error('[generateProfileAnalysis] Error during profile analysis generation:', error);
      setProfileAnalysisError(error.message || "Une erreur inconnue est survenue.");
      toast.error("Échec de la génération de l'analyse", { description: error.message });
      setProfileAnalysis(null);
    }
  };

  // Check API Key and Provider status on mount and when they change
  useEffect(() => {
    const importEffect = async () => {
      setIsImportingData(true);
      try {
        // Simulation d'un import de données
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Dans une implémentation réelle, cela déclencherait un téléchargement
        toast.success("Données importées avec succès", {
          description: "Vos données ont été importées et appliquées à votre compte."
        });
      } catch (error) {
        toast.error("Erreur lors de l'importation", {
          description: "Une erreur est survenue. Veuillez réessayer."
        });
      } finally {
        setIsImportingData(false);
      }
    };
    // Commenté pour éviter l'exécution automatique
    // importEffect();
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Paramètres avancés</h2>
        <p className="text-muted-foreground">
          Configurez les options avancées de l'application et gérez vos données.
        </p>
      </div>

      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="storage">Stockage</TabsTrigger>
          <TabsTrigger value="data">Données</TabsTrigger>
          <TabsTrigger value="ai">IA</TabsTrigger>
        </TabsList>
        
        <TabsContent value="performance" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Options de performance</CardTitle>
              <CardDescription>
                Optimisez les performances de l'application selon votre matériel.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="audio-buffer">Taille du buffer audio</Label>
                    <p className="text-sm text-muted-foreground">
                      Une valeur plus élevée réduit les coupures mais augmente la latence.
                    </p>
                  </div>
                  <Select defaultValue="512">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Sélectionner" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="128">128 (Latence minimale)</SelectItem>
                      <SelectItem value="256">256 (Faible latence)</SelectItem>
                      <SelectItem value="512">512 (Équilibré)</SelectItem>
                      <SelectItem value="1024">1024 (Stable)</SelectItem>
                      <SelectItem value="2048">2048 (Très stable)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="multicore">Utilisation multi-cœurs</Label>
                    <Switch id="multicore" defaultChecked />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Utilise tous les cœurs du processeur pour améliorer les performances.
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="hardware-accel">Accélération matérielle</Label>
                    <Switch id="hardware-accel" defaultChecked />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Utilise le GPU pour accélérer le rendu des formes d'onde et des visualisations.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="storage" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Gestion du stockage</CardTitle>
              <CardDescription>
                Configurez comment l'application stocke et gère vos fichiers.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Intervalle de sauvegarde automatique (minutes)</Label>
                <div className="flex items-center gap-4">
                  <Slider
                    value={[autoSaveInterval]}
                    onValueChange={(value) => setAutoSaveInterval(value[0])}
                    max={30}
                    min={1}
                    step={1}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{autoSaveInterval}</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Fréquence à laquelle vos projets sont automatiquement sauvegardés.
                </p>
              </div>

              <div className="space-y-2">
                <Label>Qualité audio par défaut</Label>
                <Select 
                  value={audioQuality} 
                  onValueChange={setAudioQuality}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner une qualité" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Basse (128 kbps)</SelectItem>
                    <SelectItem value="medium">Moyenne (256 kbps)</SelectItem>
                    <SelectItem value="high">Haute (320 kbps)</SelectItem>
                    <SelectItem value="lossless">Sans perte (FLAC)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Qualité utilisée pour les exports et le streaming.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="offline-mode">Mode hors ligne</Label>
                  <Switch id="offline-mode" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Stocke les projets récents localement pour un accès hors ligne.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cache-dir">Répertoire du cache</Label>
                <div className="flex gap-2">
                  <Input id="cache-dir" value="/tmp/mouvik-cache" readOnly className="flex-1" />
                  <Button variant="outline">Parcourir</Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Emplacement où sont stockés les fichiers temporaires.
                </p>
              </div>

              <Button variant="outline" className="w-full">
                Vider le cache (1.2 GB)
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="data" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Gestion des données</CardTitle>
              <CardDescription>
                Exportez, importez ou supprimez vos données personnelles.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <Button 
                  variant="outline" 
                  className="h-auto py-4 flex flex-col items-center justify-center gap-2"
                  onClick={handleExportData}
                  disabled={isExportingData}
                >
                  <Download className="h-6 w-6" />
                  <div>
                    <div className="font-medium">Exporter mes données</div>
                    <div className="text-xs text-muted-foreground">
                      Téléchargez toutes vos données au format JSON
                    </div>
                  </div>
                </Button>

                <Button 
                  variant="outline" 
                  className="h-auto py-4 flex flex-col items-center justify-center gap-2"
                  onClick={handleExportData}
                  disabled={isImportingData}
                >
                  <Upload className="h-6 w-6" />
                  <div>
                    <div className="font-medium">Importer des données</div>
                    <div className="text-xs text-muted-foreground">
                      Restaurez à partir d'une sauvegarde précédente
                    </div>
                  </div>
                </Button>
              </div>

              <div className="border-t pt-4">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="w-full">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Supprimer mon compte et mes données
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Êtes-vous absolument sûr ?</AlertDialogTitle>
                      <AlertDialogDescription>
                        Cette action est irréversible. Elle supprimera définitivement votre compte
                        et toutes les données associées de nos serveurs.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Annuler</AlertDialogCancel>
                      <AlertDialogAction className="bg-destructive text-destructive-foreground">
                        Supprimer définitivement
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="ai" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Configuration de l'IA
              </CardTitle>
              <CardDescription>
                Configurez les modèles d'IA utilisés pour la composition musicale, l'analyse et les suggestions créatives.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Intégration du composant AiConfigMenu existant */}
              <div className="border border-slate-200 dark:border-slate-800 rounded-xl p-6 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 shadow-sm">
                <div className="flex items-center gap-2 mb-3">
                  <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-2 rounded-lg">
                    <Brain className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold">Fournisseur d'IA et modèles</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-5 ml-1">
                  Sélectionnez le fournisseur d'IA et configurez les modèles pour toutes les fonctionnalités IA de Mouvik.
                </p>
                <div className="p-1">
                  <AiConfigMenu inlineMode={true} />
                </div>
              </div>
              
              {/* Nouveau bloc de test d'IA avec analyse de profil */}
              <div className="mt-8 border border-slate-200 dark:border-slate-800 rounded-xl p-6 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 shadow-sm">
                <div className="flex items-center gap-2 mb-3">
                  <div className="bg-gradient-to-br from-blue-500 to-violet-600 p-2 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white"><path d="M9 3H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h5"></path><path d="M18 3h-5"></path><path d="M16 8h2"></path><path d="M19 12h-3"></path><path d="M16 16h2"></path><path d="M16 21h2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-2"></path></svg>
                  </div>
                  <h3 className="text-lg font-semibold">Test de connexion IA et analyse de profil</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-5 ml-1">
                  Vérifiez que votre configuration IA fonctionne correctement et obtenez une analyse personnalisée de votre profil musical.
                </p>
                
                <div className="bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`w-2 h-2 rounded-full ${aiConnectionStatus === 'connected' ? 'bg-green-500' : aiConnectionStatus === 'error' ? 'bg-red-500' : aiConnectionStatus === 'testing' ? 'bg-blue-500 animate-pulse' : 'bg-yellow-500'}`}></div>
                    <span className="font-medium">
                      {aiConnectionStatus === 'connected' ? 'IA connectée' : 
                       aiConnectionStatus === 'error' ? 'Erreur de connexion' : 
                       aiConnectionStatus === 'testing' ? 'Test en cours...' : 
                       'Non testé'}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {aiConnectionStatus === 'connected' ? 'Votre configuration IA est correctement configurée et prête à être utilisée.' : 
                     aiConnectionStatus === 'error' ? 'Impossible de se connecter au modèle IA. Vérifiez votre configuration et réessayez.' : 
                     aiConnectionStatus === 'testing' ? 'Test de connexion en cours...' : 
                     'Cliquez sur "Tester la connexion" pour vérifier votre configuration.'}
                  </p>
                </div>
                
                {/* Container for AI Generation Parameters */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6 p-4 border border-slate-200 dark:border-slate-700 rounded-lg bg-slate-100/50 dark:bg-slate-800/30">
                  {/* Niveau de créativité de l'IA (Temperature) */}
                  <div>
                    <Label htmlFor="aiTemperature" className="text-sm font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Niveau de créativité de l'IA</Label>
                    <div className="flex items-center">
                      <Slider
                        id="aiTemperature"
                        min={0}
                        max={1}
                        step={0.1}
                        value={[aiTemperature]}
                        onValueChange={(value) => setAiTemperature(value[0])}
                        className="w-full mr-4"
                      />
                      <span className="text-sm text-slate-600 dark:text-slate-400 min-w-[35px] text-right font-medium tabular-nums">{aiTemperature.toFixed(1)}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">Contrôle l'inventivité des suggestions (température).</p>
                  </div>

                  {/* Limite de tokens par requête (Token Limit) */}
                  <div>
                    <Label htmlFor="aiTokenLimit" className="text-sm font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Limite de tokens par requête</Label>
                    <Select value={aiTokenLimit} onValueChange={setAiTokenLimit}>
                      <SelectTrigger id="aiTokenLimit" className="w-full bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Sélectionner une limite" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2000">2000 tokens (Économique)</SelectItem>
                        <SelectItem value="4000">4000 tokens (Standard)</SelectItem>
                        <SelectItem value="8000">8000 tokens (Étendu)</SelectItem>
                        <SelectItem value="16000">16000 tokens (Maximum)</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground mt-1">Taille maximale des requêtes/réponses IA.</p>
                  </div>

                  {/* Profondeur de l'analyse de profil */}
                  <div>
                    <Label htmlFor="analysisDepth" className="text-sm font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Profondeur de l'analyse de profil</Label>
                    <Select value={analysisDepth} onValueChange={(value: 'basic' | 'standard' | 'deep') => setAnalysisDepth(value)}>
                      <SelectTrigger id="analysisDepth" className="w-full bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Choisir la profondeur" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basic">Basique (Rapide, Vue d'ensemble)</SelectItem>
                        <SelectItem value="standard">Standard (Détaillée, Équilibrée)</SelectItem>
                        <SelectItem value="deep">Approfondie (Très détaillée, Complète)</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground mt-1">Niveau de détail pour l'analyse de profil.</p>
                  </div>

                  {/* Limite de mots pour l'analyse (env.) */}
                  <div>
                    <Label htmlFor="wordLimit" className="text-sm font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Limite de mots pour l'analyse (env.)</Label>
                    <div className="flex items-center">
                      <Slider
                        id="wordLimit"
                        min={150}
                        max={2000}
                        step={50}
                        value={[wordLimit]}
                        onValueChange={(value) => setWordLimit(value[0])}
                        className="w-full mr-4"
                      />
                      <span className="text-sm text-slate-600 dark:text-slate-400 min-w-[45px] text-right font-medium tabular-nums">{wordLimit}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">Longueur approximative de l'analyse générée.</p>
                  </div>
                </div>

                {/* Champs pour les mots-clés et mini-prompts d'orientation de l'analyse */}
                <div className="grid grid-cols-1 gap-4 mb-4">
                  <div>
                    <Label htmlFor="analysisKeywords" className="text-sm font-medium text-slate-700 dark:text-slate-300 block mb-1.5">
                      Mots-clés <span className="text-xs text-slate-500">(optionnel)</span>
                    </Label>
                    <Input
                      id="analysisKeywords"
                      placeholder="jazz, improvisation, composition, etc."
                      value={analysisKeywords}
                      onChange={(e) => setAnalysisKeywords(e.target.value)}
                      className="w-full bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
                    />
                    <p className="text-xs text-muted-foreground mt-1">Séparez les mots-clés par des virgules pour orienter l'analyse.</p>
                  </div>
                  
                  <div>
                    <Label htmlFor="analysisPrompt" className="text-sm font-medium text-slate-700 dark:text-slate-300 block mb-1.5">
                      Question ou mini-prompt <span className="text-xs text-slate-500">(optionnel)</span>
                    </Label>
                    <Input
                      id="analysisPrompt"
                      placeholder="Comment orienter mon style vers du grunge ? Conseils pour améliorer mes compositions ?"
                      value={analysisPrompt}
                      onChange={(e) => setAnalysisPrompt(e.target.value)}
                      className="w-full bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
                    />
                    <p className="text-xs text-muted-foreground mt-1">Posez une question spécifique ou ajoutez une instruction pour personnaliser l'analyse.</p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-3 items-center mt-2 mb-6">
                  <Button 
                    variant="outline"
                    size="sm"
                    className="bg-white dark:bg-slate-900 border-slate-300 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/70"
                    onClick={testAiConnection}
                    disabled={aiConnectionStatus === 'testing'}
                  >
                    {aiConnectionStatus === 'testing' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {aiConnectionStatus !== 'testing' && (aiConnectionStatus === 'connected' ? <CheckCircle className="mr-2 h-4 w-4 text-green-500" /> : <RefreshCw className="mr-2 h-4 w-4" />)}
                    {aiConnectionStatus === 'testing' ? 'Test en cours...' : aiConnectionStatus === 'connected' ? 'IA Connectée' : 'Tester Connexion IA'}
                  </Button>
                  
                  <Button 
                    size="sm"
                    className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white transition-all duration-300 ease-in-out transform hover:scale-105 shadow-md disabled:opacity-60 disabled:transform-none disabled:shadow-none"
                    onClick={() => { console.log('[AdvancedSettingsTab] Generate Profile Analysis BUTTON CLICKED.'); generateProfileAnalysis(); }} 
                    disabled={isGeneratingProfile || aiConnectionStatus !== 'connected'}
                  >
                    {isGeneratingProfile ? (
                      <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Génération...</>
                    ) : (
                      <><Sparkles className="mr-2 h-4 w-4" /> Analyser mon profil musical</>
                    )}
                  </Button>
                  {aiConnectionStatus === 'error' && <p className="text-xs text-red-500 flex items-center"><AlertTriangle size={14} className="mr-1"/> La connexion IA a échoué. Vérifiez la configuration.</p>}
                </div>

                {/* Zone d'affichage de l'analyse */} 
                <div className="mt-4 p-1 border border-slate-200 dark:border-slate-700/80 rounded-xl bg-slate-50 dark:bg-slate-900/60 min-h-[250px] shadow-inner overflow-y-auto max-h-[calc(100vh-450px)] scrollbar-thin scrollbar-thumb-slate-300 dark:scrollbar-thumb-slate-700 scrollbar-track-transparent">
                  {isGeneratingProfile ? (
                    <div className="flex flex-col items-center justify-center h-full text-slate-500 dark:text-slate-400 p-6 text-center">
                      <div className="relative h-32 w-32 mb-4">
                        {/* Animation de chargement visible */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Loader2 className="h-16 w-16 animate-spin text-purple-500" />
                        </div>
                        <svg className="h-full w-full animate-pulse opacity-70" viewBox="0 0 100 100">
                          <circle
                            className="text-slate-200 dark:text-slate-700 stroke-current"
                            strokeWidth="6"
                            cx="50"
                            cy="50"
                            r="40"
                            fill="transparent"
                          />
                          <circle
                            className="text-purple-500 stroke-current"
                            strokeWidth="6"
                            strokeLinecap="round"
                            cx="50"
                            cy="50"
                            r="40"
                            fill="transparent"
                            strokeDasharray={`${2 * Math.PI * 40}`}
                            strokeDashoffset={`${2 * Math.PI * 40 * (1 - connectionProgress / 100)}`}
                            transform="rotate(-90 50 50)"
                          />
                        </svg>
                      </div>
                      <p className="text-xl font-semibold mb-2">L'IA compose votre symphonie d'insights...</p>
                      <div className="bg-amber-50 dark:bg-amber-950/40 border border-amber-200 dark:border-amber-800 rounded-md p-3 mb-4 max-w-md">
                        <p className="text-sm text-amber-800 dark:text-amber-300 flex items-center">
                          <AlertTriangle size={16} className="mr-2 flex-shrink-0" />
                          <span>
                            <strong>Patience requise :</strong> L'analyse peut prendre plusieurs minutes selon la configuration de votre machine (CPU/GPU) et le modèle sélectionné ({localStorage.getItem('ollama_selected_model_mouvik') || 'phi3:latest'}).
                          </span>
                        </p>
                      </div>
                      <div className="w-3/4 max-w-sm">
                        <div className="flex justify-between text-xs mb-1">
                          <span>Analyse en cours...</span>
                          <span className="font-medium">{connectionProgress.toFixed(0)}%</span>
                        </div>
                        <Progress value={connectionProgress} className="w-full h-3 transition-all duration-100 ease-linear" />
                      </div>
                    </div>
                  ) : profileAnalysis ? (
                    <div className="p-3 sm:p-4 prose prose-slate dark:prose-invert max-w-none">
                      {profileAnalysis}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center text-slate-500 dark:text-slate-400 p-6">
                      <div className="relative mb-6">
                        <Sparkles size={64} className="mx-auto text-purple-400 opacity-20" />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Zap size={32} className="text-amber-500 opacity-60" />
                        </div>
                      </div>
                      <p className="text-2xl font-semibold mb-3">Prêt à illuminer votre parcours musical ?</p>
                      {aiConnectionStatus === 'connected' ? (
                        <p className="text-sm max-w-md">Sélectionnez la profondeur et la limite de mots souhaitées, puis cliquez sur "Analyser mon profil musical" pour obtenir des insights personnalisés et des suggestions créatives.</p>
                      ) : (
                        <p className="text-sm max-w-md">Veuillez d'abord <span className="font-semibold text-purple-500">tester et établir la connexion IA</span> pour débloquer cette fonctionnalité d'analyse.</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="mt-8">
                <div className="flex items-center gap-2 mb-3">
                  <div className="bg-gradient-to-br from-emerald-500 to-teal-600 p-2 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                  </div>
                  <h3 className="text-lg font-semibold">Options d'IA par fonctionnalité</h3>
                </div>
                <p className="text-base text-muted-foreground mb-6 ml-1">
                  Personnalisez comment l'IA est utilisée dans les différentes parties de l'application.
                </p>
                <div className="grid gap-5 md:grid-cols-2">
                  {/* Bloc IA Composition */}
                  <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 shadow-sm hover:shadow-md transition-all rounded-xl p-5 border border-slate-200 dark:border-slate-800 relative overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="flex items-center justify-between mb-1">
                      <Label htmlFor="ai-composer" className="text-base font-bold">Utiliser l'IA pour la composition</Label>
                      <Switch id="ai-composer" defaultChecked className="scale-125 data-[state=checked]:bg-primary/80" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Active les suggestions d'accords, de mélodies et de structures pour la composition musicale.
                    </p>
                  </div>
                  {/* Bloc IA Paroles */}
                  <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 shadow-sm hover:shadow-md transition-all rounded-xl p-5 border border-slate-200 dark:border-slate-800 relative overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="flex items-center justify-between mb-1">
                      <Label htmlFor="ai-lyrics" className="text-base font-bold">Utiliser l'IA pour les paroles</Label>
                      <Switch id="ai-lyrics" defaultChecked className="scale-125 data-[state=checked]:bg-primary/80" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Active les suggestions et l'aide à l'écriture pour les paroles.
                    </p>
                  </div>
                  {/* Bloc IA Genre/Style */}
                  <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 shadow-sm hover:shadow-md transition-all rounded-xl p-5 border border-slate-200 dark:border-slate-800 relative overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="flex items-center justify-between mb-1">
                      <Label htmlFor="ai-genre" className="text-base font-bold">Analyse de genre et style</Label>
                      <Switch id="ai-genre" defaultChecked className="scale-125 data-[state=checked]:bg-primary/80" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Utilise l'IA pour analyser et suggérer des genres et styles musicaux.
                    </p>
                  </div>
                  {/* Bloc IA Collaboration */}
                  <div className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 shadow-sm hover:shadow-md transition-all rounded-xl p-5 border border-slate-200 dark:border-slate-800 relative overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="flex items-center justify-between mb-1">
                      <Label htmlFor="ai-collab" className="text-base font-bold">Suggestions de collaboration</Label>
                      <Switch id="ai-collab" defaultChecked className="scale-125 data-[state=checked]:bg-primary/80" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Permet à l'IA de suggérer des collaborateurs potentiels en fonction de votre style.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 justify-end mt-8">
                <Button 
                  variant="outline"
                  className="border-slate-300 hover:bg-slate-100 dark:border-slate-700 dark:hover:bg-slate-800 transition-all duration-200"
                  onClick={() => {
                    toast.info("Configuration réinitialisée", {
                      description: "Les paramètres d'IA ont été réinitialisés aux valeurs par défaut."
                    });
                  }}
                >
                  Réinitialiser
                </Button>
                <Button 
                  className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-sm hover:shadow transition-all duration-200"
                  onClick={() => {
                    setIsSavingAiConfig(true);
                    setTimeout(() => {
                      setIsSavingAiConfig(false);
                      toast.success("Configuration IA enregistrée", {
                        description: "Vos paramètres d'IA ont été mis à jour avec succès."
                      });
                    }, 1000);
                  }}
                  disabled={isSavingAiConfig}
                >
                  {isSavingAiConfig ? "Enregistrement..." : "Enregistrer la configuration"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
