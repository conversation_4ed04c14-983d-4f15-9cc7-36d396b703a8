'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { BrainCircuit, Sparkles, Library, Zap, ChevronUp, ChevronDown, Wand2 } from 'lucide-react';
import { cn } from '@/lib/utils';

const FeatureCard = ({ icon: Icon, title, description }: { icon: React.ElementType, title: string, description: string }) => (
  <div className="flex flex-col items-center text-center p-4">
    <div className="mb-3 text-primary">
      <Icon className="h-8 w-8" />
    </div>
    <h3 className="font-semibold mb-1 text-card-foreground">{title}</h3>
    <p className="text-sm text-muted-foreground">{description}</p>
  </div>
);

export function WelcomeBanner() {
  const [isExpanded, setIsExpanded] = useState(true);

  useEffect(() => {
    const storedState = localStorage.getItem('aiComposerBannerExpanded');
    if (storedState !== null) {
      setIsExpanded(JSON.parse(storedState));
    }
  }, []);

  const toggleExpansion = () => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    localStorage.setItem('aiComposerBannerExpanded', JSON.stringify(newState));
  };

  return (
    <Card className="mb-8 overflow-hidden bg-card/50 border-border/50 backdrop-blur-lg">
      <div className={cn('transition-all duration-500 ease-in-out', isExpanded ? 'max-h-[1000px]' : 'max-h-0')}>
        <CardContent className="p-6 sm:p-8">
          <div className="flex flex-col lg:flex-row items-center gap-8">
            <div className="relative flex-shrink-0">
              <Image
                src="/images/mouvy-avatar.png" // Assumed path, can be corrected later
                alt="MOUVY Avatar"
                width={160}
                height={160}
                className="rounded-full border-4 border-primary/20 shadow-lg"
              />
              <Wand2 className="absolute top-2 right-2 h-8 w-8 text-white/80 drop-shadow-lg" />
            </div>
            <div className="flex-grow text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl font-bold text-card-foreground mb-2">Bienvenue dans l'Espace Compositeur IA</h1>
              <p className="text-lg text-muted-foreground mb-6">
                Prêt à composer votre prochain chef-d'œuvre avec MOUVY ? Créez une nouvelle chanson ou sélectionnez-en une existante pour commencer.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <FeatureCard icon={BrainCircuit} title="IA Créative" description="Générez des paroles, mélodies et accords avec l'IA avancée." />
                <FeatureCard icon={Sparkles} title="Prompts Personnalisés" description="Créez vos propres instructions pour l'IA." />
                <FeatureCard icon={Library} title="Bibliothèque d'Accords" description="Accédez à une vaste collection d'accords et progressions." />
                <FeatureCard icon={Zap} title="Actions Rapides" description="Améliorez vos compositions en un clic." />
              </div>
            </div>
          </div>
        </CardContent>
      </div>
      <div className="border-t border-border/50 bg-card/20 px-6 py-1 flex justify-center">
        <Button onClick={toggleExpansion} variant="ghost" size="sm" className="w-full text-muted-foreground">
          {isExpanded ? <ChevronUp className="h-4 w-4 mr-2" /> : <ChevronDown className="h-4 w-4 mr-2" />}
          {isExpanded ? 'Réduire le panneau' : 'Afficher le panneau de bienvenue'}
        </Button>
      </div>
    </Card>
  );
}
