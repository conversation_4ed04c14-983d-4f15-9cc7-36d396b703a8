import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  console.log('[Middleware] Incoming cookies:', JSON.stringify(Array.from(request.cookies.getAll())));
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          // If the cookie is updated, update the cookies on the request and response
          request.cookies.set({ name, value, ...options });
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          // If the cookie is removed, update the cookies on the request and response
          request.cookies.set({ name, value: '', ...options });
          response.cookies.delete({ name, ...options });
        },
      },
    }
  )

  // Refresh session if expired - required for Server Components
  // https://supabase.com/docs/guides/auth/auth-helpers/nextjs#managing-session-with-middleware
  // console.log('[Middleware] Response cookies BEFORE auth.getUser():', JSON.stringify(Array.from(response.cookies.getAll()))); // Optional: for deep debugging
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error('[Middleware] supabase.auth.getUser() error:', error.message);
    } else if (user) {
      console.log('[Middleware] supabase.auth.getUser() successful, user ID:', user.id);
    } else {
      console.warn('[Middleware] supabase.auth.getUser() returned no user and no error.');
    }
  } catch (error) {
    console.error('[Middleware] Error in supabase.auth.getUser():', error);
    // Decide if you want to rethrow, or handle gracefully
    // For now, we'll log and let the request proceed, but this might hide auth issues later
  }

  console.log('[Middleware] Outgoing response cookies AFTER auth.getUser():', JSON.stringify(Array.from(response.cookies.getAll())));
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}