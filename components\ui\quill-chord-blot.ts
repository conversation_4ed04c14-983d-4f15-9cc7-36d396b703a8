// components/ui/quill-chord-blot.ts
import Quill from 'quill';

const Inline = Quill.import('blots/inline');

class ChordBlot extends Inline {
  static blotName = 'chord';
  static tagName = 'span';
  static className = 'chord-blot';

  static create(value: string) {
    const node = super.create();
    // Sanitize value to prevent XSS
    const sanitizedValue = String(value).replace(/[^a-zA-Z0-9#\/]/g, '');
    node.setAttribute('data-chord-name', sanitizedValue);
    node.textContent = `[${sanitizedValue}]`;
    return node;
  }

  static formats(domNode: HTMLElement) {
    return domNode.getAttribute('data-chord-name');
  }

  format(name: string, value: any) {
    if (name === 'chord' && value) {
      this.domNode.setAttribute('data-chord-name', value);
      this.domNode.textContent = `[${value}]`;
    } else {
      super.format(name, value);
    }
  }
}

export default ChordBlot;
