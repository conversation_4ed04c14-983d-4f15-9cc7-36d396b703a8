-- Fonction pour obtenir des données de comparaison d'un artiste avec des artistes similaires
-- NOTE: Cette fonction est une ébauche et nécessite une logique métier plus complexe
-- pour définir les "artistes similaires" et calculer les percentiles de manière robuste.
CREATE OR REPLACE FUNCTION get_artist_comparison_data(p_user_id UUID)
RETURNS JSON -- Le composant attend un objet JSON complexe
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_main_genre TEXT;
  v_avg_plays_similar_genre NUMERIC;
  v_avg_likes_similar_genre NUMERIC;
  v_avg_followers_similar_genre NUMERIC;
  v_user_total_plays BIGINT;
  v_user_total_likes BIGINT;
  v_user_total_followers BIGINT;
  v_result JSON;
BEGIN
  -- 1. Obtenir le genre principal de l'utilisateur (simplification, prend le premier)
  SELECT genres[1] INTO v_user_main_genre FROM profiles WHERE id = p_user_id;

  -- 2. Calculer les moyennes pour les artistes du même genre principal (simplification)
  -- Ceci est une simplification grossière. Une vraie comparaison nécessiterait
  -- de définir des cohortes d'artistes, de calculer des distributions, etc.
  SELECT
    COALESCE(AVG(s.plays), 0),
    COALESCE(AVG(s.like_count), 0) -- Supposant que songs a like_count
  INTO v_avg_plays_similar_genre, v_avg_likes_similar_genre
  FROM songs s
  JOIN profiles pr ON s.creator_user_id = pr.id
  WHERE pr.id != p_user_id AND v_user_main_genre = ANY(pr.genres) AND s.status = 'published';
  
  -- Pour les followers, c'est plus complexe, car c'est par profil.
  -- SELECT COALESCE(AVG(p.followers_count), 0) -- Supposant que profiles a followers_count
  -- INTO v_avg_followers_similar_genre
  -- FROM profiles p
  -- WHERE p.id != p_user_id AND v_user_main_genre = ANY(p.genres);
  v_avg_followers_similar_genre := 15; -- Valeur factice pour l'instant

  -- 3. Obtenir les statistiques de l'utilisateur actuel
  SELECT
    COALESCE(SUM(s.plays), 0),
    COALESCE(SUM(s.like_count), 0) -- Supposant que songs a like_count
  INTO v_user_total_plays, v_user_total_likes
  FROM songs s
  WHERE s.creator_user_id = p_user_id AND s.status = 'published';

  -- SELECT COALESCE(p.followers_count, 0) -- Supposant que profiles a followers_count
  -- INTO v_user_total_followers
  -- FROM profiles p
  -- WHERE p.id = p_user_id;
  v_user_total_followers := 25; -- Valeur factice

  -- 4. Construire le JSON de résultat (avec des valeurs de percentile et de rang factices)
  -- Un vrai calcul de percentile nécessiterait de comparer à une population.
  v_result := json_build_object(
    'rank', 42, -- Factice
    'growth_percentile', 78, -- Factice
    'engagement_percentile', 65, -- Factice
    'metrics', json_build_array(
      json_build_object('metric', 'Écoutes totales', 'value', v_user_total_plays, 'average', ROUND(v_avg_plays_similar_genre), 'percentile', 82), -- Factice
      json_build_object('metric', 'Likes totaux', 'value', v_user_total_likes, 'average', ROUND(v_avg_likes_similar_genre), 'percentile', 75), -- Factice
      json_build_object('metric', 'Followers', 'value', v_user_total_followers, 'average', ROUND(v_avg_followers_similar_genre), 'percentile', 80) -- Factice
      -- Ajouter d'autres métriques si nécessaire
    )
  );

  RETURN v_result;
END;
$$;

COMMENT ON FUNCTION get_artist_comparison_data(UUID) IS 'Fournit des données de comparaison pour un artiste par rapport à des artistes similaires (ÉBAUCHE - nécessite une logique métier complexe).';

-- Exemple d'appel :
-- SELECT * FROM get_artist_comparison_data('VOTRE_USER_ID_ICI');
