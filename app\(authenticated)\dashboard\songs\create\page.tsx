"use client"

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getSupabaseClient } from "@/lib/supabase/client";
import { SongForm } from "@/components/songs/SongForm"; // Import the main SongForm
import type { SongFormValues } from "@/components/songs/song-schema"; // Corrected import
import type { Album } from "@/types/album"; // Corrected import
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Save, Loader2 as LoadingSpinner } from "lucide-react";

export default function CreateSongPage() {
  const router = useRouter();
  const { toast } = useToast();
  const supabase = getSupabaseClient(); // Get Supabase client instance

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [albums, setAlbums] = useState<Album[]>([]);
  const [isLoadingAlbums, setIsLoadingAlbums] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [currentUserName, setCurrentUserName] = useState<string>("");

  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoadingAlbums(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setCurrentUserId(user.id);
        // Fetch user's albums for the album selector in SongForm
        const { data: albumData, error: albumError } = await supabase
          .from("albums")
          .select("id, title")
          .eq("user_id", user.id)
          .order("title", { ascending: true });

        if (albumError) {
          toast({ title: "Erreur", description: "Impossible de charger vos albums.", variant: "destructive" });
        } else {
          setAlbums(albumData || []);
        }
        
        // Fetch user's profile to get artist_name
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('display_name, username')
          .eq('id', user.id)
          .single();
        
        if (profileError) {
            console.warn("Could not fetch profile for default artist name:", profileError.message);
        } else if (profileData) {
            setCurrentUserName(profileData.display_name || profileData.username || "");
        }

      } else {
        // Handle case where user is not authenticated, though route protection should prevent this
        toast({ title: "Erreur d'authentification", description: "Utilisateur non trouvé.", variant: "destructive" });
        router.push("/login"); // Or appropriate auth page
      }
      setIsLoadingAlbums(false);
    };
    fetchInitialData();
  }, [supabase, toast, router]);

  const handleFormSubmit = async (data: SongFormValues, coverArtFile?: File | null, audioFile?: File | null) => {
    if (!currentUserId) {
      toast({ title: "Erreur", description: "Utilisateur non identifié.", variant: "destructive" });
      return;
    }
    setIsSubmitting(true);

    // The 'data' parameter (SongFormValues) should already contain the 'status'. 
    // If SongForm differentiates actions like 'Save Draft' vs 'Publish', it should set data.status accordingly.
    // Default status is set in initialSongFormValues.
    const dataToSubmit = data; // data already includes status from SongFormValues

    // Explicitly remove 'content' if it somehow exists, to prevent PGRST204
    if ('content' in dataToSubmit) {
      delete (dataToSubmit as any).content;
    }

    const songPayload = {
      creator_user_id: currentUserId,
      title: dataToSubmit.title,
      artist: dataToSubmit.artist, // From songSchema
      featured_artists: dataToSubmit.featured_artists || [], // Already string[] in songSchema
      genre: dataToSubmit.genre || null, // Already string in songSchema
      mood: dataToSubmit.moods && dataToSubmit.moods.length > 0 ? dataToSubmit.moods[0] : null,
      instrumentation: dataToSubmit.instruments && dataToSubmit.instruments.length > 0 ? dataToSubmit.instruments[0] : null, // instruments in songSchema
      key: dataToSubmit.key || dataToSubmit.musical_key || null,
      bpm: dataToSubmit.bpm,
      time_signature: dataToSubmit.time_signature || null,
      duration_ms: dataToSubmit.duration_ms, // duration_ms in songSchema
      // capo: dataToSubmit.capo, // Not in songSchema, check if needed for DB
      tuning_frequency: dataToSubmit.tuning_frequency,
      description: dataToSubmit.description || null,
      audio_url: dataToSubmit.audio_url || null,
      cover_art_url: dataToSubmit.cover_art_url, // cover_art_url in songSchema
      album_id: dataToSubmit.album_id === "__NO_ALBUM__" ? null : dataToSubmit.album_id,
      composer_name: dataToSubmit.composer_name || null,
      writers: dataToSubmit.writers || [], // Already string[] in songSchema
      producers: dataToSubmit.producers || [], // Already string[] in songSchema
      release_date: dataToSubmit.release_date ? new Date(dataToSubmit.release_date).toISOString() : null,
      recording_date: dataToSubmit.recording_date ? new Date(dataToSubmit.recording_date).toISOString() : null,
      // distributor: dataToSubmit.distributor, // Not in songSchema, check if needed for DB
      lyrics_language: dataToSubmit.lyrics_language || null, // Already string in songSchema
      is_explicit: dataToSubmit.is_explicit || false,
      // is_ai_generated: dataToSubmit.is_ai_generated === true, // Not in songSchema, check if needed for DB
      // stems_available: dataToSubmit.stems_available === true, // Not in songSchema, check if needed for DB
      allow_downloads: dataToSubmit.allow_downloads || false,
      allow_comments: dataToSubmit.allow_comments === null || dataToSubmit.allow_comments === undefined ? true : dataToSubmit.allow_comments,
      lyrics: dataToSubmit.lyrics || null,
      bloc_note: dataToSubmit.bloc_note || null,
      status: dataToSubmit.status,
      theme: dataToSubmit.themes && dataToSubmit.themes.length > 0 ? dataToSubmit.themes[0] : null,
      subgenre: dataToSubmit.subgenre && dataToSubmit.subgenre.length > 0 ? dataToSubmit.subgenre[0] : null, // subgenre is string[] in songSchema
      visibility: dataToSubmit.is_public ? 'public' : 'private',
      // Fields like 'capo', 'distributor', 'is_ai_generated', 'stems_available' are not in song-schema.ts.
      // If they are required by the 'songs' table, they need to be added to SongFormValues or handled differently.
      // For now, they are commented out to avoid type errors.
      // Tags are handled separately after song insertion.
    };

    console.log("Attempting to insert songPayload:", JSON.stringify(songPayload, null, 2)); // DEBUG: Log payload
    try {
      const { data: songData, error: songError } = await supabase
        .from("songs")
        .insert(songPayload)
        .select()
        .single();

      if (songError) throw songError;
      if (!songData) throw new Error("La création du morceau a échoué.");

      const songId = songData.id;

      // Handle tags
      if (dataToSubmit.tags && dataToSubmit.tags.length > 0) {
        for (const tagName of dataToSubmit.tags) {
          const { data: existingTag } = await supabase.from("tags").select("id").eq("name", tagName).single();
          let tagId = existingTag?.id;
          if (!existingTag) {
            const { data: newTag, error: tagError } = await supabase.from("tags").insert({ name: tagName }).select().single();
            if (tagError) { console.error("Erreur création tag:", tagError); continue; }
            tagId = newTag?.id;
          }
          if (tagId) {
            await supabase.from("resource_tags").insert({ tag_id: tagId, resource_type: "song", resource_id: songId });
          }
        }
      }

      // Handle activity log
      await supabase.from("activities").insert({
        user_id: currentUserId,
        type: "song_created", // Corrected column name
        target_type: "song",    // Corrected column name
        target_id: songId,      // Corrected column name
        content: `a créé un nouveau morceau: ${songPayload.title}`,
      });

      toast({ title: status === "published" ? "Morceau Publié" : "Brouillon Enregistré", description: "Votre morceau a été sauvegardé." });
      router.push(`/dashboard/songs/${songId}/edit`); // Redirect to edit page after creation

    } catch (error: any) {
      console.error("Erreur sauvegarde morceau:", error);
      toast({ title: "Erreur", description: error.message || "Une erreur est survenue.", variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const initialSongFormValues: Partial<SongFormValues> = {
    artist_name: currentUserName, // Pre-fill artist name
    // Set other sensible defaults for create mode if needed
    bpm: 120,
    tuning_frequency: 440,
    allow_comments: true,
    status: 'draft',
    progress_data: { /* initial progress structure */ }
  };


  if (isLoadingAlbums || !currentUserId) {
    return <div className="flex justify-center items-center h-screen"><LoadingSpinner className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8 h-full flex flex-col">
      <SongForm
        mode="create"
        onFormSubmit={handleFormSubmit}
        isSubmittingGlobal={isSubmitting}
        albumsData={albums}
        isLoadingAlbums={isLoadingAlbums}
        supabaseClient={supabase}
        currentUserId={currentUserId}
        initialSongData={null}
      />
    </div>
  );
}
