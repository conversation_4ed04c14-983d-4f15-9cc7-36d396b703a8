'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { 
  Music, Brain, Sparkles, MessageSquare, Settings, Target,
  FileText, Guitar, Layers, Volume2, TrendingUp, Lightbulb,
  Wand2, BarChart3, Heart, Clock, Users, Mic, Play, Save,
  Menu, X, ChevronLeft, ChevronRight, Maximize2, Minimize2,
  Smartphone, Tablet, Monitor, Eye, EyeOff, Pause, Hash,
  ChevronDown, ChevronUp, RotateCcw, Zap, MoreHorizontal,
  Plus, Check, AlertCircle
} from 'lucide-react';

// Import des composants optimisés
import { LyricsEditorMega } from './LyricsEditorMega';
import { AIAssistantMega } from './AIAssistantMega';
import { SongInfoHeaderComplete } from './SongInfoHeaderComplete';
import { TimelineHorizontalAdvanced } from './TimelineHorizontalAdvanced';
import { AIActivityFeedback } from './AIActivityFeedback';
import { useAIComposerSupabase } from './hooks/useAIComposerSupabase';

// Import du hook responsive et du système d'aide
import { useResponsiveState } from './hooks/useResponsiveState';
import { ResponsiveHelpSystem } from './ResponsiveHelpSystem';

// Import des styles CSS responsives
import './ai-composer-responsive.css';

// Types pour les modes d'affichage
type ViewMode = 'focus' | 'complete' | 'analysis';
type PanelState = 'hidden' | 'collapsed' | 'expanded';

interface AIComposerMegaUnifiedResponsiveProps {
  // États principaux
  songId?: string;
  currentSong: any;
  setCurrentSong: (song: any) => void;
  
  // Contenu musical
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  
  // Configuration
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  
  // IA
  isConfigured: boolean;
  aiHistory: any[];
  setAiHistory: (history: any[]) => void;
  lastAiResult: string;
  setLastAiResult: (result: string) => void;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: any;
  setAiConfig?: (config: any) => void;
  
  // Actions
  handleAIGenerate: (prompt: string, type: string) => Promise<void>;
  handleLyricsChange: (content: string) => void;
  handleSave: () => Promise<void>;
  
  // Instruments
  availableInstruments: any[];
}

export const AIComposerMegaUnifiedResponsive: React.FC<AIComposerMegaUnifiedResponsiveProps> = ({
  songId,
  currentSong,
  setCurrentSong,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  isConfigured,
  aiHistory,
  setAiHistory,
  lastAiResult,
  setLastAiResult,
  aiLoading,
  aiError,
  aiConfig,
  setAiConfig,
  handleAIGenerate,
  handleLyricsChange,
  handleSave,
  availableInstruments
}) => {
  
  // Hook responsive pour gérer l'état de l'interface
  const {
    screenSize,
    viewMode,
    aiPanelState,
    activityPanelState,
    isFullscreen,
    showMobileMenu,
    preferences,
    setViewMode,
    setAiPanelState,
    setActivityPanelState,
    toggleFullscreen,
    setShowMobileMenu,
    isMobile,
    isTablet,
    isDesktop,
    isCompactMode,
    getResponsiveClasses
  } = useResponsiveState();
  
  // États pour le contenu
  const [generalPrompt, setGeneralPrompt] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(240);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  // Hook Supabase pour la gestion des données
  const {
    currentSong: supabaseSong,
    isLoading: isLoadingSong,
    isSaving: isSavingSong,
    error: songError,
    saveSong,
    createNewSong,
    uploadAudioFile,
    uploadCoverArt,
    updateSongField,
  } = useAIComposerSupabase();

  // Métriques du projet
  const projectMetrics = React.useMemo(() => {
    const totalWords = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const totalSections = songSections.length;
    const totalChords = songSections.reduce((total, section) => total + (section.chords?.length || 0), 0);
    const completionScore = Math.min(100, (totalWords / 100) * 100);
    const structureScore = Math.min(100, (totalSections / 4) * 100);
    const harmonyScore = Math.min(100, (totalChords / 8) * 100);
    const aiUsageScore = Math.min(100, (aiHistory.length / 10) * 100);
    
    return {
      totalWords,
      totalSections,
      totalChords,
      completion: completionScore,
      structure: structureScore,
      harmony: harmonyScore,
      aiUsage: aiUsageScore,
      overall: Math.round((completionScore + structureScore + harmonyScore + aiUsageScore) / 4),
      estimatedDuration: totalSections * 30,
      lastModified: new Date().toLocaleTimeString(),
      completeness: Math.round((completionScore + structureScore + harmonyScore + aiUsageScore) / 4),
      wordCount: totalWords,
      sectionsCount: totalSections
    };
  }, [lyricsContent, songSections, aiHistory]);

  // Fonctions pour basculer les panneaux (utilisant le hook responsive)
  const toggleAiPanel = () => {
    const newState = aiPanelState === 'hidden' ? 'collapsed' : 
                    aiPanelState === 'collapsed' ? 'expanded' : 'hidden';
    setAiPanelState(newState);
  };

  const toggleActivityPanel = () => {
    const newState = activityPanelState === 'hidden' ? 'collapsed' : 
                    activityPanelState === 'collapsed' ? 'expanded' : 'hidden';
    setActivityPanelState(newState);
  };

  // Gestionnaires d'événements
  const handleSaveGeneralPrompt = useCallback(() => {
    setStyleConfig(prev => ({ ...prev, generalPrompt }));
    setCurrentSong(prev => ({ ...prev, generalPrompt }));
  }, [generalPrompt, setStyleConfig, setCurrentSong]);

  const handleAIGenerateWithContext = useCallback(async (prompt: string, type: string) => {
    if (!isConfigured) return;
    
    const contextualPrompt = `${generalPrompt}\n\nCONTEXTE ACTUEL :\n- Section : ${songSections.find(s => s.id === selectedSection)?.title || 'Nouvelle section'}\n- Type : ${songSections.find(s => s.id === selectedSection)?.type || 'verse'}\n- Contenu actuel : ${lyricsContent.substring(0, 200)}${lyricsContent.length > 200 ? '...' : ''}\n- Progression : ${projectMetrics.overall}% complété\n\nDEMANDE SPÉCIFIQUE :\n${prompt}\n\nRéponds en tenant compte de la vision générale du projet et du contexte actuel.`;

    await handleAIGenerate(contextualPrompt, type);
  }, [generalPrompt, selectedSection, songSections, lyricsContent, projectMetrics.overall, isConfigured, handleAIGenerate]);

  const handleLoadAudio = useCallback(async (file: File) => {
    try {
      const uploadedUrl = await uploadAudioFile(file);
      if (uploadedUrl) {
        setAudioUrl(uploadedUrl);
        updateSongField('audio_url', uploadedUrl);
      } else {
        const url = URL.createObjectURL(file);
        setAudioUrl(url);
      }
    } catch (error) {
      console.error('Erreur upload audio:', error);
      const url = URL.createObjectURL(file);
      setAudioUrl(url);
    }
  }, [uploadAudioFile, updateSongField]);

  const handleRecordAudio = useCallback(() => {
    console.log('Démarrage enregistrement audio...');
  }, []);

  const handleSaveSong = useCallback(async () => {
    try {
      const songData = {
        title: currentSong?.title || 'Nouveau morceau',
        artist: currentSong?.artist || 'Artiste',
        description: currentSong?.description || '',
        bpm: styleConfig?.bpm || 120,
        musical_key: styleConfig?.key || 'C',
        time_signature: styleConfig?.timeSignature || '4/4',
        genres: styleConfig?.genres || [],
        moods: styleConfig?.mood ? [styleConfig.mood] : [],
        audio_url: audioUrl,
        lyrics: currentSong?.lyrics || '',
        chords: currentSong?.chords || '',
        ai_composer_data: {
          sections: songSections,
          styleConfig,
          aiHistory,
          lastAiResult,
        },
        editor_data: {
          selectedSection,
          generalPrompt,
          viewMode,
          aiPanelState,
          activityPanelState,
        }
      };

      await saveSong(songData);
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
    }
  }, [
    currentSong, styleConfig, audioUrl, songSections, aiHistory,
    lastAiResult, selectedSection, generalPrompt, viewMode,
    aiPanelState, activityPanelState, saveSong
  ]);

  // Auto-sauvegarde
  useEffect(() => {
    const interval = setInterval(() => {
      if (supabaseSong?.id && !isSavingSong) {
        handleSaveSong();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [supabaseSong?.id, isSavingSong, handleSaveSong]);

  // Créer une nouvelle chanson au montage si aucune n'existe
  useEffect(() => {
    if (!supabaseSong && !isLoadingSong) {
      createNewSong({
        title: 'Nouveau morceau AI Composer Pro',
        artist: 'Artiste',
        description: 'Créé avec AI Composer Mega Responsive',
      });
    }
  }, [supabaseSong, isLoadingSong, createNewSong]);

  // État pour le contexte d'aide
  const [currentHelpContext, setCurrentHelpContext] = useState('workspace');

  // Composant pour les contrôles de vue
  const ViewModeControls = () => (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1 p-1 bg-slate-800 rounded-lg">
        <Button
          variant={viewMode === 'focus' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('focus')}
          className="h-8 px-2"
        >
          <Eye className="h-4 w-4" />
          <span className="hidden sm:inline ml-1">Focus</span>
        </Button>
        <Button
          variant={viewMode === 'complete' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('complete')}
          className="h-8 px-2"
        >
          <Monitor className="h-4 w-4" />
          <span className="hidden sm:inline ml-1">Complet</span>
        </Button>
        <Button
          variant={viewMode === 'analysis' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('analysis')}
          className="h-8 px-2"
        >
          <BarChart3 className="h-4 w-4" />
          <span className="hidden sm:inline ml-1">Analyse</span>
        </Button>
      </div>
      
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsFullscreen(!isFullscreen)}
        className="h-8 px-2"
      >
        {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
      </Button>
    </div>
  );

  // Composant pour les métriques responsives
  const ResponsiveMetrics = () => (
    <div className="flex items-center gap-2 sm:gap-4 lg:gap-6">
      <div className="text-center">
        <div className="text-lg sm:text-2xl font-bold text-blue-400">{projectMetrics.overall}%</div>
        <div className="text-xs text-slate-400 hidden sm:block">Complétude</div>
      </div>
      <div className="text-center hidden sm:block">
        <div className="text-sm sm:text-lg font-bold text-white">{projectMetrics.totalWords}</div>
        <div className="text-xs text-slate-400">Mots</div>
      </div>
      <div className="text-center hidden md:block">
        <div className="text-sm sm:text-lg font-bold text-white">{projectMetrics.totalSections}</div>
        <div className="text-xs text-slate-400">Sections</div>
      </div>
      <div className="text-center hidden lg:block">
        <div className="text-sm sm:text-lg font-bold text-white">{Math.floor(projectMetrics.estimatedDuration / 60)}:{(projectMetrics.estimatedDuration % 60).toString().padStart(2, '0')}</div>
        <div className="text-xs text-slate-400">Durée est.</div>
      </div>
    </div>
  );

  // Menu mobile
  const MobileMenu = () => (
    <Sheet open={showMobileMenu} onOpenChange={setShowMobileMenu}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="md:hidden">
          <Menu className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-80 bg-slate-900 border-slate-700">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">Menu</h2>
            <Button variant="ghost" size="sm" onClick={() => setShowMobileMenu(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex-1 space-y-4">
            <ViewModeControls />
            
            <Separator className="bg-slate-700" />
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-slate-300">Panneaux</h3>
              <Button
                variant={aiPanelState !== 'hidden' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAiPanelState(aiPanelState === 'hidden' ? 'expanded' : 'hidden')}
                className="w-full justify-start"
              >
                <Brain className="h-4 w-4 mr-2" />
                Assistant IA
              </Button>
              <Button
                variant={activityPanelState !== 'hidden' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActivityPanelState(activityPanelState === 'hidden' ? 'expanded' : 'hidden')}
                className="w-full justify-start"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Activité
              </Button>
            </div>
            
            <Separator className="bg-slate-700" />
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-slate-300">Actions</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                className="w-full justify-start"
              >
                <Play className="h-4 w-4 mr-2" />
                {isPlaying ? 'Pause' : 'Play'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveSong}
                className="w-full justify-start"
              >
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );

  return (
    <div className={`ai-composer-workspace ${getResponsiveClasses()} ${
      isFullscreen ? 'fullscreen fixed inset-0 z-50' : ''
    } h-screen flex flex-col bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden`}>
      {/* Système d'aide responsive */}
      <ResponsiveHelpSystem 
        context={currentHelpContext}
        screenSize={screenSize}
        isVisible={preferences.showHelp}
      />
      {/* Header avec infos du morceau */}
      <div className="border-b border-slate-700 flex-shrink-0">
        {/* Indicateur de statut */}
        {(isLoadingSong || isSavingSong || songError) && (
          <div className="px-3 py-1 bg-slate-700/50 border-b border-slate-600">
            <div className="flex items-center gap-2 text-xs">
              {isLoadingSong && (
                <>
                  <div className="animate-spin h-3 w-3 border border-blue-400 border-t-transparent rounded-full" />
                  <span className="text-blue-400">Chargement...</span>
                </>
              )}
              {isSavingSong && (
                <>
                  <div className="animate-spin h-3 w-3 border border-green-400 border-t-transparent rounded-full" />
                  <span className="text-green-400">Sauvegarde...</span>
                </>
              )}
              {songError && (
                <span className="text-red-400">Erreur: {songError}</span>
              )}
            </div>
          </div>
        )}

        {/* Header principal responsive */}
        <div className={`${isMobile ? 'p-2' : 'p-2 sm:p-4'}`}>
          <div className={`flex items-center justify-between gap-4 ${isMobile ? 'flex-col space-y-2' : ''}`}>
            {/* Logo et titre */}
            <div className={`flex items-center gap-2 sm:gap-4 min-w-0 ${isMobile ? 'w-full justify-center' : ''}`}>
              <div className="p-1 sm:p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex-shrink-0">
                <Music className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0">
                <h1 className={`font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent truncate ${isMobile ? 'text-lg' : 'text-sm sm:text-xl'}`}>
                  AI Composer Pro
                </h1>
                <p className="text-xs text-slate-400 hidden sm:block">Interface responsive et professionnelle</p>
              </div>
            </div>

            {/* Métriques responsives */}
            <div className="hidden sm:block">
              <ResponsiveMetrics />
            </div>

            {/* Contrôles */}
            <div className={`flex items-center gap-2 flex-shrink-0 ${isMobile ? 'w-full justify-center' : ''}`}>
              {screenSize !== 'mobile' && (
                <>
                  {isConfigured && (
                    <Badge variant="default" className="gap-1 bg-green-500 hidden sm:flex">
                      <Brain className="h-3 w-3" />
                      IA Active
                    </Badge>
                  )}
                  
                  <ViewModeControls />
                </>
              )}
              
              <MobileMenu />
            </div>
          </div>
          
          {/* Métriques mobiles */}
          <div className="mt-2 sm:hidden">
            <ResponsiveMetrics />
          </div>
        </div>

        {/* Prompt général - adaptatif */}
        {viewMode !== 'focus' && (
          <div className="border-t border-slate-700 p-2 sm:p-4 bg-slate-800/50">
            <div className="flex flex-col sm:flex-row items-start gap-3">
              <div className="flex-1 w-full">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-purple-400" />
                  <span className="text-sm font-medium text-white">Vision Générale du Projet</span>
                  <Badge variant="outline" className="text-xs hidden sm:inline-flex">
                    Guide toutes les suggestions IA
                  </Badge>
                </div>
                <Textarea
                  value={generalPrompt}
                  onChange={(e) => setGeneralPrompt(e.target.value)}
                  placeholder="Décrivez la vision, l'ambiance, le style et les influences de votre chanson..."
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 text-sm"
                  rows={screenSize === 'mobile' ? 2 : 3}
                />
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleSaveGeneralPrompt}
                className="mt-2 sm:mt-6 w-full sm:w-auto"
              >
                <Save className="h-4 w-4" />
                <span className="sm:hidden ml-2">Sauver</span>
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Timeline - adaptatif selon le mode */}
      {viewMode !== 'focus' && (
        <div className="border-b border-slate-700 flex-shrink-0">
          <TimelineHorizontalAdvanced
            sections={songSections}
            setSections={setSongSections}
            selectedSection={selectedSection}
            setSelectedSection={setSelectedSection}
            isPlaying={isPlaying}
            setIsPlaying={setIsPlaying}
            currentTime={currentTime}
            duration={duration}
            onLoadAudio={handleLoadAudio}
            onRecordAudio={handleRecordAudio}
            audioUrl={audioUrl}
          />
        </div>
      )}

      {/* Menu mobile overlay */}
      {isMobile && showMobileMenu && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setShowMobileMenu(false)}>
          <div className="absolute top-0 right-0 w-80 h-full bg-slate-800 border-l border-slate-700 p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Menu</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMobileMenu(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            <div className="space-y-2">
              <Button
                variant="ghost"
                onClick={toggleAiPanel}
                className="w-full justify-start"
              >
                <Zap className="w-4 h-4 mr-2" />
                Assistant IA
              </Button>
              <Button
                variant="ghost"
                onClick={toggleActivityPanel}
                className="w-full justify-start"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Activité IA
              </Button>
              <Button
                variant="ghost"
                onClick={toggleFullscreen}
                className="w-full justify-start"
              >
                {isFullscreen ? <Minimize2 className="w-4 h-4 mr-2" /> : <Maximize2 className="w-4 h-4 mr-2" />}
                {isFullscreen ? 'Quitter' : 'Plein écran'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Workspace principal responsive */}
      <div className="flex-1 overflow-hidden flex flex-col lg:flex-row min-h-0 h-full">
        {/* Éditeur principal */}
        <div className="flex-1 min-w-0 min-h-0 flex flex-col h-full overflow-hidden">
          {/* Zone principale - Éditeur de paroles */}
          <div className="flex-1 min-w-0 flex flex-col h-full overflow-hidden">
            <LyricsEditorMega
              content={lyricsContent}
              onChange={setLyricsContent}
              selectedSection={selectedSection}
              onSectionSelect={setSelectedSection}
              songSections={songSections}
              setSongSections={setSongSections}
              styleConfig={styleConfig}
              setStyleConfig={setStyleConfig}
              onSave={handleSaveSong}
              aiLoading={aiLoading}
              onAIGenerate={handleAIGenerateWithContext}
              generalPrompt={generalPrompt}
              setGeneralPrompt={setGeneralPrompt}
              projectMetrics={projectMetrics}
              isFullscreen={isFullscreen}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              helpContext={currentHelpContext}
              setHelpContext={setCurrentHelpContext}
            />
          </div>
        </div>

        {/* Panneaux latéraux - adaptatifs pour toutes les tailles d'écran */}
        {/* Activité IA */}
        {activityPanelState !== 'hidden' && (
          <div className={`${
            isMobile || isTablet ? (
              activityPanelState === 'expanded' 
                ? 'fixed inset-y-0 right-0 w-80 z-40 bg-slate-800 shadow-2xl' 
                : 'hidden'
            ) : (
              activityPanelState === 'collapsed' ? 'w-16' : 'w-72'
            )
          } border-l border-slate-700 bg-slate-800/30 flex-shrink-0 transition-all duration-300 flex flex-col`}>
            {activityPanelState === 'expanded' ? (
              <>
                {(isMobile || isTablet) && (
                  <div className="flex items-center justify-between p-3 border-b border-slate-700 bg-slate-800">
                    <h3 className="font-semibold text-white">Activité IA</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActivityPanelState('hidden')}
                      className="text-slate-400 hover:text-white"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                )}
                <div className="flex-1 overflow-hidden">
                  <AIActivityFeedback
                    aiLoading={aiLoading}
                    aiError={aiError}
                    lastAiResult={lastAiResult}
                    aiHistory={aiHistory}
                    onClearHistory={() => setAiHistory([])}
                    onCopyResult={(result) => navigator.clipboard.writeText(result)}
                  />
                </div>
              </>
            ) : (
              <div className="p-2 flex flex-col items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActivityPanelState('expanded')}
                  className="w-full p-2 text-slate-400 hover:text-white"
                  title="Ouvrir l'activité IA"
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Assistant IA */}
        {aiPanelState !== 'hidden' && (
          <div className={`${
            isMobile || isTablet ? (
              aiPanelState === 'expanded' 
                ? 'fixed inset-y-0 right-0 w-80 z-40 bg-slate-800 shadow-2xl' 
                : 'hidden'
            ) : (
              aiPanelState === 'collapsed' ? 'w-16' : 'w-80'
            )
          } border-l border-slate-700 bg-slate-800/30 flex-shrink-0 transition-all duration-300 flex flex-col`}>
            {aiPanelState === 'expanded' ? (
              <>
                {(isMobile || isTablet) && (
                  <div className="flex items-center justify-between p-3 border-b border-slate-700 bg-slate-800">
                    <h3 className="font-semibold text-white">Assistant IA</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setAiPanelState('hidden')}
                      className="text-slate-400 hover:text-white"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                )}
                <div className="flex-1 overflow-hidden">
                  <AIAssistantMega
                    isConfigured={isConfigured}
                    aiLoading={aiLoading}
                    aiError={aiError}
                    aiConfig={aiConfig}
                    setAiConfig={setAiConfig}
                    currentSection={selectedSection}
                    songSections={songSections}
                    styleConfig={styleConfig}
                    lyricsContent={lyricsContent}
                    aiHistory={aiHistory}
                    lastAiResult={lastAiResult}
                    setAiHistory={setAiHistory}
                    setLastAiResult={setLastAiResult}
                    onAIGenerate={handleAIGenerateWithContext}
                    generalPrompt={generalPrompt}
                    projectMetrics={projectMetrics}
                  />
                </div>
              </>
            ) : (
              <div className="p-2 flex flex-col items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setAiPanelState('expanded')}
                  className="w-full p-2 text-slate-400 hover:text-white"
                  title="Ouvrir l'assistant IA"
                >
                  <Brain className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer responsive */}
      <div className="border-t border-slate-700 bg-slate-800/80 px-2 sm:px-4 py-2 flex-shrink-0">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 text-xs sm:text-sm text-slate-400">
          <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
            <span>Section: {songSections.find(s => s.id === selectedSection)?.title || 'Aucune'}</span>
            <span className="hidden sm:inline">•</span>
            <span>Tonalité: {currentSong.key || styleConfig.key || 'C'}</span>
            <span className="hidden sm:inline">•</span>
            <span>Tempo: {currentSong.tempo || styleConfig.bpm || 120} BPM</span>
            <span className="hidden md:inline">•</span>
            <span className="hidden md:inline">Complétude: {projectMetrics.overall}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="hidden sm:inline">Dernière modif: {projectMetrics.lastModified}</span>
            {songId && (
              <Badge variant="outline" className="text-xs">
                Synchronisé
              </Badge>
            )}
            <div className="flex items-center gap-1">
              {screenSize === 'mobile' && <Smartphone className="h-3 w-3" />}
              {screenSize === 'tablet' && <Tablet className="h-3 w-3" />}
              {screenSize === 'desktop' && <Monitor className="h-3 w-3" />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIComposerMegaUnifiedResponsive;