-- Fonction pour obtenir les statistiques générales d'un utilisateur
CREATE OR REPLACE FUNCTION get_user_overview_stats(p_user_id UUID, p_time_range TEXT)
RETURNS JSON AS $$
DECLARE
  result JSON;
  current_period_start TIMESTAMP;
  previous_period_start TIMESTAMP;
BEGIN
  -- Déterminer les périodes en fonction du range
  IF p_time_range = '7d' THEN
    current_period_start := NOW() - INTERVAL '7 days';
    previous_period_start := current_period_start - INTERVAL '7 days';
  ELSIF p_time_range = '30d' THEN
    current_period_start := NOW() - INTERVAL '30 days';
    previous_period_start := current_period_start - INTERVAL '30 days';
  ELSIF p_time_range = '90d' THEN
    current_period_start := NOW() - INTERVAL '90 days';
    previous_period_start := current_period_start - INTERVAL '90 days';
  ELSE -- '1y' par défaut
    current_period_start := NOW() - INTERVAL '1 year';
    previous_period_start := current_period_start - INTERVAL '1 year';
  END IF;

  -- Calculer les statistiques
  WITH current_stats AS (
    SELECT
      COUNT(p.id) AS total_plays,
      COUNT(DISTINCT p.user_id) AS unique_listeners,
      COUNT(DISTINCT sc.id) AS total_comments, -- Modifié pour song_comments
      COUNT(DISTINCT lk.id) AS total_likes
    FROM songs s
    LEFT JOIN plays p ON s.id = p.song_id AND p.created_at >= current_period_start
    LEFT JOIN song_comments sc ON s.id = sc.song_id AND sc.created_at >= current_period_start -- Modifié pour song_comments
    LEFT JOIN likes lk ON s.id = lk.resource_id AND lk.resource_type = 'song' AND lk.created_at >= current_period_start
    WHERE s.user_id = p_user_id
  ),
  previous_stats AS (
    SELECT
      COUNT(p.id) AS total_plays,
      COUNT(DISTINCT p.user_id) AS unique_listeners,
      COUNT(DISTINCT sc.id) AS total_comments, -- Modifié pour song_comments
      COUNT(DISTINCT lk.id) AS total_likes
    FROM songs s
    LEFT JOIN plays p ON s.id = p.song_id AND p.created_at BETWEEN previous_period_start AND current_period_start
    LEFT JOIN song_comments sc ON s.id = sc.song_id AND sc.created_at BETWEEN previous_period_start AND current_period_start -- Modifié pour song_comments
    LEFT JOIN likes lk ON s.id = lk.resource_id AND lk.resource_type = 'song' AND lk.created_at BETWEEN previous_period_start AND current_period_start
    WHERE s.user_id = p_user_id
  )
  
  -- Calculer les variations et retourner le résultat
  SELECT json_build_object(
    'total_plays', cs.total_plays,
    'plays_change', CASE WHEN ps.total_plays = 0 THEN 100 ELSE ((cs.total_plays - ps.total_plays) * 100.0 / NULLIF(ps.total_plays, 0)) END,
    'unique_listeners', cs.unique_listeners,
    'listeners_change', CASE WHEN ps.unique_listeners = 0 THEN 100 ELSE ((cs.unique_listeners - ps.unique_listeners) * 100.0 / NULLIF(ps.unique_listeners, 0)) END,
    'total_comments', cs.total_comments,
    'comments_change', CASE WHEN ps.total_comments = 0 THEN 100 ELSE ((cs.total_comments - ps.total_comments) * 100.0 / NULLIF(ps.total_comments, 0)) END,
    'total_likes', cs.total_likes,
    'likes_change', CASE WHEN ps.total_likes = 0 THEN 100 ELSE ((cs.total_likes - ps.total_likes) * 100.0 / NULLIF(ps.total_likes, 0)) END,
    'engagement_rate', CASE WHEN cs.total_plays = 0 THEN 0 ELSE ((cs.total_likes + cs.total_comments) * 100.0 / NULLIF(cs.total_plays, 0)) END,
    'engagement_change', CASE 
      WHEN ps.total_plays = 0 OR (ps.total_likes + ps.total_comments) = 0 THEN 100 
      ELSE (
        ((cs.total_likes + cs.total_comments) * 100.0 / NULLIF(cs.total_plays, 0)) - 
        ((ps.total_likes + ps.total_comments) * 100.0 / NULLIF(ps.total_plays, 0))
      ) 
    END
  ) INTO result
  FROM current_stats cs, previous_stats ps;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les données de timeline d'activité
CREATE OR REPLACE FUNCTION get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)
RETURNS JSON AS $$
DECLARE
  result JSON;
  start_date TIMESTAMP;
BEGIN
  start_date := NOW() - (p_days || ' days')::INTERVAL;
  
  WITH dates AS (
    SELECT generate_series(
      date_trunc(p_interval, start_date),
      date_trunc(p_interval, NOW()),
      (1 || ' ' || p_interval)::INTERVAL
    ) AS date
  ),
  plays_data AS (
    SELECT 
      date_trunc(p_interval, p.created_at) AS date,
      COUNT(p.id) AS plays
    FROM plays p
    JOIN songs s ON p.song_id = s.id
    WHERE s.user_id = p_user_id AND p.created_at >= start_date
    GROUP BY 1
  ),
  likes_data AS (
    SELECT 
      date_trunc(p_interval, l.created_at) AS date,
      COUNT(l.id) AS likes
    FROM likes l
    JOIN songs s ON l.resource_id = s.id AND l.resource_type = 'song'
    WHERE s.user_id = p_user_id AND l.created_at >= start_date
    GROUP BY 1
  ),
  comments_data AS (
    SELECT 
      date_trunc(p_interval, sc.created_at) AS date,
      COUNT(sc.id) AS comments
    FROM song_comments sc -- Modifié pour song_comments
    JOIN songs s ON sc.song_id = s.id -- Modifié pour song_comments
    WHERE s.user_id = p_user_id AND sc.created_at >= start_date
    GROUP BY 1
  )
  
  SELECT json_agg(
    json_build_object(
      'date', d.date,
      'plays', COALESCE(pd.plays, 0),
      'likes', COALESCE(ld.likes, 0),
      'comments', COALESCE(cd.comments, 0)
    )
    ORDER BY d.date
  ) INTO result
  FROM dates d
  LEFT JOIN plays_data pd ON d.date = pd.date
  LEFT JOIN likes_data ld ON d.date = ld.date
  LEFT JOIN comments_data cd ON d.date = cd.date;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les performances par genre
CREATE OR REPLACE FUNCTION get_genre_performance(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  WITH genre_stats AS (
    SELECT 
      unnest(s.genres) AS genre,
      COUNT(p.id) AS plays,
      COUNT(DISTINCT p.user_id) AS unique_listeners,
      COUNT(l.id) AS likes
    FROM songs s
    LEFT JOIN plays p ON s.id = p.song_id
    LEFT JOIN likes l ON s.id = l.resource_id AND l.resource_type = 'song'
    WHERE s.user_id = p_user_id
    GROUP BY 1
  ),
  total_plays AS (
    SELECT SUM(plays) AS total FROM genre_stats
  )
  
  SELECT json_agg(
    json_build_object(
      'name', gs.genre,
      'total_plays', gs.plays,
      'unique_listeners', gs.unique_listeners,
      'likes', gs.likes,
      'engagement_rate', CASE WHEN gs.plays = 0 THEN 0 ELSE (gs.likes * 100.0 / gs.plays) END,
      'percentage', CASE WHEN tp.total = 0 THEN 0 ELSE (gs.plays * 100.0 / tp.total) END
    )
    ORDER BY gs.plays DESC
  ) INTO result
  FROM genre_stats gs, total_plays tp;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
