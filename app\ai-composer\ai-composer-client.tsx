"use client";

import React, { useState, useMemo } from 'react';
import { type Song, SongStatus } from '@/components/songs/song-schema';
import { type UserProfileForSidebar } from "@/components/sidebar";
import { Button } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { PlusCircle, Search, List, LayoutGrid } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { SongListItem } from '@/components/songs/song-list-item';
import { createSong } from '@/lib/actions/song.actions';
import { WelcomeBanner } from '@/components/ai-composer/welcome-banner';

interface AIComposerClientProps {
  songs: Song[];
  user: UserProfileForSidebar;
}

export default function AIComposerClient({ songs: initialSongs, user }: AIComposerClientProps) {
  const [songs, setSongs] = useState<Song[]>(initialSongs);
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOrder, setSortOrder] = useState('created_at_desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const router = useRouter();

  const handleCreateSong = async () => {
    setIsCreating(true);
    try {
      const result = await createSong({ 
        title: 'Nouveau morceau',
        status: SongStatus.DRAFT,
      });

      if (!result.success) {
        toast.error("Erreur lors de la création du morceau", {
          description: result.error,
        });
        return;
      }

      toast.success("Nouveau morceau créé ! Redirection...");
      router.push(`/ai-composer/workspace/${result.data.id}`);

    } catch (error) {
      console.error("Erreur inattendue lors de la création du morceau:", error);
      toast.error("Une erreur client s'est produite.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleSongUpdate = (songId: string, updatedData: Partial<Song>) => {
    setSongs(currentSongs =>
      currentSongs.map(song =>
        song.id === songId ? { ...song, ...updatedData } : song
      )
    );
    router.refresh();
  };

  const handleSongDelete = (songId: string) => {
    setSongs(currentSongs => currentSongs.filter(song => song.id !== songId));
    toast.info("Morceau supprimé.");
    router.refresh();
  };

  const filteredAndSortedSongs = useMemo(() => {
    return initialSongs
      .filter(song => song.title?.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => {
        switch (sortOrder) {
          case 'title_asc':
            return (a.title || '').localeCompare(b.title || '');
          case 'title_desc':
            return (b.title || '').localeCompare(a.title || '');
          case 'updated_at_asc':
            return new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
          case 'updated_at_desc':
            return new Date(b.updated_at || 0).getTime() - new Date(a.updated_at || 0).getTime();
          case 'created_at_asc':
            return new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
          case 'created_at_desc':
          default:
            return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        }
      });
  }, [initialSongs, searchTerm, sortOrder]);

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <WelcomeBanner />
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-8">
        <h1 className="text-3xl font-bold tracking-tight self-start sm:self-center">AI Composer</h1>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button onClick={handleCreateSong} disabled={isCreating} className="w-full sm:w-auto">
            <PlusCircle className="mr-2 h-4 w-4" />
            {isCreating ? 'Création...' : 'Créer un morceau'}
          </Button>
        </div>
      </div>

            <div className="flex flex-col md:flex-row gap-4 mb-6 px-4 py-3 bg-muted/50 rounded-lg border">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un morceau..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-full"
          />
        </div>
        <div className="flex items-center gap-4">
          <Select value={sortOrder} onValueChange={setSortOrder}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Trier par" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created_at_desc">Plus récents</SelectItem>
              <SelectItem value="created_at_asc">Plus anciens</SelectItem>
              <SelectItem value="updated_at_desc">Modifiés récemment</SelectItem>
              <SelectItem value="title_asc">Titre (A-Z)</SelectItem>
              <SelectItem value="title_desc">Titre (Z-A)</SelectItem>
            </SelectContent>
          </Select>
          <ToggleGroup type="single" value={viewMode} onValueChange={(value: 'grid' | 'list') => { if (value) setViewMode(value); }} className="hidden sm:flex">
            <ToggleGroupItem value="grid" title="Grille"><LayoutGrid className="h-4 w-4" /></ToggleGroupItem>
            <ToggleGroupItem value="list" title="Liste"><List className="h-4 w-4" /></ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      <div className={cn(
        viewMode === 'grid'
          ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'
          : 'flex flex-col gap-3'
      )}>
        {filteredAndSortedSongs.map((song) => (
          <SongListItem
              key={song.id}
              song={song}
              onUpdateStatus={handleSongUpdate}
              onDelete={() => song.id && handleSongDelete(song.id)}
              viewMode={viewMode}
            />
        ))}
      </div>

      {filteredAndSortedSongs.length === 0 && !isCreating && (
        <div className="text-center py-20 border-2 border-dashed rounded-lg mt-8">
          <h3 className="text-xl font-medium">Commencez votre prochain chef-d'œuvre</h3>
          <p className="text-muted-foreground mt-2 mb-6">
            Vous n'avez aucun projet dans l'AI Composer.
          </p>
          <Button onClick={handleCreateSong} disabled={isCreating} size="lg">
            <PlusCircle className="mr-2 h-5 w-5" />
            {isCreating ? 'Création en cours...' : 'Créer votre premier morceau'}
          </Button>
        </div>
      )}
    </div>
  );
}
