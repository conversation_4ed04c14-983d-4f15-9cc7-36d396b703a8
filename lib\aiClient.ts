import { z } from 'zod';

/**
 * Unified AI client: abstrait l'accès à plusieurs fournisseurs (OpenAI, Ollama, HuggingFace, etc.).
 * Chaque fournisseur doit implémenter la même interface.
 */
export interface AIProvider {
  name: string;
  generate(opts: GenerateOptions): Promise<GenerateResponse>;
  listModels?(): Promise<string[]>;
  healthCheck?(): Promise<boolean>;
}

export const GenerateOptionsSchema = z.object({
  prompt: z.string(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().min(1).max(4096).optional(),
  // Additional vendor-specific params allowed
  vendorOptions: z.record(z.any()).optional()
});
export type GenerateOptions = z.infer<typeof GenerateOptionsSchema>;

export const GenerateResponseSchema = z.object({
  text: z.string(),
  raw: z.any().optional()
});
export type GenerateResponse = z.infer<typeof GenerateResponseSchema>;

/************************
 * OpenAI implementation
 ************************/
export class OpenAIProvider implements AIProvider {
  name = 'openai';
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://api.openai.com/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async generate(opts: GenerateOptions): Promise<GenerateResponse> {
    GenerateOptionsSchema.parse(opts);
    const body = {
      model: opts.model || 'gpt-4o-mini',
      temperature: opts.temperature ?? 0.7,
      max_tokens: opts.maxTokens ?? 1024,
      messages: [{ role: 'user', content: opts.prompt }],
      ...opts.vendorOptions
    };
    const res = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.apiKey}`
      },
      body: JSON.stringify(body)
    });
    if (!res.ok) throw new Error(`OpenAI error: ${res.status}`);
    const json = await res.json();
    const text = json.choices?.[0]?.message?.content ?? '';
    return { text, raw: json };
  }
}

/************************
 * Ollama implementation
 ************************/
export class OllamaProvider implements AIProvider {
  name = 'ollama';
  private endpoint: string;

  constructor(endpoint: string = 'http://localhost:11434') {
    this.endpoint = endpoint.replace(/\/$/, '');
  }

  async generate(opts: GenerateOptions): Promise<GenerateResponse> {
    GenerateOptionsSchema.parse(opts);
    const body = {
      model: opts.model || 'mistral',
      prompt: opts.prompt,
      options: {
        temperature: opts.temperature ?? 0.7,
        num_predict: opts.maxTokens ?? 1024,
        ...opts.vendorOptions
      }
    };
    const res = await fetch(`${this.endpoint}/api/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    });
    if (!res.ok) throw new Error(`Ollama error: ${res.status}`);
    const json = await res.json();
    return { text: json.response as string, raw: json };
  }

  async listModels(): Promise<string[]> {
    const res = await fetch(`${this.endpoint}/api/tags`);
    if (!res.ok) throw new Error('Ollama list models failed');
    const json = await res.json();
    return (json.models || []).map((m: any) => m.name as string);
  }

  async healthCheck(): Promise<boolean> {
    try {
      const res = await fetch(`${this.endpoint}/`);
      return res.ok;
    } catch {
      return false;
    }
  }
}

/*********************
 * Factory + helpers
 *********************/
export type ProviderName = 'openai' | 'ollama';

export function createProvider(name: ProviderName, config: Record<string, any>): AIProvider {
  switch (name) {
    case 'openai':
      return new OpenAIProvider(config.apiKey, config.baseUrl);
    case 'ollama':
      return new OllamaProvider(config.endpoint);
    default:
      throw new Error(`Unknown provider: ${name}`);
  }
}

/**
 * Simple cache of instantiated providers.
 */
const _providers = new Map<string, AIProvider>();

export function getAIProvider(name: ProviderName, config: Record<string, any>): AIProvider {
  const key = `${name}-${JSON.stringify(config)}`;
  if (!_providers.has(key)) {
    _providers.set(key, createProvider(name, config));
  }
  return _providers.get(key)!;
}
