"use client";

import { useState, useEffect, useTransition, useMemo } from 'react';
import Link from "next/link";
import { createBrowserClient } from "@/lib/supabase/client";
import { useUser } from '@/contexts/user-context';
import { Button } from "@/components/ui/button";
import PlaylistCard, { PlaylistWithSongsAndCreator } from "@/components/playlists/playlist-card";
import { Plus, ListMusic, Loader2, LayoutGrid, ListFilter, Search as SearchIcon, ListMusic as ListMusicIcon } from "lucide-react";
import { toast } from 'sonner';
import { togglePlaylistVisibility, deletePlaylist } from './actions';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export default function PlaylistsPage() {
  const supabase = createBrowserClient();
  const { user } = useUser();
  const [allPlaylists, setAllPlaylists] = useState<PlaylistWithSongsAndCreator[]>([]);
  const [displayedPlaylists, setDisplayedPlaylists] = useState<PlaylistWithSongsAndCreator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [updatingPlaylistId, setUpdatingPlaylistId] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('created_at-desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    const fetchPlaylists = async () => {
      if (!user?.id) {
        setAllPlaylists([]);
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      const { data, error } = await supabase.rpc('get_playlists_for_user', {
        p_user_id: user.id,
      });

      if (error) {
        console.error("Error fetching playlists via RPC:", error.message);
        toast.error("Impossible de charger les playlists.");
        setAllPlaylists([]);
      } else if (data) {
        if (Array.isArray(data)) {
          setAllPlaylists(data as PlaylistWithSongsAndCreator[]);
        } else {
          console.error('Error: Expected an array of playlists, but received:', data);
          toast.error('Les données reçues pour les playlists sont dans un format inattendu.');
          setAllPlaylists([]);
        }
      } else {
        setAllPlaylists([]);
      }
      setIsLoading(false);
    };

    fetchPlaylists();
  }, [user?.id, supabase]);

  useEffect(() => {
    let filtered = [...allPlaylists];

    if (searchTerm) {
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    const [key, direction] = sortOption.split('-');
    filtered.sort((a, b) => {
      const valA = a[key as keyof PlaylistWithSongsAndCreator];
      const valB = b[key as keyof PlaylistWithSongsAndCreator];

      if (valA === null || valA === undefined) return 1;
      if (valB === null || valB === undefined) return -1;

      if (typeof valA === 'string' && typeof valB === 'string') {
        return direction === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
      }

      if (valA < valB) return direction === 'asc' ? -1 : 1;
      if (valA > valB) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    setDisplayedPlaylists(filtered);
  }, [allPlaylists, searchTerm, sortOption]);

  const handleToggleVisibility = (playlistId: string, currentIsPublic: boolean) => {
    setUpdatingPlaylistId(playlistId);
    startTransition(async () => {
      const result = await togglePlaylistVisibility(playlistId, currentIsPublic);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(`Visibilité de la playlist mise à jour.`);
        setAllPlaylists((prev: PlaylistWithSongsAndCreator[]) => 
          prev.map((p: PlaylistWithSongsAndCreator) => p.id === playlistId ? { ...p, is_public: result.newIsPublic! } : p)
        );
      }
      setUpdatingPlaylistId(null);
    });
  };

  const handleDeletePlaylist = async (playlistId: string) => {
    const originalPlaylists = [...allPlaylists];
    setAllPlaylists(prev => prev.filter(p => p.id !== playlistId));

    const result = await deletePlaylist(playlistId);

    if (result.error) {
      toast.error(result.error);
      setAllPlaylists(originalPlaylists);
    } else {
      toast.success("Playlist supprimée avec succès.");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <Loader2 className="w-12 h-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div className="flex items-center gap-3">
          <ListMusicIcon className="w-8 h-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Mes Playlists</h1>
            <p className="text-muted-foreground">Organisez et partagez vos compilations.</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/playlists/create">
              <Plus className="w-4 h-4 mr-2" />
              Créer une playlist
            </Link>
          </Button>
        </div>
      </div>

      <div className="bg-card border rounded-lg p-4 mb-6">
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="relative w-full sm:w-auto sm:flex-grow">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
            <Input
              placeholder="Rechercher par nom..."
              className="pl-10 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-4 w-full sm:w-auto">
            <Select value={sortOption} onValueChange={setSortOption}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Trier par" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at-desc">Plus récentes</SelectItem>
                <SelectItem value="created_at-asc">Plus anciennes</SelectItem>
                <SelectItem value="name-asc">Nom (A-Z)</SelectItem>
                <SelectItem value="name-desc">Nom (Z-A)</SelectItem>
                <SelectItem value="song_count-desc">Plus de morceaux</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant={viewMode === 'grid' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('grid')}>
                      <LayoutGrid className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Vue Grille</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant={viewMode === 'list' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('list')}>
                      <ListMusicIcon className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Vue Liste</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>

      {displayedPlaylists.length === 0 && !isLoading ? (
        <div className="text-center py-16 border-2 border-dashed rounded-lg">
          <ListMusic className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Aucune playlist trouvée</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Créez votre première playlist ou affinez votre recherche.
          </p>
          <div className="mt-6">
            <Button asChild>
              <Link href="/playlists/create">
                <Plus className="mr-2 h-4 w-4" /> Créer une playlist
              </Link>
            </Button>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        <div 
          className="grid gap-4 md:gap-6"
          style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))' }}
        >
          {displayedPlaylists.map((playlist) => (
            <PlaylistCard 
              key={playlist.id} 
              playlist={playlist} 
              onToggleVisibility={handleToggleVisibility}
              isVisibilityUpdating={updatingPlaylistId === playlist.id}
              onDelete={handleDeletePlaylist}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 border-2 border-dashed rounded-lg">
           <h3 className="mt-4 text-lg font-medium">Vue Liste en construction</h3>
        </div>
      )}
    </div>
  );
}