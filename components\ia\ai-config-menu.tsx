"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input"; // Added for API keys
import { Textarea } from "@/components/ui/textarea"; // Added for cost display placeholder
import { toast } from "@/hooks/use-toast";
import { Cog, CheckCircle, XCircle, RefreshCw, AlertTriangle, Loader2, Brain, KeyRound, DollarSign } from 'lucide-react'; // Added Brain, KeyRound, DollarSign

const OLLAMA_API_BASE_URL = "http://localhost:11434/api";
const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";
const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik";
const LOCAL_STORAGE_OPENAI_API_KEY = "openai_api_key_mouvik";
const LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY = "openai_selected_model_mouvik"; // Added
const LOCAL_STORAGE_OPENROUTER_API_KEY = "openrouter_api_key_mouvik";
const LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY = "openrouter_selected_model_mouvik"; // Added
const LOCAL_STORAGE_ANTHROPIC_API_KEY = "anthropic_api_key_mouvik";
const LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY = "anthropic_selected_model_mouvik"; // Added

interface OllamaTag {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[] | null;
    parameter_size: string;
    quantization_level: string;
  };
}

type AiProvider = 'ollama' | 'openai' | 'openrouter' | 'anthropic';

interface AiConfigMenuProps {
  inlineMode?: boolean;
  isPopoverOpen?: boolean;
  setIsPopoverOpen?: (open: boolean) => void;
}

// Placeholder for OpenAI models - replace with actual list if needed
const openAIModels = [
    { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
    { value: "gpt-4", label: "GPT-4" },
    { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
];

// Placeholder for Anthropic models
const anthropicModels = [
    { value: "claude-3-opus-20240229", label: "Claude 3 Opus" },
    { value: "claude-3-sonnet-20240229", label: "Claude 3 Sonnet" },
    { value: "claude-3-haiku-20240307", label: "Claude 3 Haiku" },
    { value: "claude-2.1", label: "Claude 2.1" },
    { value: "claude-2.0", label: "Claude 2.0" },
];


export function AiConfigMenu({ inlineMode = false, isPopoverOpen, setIsPopoverOpen }: AiConfigMenuProps) {
  const [selectedProvider, setSelectedProvider] = useState<AiProvider>('ollama');
  
  const [ollamaStatus, setOllamaStatus] = useState<'uninitialized' | 'loading' | 'connected' | 'error'>('uninitialized');
  const [installedOllamaModels, setInstalledOllamaModels] = useState<OllamaTag[]>([]);
  const [selectedOllamaModel, setSelectedOllamaModel] = useState<string | null>(null);
  const [selectedOpenAIModel, setSelectedOpenAIModel] = useState<string>("gpt-3.5-turbo"); // Default OpenAI model
  const [selectedOpenRouterModel, setSelectedOpenRouterModel] = useState<string>(""); // Default OpenRouter model (user input)
  const [selectedAnthropicModel, setSelectedAnthropicModel] = useState<string>("claude-3-haiku-20240307"); // Default Anthropic model
  
  const [openaiApiKey, setOpenaiApiKey] = useState<string>("");
  const [openrouterApiKey, setOpenrouterApiKey] = useState<string>("");
  const [anthropicApiKey, setAnthropicApiKey] = useState<string>("");

  const [internalPopoverOpen, setInternalPopoverOpen] = useState(false);
  // Use controlled or uncontrolled mode
  const popoverOpen = typeof isPopoverOpen === 'boolean' ? isPopoverOpen : internalPopoverOpen;
  const handlePopoverChange = setIsPopoverOpen ? setIsPopoverOpen : setInternalPopoverOpen;

  // Load initial state from localStorage
  useEffect(() => {
    const storedProvider = localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY) as AiProvider | null;
    if (storedProvider) {
      setSelectedProvider(storedProvider);
    } else {
      // No provider stored, so set the default 'ollama' and also store it.
      const defaultProvider = 'ollama';
      setSelectedProvider(defaultProvider);
      localStorage.setItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY, defaultProvider);
      console.log('[AiConfigMenu] No provider in localStorage, defaulted to and stored:', defaultProvider);
    }

    const storedOllamaModel = localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY);
    if (storedOllamaModel) setSelectedOllamaModel(storedOllamaModel);
    
    const storedOpenAIModel = localStorage.getItem(LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY);
    if (storedOpenAIModel) setSelectedOpenAIModel(storedOpenAIModel);

    const storedOpenRouterModel = localStorage.getItem(LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY);
    if (storedOpenRouterModel) setSelectedOpenRouterModel(storedOpenRouterModel);

    const storedAnthropicModel = localStorage.getItem(LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY);
    if (storedAnthropicModel) setSelectedAnthropicModel(storedAnthropicModel);

    const storedOpenaiKey = localStorage.getItem(LOCAL_STORAGE_OPENAI_API_KEY);
    if (storedOpenaiKey) setOpenaiApiKey(storedOpenaiKey);

    const storedOpenrouterKey = localStorage.getItem(LOCAL_STORAGE_OPENROUTER_API_KEY);
    if (storedOpenrouterKey) setOpenrouterApiKey(storedOpenrouterKey);

    const storedAnthropicKey = localStorage.getItem(LOCAL_STORAGE_ANTHROPIC_API_KEY);
    if (storedAnthropicKey) setAnthropicApiKey(storedAnthropicKey);

    console.log('[AiConfigMenu] Initial state loaded from localStorage:', {
      provider: storedProvider,
      ollamaModel: storedOllamaModel,
      openAIModel: storedOpenAIModel,
      openRouterModel: storedOpenRouterModel,
      anthropicModel: storedAnthropicModel,
      openaiApiKey: storedOpenaiKey,
      openrouterApiKey: storedOpenrouterKey,
      anthropicApiKey: storedAnthropicKey,
    });
  }, []);

  // Fetch Ollama models when provider is Ollama and popover opens
  const fetchOllamaModels = useCallback(async () => {
    console.log(`[AiConfigMenu] fetchOllamaModels triggered. Current localStorage for ${LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY}:`, localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY));
    setOllamaStatus('loading');
    try {
      const response = await fetch(`${OLLAMA_API_BASE_URL}/tags`);
      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.statusText} (status: ${response.status})`);
      }
      const data = await response.json();
      const models: OllamaTag[] = data.models || [];
      setInstalledOllamaModels(models);
      setOllamaStatus('connected');

      console.log('[AiConfigMenu] fetchOllamaModels: Fetched models. Stored Ollama model in localStorage:', localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY));

      if (models.length > 0) {
        const currentModel = selectedOllamaModel;
        const storedModel = localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY);
        console.log(`[AiConfigMenu] fetchOllamaModels: Models available. Current component state ollamaModel: ${currentModel}, localStorage model: ${storedModel}`);
        if (storedModel && models.some(m => m.name === storedModel)) {
          setSelectedOllamaModel(storedModel);
        } else if (models.length > 0) {
          const defaultModel = models[0].name;
          setSelectedOllamaModel(defaultModel);
          console.log(`[AiConfigMenu] fetchOllamaModels: Setting localStorage ${LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY} to default: ${defaultModel}`);
          localStorage.setItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY, defaultModel);
        }
      } else {
        setSelectedOllamaModel(null); 
        console.warn('[AiConfigMenu] fetchOllamaModels: No Ollama models found. Removing item from localStorage. Key:', LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY);
        localStorage.removeItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY);
      }
      toast({
        title: "Ollama Connecté",
        description: models.length > 0 ? `${models.length} modèle(s) locaux trouvés.` : "Aucun modèle Ollama local n'a été trouvé.",
      });
    } catch (error: any) {
      console.error("Failed to fetch Ollama models:", error);
      setOllamaStatus('error');
      setInstalledOllamaModels([]);
      setSelectedOllamaModel(null);
      toast({
        title: "Erreur Connexion Ollama",
        description: `Impossible de joindre l'API Ollama locale. Détails: ${error.message}`,
        variant: "destructive",
      });
    }
  }, []);

  useEffect(() => {
    console.log(`[AiConfigMenu] useEffect for Ollama model fetch triggered. popoverOpen: ${popoverOpen}, selectedProvider: ${selectedProvider}, ollamaStatus: ${ollamaStatus}`);
    // Use popoverOpen derived from controlled/uncontrolled state
    if (popoverOpen && selectedProvider === 'ollama' && (ollamaStatus === 'uninitialized' || ollamaStatus === 'error')) {
      fetchOllamaModels();
    }
  }, [popoverOpen, selectedProvider, ollamaStatus, fetchOllamaModels]);

  const handleProviderChange = (providerName: AiProvider) => {
    setSelectedProvider(providerName);
    localStorage.setItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY, providerName);
    console.log('[AiConfigMenu] handleProviderChange: Provider changed to', providerName);
    toast({
      title: "Fournisseur IA Sélectionné",
      description: `Le fournisseur ${providerName.toUpperCase()} est maintenant actif.`
    });
    if (providerName === 'ollama' && (ollamaStatus === 'uninitialized' || ollamaStatus === 'error')) {
      fetchOllamaModels();
    }
  };

  const handleModelChange = (provider: AiProvider, modelName: string) => {
     switch(provider) {
        case 'ollama':
            setSelectedOllamaModel(modelName);
            console.log('[AiConfigMenu] handleModelChange (ollama): Attempting to set localStorage. Key:', LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY, 'Value:', modelName);
            localStorage.setItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY, modelName);
            console.log('[AiConfigMenu] handleModelChange (ollama): localStorage.setItem called. Current value:', localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY));
            break;
        case 'openai':
            setSelectedOpenAIModel(modelName);
            localStorage.setItem(LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY, modelName);
            break;
        case 'openrouter':
            setSelectedOpenRouterModel(modelName);
            localStorage.setItem(LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY, modelName);
            break;
        case 'anthropic':
            setSelectedAnthropicModel(modelName);
            localStorage.setItem(LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY, modelName);
            break;
     }
     console.log('[AiConfigMenu] handleModelChange: Model changed for provider', provider, 'to', modelName);
     toast({
        title: `Modèle ${provider.toUpperCase()} Sélectionné`,
        description: `Le modèle ${modelName} est maintenant actif pour ${provider.toUpperCase()}.`
      });
  };

  const handleApiKeyChange = (provider: AiProvider, key: string) => {
    switch(provider) {
      case 'openai':
        setOpenaiApiKey(key);
        localStorage.setItem(LOCAL_STORAGE_OPENAI_API_KEY, key);
        break;
      case 'openrouter':
        setOpenrouterApiKey(key);
        localStorage.setItem(LOCAL_STORAGE_OPENROUTER_API_KEY, key);
        break;
      case 'anthropic':
        setAnthropicApiKey(key);
        localStorage.setItem(LOCAL_STORAGE_ANTHROPIC_API_KEY, key);
        break;
    }
    console.log('[AiConfigMenu] handleApiKeyChange: API key changed for provider', provider, 'to', key);
    if(key && key.length > 10) { // Simple check if key seems valid enough to save
        toast({ title: `Clé API ${provider.toUpperCase()} Enregistrée`, description: "La clé a été sauvegardée localement." });
    } else if (!key) {
        toast({ title: `Clé API ${provider.toUpperCase()} Supprimée`, variant: "destructive" });
    }
  };

  const getOllamaStatusIconAndColor = () => {
    if (selectedProvider !== 'ollama') return { icon: <Brain className="h-5 w-5 text-gray-400"/>, color: 'bg-gray-400'}; // Neutral when Ollama not active provider
    switch (ollamaStatus) {
      case 'connected':
        return { icon: <CheckCircle className="h-5 w-5 text-green-500" />, color: 'bg-green-500' };
      case 'error':
        return { icon: <XCircle className="h-5 w-5 text-red-500" />, color: 'bg-red-500' };
      case 'loading':
        return { icon: <Loader2 className="h-5 w-5 animate-spin text-blue-500" />, color: 'bg-blue-500' };
      default:
        return { icon: <AlertTriangle className="h-5 w-5 text-yellow-500" />, color: 'bg-yellow-500' };
    }
  };

  const { icon: statusIcon, color: statusDotColor } = getOllamaStatusIconAndColor();

  // Contenu partagé (menu config IA)
  const renderConfigContent = () => (
    <div className={`grid gap-4 ${inlineMode ? 'shadow-none border-none p-0 bg-transparent' : 'p-4'}`}>
      <div className="space-y-2">
        <h4 className="font-medium leading-none">Configuration IA</h4>
        <p className="text-sm text-muted-foreground">
          Gérez les fournisseurs IA et leurs configurations.
        </p>
      </div>

      <div className="grid gap-3">
        <div>
          <Label htmlFor="ai-provider" className="text-sm font-medium">Fournisseur IA Actif</Label>
          <Select value={selectedProvider} onValueChange={handleProviderChange as (value: string) => void}> 
            <SelectTrigger id="ai-provider" className="w-full mt-1">
              <SelectValue placeholder="Sélectionner un fournisseur" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ollama">Ollama (Local)</SelectItem>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="openrouter">OpenRouter</SelectItem>
              <SelectItem value="anthropic">Anthropic</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* --- Ollama Specific --- */}
        {selectedProvider === 'ollama' && (
          <div className="border p-3 rounded-md space-y-3">
            <div className="flex items-center justify-between">
                <Label htmlFor="ollama-status" className="text-sm font-medium flex items-center"><Brain className="h-4 w-4 mr-2"/>Statut Ollama</Label>
                <div className="flex items-center gap-2">
                    {statusIcon}
                    <span id="ollama-status" className="text-sm capitalize">
                    {ollamaStatus === 'uninitialized' ? 'Non Vérifié' : ollamaStatus}
                    </span>
                </div>
            </div>
            {ollamaStatus === 'connected' && (
              <>
                {installedOllamaModels.length > 0 ? (
                  <div className="space-y-1">
                    <Label htmlFor="ollama-model" className="text-sm font-medium">Modèle Ollama Actif</Label>
                    <Select value={selectedOllamaModel || ""} onValueChange={(value) => handleModelChange('ollama', value)}>
                      <SelectTrigger id="ollama-model" className="w-full">
                        <SelectValue placeholder="Sélectionner un modèle Ollama" />
                      </SelectTrigger>
                      <SelectContent>
                        {installedOllamaModels.map((model) => (
                          <SelectItem key={model.name} value={model.name}>
                            {model.name} ({model.details?.parameter_size || 'N/A'})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground p-2 border rounded-md bg-accent/50 flex items-start">
                      <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" /> 
                      <span>Ollama est connecté, mais aucun modèle n'est installé.</span>
                  </p>
                )}
              </>
            )}
            {ollamaStatus === 'error' && (
              <div className="text-sm text-red-700 dark:text-red-400 p-3 border border-red-700/30 rounded-md bg-red-500/10 flex items-start">
                <XCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" /> 
                <span>Erreur Ollama. <a href="https://ollama.com" target="_blank" rel="noopener noreferrer" className="underline font-semibold">Aide?</a></span>
              </div>
            )}
            <Button variant="outline" size="sm" onClick={fetchOllamaModels} disabled={ollamaStatus === 'loading'} className="w-full mt-1">
              <RefreshCw className={`mr-2 h-4 w-4 ${ollamaStatus === 'loading' ? 'animate-spin' : ''}`} />
              {ollamaStatus === 'loading' ? 'Vérification...' : 'Rafraîchir Modèles Ollama'}
            </Button>
          </div>
        )}

        {/* --- OpenAI Specific --- */}
        {selectedProvider === 'openai' && (
          <div className="border p-3 rounded-md space-y-2">
            <Label htmlFor="openai-key" className="text-sm font-medium flex items-center"><KeyRound className="h-4 w-4 mr-2"/>Clé API OpenAI</Label>
            <Input id="openai-key" type="password" placeholder="sk-..." value={openaiApiKey} onChange={(e) => handleApiKeyChange('openai', e.target.value)} />
             <div className="space-y-1">
                <Label htmlFor="openai-model" className="text-sm font-medium">Modèle OpenAI</Label>
                <Select value={selectedOpenAIModel} onValueChange={(value) => handleModelChange('openai', value)}>
                    <SelectTrigger id="openai-model" className="w-full">
                    <SelectValue placeholder="Sélectionner un modèle OpenAI" />
                    </SelectTrigger>
                    <SelectContent>
                    {openAIModels.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                        {model.label}
                        </SelectItem>
                    ))}
                    </SelectContent>
                </Select>
             </div>
             {/* Placeholder for cost */}
             <div className="text-xs text-muted-foreground flex items-center gap-1 pt-1">
                <DollarSign className="h-3 w-3"/> Coût estimé: (Information non disponible)
             </div>
          </div> 
        )}

        {/* --- OpenRouter Specific --- */}
        {selectedProvider === 'openrouter' && (
          <div className="border p-3 rounded-md space-y-2">
            <Label htmlFor="openrouter-key" className="text-sm font-medium flex items-center"><KeyRound className="h-4 w-4 mr-2"/>Clé API OpenRouter</Label>
            <Input id="openrouter-key" type="password" placeholder="sk-or-..." value={openrouterApiKey} onChange={(e) => handleApiKeyChange('openrouter', e.target.value)} />
             <div className="space-y-1">
                <Label htmlFor="openrouter-model" className="text-sm font-medium">Modèle OpenRouter</Label>
                <Input 
                    id="openrouter-model" 
                    placeholder="Ex: mistralai/mistral-7b-instruct" 
                    value={selectedOpenRouterModel} 
                    onChange={(e) => handleModelChange('openrouter', e.target.value)} 
                />
                <p className="text-xs text-muted-foreground">Entrez l'identifiant complet du modèle.</p>
             </div>
             {/* Placeholder for cost */}
             <div className="text-xs text-muted-foreground flex items-center gap-1 pt-1">
                <DollarSign className="h-3 w-3"/> Coût estimé: (Voir site OpenRouter)
             </div>
          </div>
        )}

        {/* --- Anthropic Specific --- */}
        {selectedProvider === 'anthropic' && (
          <div className="border p-3 rounded-md space-y-2">
            <Label htmlFor="anthropic-key" className="text-sm font-medium flex items-center"><KeyRound className="h-4 w-4 mr-2"/>Clé API Anthropic</Label>
            <Input id="anthropic-key" type="password" placeholder="sk-ant-..." value={anthropicApiKey} onChange={(e) => handleApiKeyChange('anthropic', e.target.value)} />
             <div className="space-y-1">
                <Label htmlFor="anthropic-model" className="text-sm font-medium">Modèle Anthropic</Label>
                 <Select value={selectedAnthropicModel} onValueChange={(value) => handleModelChange('anthropic', value)}>
                    <SelectTrigger id="anthropic-model" className="w-full">
                    <SelectValue placeholder="Sélectionner un modèle Anthropic" />
                    </SelectTrigger>
                    <SelectContent>
                    {anthropicModels.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                        {model.label}
                        </SelectItem>
                    ))}
                    </SelectContent>
                </Select>
             </div>
             {/* Placeholder for cost */}
             <div className="text-xs text-muted-foreground flex items-center gap-1 pt-1">
                <DollarSign className="h-3 w-3"/> Coût estimé: (Information non disponible)
             </div>
          </div>
        )}

      </div>
    </div>
  );

  if (inlineMode) {
    return (
      <div className="ai-config-inline-menu w-full">
        {renderConfigContent()}
      </div>
    );
  }

  return (
    <Popover open={popoverOpen} onOpenChange={handlePopoverChange}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Cog className="h-5 w-5" />
          {selectedProvider === 'ollama' && (
            <span className={`absolute -top-1 -right-1 h-3 w-3 rounded-full ${statusDotColor} border-2 border-background`} title={`Statut Ollama: ${ollamaStatus}`} />
          )}
          {selectedProvider !== 'ollama' && (
             <span className={`absolute -top-1 -right-1 h-3 w-3 rounded-full bg-purple-500 border-2 border-background`} title={`Fournisseur IA: ${selectedProvider.toUpperCase()}`} />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 z-50 ai-config-popover-content" side="bottom" align="end">
        {renderConfigContent()}
      </PopoverContent>
    </Popover>
  );
}
