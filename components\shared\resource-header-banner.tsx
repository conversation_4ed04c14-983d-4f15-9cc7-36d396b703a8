"use client";

import Image from 'next/image';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { UserCircle, Music2 } from 'lucide-react'; // Added Music2

interface ResourceHeaderBannerProps {
  mainImageUrl?: string | null; // For the main prominent image (e.g., album cover, playlist cover)
  backgroundImageUrl?: string | null; // For the large blurred background image (e.g., artist banner, playlist banner)
  avatarUrl?: string | null; // Kept for potential future use, but currently not used for primary image display
  defaultIcon?: React.ReactNode; // Icon if no image (e.g., Music2, Disc, ListMusic)
  resourceTypeLabel?: string; // Made optional as requested to remove "Morceau" label
  title: string;
  artistName?: string | null;
  artistLink?: string | null;
  details?: React.ReactNode; // For release date, song count, duration etc.
  actionButtons?: React.ReactNode; // Slot for Like, Share, Edit buttons (plus Play si besoin)
  stats?: React.ReactNode; // Slot for like count, view count etc.
  description?: string | null; // Optional description to display in the header
  topRightSlot?: React.ReactNode; // New prop for content in the top-right corner
  musicalInfoLine?: React.ReactNode; // Ligne d'infos musicales (key, bpm, time_signature, capo)
  genreBadges?: React.ReactNode; // Slot for genre badges under artist name
  waveform?: React.ReactNode; // Slot pour la waveform/audio player
}

export function ResourceHeaderBanner({
  mainImageUrl,
  backgroundImageUrl,
  // avatarUrl, // Currently not used for primary image display
  defaultIcon,
  // resourceTypeLabel, // Removed as requested
  title,
  artistName,
  artistLink,
  details, // Used for genre tags primarily now in this component
  actionButtons,
  stats,
  description,
  topRightSlot, // For Public/Private toggle
  musicalInfoLine,
  genreBadges,
  waveform,
}: ResourceHeaderBannerProps) {
  // mainImageUrl and backgroundImageUrl are now direct props

  return (
    <header 
      className="relative py-10 md:py-12 lg:py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-background/30 to-background"
    >
      {backgroundImageUrl && (
        <div className="absolute inset-0 overflow-hidden z-0">
          <Image
            src={backgroundImageUrl}
            alt={`Fond pour ${title}`}
            fill
            className="object-cover blur scale-125 opacity-70 dark:opacity-60"
            priority
          />
          <div className="absolute inset-0 bg-background/50 backdrop-blur-sm"></div>
        </div>
      )}

      {/* Public/Private Toggle - Top Right of Header */}
      {topRightSlot && (
        <div className="absolute top-4 right-4 md:top-6 md:right-6 z-20">
          {topRightSlot}
        </div>
      )}

      <div className="relative z-10 flex flex-col md:flex-row items-center md:items-start gap-6 md:gap-8 max-w-6xl mx-auto">
        {/* Left Column: Cover Art */}
        <div className="w-48 h-48 md:w-56 md:h-56 lg:w-64 lg:h-64 flex-shrink-0 mt-4 md:mt-0">
          {mainImageUrl ? (
            <Image
              src={mainImageUrl}
              alt={`Image pour ${title}`}
              width={256}
              height={256}
              className="rounded-lg shadow-xl aspect-square object-cover border-2 border-background/50"
              priority
            />
          ) : (
            <div className="w-full h-full bg-muted rounded-lg shadow-xl flex items-center justify-center border-2 border-background/50">
              {defaultIcon || <Music2 className="w-24 h-24 text-muted-foreground" />}
            </div>
          )}
          {/* Stats moved under cover art */}
          {stats && (
            <div className="mt-2 text-center w-full">
              {stats}
            </div>
          )}
        </div>

        {/* Right Column: Info, Waveform, Actions */}
        <div className="flex flex-col flex-grow w-full items-center md:items-start text-center md:text-left">
          {/* Top Section: Title, Artist, Musical Info, Genres */}
          <div className="w-full mb-3 md:mb-4">
            {/* resourceTypeLabel removed */}
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground drop-shadow-sm break-words">
              {title}
            </h1>
            {artistName && (
              <div className="flex flex-wrap items-center justify-center md:justify-start gap-x-2 gap-y-1 mt-1 mb-2 text-lg text-muted-foreground">
                <div className="flex items-center gap-2 hover:text-primary transition-colors">
                  <UserCircle className="w-5 h-5 flex-shrink-0" />
                  {artistLink ? (
                    <Link href={artistLink} className="hover:underline">
                      {artistName}
                    </Link>
                  ) : (
                    <span>{artistName}</span>
                  )}
                </div>
                {/* Render genre tags (details) inline here if they are badges */}
                {Array.isArray(details) && details.every(d => d?.type === Badge) && (
                  <div className="flex flex-wrap gap-1 items-center">
                    {details.map((detail, index) => <span key={index}>{detail}</span>)}
                  </div>
                )}
              </div>
            )}
            {genreBadges && (
              <div className="mt-2 flex flex-wrap gap-1 justify-center md:justify-start">
                {genreBadges}
              </div>
            )}
            {/* Render musicalInfoLine and non-badge details (if any) separately */}
            {(musicalInfoLine || (Array.isArray(details) && !details.every(d => d?.type === Badge))) && (
              <div className="flex flex-wrap gap-x-3 gap-y-1.5 mt-2 mb-1 justify-center md:justify-start items-center">
                {musicalInfoLine}
                {/* Render details here ONLY if they are not all badges (already rendered above) */}
                {Array.isArray(details) && !details.every(d => d?.type === Badge) && 
                  details.map((detail, index) => <span key={index}>{detail}</span>)
                }
                {/* If details is not an array, render it here */}
                {!Array.isArray(details) && details}
              </div>
            )}
            {description && (
              <p className="text-xs text-muted-foreground mt-2 max-w-lg line-clamp-2">
                {description}
              </p>
            )}
          </div>

          {/* Middle Section: Waveform Player */}
          {waveform && (
            <div className="w-full my-3 md:my-4 pr-0 md:pr-8 lg:pr-12">
              {waveform}
            </div>
          )}

          {/* Bottom Section: Action Buttons & Stats (Styled for discretion) */}
          <div className="flex flex-col items-center md:items-start gap-2 w-full mt-2">
            {actionButtons && (
              <div className="flex flex-wrap justify-center md:justify-start items-center gap-2">
                {/* Styling for buttons (size, variant) will be handled in page.tsx when passing this prop */}
                {actionButtons}
              </div>
            )}
            {/* Stats have been moved under the cover art */}
          </div>
        </div>
      </div>
    </header>
  );
}
