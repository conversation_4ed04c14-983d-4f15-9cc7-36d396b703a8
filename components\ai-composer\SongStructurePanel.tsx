'use client';

import React, { useState, useCallback, useMemo, memo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Music, Plus, Trash2, Edit3, ArrowUp, ArrowDown,
  Play, Clock, BarChart3, Target, Shuffle
} from 'lucide-react';

interface SongSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  duration?: number;
  key?: string;
  tempo?: number;
  chords?: string[];
}

interface SongStructure {
  sections: SongSection[];
  totalDuration: number;
  key: string;
  tempo: number;
  timeSignature: string;
}

interface SongStructurePanelProps {
  sections: any[]; // TODO: Reconcile LyricsSection[] with SongSection[] or adapt component
  onSectionsUpdate?: (sections: any[]) => void; // Was onStructureChange
  onSectionSelect?: (sectionId: string) => void;
  selectedSectionId?: string; // Prop being passed from layout is 'selectedSection'
  currentSongId?: string; // Prop being passed from layout
}

const SECTION_TYPES = [
  { value: 'intro', label: 'Intro', color: 'bg-blue-100 text-blue-800' },
  { value: 'verse', label: 'Couplet', color: 'bg-green-100 text-green-800' },
  { value: 'pre-chorus', label: 'Pré-refrain', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'chorus', label: 'Refrain', color: 'bg-red-100 text-red-800' },
  { value: 'bridge', label: 'Pont', color: 'bg-purple-100 text-purple-800' },
  { value: 'outro', label: 'Outro', color: 'bg-gray-100 text-gray-800' },
  { value: 'coda', label: 'Coda', color: 'bg-indigo-100 text-indigo-800' }
] as const;

const DEFAULT_STRUCTURE: SongStructure = {
  sections: [
    { id: '1', type: 'intro', title: 'Intro', duration: 8 },
    { id: '2', type: 'verse', title: 'Couplet 1', duration: 16 },
    { id: '3', type: 'chorus', title: 'Refrain', duration: 16 },
    { id: '4', type: 'verse', title: 'Couplet 2', duration: 16 },
    { id: '5', type: 'chorus', title: 'Refrain', duration: 16 },
    { id: '6', type: 'outro', title: 'Outro', duration: 8 }
  ],
  totalDuration: 80,
  key: 'C',
  tempo: 120,
  timeSignature: '4/4'
};

const SongStructurePanelComponentInternal = ({
  sections,
  onSectionsUpdate,
  onSectionSelect,
  selectedSectionId,
  currentSongId
}: SongStructurePanelProps) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);

  const getSectionTypeInfo = useCallback((type: string) => {
    return SECTION_TYPES.find(t => t.value === type) || SECTION_TYPES[0];
  }, []);

  const addSection = useCallback((type: SongSection['type']) => {
    const newSectionToAdd: SongSection = {
      id: Date.now().toString(),
      type,
      title: getSectionTypeInfo(type).label,
      duration: 16
    };
    onSectionsUpdate?.([...sections, newSectionToAdd]);
  }, [sections, onSectionsUpdate, getSectionTypeInfo]);

  const removeSection = useCallback((sectionId: string) => {
    const updatedSections = sections.filter((s: any) => s.id !== sectionId);
    onSectionsUpdate?.(updatedSections);
  }, [sections, onSectionsUpdate]);

  const moveSectionUp = useCallback((index: number) => {
    if (index === 0) return;
    
    const newSectionsArray = [...sections];
    [newSectionsArray[index - 1], newSectionsArray[index]] = [newSectionsArray[index], newSectionsArray[index - 1]];
    onSectionsUpdate?.(newSectionsArray);
  }, [sections, onSectionsUpdate]);

  const moveSectionDown = useCallback((index: number) => {
    if (index === sections.length - 1) return;
    
    const newSectionsArray = [...sections];
    [newSectionsArray[index], newSectionsArray[index + 1]] = [newSectionsArray[index + 1], newSectionsArray[index]];
    onSectionsUpdate?.(newSectionsArray);
  }, [sections, onSectionsUpdate]);

  const formatDuration = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Music className="h-5 w-5" />
            Structure de la Chanson
          </CardTitle>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            {formatDuration(sections.reduce((sum, s: any) => sum + (s.duration || 0), 0))}
          </div>
        </div>
        
        <div className="flex items-center gap-4 text-sm">
          <Badge variant="outline">N/A</Badge>
          <Badge variant="outline">N/A BPM</Badge>
          <Badge variant="outline">N/A</Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Section Templates */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Ajouter une section</h4>
          <div className="flex flex-wrap gap-2">
            {SECTION_TYPES.map((type) => (
              <Button
                key={type.value}
                variant="outline"
                size="sm"
                onClick={() => addSection(type.value as SongSection['type'])}
                className="h-8"
              >
                <Plus className="h-3 w-3 mr-1" />
                {type.label}
              </Button>
            ))}
          </div>
        </div>
        
        <Separator />
        
        {/* Structure Timeline */}
        <ScrollArea className="h-[400px]">
          <div className="space-y-2">
            {sections.map((section: any, index: number) => {
              const typeInfo = getSectionTypeInfo(section.type);
              const isSelected = selectedSectionId === section.id;
              
              return (
                <div
                  key={section.id}
                  className={`p-3 rounded-lg border transition-all cursor-pointer ${
                    isSelected 
                      ? 'border-primary bg-primary/5 shadow-sm' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => onSectionSelect?.(section.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex flex-col gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            moveSectionUp(index);
                          }}
                          disabled={index === 0}
                          className="h-6 w-6 p-0"
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            moveSectionDown(index);
                          }}
                          disabled={index === sections.length - 1}
                          className="h-6 w-6 p-0"
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge className={typeInfo.color}>
                          {typeInfo.label}
                        </Badge>
                        <span className="font-medium">{section.title}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {formatDuration(section.duration || 0)}
                      </span>
                      
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingSection(section.id);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Edit3 className="h-3 w-3" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeSection(section.id);
                          }}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {section.chords && section.chords.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {section.chords.map((chord: string, i: number) => (
                        <Badge key={i} variant="secondary" className="text-xs">
                          {chord}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>
        
        {/* Quick Actions */}
        <Separator />
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="flex-1">
            <Shuffle className="h-4 w-4 mr-2" />
            Réorganiser
          </Button>
          
          <Button variant="outline" size="sm" className="flex-1">
            <Target className="h-4 w-4 mr-2" />
            Optimiser
          </Button>
          
          <Button variant="outline" size="sm" className="flex-1">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analyser
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Mémoisation du composant pour éviter les re-rendus inutiles
export const SongStructurePanel = memo(SongStructurePanelComponentInternal);