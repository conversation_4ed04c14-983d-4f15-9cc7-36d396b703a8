'use client'

import React, { useCallback, useState, useEffect, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { SupabaseClient } from '@supabase/supabase-js'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { FileAudio, Trash2, UploadCloud, Eye, EyeOff, AlertTriangle } from 'lucide-react' // Added AlertTriangle
import { AudioSliderPlayer, randomColorFromString } from './audio-slider-player'
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from '@/components/ui/collapsible'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useUser } from '@/contexts/user-context';
import { usePlanLimits } from '@/hooks/use-plan-limits';
import { useUserUsageStats } from '@/hooks/use-user-usage-stats';

interface VaultItem {
  id: string;
  db_id?: string;
  file?: File;
  fileName: string;
  storage_path?: string;
  previewUrl: string;
  annotations: string;
  color?: string;
  is_public: boolean;
  file_size?: number;
  mime_type?: string;
  isPlaying?: boolean;
  waveformError?: string;
  isUploading?: boolean;
}

export interface SongVaultActions {
  savePendingItems: (songIdToLink: string, userIdToLink: string) => Promise<void>;
  addFileToVault: (file: File, fileName?: string) => void;
}

interface SongVaultProps {
  songId?: string;
  userId?: string;
  supabaseClient: SupabaseClient;
  onReady?: (actions: SongVaultActions) => void;
}

export default function SongVault({ songId, userId: propUserId, supabaseClient, onReady }: SongVaultProps) {
  const { user:authUser } = useUser(); 
  const userIdToUse = propUserId || authUser?.id; 

  const { limits: planLimits, isLoading: isLoadingPlanLimits } = usePlanLimits();
  const { stats: usageStats, isLoading: isLoadingUsageStats, refreshUsageStats } = useUserUsageStats();

  const [vaultItems, setVaultItems] = useState<VaultItem[]>([]);
  const [currentVaultInstanceSize, setCurrentVaultInstanceSize] = useState(0);

  // Determine effective vault storage limit
  const customVaultLimitGB = authUser?.custom_vault_space_gb;
  const planVaultLimitGB = planLimits?.vault_space_gb;
  let effectiveVaultLimitGB: number | null | undefined = undefined;
  if (customVaultLimitGB !== undefined && customVaultLimitGB !== null) {
    effectiveVaultLimitGB = customVaultLimitGB;
  } else if (planLimits) {
    effectiveVaultLimitGB = planVaultLimitGB;
  }
  const effectiveVaultLimitBytes = effectiveVaultLimitGB !== null && effectiveVaultLimitGB !== undefined 
    ? effectiveVaultLimitGB * 1024 * 1024 * 1024 
    : null; 
  const currentTotalUserVaultUsageBytes = usageStats?.vaultUsageGB ? usageStats.vaultUsageGB * 1024 * 1024 * 1024 : 0;
  const canUploadMoreStorage = effectiveVaultLimitBytes === null || currentTotalUserVaultUsageBytes < effectiveVaultLimitBytes;
  const remainingBytes = effectiveVaultLimitBytes !== null ? Math.max(0, effectiveVaultLimitBytes - currentTotalUserVaultUsageBytes) : Infinity;

  // Determine effective vault file count limit
  const customVaultMaxFiles = authUser?.custom_vault_max_files;
  const planVaultMaxFiles = planLimits?.vault_max_files;
  
  let determinedVaultMaxFiles: number | null;
  if (customVaultMaxFiles !== undefined && customVaultMaxFiles !== null) {
    determinedVaultMaxFiles = customVaultMaxFiles; // User's custom override (can be number or null for unlimited)
  } else if (planLimits && planLimits.vault_max_files !== undefined) {
    determinedVaultMaxFiles = planLimits.vault_max_files; // Plan limit (can be number or null for unlimited)
  } else {
    // Fallback if planLimits are null or vault_max_files is not on the plan object.
    // This state should ideally be caught by isLoadingPlanLimits, but as a safeguard:
    determinedVaultMaxFiles = 0; // Default to 0 if limit is unknown after loading.
  }

  const canUploadMoreFiles = 
    determinedVaultMaxFiles === null || // Unlimited
    (typeof determinedVaultMaxFiles === 'number' && vaultItems.length < determinedVaultMaxFiles);


  const savePendingItems = useCallback(async (songIdToLink: string, userIdToLink: string) => {
    // ... (savePendingItems logic remains the same)
    const pendingItems = vaultItems.filter(item => item.file && !item.db_id);
    if (pendingItems.length === 0) return;
    toast({ title: "Sauvegarde du vault...", description: `Téléversement de ${pendingItems.length} fichier(s) en cours.`});
    for (const item of pendingItems) {
      if (item.file && !item.waveformError) { 
        setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: true } : i));
        const filePathInStorage = `public/${userIdToLink}/${songIdToLink}/${crypto.randomUUID()}-${item.fileName}`;
        try {
          const { error: uploadError } = await supabaseClient.storage.from('song-audio-vault').upload(filePathInStorage, item.file, { cacheControl: '3600', upsert: false });
          if (uploadError) throw uploadError;
          const { data: publicUrlData } = supabaseClient.storage.from('song-audio-vault').getPublicUrl(filePathInStorage);
          const newDbRecordPayload = { song_id: songIdToLink, user_id: userIdToLink, file_name: item.fileName, storage_path: filePathInStorage, annotations: item.annotations, color: item.color, is_public: item.is_public, file_size: item.file_size, mime_type: item.mime_type, };
          const { data: insertedRecord, error: insertError } = await supabaseClient.from('song_vault_items').insert(newDbRecordPayload).select().single();
          if (insertError) throw insertError;
          setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, db_id: insertedRecord.id, storage_path: filePathInStorage, previewUrl: publicUrlData.publicUrl || i.previewUrl, isUploading: false, file: undefined } : i));
        } catch (error: any) {
          console.error(`Error saving pending vault item ${item.fileName}:`, error);
          toast({ title: `Erreur avec ${item.fileName}`, description: error.message, variant: "destructive" });
          setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: false, waveformError: "Échec du téléversement différé" } : i));
        }
      }
    }
    toast({ title: "Vault sauvegardé!", description: "Tous les fichiers en attente ont été traités."});
  }, [vaultItems, supabaseClient]); 

  const addFileToVault = useCallback((file: File, fileName?: string) => {
    if (isLoadingPlanLimits || isLoadingUsageStats) {
      toast({ title: "Chargement des limites...", description: "Veuillez patienter.", variant: "default" });
      return;
    }
    if (!canUploadMoreStorage && effectiveVaultLimitBytes !== null) {
      toast({ title: 'Limite de stockage atteinte', description: 'Votre espace de stockage vault est plein.', variant: 'destructive' });
      return;
    }
    if (!canUploadMoreFiles && determinedVaultMaxFiles !== null) {
       toast({ title: 'Limite de fichiers atteinte', description: `Vous ne pouvez pas ajouter plus de ${determinedVaultMaxFiles} fichiers au vault.`, variant: 'destructive' });
       return;
    }
    if (effectiveVaultLimitBytes !== null && (currentTotalUserVaultUsageBytes + file.size) > effectiveVaultLimitBytes) {
      toast({ title: 'Espace insuffisant', description: `L'ajout de ce fichier dépasserait votre limite de stockage. Espace restant: ${(remainingBytes / (1024*1024)).toFixed(1)} Mo`, variant: 'destructive' });
      return;
    }
    if (determinedVaultMaxFiles !== null && (vaultItems.length + 1 > determinedVaultMaxFiles)) {
      toast({ title: 'Limite de fichiers atteinte', description: `Vous ne pouvez pas ajouter plus de ${determinedVaultMaxFiles} fichiers au vault.`, variant: 'destructive' });
      return;
    }

    const actualFileName = fileName || file.name;
    const isMp3 = file.type === 'audio/mpeg' || actualFileName.toLowerCase().endsWith('.mp3');
    const newItemId = `${actualFileName}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    let newItem: VaultItem;

    if (!isMp3) {
      newItem = {
        id: newItemId, file, fileName: actualFileName, previewUrl: '', annotations: '', is_public: false,
        file_size: file.size, mime_type: file.type, waveformError: 'Fichier non supporté (MP3 uniquement)', isUploading: false,
      };
    } else {
      newItem = {
        id: newItemId, file, fileName: actualFileName, previewUrl: URL.createObjectURL(file), annotations: '',
        color: randomColorFromString(actualFileName), is_public: false, file_size: file.size, mime_type: file.type, 
        isUploading: true, 
      };
    }

    setVaultItems(prevItems => [...prevItems, newItem]);
    setCurrentVaultInstanceSize(prevSize => prevSize + (file.size || 0));

    if (songId && userIdToUse && newItem.file && !newItem.waveformError) {
      const uploadAndSave = async () => {
        const filePathInStorage = `public/${userIdToUse}/${songId}/${crypto.randomUUID()}-${newItem.fileName}`;
        try {
          const { error: uploadError } = await supabaseClient.storage.from('song-audio-vault').upload(filePathInStorage, newItem.file!, { cacheControl: '3600', upsert: false });
          if (uploadError) throw uploadError;
          const { data: publicUrlData } = supabaseClient.storage.from('song-audio-vault').getPublicUrl(filePathInStorage);
          const newDbRecordPayload = {
            song_id: songId,
            user_id: userIdToUse,
            file_name: newItem.fileName,
            storage_path: filePathInStorage,
            annotations: newItem.annotations,
            color: newItem.color,
            is_public: newItem.is_public,
            file_size: newItem.file_size,
            mime_type: newItem.mime_type,
          };
          const { data: insertedRecord, error: insertError } = await supabaseClient.from('song_vault_items').insert(newDbRecordPayload).select().single();
          if (insertError) throw insertError;
          setVaultItems(prev => prev.map(i => i.id === newItem.id ? { ...i, db_id: insertedRecord.id, storage_path: filePathInStorage, previewUrl: publicUrlData.publicUrl || i.previewUrl, isUploading: false, file: undefined } : i));
          toast({ title: "Fichier ajouté au vault!", description: `${newItem.fileName} a été téléversé et sauvegardé.`});
          refreshUsageStats(); // Refresh usage stats after successful upload
        } catch (error: any) {
          console.error(`Error saving new vault item ${newItem.fileName}:`, error);
          toast({ title: `Erreur avec ${newItem.fileName}`, description: error.message, variant: "destructive" });
          setVaultItems(prev => prev.map(i => i.id === newItem.id ? { ...i, isUploading: false, waveformError: "Échec du téléversement" } : i));
        }
      };
      uploadAndSave();
    } else if (newItem.file && !newItem.waveformError) {
      // File is valid but songId/userId not available, will be saved by savePendingItems
      toast({ title: "Fichier prêt pour le vault", description: `${newItem.fileName} sera sauvegardé avec le morceau.`});
    } else if (newItem.waveformError) {
        toast({ title: "Fichier non ajouté", description: `${newItem.fileName}: ${newItem.waveformError}`, variant: "destructive"});
    }
  }, [songId, userIdToUse, supabaseClient, vaultItems, canUploadMoreStorage, canUploadMoreFiles, effectiveVaultLimitBytes, currentTotalUserVaultUsageBytes, determinedVaultMaxFiles, remainingBytes, isLoadingPlanLimits, isLoadingUsageStats, refreshUsageStats, setCurrentVaultInstanceSize]);

  useEffect(() => { if (onReady) { onReady({ savePendingItems, addFileToVault }); } }, [onReady, savePendingItems, addFileToVault]);

  useEffect(() => {
    // ... (fetchVaultItems logic remains the same)
    const fetchVaultItems = async () => {
      if (!songId || !userIdToUse) { setVaultItems([]); setCurrentVaultInstanceSize(0); return; }
      try {
        const { data, error } = await supabaseClient.from('song_vault_items').select('*').eq('song_id', songId).eq('user_id', userIdToUse).order('created_at', { ascending: true });
        if (error) { console.error('Error fetching vault items:', error); toast({ title: "Erreur de chargement du vault", description: error.message, variant: "destructive" }); setVaultItems([]); setCurrentVaultInstanceSize(0); return; }
        if (data) {
          const fetchedItems: VaultItem[] = data.map(dbItem => { const { data: publicUrlData } = supabaseClient.storage.from('song-audio-vault').getPublicUrl(dbItem.storage_path); return { id: dbItem.id, db_id: dbItem.id, fileName: dbItem.file_name, storage_path: dbItem.storage_path, previewUrl: publicUrlData?.publicUrl || '', annotations: dbItem.annotations || '', color: dbItem.color, is_public: dbItem.is_public || false, file_size: dbItem.file_size, mime_type: dbItem.mime_type, isPlaying: false, isUploading: false, }; });
          setVaultItems(fetchedItems);
          const currentInstanceSize = fetchedItems.reduce((acc, item) => acc + (item.file_size || 0), 0);
          setCurrentVaultInstanceSize(currentInstanceSize);
        }
      } catch (e: any) { console.error('Unexpected error fetching vault items:', e); toast({ title: "Erreur inattendue", description: "Impossible de charger les fichiers du vault.", variant: "destructive" }); setVaultItems([]); setCurrentVaultInstanceSize(0); }
    };
    fetchVaultItems();
  }, [songId, userIdToUse, supabaseClient]);

  const handlePlayPauseSingle = (id: string) => { /* ... */ };
  const updateVaultItemInDb = async (itemId: string, updates: Partial<VaultItem>) => { /* ... */ };
  const handleColorChange = (id: string, color: string) => { /* ... */ };
  const handleIsPublicChange = (id: string, isPublic: boolean) => { /* ... */ };
  const handleAnnotationChange = (id: string, value: string) => { /* ... */ };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (isLoadingPlanLimits || isLoadingUsageStats) {
      toast({ title: "Chargement des limites...", description: "Veuillez patienter.", variant: "default" });
      return;
    }
    if (!canUploadMoreStorage && effectiveVaultLimitBytes !== null) { // Check storage limit first
      toast({ title: 'Limite de stockage atteinte', description: 'Votre espace de stockage vault est plein.', variant: 'destructive' });
      return;
    }
    // Use determinedVaultMaxFiles which is guaranteed to be number or null here
    if (!canUploadMoreFiles && determinedVaultMaxFiles !== null) { 
       toast({ title: 'Limite de fichiers atteinte', description: `Vous ne pouvez pas ajouter plus de ${determinedVaultMaxFiles} fichiers au vault.`, variant: 'destructive' });
       return;
    }

    let newFilesSize = 0;
    acceptedFiles.forEach(file => newFilesSize += file.size);

    if (effectiveVaultLimitBytes !== null && (currentTotalUserVaultUsageBytes + newFilesSize) > effectiveVaultLimitBytes) {
      toast({ title: 'Espace insuffisant', description: `L'ajout de ces fichiers dépasserait votre limite de stockage. Espace restant: ${(remainingBytes / (1024*1024)).toFixed(1)} Mo`, variant: 'destructive' });
      return;
    }
    
    let currentVaultInstanceSizeTracker = currentVaultInstanceSize;
    const newLocalItems: VaultItem[] = []; 

    for (const file of acceptedFiles) { 
      // Use determinedVaultMaxFiles here
      if (determinedVaultMaxFiles !== null && (vaultItems.length + newLocalItems.length >= determinedVaultMaxFiles)) {
        toast({ title: 'Limite de fichiers atteinte', description: `Vous ne pouvez pas ajouter plus de ${determinedVaultMaxFiles} fichiers au vault.`, variant: 'destructive' });
        break; 
      }
      
      const isMp3 = file.type === 'audio/mpeg' || file.name.toLowerCase().endsWith('.mp3');
      const newItemId = `${file.name}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

      if (!isMp3) {
        newLocalItems.push({
          id: newItemId, file, fileName: file.name, previewUrl: '', annotations: '', is_public: false,
          file_size: file.size, mime_type: file.type, waveformError: 'Fichier non supporté (MP3 uniquement)', isUploading: false,
        });
      } else {
        currentVaultInstanceSizeTracker += file.size; 
        newLocalItems.push({
          id: newItemId, file, fileName: file.name, previewUrl: URL.createObjectURL(file), annotations: '',
          color: randomColorFromString(file.name), is_public: false, file_size: file.size, mime_type: file.type, 
          isUploading: true, 
        });
      }
    }

    setVaultItems(prevItems => [...prevItems, ...newLocalItems]);
    setCurrentVaultInstanceSize(currentVaultInstanceSizeTracker); 

    if (songId && userIdToUse) { 
      newLocalItems.forEach(async (item) => {
        if (item.file && !item.waveformError) {
          setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: true } : i));
          const filePathInStorage = `public/${userIdToUse}/${songId}/${crypto.randomUUID()}-${item.fileName}`;
          try {
            const { error: uploadError } = await supabaseClient.storage.from('song-audio-vault').upload(filePathInStorage, item.file, { cacheControl: '3600', upsert: false });
            if (uploadError) throw uploadError;
            const { data: publicUrlData } = supabaseClient.storage.from('song-audio-vault').getPublicUrl(filePathInStorage);
            const newDbRecordPayload = { song_id: songId, user_id: userIdToUse, file_name: item.fileName, storage_path: filePathInStorage, annotations: item.annotations, color: item.color, is_public: item.is_public, file_size: item.file_size, mime_type: item.mime_type, };
            const { data: insertedRecord, error: insertError } = await supabaseClient.from('song_vault_items').insert(newDbRecordPayload).select().single();
            if (insertError) throw insertError;
            setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, db_id: insertedRecord.id, storage_path: filePathInStorage, previewUrl: publicUrlData.publicUrl || i.previewUrl, isUploading: false, file: undefined } : i));
            toast({ title: "Fichier téléversé!", description: `${item.fileName} a été ajouté au vault.` });
            refreshUsageStats(); 
          } catch (error: any) {
            console.error(`Error processing vault item ${item.fileName}:`, error);
            toast({ title: `Erreur avec ${item.fileName}`, description: error.message, variant: "destructive" });
            setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: false, waveformError: "Échec du téléversement" } : i));
          }
        } else if (item.waveformError) {
           setVaultItems(prev => prev.map(i => i.id === item.id ? {...i, isUploading: false} : i));
        }
      });
    } else { 
      newLocalItems.forEach(item => { if (!item.waveformError) console.log(`Item ${item.fileName} added locally, isUploading: ${item.isUploading}, pending song save.`); });
    }
  }, [vaultItems, currentVaultInstanceSize, supabaseClient, songId, userIdToUse, isLoadingPlanLimits, isLoadingUsageStats, effectiveVaultLimitBytes, currentTotalUserVaultUsageBytes, remainingBytes, determinedVaultMaxFiles, refreshUsageStats, canUploadMoreFiles, canUploadMoreStorage]); // Added determinedVaultMaxFiles to dependencies

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop, 
    accept: { 'audio/mpeg': ['.mp3'] }, 
    multiple: true,
    disabled: (!canUploadMoreStorage && effectiveVaultLimitBytes !== null) || (!canUploadMoreFiles && determinedVaultMaxFiles !== null) || isLoadingPlanLimits || isLoadingUsageStats
  });

  const handleRemoveFile = async (id: string) => { /* ... (logic remains the same) ... */ };
  
  const displayVaultUsageGB = usageStats?.vaultUsageGB?.toFixed(1) || "0.0";
  const displayVaultLimitGB = effectiveVaultLimitGB?.toFixed(1) || (effectiveVaultLimitGB === null ? "Illimité" : "N/A");
  const displayVaultFileCountLimit = determinedVaultMaxFiles === null ? "Illimité" : determinedVaultMaxFiles?.toString() || "N/A";
  
  const storagePercentage = effectiveVaultLimitBytes && currentTotalUserVaultUsageBytes > 0 
    ? (currentTotalUserVaultUsageBytes / effectiveVaultLimitBytes) * 100 
    : 0;

  if (isLoadingPlanLimits || isLoadingUsageStats) {
    return <div className="p-4 text-center text-sm text-muted-foreground">Chargement du statut du vault...</div>;
  }

  return (
    <Collapsible defaultOpen className="w-full border rounded-lg shadow-sm bg-card dark:bg-zinc-900">
      <CollapsibleTrigger asChild>
        <div className="flex items-center justify-between px-4 py-3 border-b sticky top-0 bg-card z-10 cursor-pointer">
          <h3 className="font-semibold text-base">Song Vault</h3>
          <Button variant="ghost" size="icon" className="rounded-full h-7 w-7" aria-label="Toggle Vault Content">
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24" className="transition-transform duration-200 data-[state=open]:rotate-0 data-[state=closed]:-rotate-180">
              <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Button>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="flex flex-col flex-grow p-4 gap-3">
          <div className="flex flex-row flex-wrap gap-x-4 gap-y-1 items-center text-xs text-muted-foreground">
            <span>Fichiers (Vault Actuel): {vaultItems.length} / {displayVaultFileCountLimit}</span>
            <span>Utilisation Totale Vault: {displayVaultUsageGB} Go / {displayVaultLimitGB} Go</span>
          </div>
          {effectiveVaultLimitBytes !== null && <Progress value={storagePercentage} className="w-full h-2" />}
          {(!canUploadMoreStorage && effectiveVaultLimitBytes !== null) && (
            <div className="p-2 text-xs bg-destructive/10 text-destructive border border-destructive rounded-md flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Votre espace de stockage vault est plein.
            </div>
          )}
           {(!canUploadMoreFiles && determinedVaultMaxFiles !== null) && (
            <div className="p-2 text-xs bg-destructive/10 text-destructive border border-destructive rounded-md flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Vous avez atteint la limite du nombre de fichiers pour votre vault.
            </div>
          )}
          
          <div className="flex-grow overflow-y-auto space-y-3 mt-2 max-h-[calc(100vh-350px)] pr-1">
            {/* ... (vaultItems.map logic remains the same) ... */}
          </div>
          
          <div className="mt-auto pt-3">
            <div
              {...getRootProps()}
              className={`p-6 border-2 border-dashed rounded-md text-center cursor-pointer transition-colors
                          ${isDragActive ? 'border-primary bg-primary/10' : 'border-muted-foreground/30 hover:border-primary/70 hover:bg-muted/50'}
                          ${((!canUploadMoreStorage && effectiveVaultLimitBytes !== null) || (!canUploadMoreFiles && determinedVaultMaxFiles !== null)) ? 'opacity-50 cursor-not-allowed' : ''}
                        `}
            >
              <input {...getInputProps()} />
              <UploadCloud className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              {isDragActive ? (
                <p className="text-sm">Relâchez pour ajouter...</p>
              ) : (
                <p className="text-sm">Glissez des MP3 ici, ou cliquez.</p>
              )}
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
