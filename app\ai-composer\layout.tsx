import type React from "react";
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { ClientLayout } from "@/components/client-layout";
import { AppSidebar, type UserProfileForSidebar } from "@/components/sidebar";
import { redirect } from 'next/navigation';
import { SidebarManager } from './sidebar-manager';

export default async function AiComposerLayout({ children }: { children: React.ReactNode }) {
  const supabase = createSupabaseServerClient();

  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error('AI Composer Layout: User not authenticated, redirecting to login.');
    redirect('/login');
  }

  // Fetch profile data to construct userObj for AppSidebar
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('username, avatar_url, user_role, last_seen, settings')
    .eq('id', user.id)
    .single();

  if (profileError) {
    console.error(`Error fetching profile for AI Composer Layout:`, profileError);
    // We can still render the layout with partial data or a default state
  }

  const userForSidebar: UserProfileForSidebar | null = profile ? {
    id: user.id,
    username: profile.username,
    avatar_url: profile.avatar_url,
    user_role: profile.user_role,
  } : null;

  return (
      <ClientLayout initialUser={userForSidebar}>
      <SidebarManager defaultCollapsed={true} />
      <div className="flex h-full">
        <AppSidebar user={userForSidebar} />
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </ClientLayout>
  );
}
