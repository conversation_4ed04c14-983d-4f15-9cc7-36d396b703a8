"use client";
import { useEffect, useState } from "react";
import { getSupabaseClient } from "@/lib/supabase/client";
import { GlobalAudioPlayer } from "@/components/audio/global-audio-player";
import { useSidebar } from "@/components/ui/sidebar-engine";
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore";
import { usePathname } from "next/navigation";

export function AuthPlayerWrapper() {
  const [authenticated, setAuthenticated] = useState(false);
  const pathname = usePathname();
  const { currentSong } = useAudioPlayerStore();
  const { open: isSidebarActuallyOpen, isMobile } = useSidebar();

  useEffect(() => {
    const supabase = getSupabaseClient();
    supabase.auth.getSession().then(({ data: { session } }) => {
      setAuthenticated(!!session);
    });
    const { data: listener } = supabase.auth.onAuthStateChange((event, session) => {
      setAuthenticated(!!session);
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, []);

  const shouldPlayerComponentRender = authenticated && pathname !== "/" && !!currentSong;
  const isLeftSidebarOpen = isSidebarActuallyOpen && !isMobile;

  if (!shouldPlayerComponentRender) return null;
  
  return (
    <div
      style={{
        position: "fixed",
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000, // This z-index might be better on GlobalAudioPlayer itself
      }}
    >
      <GlobalAudioPlayer 
        isPlayerVisible={shouldPlayerComponentRender} // This prop controls animation
        isLeftSidebarOpen={isLeftSidebarOpen}
        isRightSidebarOpen={false} // Assuming no right sidebar for now
      />
    </div>
  );
}
