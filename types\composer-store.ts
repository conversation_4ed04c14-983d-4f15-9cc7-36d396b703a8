import { z } from 'zod';
import { ChordPlacementDisplay, ChordPlacementStorage } from '@/types/composer';
import { LayoutState } from '@/types/layout-state';
import { songSchema } from '@/components/songs/song-schema';

export type ChordPlacement = ChordPlacementDisplay | ChordPlacementStorage;

export interface ComposerStore {
  currentSong: z.infer<typeof songSchema> | null;
  unsavedChanges: boolean;
  isGeneratingAI: boolean;
  selectedChords: ChordPlacement[];
  currentProgression: ChordPlacement[];
  selectedInstrument: string;
  selectedTuning: string;
  layoutState: LayoutState;
  updateSongFields: (fields: Partial<z.infer<typeof songSchema>>) => void;
  hasUnsavedChanges: () => boolean;
  setCurrentSong: (song: z.infer<typeof songSchema> | null) => void;
  setUnsavedChanges: (value: boolean) => void;
  setIsGeneratingAI: (value: boolean) => void;
  setSelectedChords: (chords: ChordPlacement[]) => void;
  setCurrentProgression: (progression: ChordPlacement[]) => void;
  setSelectedInstrument: (instrument: string) => void;
  setSelectedTuning: (tuning: string) => void;
  setLayoutState: (state: Partial<LayoutState>) => void;
  updateSong: (song: Partial<z.infer<typeof songSchema>>) => void;
}
