"use server";

import { revalidatePath } from "next/cache";

export async function revalidateBandPaths(bandId: string, bandSlug: string | null) {
  try {
    if (bandSlug) {
      revalidatePath(`/bands/${bandSlug}`, 'page');
    }
    // Assuming the path /manage-bands/id/[id] was a temporary or incorrect structure
    // and the correct authenticated band page is /manage-bands/[id]
    revalidatePath(`/manage-bands/${bandId}`, 'page'); 
    revalidatePath('/manage-bands', 'page'); // Revalidate the listing page
    
    // If you also have a public listing page for all bands, e.g., /bands
    // revalidatePath('/bands', 'layout'); // or 'page' if it's a page

    return { success: true };
  } catch (error) {
    console.error("Error during revalidation:", error);
    return { success: false, error: "Revalidation failed" };
  }
}
