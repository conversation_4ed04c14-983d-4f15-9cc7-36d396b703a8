'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Maximize2,
  Minimize2,
  ListMusic,
} from 'lucide-react';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore';
import { useUser } from '@/contexts/user-context';
import { useSidebar } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { WaveformPlayer } from './waveform-player';
import { LikeButton } from '@/components/social/like-button';
import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button';
import { type Song } from '@/components/songs/song-schema';

// Helper to format time
const formatTime = (seconds: number) => {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
};

interface PlayerProps {
  song: Song;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
  togglePlay: () => void;
  setVolume: (value: number) => void;
  toggleMute: () => void;
  playNext: () => void;
  playPrevious: () => void;
  cycleMode: () => void;
  userId?: string;
}

// Mode: micro (a simple bubble)
const PlayerMicro = ({ isPlaying, togglePlay, cycleMode }: PlayerProps) => (
  <div className="flex items-center p-1 rounded-full bg-background/90 backdrop-blur-sm shadow-lg border border-border">
    <Button variant="ghost" size="icon" className="w-12 h-12 rounded-full" onClick={togglePlay}>
      {isPlaying ? <Pause size={22} /> : <Play size={22} className="ml-1" />}
    </Button>
    <Button variant="ghost" size="icon" className="w-12 h-12 rounded-full" onClick={cycleMode}>
      <Maximize2 size={20} />
    </Button>
  </div>
);

// Mode: mini (floating player with cover and title)
const PlayerMini = ({ song, isPlaying, togglePlay, cycleMode }: PlayerProps) => {
  const coverUrl = song.cover_art_url || song.albums?.cover_url || '/images/covers/mouvk.png';
  return (
    <div className="flex items-center gap-3 p-2 rounded-lg bg-background/90 backdrop-blur-sm shadow-lg border border-border">
      <Image
        src={coverUrl}
        alt={song.title ?? 'Pochette'}
        width={48}
        height={48}
        className="rounded-md object-cover h-12 w-12"
      />
      <div className="flex-grow min-w-0">
        <p className="font-semibold truncate text-sm" title={song.title}>
          {song.title}
        </p>
        <p className="text-muted-foreground truncate text-xs" title={song.artist_name}>
          {song.artist_name || 'Artiste inconnu'}
        </p>
      </div>
      <Button variant="ghost" size="icon" className="w-10 h-10 rounded-full" onClick={togglePlay}>
        {isPlaying ? <Pause size={20} /> : <Play size={20} className="ml-1" />}
      </Button>
      <Button variant="ghost" size="icon" className="w-10 h-10 rounded-full" onClick={cycleMode}>
        <Maximize2 size={18} />
      </Button>
    </div>
  );
};

// Mode: normal (the main player bar)
const PlayerNormal = ({
  song,
  isPlaying,
  volume,
  isMuted,
  togglePlay,
  setVolume,
  toggleMute,
  playNext,
  playPrevious,
  cycleMode,
  userId,
}: PlayerProps) => {
  const { currentTime, duration } = useAudioPlayerStore();

  const coverUrl = song.cover_art_url || song.albums?.cover_url || '/images/covers/mouvk.png';
  const artistName = song.profiles?.display_name || song.artist_name || 'Artiste inconnu';

  return (
    <div className="flex items-center justify-between gap-6 px-4 h-full w-full">
      {/* Section 1: Song Info */}
      <div className="flex items-center gap-3 min-w-0 w-[300px] flex-shrink-0">
        <Image
          src={coverUrl}
          alt={song.title ?? 'Pochette'}
          width={56}
          height={56}
          className="rounded-md object-cover h-14 w-14 flex-shrink-0"
        />
        <div className="min-w-0">
          <p className="font-semibold truncate text-sm" title={song.title}>
            {song.title}
          </p>
          <p className="text-muted-foreground truncate text-xs" title={artistName}>
            {artistName}
          </p>
        </div>
        <div className="flex items-center gap-1 ml-auto">
          {userId && <LikeButton resourceId={song.id} resourceType="song" />}
          {userId && <AddToPlaylistButton songId={song.id} />}
        </div>
      </div>

      {/* Section 2: Player Controls & Waveform */}
      <div className="flex flex-col items-center justify-center flex-grow gap-1">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="rounded-full" onClick={playPrevious}>
            <SkipBack size={20} />
          </Button>
          <Button
            variant="default"
            size="icon"
            className="w-10 h-10 rounded-full"
            onClick={togglePlay}
          >
            {isPlaying ? <Pause size={22} /> : <Play size={22} className="ml-1" />}
          </Button>
          <Button variant="ghost" size="icon" className="rounded-full" onClick={playNext}>
            <SkipForward size={20} />
          </Button>
        </div>
        <div className="flex items-center gap-2 w-full">
          <span className="text-xs text-muted-foreground w-12 text-right">{formatTime(currentTime)}</span>
          <WaveformPlayer song={song} height={40} />
          <span className="text-xs text-muted-foreground w-12 text-left">{formatTime(duration)}</span>
        </div>
      </div>

      {/* Section 3: Volume & Mode */}
      <div className="flex items-center gap-4 w-[200px] flex-shrink-0 justify-end">
        <div className="flex items-center gap-2 w-[120px]">
          <Button variant="ghost" size="icon" className="rounded-full" onClick={toggleMute}>
            {isMuted || volume === 0 ? <VolumeX size={20} /> : <Volume2 size={20} />}
          </Button>
          <Slider
            value={[isMuted ? 0 : volume * 100]}
            onValueChange={(value) => setVolume(value[0] / 100)}
            max={100}
            step={1}
          />
        </div>
        <Button variant="ghost" size="icon" className="rounded-full" onClick={cycleMode}>
          <ListMusic size={20} />
        </Button>
      </div>
    </div>
  );
};

// Mode: large (fullscreen player)
const PlayerLarge = ({
  song,
  isPlaying,
  volume,
  isMuted,
  togglePlay,
  setVolume,
  toggleMute,
  playNext,
  playPrevious,
  cycleMode,
  userId,
}: PlayerProps) => {
  const coverUrl = song.cover_art_url || song.albums?.cover_url || '/images/covers/mouvk.png';
  const artistName = song.profiles?.display_name || song.artist_name || 'Artiste inconnu';

  return (
    <div className="relative h-full w-full flex flex-col items-center justify-center p-8">
      <div
        className="absolute inset-0 bg-cover bg-center blur-3xl scale-110"
        style={{ backgroundImage: `url(${coverUrl})` }}
      />
      <div className="absolute inset-0 bg-black/60" />

      <div className="relative z-10 flex flex-col items-center text-white w-full max-w-2xl">
        <Image
          src={coverUrl}
          alt={song.title ?? 'Pochette'}
          width={400}
          height={400}
          className="rounded-lg shadow-2xl object-cover h-[400px] w-[400px]"
        />
        <h2 className="text-4xl font-bold mt-8" title={song.title}>
          {song.title}
        </h2>
        <h3 className="text-xl text-white/80 mt-2" title={artistName}>
          {artistName}
        </h3>

        <div className="w-full mt-8">
          <WaveformPlayer
            song={song}
            height={64}
            waveColor="rgba(255, 255, 255, 0.5)"
            progressColor="rgba(255, 255, 255, 1)"
          />
        </div>

        <div className="flex items-center gap-6 mt-6">
          <Button variant="ghost" size="icon" className="text-white/80 hover:text-white rounded-full" onClick={playPrevious}>
            <SkipBack size={28} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="w-20 h-20 bg-white/20 hover:bg-white/30 rounded-full text-white"
            onClick={togglePlay}
          >
            {isPlaying ? <Pause size={44} /> : <Play size={44} className="ml-2" />}
          </Button>
          <Button variant="ghost" size="icon" className="text-white/80 hover:text-white rounded-full" onClick={playNext}>
            <SkipForward size={28} />
          </Button>
        </div>
      </div>

      <div className="absolute bottom-8 right-8 z-10 flex items-center gap-4">
        <div className="flex items-center gap-2 w-[120px]">
          <Button variant="ghost" size="icon" className="text-white/80 hover:text-white rounded-full" onClick={toggleMute}>
            {isMuted || volume === 0 ? <VolumeX size={20} /> : <Volume2 size={20} />}
          </Button>
          <Slider
            value={[isMuted ? 0 : volume * 100]}
            onValueChange={(value) => setVolume(value[0] / 100)}
            max={100}
            step={1}
            className="[&>span:first-child]:bg-white/80"
          />
        </div>
        <Button variant="ghost" size="icon" className="text-white/80 hover:text-white rounded-full" onClick={cycleMode}>
          <Minimize2 size={20} />
        </Button>
      </div>
    </div>
  );
};

// Main component that orchestrates everything
const GlobalAudioPlayer = () => {
  const {
    currentSong,
    isPlaying,
    volume,
    isMuted,
    playerMode,
    togglePlayPause,
    setVolume,
    toggleMute,
    nextSong,
    previousSong,
    cyclePlayerMode,
  } = useAudioPlayerStore();

  const { user } = useUser();
  const { state: sidebarState } = useSidebar();

  if (!currentSong || !currentSong.audio_url) {
    return null;
  }

  const isFloating = playerMode === 'micro' || playerMode === 'mini';
  const isLarge = playerMode === 'large';

  const playerStyle: React.CSSProperties = {
    transition: 'all 0.3s ease-in-out',
  };

  if (isFloating) {
    Object.assign(playerStyle, {
      right: '1rem',
      width: 'auto',
      bottom: '1rem',
      height: 'auto',
      left: 'auto',
    });
  } else if (isLarge) {
    Object.assign(playerStyle, {
      height: '100vh',
      width: '100vw',
      bottom: '0',
      left: '0',
    });
  } else { // Normal mode
    const sidebarWidth = sidebarState === 'expanded' ? '16rem' : '3rem';
    Object.assign(playerStyle, {
      left: sidebarWidth,
      width: `calc(100% - ${sidebarWidth})`,
      bottom: '0',
    });
  }

  const PlayerComponent = {
    micro: PlayerMicro,
    mini: PlayerMini,
    normal: PlayerNormal,
    large: PlayerLarge,
  }[playerMode];

  return (
    <div
      style={playerStyle}
      className={cn(
        'fixed z-50',
        isFloating
          ? 'bg-transparent'
          : 'bg-background/80 backdrop-blur-md border-t border-border',
        isLarge ? 'h-screen w-screen top-0 left-0' : ''
      )}
    >
      <PlayerComponent
        song={currentSong}
        isPlaying={isPlaying}
        volume={volume}
        isMuted={isMuted}
        togglePlay={togglePlayPause}
        setVolume={setVolume}
        toggleMute={toggleMute}
        playNext={nextSong}
        playPrevious={previousSong}
        cycleMode={cyclePlayerMode}
        userId={user?.id}
      />
    </div>
  );
};

export default GlobalAudioPlayer;