import { createSupabaseServerClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { UserProvider } from "@/contexts/user-context"; // Assuming UserProvider is needed for admin layout too
import { AppSidebar } from "@/components/sidebar"; // Assuming admin might use same sidebar or a variant
import MobileMenuButton from "@/app/(authenticated)/mobile-menu-button"; // Reusing from authenticated

interface AdminLayoutProps {
  children: React.ReactNode;
}

// Define an interface for the expected shape of profile data for admin layout
// This might be simpler or different from the main authenticated layout's ProfileDataForLayout
interface AdminProfileData {
  id: string;
  email?: string | null;
  display_name?: string | null;
  username?: string | null;
  avatar_url?: string | null;
  role_primary?: string | null; // For sidebar display
  subscription_tier?: string | null; // For sidebar display
  user_role?: string | null; // Crucial for admin check
  // Include other fields if needed by admin sidebar/header
  coins_balance?: number | null;
  // Custom quotas might not be directly needed for the admin user itself in this layout,
  // but keeping structure similar for UserProvider if it expects them.
  custom_uploads_per_month?: number | null;
  custom_vault_space_gb?: number | null;
  custom_vault_max_files?: number | null;
  custom_ia_credits_month?: number | null;
  custom_coins_month?: number | null;
  custom_max_playlists?: number | null;
  custom_max_friends?: number | null;
  ia_credits?: number | null;
}


export default async function AdminLayout({ children }: AdminLayoutProps) {
  const supabase = createSupabaseServerClient();

  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    console.log("AdminLayout: No user session, redirecting to login.");
    redirect("/login");
  }

  // Fetch profile to check user_role
  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select("user_role, display_name, username, avatar_url, role_primary, subscription_tier, coins_balance") // Fetch necessary fields
    .eq("id", user.id)
    .single<AdminProfileData>(); // Use a specific type for admin profile data

  if (profileError) {
    console.error("AdminLayout: Error fetching profile:", profileError);
    // Decide how to handle: redirect, show error, or allow if it's a recoverable issue
    // For now, if profile fetch fails, assume not admin and redirect or show error.
    // Or, if it's a new admin whose profile isn't fully set up, this might be an issue.
    // A robust system might allow access if user.role from JWT indicates admin,
    // but profiles.user_role is the source of truth for app-level roles.
    redirect("/dashboard"); // Redirect to user dashboard if profile error
  }

  if (profile?.user_role !== "admin") {
    console.log(`AdminLayout: User ${user.id} with role ${profile?.user_role} is not admin. Redirecting.`);
    redirect("/dashboard"); // Or a specific "access denied" page
  }

  // Construct userObj for UserProvider and AppSidebar, similar to AuthenticatedLayout
  // This ensures consistency if admin pages reuse components expecting this user object structure.
  const userObjForAdminLayout: AdminProfileData = {
    id: user.id,
    email: user.email,
    display_name: profile?.display_name || user.email?.split('@')[0],
    username: profile?.username,
    avatar_url: profile?.avatar_url,
    role_primary: profile?.role_primary,
    subscription_tier: profile?.subscription_tier,
    user_role: profile?.user_role, // Should be 'admin' here
    coins_balance: profile?.coins_balance,
    // Fill with nulls or defaults for other fields if UserProvider expects them
    custom_uploads_per_month: null,
    custom_vault_space_gb: null,
    custom_vault_max_files: null,
    custom_ia_credits_month: null,
    custom_coins_month: null,
    custom_max_playlists: null,
    custom_max_friends: null,
    ia_credits: null,
  };


  return (
    <UserProvider value={userObjForAdminLayout as any}> {/* Cast to any if types are slightly different but compatible */}
      <div className="flex h-screen main-layout-container">
        <MobileMenuButton />
        <AppSidebar user={userObjForAdminLayout as any} /> {/* Admin might have a different sidebar later */}
        <div className="flex-1 flex flex-col">
          <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
            {children}
          </main>
        </div>
      </div>
    </UserProvider>
  );
}
