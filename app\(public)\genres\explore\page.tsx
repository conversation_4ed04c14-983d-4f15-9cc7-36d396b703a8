import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Metadata } from 'next';
import { ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const metadata: Metadata = {
  title: 'Explorer les Genres Musicaux',
  description: 'Découvrez et explorez une vaste collection de genres musicaux.',
};

const ALL_MUSIC_GENRES = [
  'Pop', 'Rock', 'Hip Hop', 'Rap Français', 'Rap US', 'Drill', 'Trap',
  'Électronique', 'House', 'Techno', 'EDM', 'Trance', 'Ambiant',
  'Lofi Hip Hop', 'Jazz', 'Smooth Jazz', 'Blues', 'Soul', 'Funk', 'R&B',
  'Disco', 'Musique Classique', 'Opéra', 'Musique de Film',
  'Musique du Monde', 'Afrobeat', 'Reggae', 'Dancehall', 'Ska',
  'Metal', 'Heavy Metal', 'Metal Progressif', 'Hard Rock', 'Punk Rock',
  'Folk', 'Country', 'Indie Pop', 'Indie Rock', 'Rock Alternatif',
  'Grunge', 'Acoustique', 'Chanson Française', 'Variété Internationale',
  'K-Pop', 'J-Rock', 'Synthwave', 'Gospel'
];

export default function ExploreGenresPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-center">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href="/discover">
            <ChevronLeft className="h-6 w-6" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Explorer les Genres Musicaux</h1>
      </div>
      <p className="text-muted-foreground mb-8 text-center">
        Cliquez sur un genre pour découvrir des morceaux, albums, artistes et playlists associés.
      </p>
      <div className="flex flex-wrap gap-3 justify-center">
        {ALL_MUSIC_GENRES.map((genreName) => {
          const href = `/genres/${encodeURIComponent(genreName.toLowerCase().replace(/\s+/g, '-'))}`;
          return (
            <Link key={genreName} href={href} passHref>
              <Badge 
                variant="outline"
                className="px-4 py-2 text-sm cursor-pointer hover:bg-primary/10 transition-colors duration-150 ease-in-out transform hover:scale-105 active:scale-95"
              >
                {genreName}
              </Badge>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
