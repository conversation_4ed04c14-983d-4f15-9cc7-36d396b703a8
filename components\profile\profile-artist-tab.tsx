"use client";

import React from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { MultiSelect } from '@/components/ui/multi-select'; // Assurez-vous que ce composant existe et est correctement importé
import { X } from 'lucide-react';
import { type ProfileFormValues } from './profile-form-schema';
import {
  genreOptions,
  influenceOptions, // Assurez-vous que cette variable existe dans vos constantes
  instrumentationOptions,
  rolePrimaryOptions, // Assurez-vous que cette variable existe
  roleSecondaryOptions,
  dawOptions,
  osOptions,
  tagOptions
} from '@/lib/constants/song-options';

// Si influenceOptions n'existe pas, utilisez une liste vide ou une autre variable pertinente
const availableInfluenceOptions = typeof influenceOptions !== 'undefined' ? influenceOptions : [];

export function ProfileArtistTab() {
  const { control } = useFormContext<ProfileFormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "instruments_played",
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profil Artiste</CardTitle>
        <CardDescription>Détaillez votre identité musicale pour les autres utilisateurs et les opportunités de collaboration.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="role_primary"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rôle Principal</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value ?? ''}>
                  <FormControl>
                    <SelectTrigger><SelectValue placeholder="Sélectionnez votre rôle principal" /></SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {rolePrimaryOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="roles_secondary"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rôles Secondaires</FormLabel>
                <MultiSelect
                  options={roleSecondaryOptions}
                  selected={field.value ?? []}
                  onChange={field.onChange}
                  placeholder="Sélectionnez vos rôles secondaires..."
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={control}
          name="genres"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Genres Musicaux</FormLabel>
              <MultiSelect
                options={genreOptions}
                selected={field.value ?? []}
                onChange={field.onChange}
                placeholder="Sélectionnez vos genres..."
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="influences"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Influences</FormLabel>
              <MultiSelect
                options={availableInfluenceOptions}
                selected={field.value ?? []}
                onChange={field.onChange}
                placeholder="Quels artistes vous inspirent ?"

              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="tags"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tags</FormLabel>
              <MultiSelect
                options={tagOptions}
                selected={field.value ?? []}
                onChange={field.onChange}
                placeholder="Ajoutez des tags (ex: 80s, instrumental, live)..."
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <FormLabel>Instruments Joués</FormLabel>
          <div className="grid grid-cols-[1fr,auto,auto] gap-2 mt-2 mb-1 text-sm text-muted-foreground">
            <div>Instrument</div>
            <div className="w-32 text-center">Exp. (années)</div>
            <div></div>
          </div>
          <div className="space-y-2">
            {fields.map((item, index) => (
              <div key={item.id} className="grid grid-cols-[1fr,auto,auto] items-start gap-2">
                <FormField
                  control={control}
                  name={`instruments_played.${index}.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input placeholder="Ex: Guitare électrique" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name={`instruments_played.${index}.experience_years`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="Ex: 5" 
                          className="w-32 text-center" 
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value, 10) || null)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon"
                  onClick={() => remove(index)} 
                  className="shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => append({ name: '', experience_years: 0 })}
          >
            Ajouter un instrument
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={control}
              name="primary_daw"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>DAW Principal</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value ?? ''}>
                    <FormControl>
                      <SelectTrigger><SelectValue placeholder="Votre logiciel principal" /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {dawOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="other_daws"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Autres DAWs</FormLabel>
                  <MultiSelect
                    options={dawOptions}
                    selected={field.value ?? []}
                    onChange={field.onChange}
                    placeholder="Autres logiciels utilisés..."
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
        </div>

        <FormField
          control={control}
          name="equipment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Équipement</FormLabel>
              <Textarea
                placeholder="Listez votre matériel principal (micros, guitares, synthés...)"
                className="resize-y"
                {...field}
                value={field.value ?? ''}
              />
              <FormMessage />
            </FormItem>
          )}
        />

      </CardContent>
    </Card>
  );
}
