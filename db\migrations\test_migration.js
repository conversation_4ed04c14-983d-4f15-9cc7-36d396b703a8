/**
 * Script de test pour la migration de tracks vers songs
 * 
 * Ce script permet de :
 * 1. Vérifier l'état actuel de la base de données
 * 2. Tester la migration
 * 3. Valider l'intégrité des données après migration
 * 4. Effectuer un rollback si nécessaire
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Configuration - À adapter selon votre environnement
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'your-anon-key';
const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'public',
  },
});

class MigrationTester {
  constructor() {
    this.testResults = [];
    this.migrationFile = path.join(__dirname, '20240516_rename_tracks_to_songs.sql');
    this.rollbackFile = path.join(__dirname, 'rollback_20240516_rename_tracks_to_songs.sql');
  }

  async logTestResult(testName, passed, message = '') {
    const result = {
      test: testName,
      status: passed ? 'PASSED' : 'FAILED',
      message,
      timestamp: new Date().toISOString(),
    };
    this.testResults.push(result);
    console.log(`[${result.status}] ${testName}: ${message}`);
    return passed;
  }

  async runQuery(query) {
    try {
      const { data, error } = await supabase.rpc('exec_sql', { query });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Query error:', error.message);
      throw error;
    }
  }

  async checkTableExists(tableName) {
    const query = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '${tableName}'
      );
    `;
    const result = await this.runQuery(query);
    return result[0]?.exists || false;
  }

  async getRowCount(tableName) {
    const query = `SELECT COUNT(*) as count FROM ${tableName};`;
    const result = await this.runQuery(query);
    return parseInt(result[0]?.count || 0);
  }

  async executeSqlFile(filePath) {
    try {
      const sql = await fs.readFile(filePath, 'utf8');
      // Exécuter chaque instruction SQL séparément
      const statements = sql.split(';').filter(statement => statement.trim());
      
      for (const statement of statements) {
        if (statement.trim().toUpperCase().startsWith('BEGIN') || 
            statement.trim().toUpperCase().startsWith('COMMIT') ||
            statement.trim() === '') {
          continue; // Ignorer les instructions de transaction dans ce contexte
        }
        await this.runQuery(statement);
      }
      
      return true;
    } catch (error) {
      console.error(`Error executing SQL file ${filePath}:`, error.message);
      throw error;
    }
  }

  async runTests() {
    console.log('=== Démarrage des tests de migration ===');
    
    try {
      // Étape 1: Vérifier l'état initial
      console.log('\n=== Étape 1: Vérification de l\'état initial ===');
      const tracksExist = await this.checkTableExists('tracks');
      const songsExist = await this.checkTableExists('songs');
      
      if (!tracksExist) {
        throw new Error('La table tracks n\'existe pas. Aucune migration nécessaire.');
      }
      
      if (songsExist) {
        throw new Error('La table songs existe déjà. Veuillez la supprimer avant de continuer.');
      }
      
      const trackCount = await this.getRowCount('tracks');
      await this.logTestResult(
        'Vérification de la table tracks", 
        true, 
        `${trackCount} enregistrements trouvés dans la table tracks`
      );
      
      // Étape 2: Exécuter la migration
      console.log('\n=== Étape 2: Exécution de la migration ===');
      await this.executeSqlFile(this.migrationFile);
      
      // Étape 3: Vérifier la migration
      console.log('\n=== Étape 3: Vérification de la migration ===');
      const songsExistAfter = await this.checkTableExists('songs');
      
      if (!songsExistAfter) {
        throw new Error('La migration a échoué : la table songs n\'a pas été créée');
      }
      
      const songsCount = await this.getRowCount('songs');
      await this.logTestResult(
        'Vérification de la table songs', 
        true, 
        `${songsCount} enregistrements migrés vers la table songs`
      );
      
      // Vérifier que le nombre d'enregistrements correspond
      await this.logTestResult(
        'Vérification de l\'intégrité des données',
        trackCount === songsCount,
        `Tracks: ${trackCount}, Songs: ${songsCount}`
      );
      
      // Vérifier que la vue de compatibilité existe
      const compatViewExists = await this.checkTableExists('tracks');
      await this.logTestResult(
        'Vérification de la vue de compatibilité',
        compatViewExists,
        compatViewExists ? 'La vue de compatibilité existe' : 'La vue de compatibilité est manquante'
      );
      
      // Étape 4: Tester le rollback
      console.log('\n=== Étape 4: Test de rollback ===');
      await this.executeSqlFile(this.rollbackFile);
      
      const songsExistAfterRollback = await this.checkTableExists('songs');
      await this.logTestResult(
        'Vérification du rollback',
        !songsExistAfterRollback,
        songsExistAfterRollback ? 'La table songs existe toujours' : 'Rollback réussi'
      );
      
      console.log('\n=== Résumé des tests ===');
      this.testResults.forEach((test, index) => {
        console.log(`[${index + 1}] ${test.test}: ${test.status} - ${test.message}`);
      });
      
      const failedTests = this.testResults.filter(t => t.status === 'FAILED');
      if (failedTests.length > 0) {
        console.error('\n⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.');
        process.exit(1);
      } else {
        console.log('\n✅ Tous les tests ont réussi !');
      }
      
    } catch (error) {
      console.error('\n❌ Erreur lors des tests:', error.message);
      process.exit(1);
    }
  }
}

// Exécuter les tests
const tester = new MigrationTester();
tester.runTests().catch(console.error);
