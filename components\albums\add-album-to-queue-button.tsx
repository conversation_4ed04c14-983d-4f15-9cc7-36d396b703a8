"use client";

import { useAudio } from '@/contexts/audio-context';
import { Button } from '@/components/ui/button';
import { ListPlus } from 'lucide-react';
import { toast } from 'sonner';
import type { Song } from '@/components/songs/song-schema';
import type { UserProfile } from '@/types';

interface AddAlbumToQueueButtonProps {
  songs: (Song & { profiles?: UserProfile | null })[];
  albumTitle: string;
}

export function AddAlbumToQueueButton({ songs, albumTitle }: AddAlbumToQueueButtonProps) {
  const { addToQueue, currentSong } = useAudio();

  const handleAddAlbumToQueue = () => {
    if (songs && songs.length > 0) {
      const songsForPlayer = songs.map(song => ({
        ...song,
        artist: song.profiles?.display_name || song.profiles?.username || 'Artiste inconnu',
      }));
      
      songsForPlayer.forEach(song => addToQueue(song));
      
      toast.success(`"${albumTitle}" ajouté à la file d'attente.`);
      
      // Example: if you wanted to play the first song of the album if nothing is playing
      // if (!currentSong && songsForPlayer.length > 0) {
      //   playSong(songsForPlayer[0]); // playSong would also come from useAudio
      // }
    }
  };

  return (
    <Button variant="outline" onClick={handleAddAlbumToQueue} title="Ajouter l'album à la file d'attente">
      <ListPlus className="mr-2 h-4 w-4" />
      Ajouter à la file
    </Button>
  );
}
