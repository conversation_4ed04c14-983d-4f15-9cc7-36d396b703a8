"use client";

import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, Edit3, Trash2, PlayCircle, Music2, UserCircle, Disc, ListPlus, Heart, ThumbsUp, Copy, Loader2, ThumbsDown, Eye, Play as PlayIconSmall,
  MoreVertical, Pencil, Clock, Music, User, Play as PlayIcon
} from 'lucide-react';
import { ActionableVisibilityToggle } from "@/components/ui/actionable-visibility-toggle";
import type { Song } from '@/components/songs/song-schema';
import type { UserProfile, Album } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { useResourceInteractionStore, getResourceInteractionStoreState, type ResourceInteractionStoreState, getResourceKey, DEFAULT_RESOURCE_STATE } from '@/lib/stores/resource-interaction-store';
import { AddToPlaylistModal } from '@/components/playlists/add-to-playlist-modal';
import { useUser } from '@/contexts/user-context';
import { useAudioPlayerStore, type AudioPlayerState } from '@/lib/stores/audioPlayerStore';
import { shallow } from 'zustand/shallow';
import { useStoreWithEqualityFn } from 'zustand/traditional';
import { PlayButton } from '@/components/audio/play-button';
import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button';
import { LikeButton } from '@/components/social/like-button';
import { DislikeButton } from '@/components/social/dislike-button';
import { createBrowserClient } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';

export interface SongForCard extends Song {
  profiles?: UserProfile | null; 
  albums?: Pick<Album, 'id' | 'title' | 'cover_url'> | null; 
  duration_ms: number | null;
  plays?: number | null;
}

interface SongCardProps {
  song: SongForCard;
  viewMode?: 'grid' | 'list';
  onDelete?: (songId: string) => void;
  onToggleVisibility?: (songId: string, currentIsPublic: boolean) => void;
  isVisibilityUpdating: boolean;
  onInfoClick?: (song: SongForCard) => void;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  onUpdateStatus?: (songId: string, newStatus: { is_public: boolean; slug: string | null; }) => Promise<void> | void;
  showExtendedInfo?: boolean;
}

const SongCardComponent = ({ song, viewMode = 'grid', onDelete, onToggleVisibility, isVisibilityUpdating, size = 'md', onInfoClick, onUpdateStatus, showExtendedInfo }: SongCardProps): JSX.Element | null => {
  const { user } = useUser();
  const supabase = createBrowserClient();
  const router = useRouter();
  const { toast } = useToast();

  const initialImageSrc = song.cover_art_url || song.albums?.cover_url;
  const fallbackImageSrc = '/images/covers/mouvk.png';
  const [currentImageSrc, setCurrentImageSrc] = useState(initialImageSrc || fallbackImageSrc);
  const [hasLoadError, setHasLoadError] = useState(false);
  const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] = useState(false);

interface SelectedAudioState {
  currentSong: Song | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
}

  const selector = (state: AudioPlayerState): SelectedAudioState => ({
    currentSong: state.currentSong,
    isPlaying: state.isPlaying,
    currentTime: state.currentTime,
    duration: state.duration,
  });

  const { currentSong, isPlaying, currentTime, duration } = useStoreWithEqualityFn(
    useAudioPlayerStore,
    selector,
    shallow
  );
  const audioPlayerProgressValue = duration > 0 ? (currentTime / duration) * 100 : 0;
  const isCurrentSong = song.id === currentSong?.id;

  useEffect(() => {
    setCurrentImageSrc(initialImageSrc || fallbackImageSrc);
    setHasLoadError(false);
  }, [initialImageSrc, fallbackImageSrc]);

  useEffect(() => {
    if (song?.id) {
      getResourceInteractionStoreState().setResourceStatus('song', song.id, {
        isLiked: song.is_liked_by_current_user ?? false,
        likeCount: song.like_count ?? 0,
        isDisliked: song.is_disliked_by_current_user ?? false,
        dislikeCount: song.dislike_count ?? 0,
      });
    }
  }, [song.id, song.is_liked_by_current_user, song.like_count, song.is_disliked_by_current_user, song.dislike_count]);

  const { likeCount, dislikeCount } = useResourceInteractionStore(
    (state: ResourceInteractionStoreState) => state.resourceStates[getResourceKey('song', song.id)] || { likeCount: song.like_count ?? 0, dislikeCount: song.dislike_count ?? 0 }
  );

  const handleImageError = useCallback(() => {
    if (!hasLoadError) {
      setCurrentImageSrc(fallbackImageSrc);
      setHasLoadError(true);
    }
  }, [hasLoadError, fallbackImageSrc]);

  const handleToggleVisibility = useCallback(() => onToggleVisibility?.(song.id, song.is_public ?? false), [onToggleVisibility, song.id, song.is_public]);
  const handleEditClick = useCallback(() => router.push(`/manage-songs/${song.id}/edit`), [router, song.id]);
  const handleInfoClick = useCallback(() => onInfoClick?.(song), [onInfoClick, song]);

  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.preventDefault(); e.stopPropagation();
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le morceau \"${song.title}\"?`)) onDelete?.(song.id);
  }, [onDelete, song.id, song.title]);

  const handleDuplicate = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault(); e.stopPropagation();
    if (!user) { toast({ title: "Connexion requise", description: "Vous devez être connecté pour dupliquer un morceau.", variant: "destructive" }); return; }
    try {
      const { error } = await supabase.rpc('duplicate_song', { p_song_id: song.id, p_user_id: user.id });
      if (error) throw error;
      toast({ title: "Morceau dupliqué", description: `\"${song.title}\" a été dupliqué avec succès.` });
      router.refresh();
    } catch (err: any) { toast({ title: "Erreur de duplication", description: err.message || "Impossible de dupliquer le morceau.", variant: "destructive" }); }
  }, [user, supabase, router, toast, song.id, song.title]);

  const handleAddToPlaylist = useCallback((e: React.MouseEvent) => {
    e.preventDefault(); e.stopPropagation();
    if (!user) { toast({ title: "Connexion requise", description: "Vous devez être connecté pour ajouter à une playlist.", variant: "destructive" }); return; }
    setIsAddToPlaylistModalOpen(true);
  }, [user, toast]);

  const handleClosePlaylistModal = useCallback(() => {
    setIsAddToPlaylistModalOpen(false);
  }, []);

  const finalArtistDisplay = song.profiles?.display_name || song.profiles?.username || song.artist_name || "Artiste inconnu";
  const viewPageUrl = song.slug ? `/songs/${song.slug}` : `/songs/${song.id}`;
  const durationMs = song.duration_ms ?? null;

  const songForPlayButton = useMemo(() => ({ ...song, artist_name: finalArtistDisplay }), [song, finalArtistDisplay]);

  const sizeConfig = useMemo(() => ({
    xs: { card: '', padding: 'p-2', title: 'text-sm', subtitle: 'text-xs', stats: 'text-xs', icon: 'h-3 w-3', showGenres: false },
    sm: { card: '', padding: 'p-2', title: 'text-sm', subtitle: 'text-xs', stats: 'text-xs', icon: 'h-3.5 w-3.5', showGenres: true },
    md: { card: '', padding: 'p-3', title: 'text-base', subtitle: 'text-sm', stats: 'text-xs', icon: 'h-4 w-4', showGenres: true },
    lg: { card: '', padding: 'p-4', title: 'text-lg', subtitle: 'text-sm', stats: 'text-sm', icon: 'h-4 w-4', showGenres: true },
    xl: { card: '', padding: 'p-4', title: 'text-xl', subtitle: 'text-base', stats: 'text-sm', icon: 'h-4 w-4', showGenres: true }
  }), []);
  const config = sizeConfig[size];

  const stopPropagation = useCallback((e: React.MouseEvent) => e.stopPropagation(), []);

  if (!song) {
    
    return null;
  }

  if (viewMode === 'list') {
    return (
      <div className="w-full flex items-center gap-4 p-2 rounded-md hover:bg-muted/50 transition-colors">
        <div className="relative flex-shrink-0 w-16 h-16 rounded-md overflow-hidden group">
          <Link href={viewPageUrl}><Image src={currentImageSrc} alt={song.title || 'Cover art'} fill className="object-cover" onError={handleImageError} /></Link>
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            {!isCurrentSong ? (
              <PlayButton song={songForPlayButton} size="sm" />
            ) : (
              <Music2 className="h-6 w-6 text-white" />
            )}
          </div>
        </div>
        <div className="flex-grow min-w-0">
          <Link href={viewPageUrl} className="hover:underline"><p className="font-semibold truncate" title={song.title}>{song.title}</p></Link>
          <Link href={`/artists/${song.profiles?.username || song.creator_user_id}`} className="hover:underline"><p className="text-sm text-muted-foreground truncate" title={finalArtistDisplay}>{finalArtistDisplay}</p></Link>
          {song.id === currentSong?.id && isPlaying && <div className="mt-1 mb-1 w-full pr-2"><Progress value={audioPlayerProgressValue} className="h-1 bg-primary/20" /></div>}
          {(song.genres?.length ?? 0 > 0) && <div className="flex flex-wrap gap-1 mt-1">{song.genres?.slice(0, 3).map((genre, index) => <Link href={`/genres/${encodeURIComponent(genre.toLowerCase())}`} key={index} onClick={stopPropagation}><Badge variant="secondary" className="text-xs px-1.5 py-0.5 hover:bg-primary/20 transition-colors">{genre}</Badge></Link>)}</div>}
        </div>
        <div className="hidden md:flex items-center gap-4 text-sm text-muted-foreground">
          <span title={`${likeCount ?? 0} likes`} className="flex items-center gap-1.5"><ThumbsUp className="w-4 h-4" /> {likeCount ?? 0}</span>
          <span title={`${song.plays ?? 0} plays`} className="flex items-center gap-1.5"><PlayIconSmall className="w-4 h-4" /> {song.plays ?? 0}</span>
          <span className="w-16 text-right">{formatDuration(durationMs)}</span>
        </div>
        <div className="flex items-center gap-1" onClick={stopPropagation}>
          <LikeButton resourceId={song.id} resourceType="song" /><DislikeButton resourceId={song.id} resourceType="song" />
          <ActionableVisibilityToggle isPublic={song.is_public ?? false} isUpdating={isVisibilityUpdating} onToggle={handleToggleVisibility} />
          <AddToPlaylistButton songId={song.id} />
          <DropdownMenu><DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-8 w-8"><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger><DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEditClick}><Pencil className="mr-2 h-4 w-4"/> Éditer</DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}><Copy className="mr-2 h-4 w-4"/> Dupliquer</DropdownMenuItem><DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-red-500 focus:text-white focus:bg-red-500"><Trash2 className="mr-2 h-4 w-4"/> Supprimer</DropdownMenuItem>
          </DropdownMenuContent></DropdownMenu>
        </div>
      </div>
    );
  }

  return (
    <>
      <Card className={cn("group relative w-full overflow-hidden flex flex-col min-w-[210px]", config.card)}>
        <CardHeader className="p-0">
          <Link href={viewPageUrl} className="block"><div className="relative aspect-square w-full overflow-hidden">
            <Image src={currentImageSrc} alt={song.title || 'Cover art'} fill className="object-cover transition-transform group-hover:scale-105" onError={handleImageError} sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw" />
            <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col items-stretch justify-between p-2">
              {/* Top-left: Visibility LED */}
              <div className="flex justify-start" onClick={stopPropagation}>
                <div onClick={(e: React.MouseEvent) => e.preventDefault()}><ActionableVisibilityToggle isPublic={song.is_public ?? false} isUpdating={isVisibilityUpdating} onToggle={handleToggleVisibility} /></div>
              </div>

              {/* Center: Play Button */}
              <div className="absolute inset-0 flex items-center justify-center">
                {!isCurrentSong ? (
                  <div className="pointer-events-auto">
                    <PlayButton song={songForPlayButton} size="lg" />
                  </div>
                ) : (
                  <div className="h-16 w-16 flex items-center justify-center rounded-full bg-black/50 backdrop-blur-sm pointer-events-none">
                    <Music2 className="h-8 w-8 text-white animate-pulse" />
                  </div>
                )}
              </div>

              {/* Top-right: Owner actions */}
              <div className="flex justify-end" onClick={stopPropagation}>
                {user?.id === song.user_id && (
                  <div className="flex flex-row space-x-1">
                    <Button title="Modifier" onClick={(e) => { e.stopPropagation(); handleEditClick(); }} variant="ghost" size="icon" className="bg-black/70 hover:bg-black/90 text-white backdrop-blur-sm p-1.5 h-auto w-auto rounded-full">
                      <Edit3 className="h-4 w-4" />
                    </Button>
                    <Button title="Dupliquer" onClick={(e) => { handleDuplicate(e); }} variant="ghost" size="icon" className="bg-black/70 hover:bg-black/90 text-white backdrop-blur-sm p-1.5 h-auto w-auto rounded-full">
                      <Copy className="h-4 w-4" />
                    </Button>
                    {onDelete && (
                      <Button title="Supprimer" onClick={(e) => { handleDelete(e); }} variant="ghost" size="icon" className="bg-red-500/70 hover:bg-red-500/90 text-white backdrop-blur-sm p-1.5 h-auto w-auto rounded-full">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div></div></Link>
        </CardHeader>
        <CardContent className={cn("flex-grow flex flex-col !pt-3 !pb-2", config.padding)}>
          <Link href={viewPageUrl} className="block group/title"><CardTitle className={cn("truncate transition-colors group-hover/title:text-primary", config.title)} title={song.title}>{song.title || "Titre inconnu"}</CardTitle></Link>
          <Link href={`/artists/${song.profiles?.username || song.creator_user_id}`} className="block group/artist"><p className={cn("truncate text-muted-foreground transition-colors group-hover/artist:text-foreground", config.subtitle)} title={finalArtistDisplay}>{finalArtistDisplay}</p></Link>
          {config.showGenres && (song.genres?.length ?? 0 > 0) && <div className="mt-1.5 flex flex-wrap gap-1">{song.genres?.slice(0, size === 'xs' ? 1 : (size === 'sm' ? 2 : 3)).map((genre, index) => <Link href={`/genres/${encodeURIComponent(genre.toLowerCase())}`} key={index} onClick={stopPropagation}><Badge variant="secondary" className="text-xs px-1.5 py-0.5 hover:bg-primary/20 transition-colors">{genre}</Badge></Link>)}</div>}
          {song.id === currentSong?.id && isPlaying && <div className="mt-2 w-full"><Progress value={audioPlayerProgressValue} className="h-1 bg-primary/20" /></div>}
        </CardContent>
        <CardFooter className={cn("items-center pt-2 !pb-2", config.padding, 'justify-between')}>
          <div className="flex items-center gap-2 text-muted-foreground text-xs">
            <div className="flex items-center gap-1" onClick={stopPropagation}><LikeButton resourceId={song.id} resourceType="song" /><DislikeButton resourceId={song.id} resourceType="song" /></div>
            {durationMs !== null && (
              <span className="flex items-center">
                <Clock className={cn("mr-1", config.icon, 'h-3 w-3')} />
                {formatDuration(durationMs)}
              </span>
            )}
          </div>
          <div onClick={stopPropagation}><DropdownMenu><DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-7 w-7"><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger><DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleAddToPlaylist}><ListPlus className="mr-2 h-4 w-4" /> Ajouter à une playlist</DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}><Copy className="mr-2 h-4 w-4" /> Dupliquer</DropdownMenuItem><DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleEditClick}><Edit3 className="mr-2 h-4 w-4" /> Modifier</DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete} className="text-red-500 focus:text-white focus:bg-red-500"><Trash2 className="mr-2 h-4 w-4" /> Supprimer</DropdownMenuItem>
          </DropdownMenuContent></DropdownMenu></div>
        </CardFooter>
      </Card>
      {isAddToPlaylistModalOpen && user && <AddToPlaylistModal songId={song.id} isOpen={isAddToPlaylistModalOpen} onClose={handleClosePlaylistModal} />}
    </>
  );
};

export const SongCard = memo(SongCardComponent);
