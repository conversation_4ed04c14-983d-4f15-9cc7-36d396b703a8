'use server';

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { Database } from '@/types/supabase';

type Song = Database['public']['Tables']['songs']['Row'];

export async function createNewSongAction() {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    throw new Error('User not authenticated');
  }

  const newSongData: Partial<Song> = {
    title: 'Untitled Song',
    creator_user_id: user.id,
    status: 'draft',
    lyrics: '',
  };

  const { data, error } = await supabase
    .from('songs')
    .insert(newSongData)
    .select('id')
    .single();

  if (error || !data?.id) {
    console.error('Error creating song in server action:', error);
    throw new Error('Failed to create a new song.');
  }

  revalidatePath('/ai-composer-workspace');
  redirect(`/ai-composer-workspace/${data.id}`);
}
