"use server";

import { z } from "zod";
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { songSchema, Song, SongFormData } from '@/components/songs/song-schema';
import { revalidatePath } from "next/cache";

type CreateSongResult = 
  | { success: true; data: Song }
  | { success: false; error: string; details?: z.ZodFormattedError<SongFormData> };

export async function createSong(values: Partial<SongFormData>): Promise<CreateSongResult> {
  const supabase = createSupabaseServerClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { success: false, error: 'Authentication required.' };
  }

  const dataToValidate = {
    ...values,
    creator_user_id: user.id,
  };

  const validatedFields = songSchema.safeParse(dataToValidate);

  if (!validatedFields.success) {
    return { success: false, error: 'Invalid input data.', details: validatedFields.error.format() };
  }

  const data = validatedFields.data;

  let editorDataObject: any = null;
  if (data.editor_data) {
    const editorData: unknown = data.editor_data;
    if (typeof editorData === 'string' && editorData.trim() !== '') {
      try {
        editorDataObject = JSON.parse(editorData);
      } catch (e) {
        return { success: false, error: 'Invalid JSON format for editor data.' };
      }
    } else if (typeof editorData === 'object') {
      editorDataObject = editorData;
    }
  }

  const songDataToInsert = {
    ...data,
    bpm: data.bpm ? Number(data.bpm) : null,
    credits: data.credits && Object.keys(data.credits).length > 0 ? data.credits : null,
    editor_data: editorDataObject,
    genre: Array.isArray(data.genre) ? data.genre : (data.genre ? [data.genre] : null),
    is_public: data.is_public === undefined ? false : data.is_public,
    is_explicit: data.is_explicit === undefined ? false : data.is_explicit,
    is_archived: data.is_archived === undefined ? false : data.is_archived,
  };

  const cleanedPayload = Object.fromEntries(
    Object.entries(songDataToInsert).filter(([, value]) => value !== undefined)
  );

  const { data: newSong, error } = await supabase
    .from('songs')
    .insert(cleanedPayload as any)
    .select()
    .single();

  if (error) {
    console.error('Supabase insert error:', error);
    return { success: false, error: 'Failed to create song in database.' };
  }

  revalidatePath('/manage-songs');
  revalidatePath(`/manage-songs/${newSong.id}/edit`);

  return { success: true, data: newSong };
}

const updateLyricsSchema = z.object({
  songId: z.string().uuid(),
  lyrics: z.string(),
});

export async function updateSongLyrics(params: { songId: string; lyrics: string }) {
  const supabase = createSupabaseServerClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'Authentication required.' };
  }

  const result = updateLyricsSchema.safeParse(params);

  if (!result.success) {
    return { error: 'Invalid input data.' };
  }

  const { songId, lyrics } = result.data;

  try {
    const { data: song, error: fetchError } = await supabase
      .from('songs')
      .select('creator_user_id')
      .eq('id', songId)
      .single();

    if (fetchError) throw fetchError;
    if (!song) return { error: 'Song not found.' };
    if (song.creator_user_id !== user.id) {
      return { error: 'Authorization denied.' };
    }

    const { error: updateError } = await supabase
      .from('songs')
      .update({
        lyrics: lyrics,
        updated_at: new Date().toISOString(),
      })
      .eq('id', songId);

    if (updateError) {
      throw updateError;
    }

    revalidatePath(`/ai-composer/workspace/${songId}`);
    return { success: true };

  } catch (error: any) {
    return { error: error.message || 'An unexpected error occurred.' };
  }
}
