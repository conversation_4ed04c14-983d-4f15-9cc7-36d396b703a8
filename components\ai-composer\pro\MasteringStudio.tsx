'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';

interface MasteringStudioProps {
  activeTab: string;
  songSections: any[];
  styleConfig: any;
  setStyleConfig: (config: any) => void;
}

export const MasteringStudio: React.FC<MasteringStudioProps> = ({
  activeTab,
  songSections,
  styleConfig,
  setStyleConfig
}) => {
  
  return (
    <div className="h-full p-6 flex items-center justify-center">
      <div className="text-center">
        <Sparkles className="h-16 w-16 text-slate-400 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-white mb-2">Studio de Mastering</h3>
        <p className="text-slate-400 mb-4">Fonctionnalité en développement</p>
        <Card className="bg-slate-700/50 border-slate-600 max-w-md">
          <CardContent className="p-4">
            <div className="text-sm text-slate-300">
              <p className="mb-2">Prochainement disponible :</p>
              <ul className="text-left space-y-1 text-slate-400">
                <li>• Chaîne de mastering complète</li>
                <li>• Analyseur de spectre</li>
                <li>• Limiteur et maximizer</li>
                <li>• Export multi-formats</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
