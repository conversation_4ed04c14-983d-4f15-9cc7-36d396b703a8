"use client"

import React, { useEffect, useRef } from "react"
import WaveSurfer from "wavesurfer.js"
import type { Song } from "@/types";
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore";

interface WaveformPlayerProps {
  song: Song;
  height?: number;
  waveColor?: string;
  progressColor?: string;
}

export function WaveformPlayer({
  song,
  height = 40,
  waveColor = "#9ca3af",
  progressColor = "#38bdf8"
}: WaveformPlayerProps) {
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurfer = useRef<WaveSurfer | null>(null);
  const isDestroyingRef = useRef(false);

  const { currentSong, isPlaying, audioElement } = useAudioPlayerStore();
  const isActivePlayer = currentSong?.id === song.id;

  useEffect(() => {
    if (!waveformRef.current || !audioElement || !isActivePlayer) {
      // If not the active player, ensure any existing instance is destroyed
      if (wavesurfer.current) {
        wavesurfer.current.destroy();
        wavesurfer.current = null;
      }
      return;
    }

    // If there's an existing instance for a different media, destroy it first
    if (wavesurfer.current && wavesurfer.current.getMediaElement() !== audioElement) {
      wavesurfer.current.destroy();
      wavesurfer.current = null;
    }

    // Initialize WaveSurfer if it doesn't exist for the current audio element
    if (!wavesurfer.current) {
      isDestroyingRef.current = false;
      const ws = WaveSurfer.create({
        container: waveformRef.current,
        waveColor,
        progressColor,
        height,
        barWidth: 2,
        barRadius: 3,
        cursorWidth: 0,
        media: audioElement, // Directly use the global audio element
        interact: false, // Disable clicks on the waveform
      });
      wavesurfer.current = ws;
    }

    // Cleanup function
    return () => {
      if (wavesurfer.current && !isDestroyingRef.current) {
        isDestroyingRef.current = true;
        try {
          wavesurfer.current.destroy();
        } catch (e) {
          console.warn("Error destroying WaveSurfer instance:", e);
        } finally {
          wavesurfer.current = null;
        }
      }
    };
  }, [isActivePlayer, audioElement, height, progressColor, waveColor, song.id]);

  // Sync play/pause state
  useEffect(() => {
    if (wavesurfer.current) {
      if (isPlaying) {
        wavesurfer.current.play();
      } else {
        wavesurfer.current.pause();
      }
    }
  }, [isPlaying]);

  return (
    <div ref={waveformRef} className="w-full h-full" style={{ minHeight: `${height}px` }} />
  );
}
