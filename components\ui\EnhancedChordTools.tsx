'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Slider } from '@/components/ui/slider';
import { 
  Play, Pause, Volume2, Filter, Search, Music, Guitar, Piano, 
  Plus, Save, Download, Upload, Eye, Edit, ZoomIn, ZoomOut, 
  Copy, FileText, Settings, RotateCcw, Trash2
} from 'lucide-react';
import { MidiChordPlayer } from '@/lib/chords/midi-chord-player';

// Types pour les données d'accords
interface ChordPosition {
  frets?: number[];
  fingers?: number[];
  keys?: string[]; // Pour piano
  barres?: Array<{
    fret: number;
    fromString: number;
    toString: number;
  }>;
  midi: number[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  baseFret?: number;
  position: string;
  notes: string[];
  style?: string; // Pour piano (classical, jazz, etc.)
  inversion?: number; // Pour piano
  fingering?: number[]; // Pour piano
}

interface ChordVariation {
  suffix: string;
  name: string;
  positions: ChordPosition[];
  styles?: Record<string, ChordPosition[]>; // Pour piano
}

interface ChordLibrary {
  instrument: string;
  tuning?: string[];
  strings?: number;
  fretRange?: [number, number];
  keys: string[];
  suffixes: string[];
  styles?: string[]; // Pour piano
  chords: Record<string, ChordVariation[]>;
  metadata?: {
    version: string;
    created: string;
    description: string;
    features: string[];
  };
}

interface CustomChord {
  id: string;
  name: string;
  instrument: string;
  tuning: string;
  position: ChordPosition;
  created: string;
  tags: string[];
}

interface EnhancedChordToolsProps {
  onChordInsert?: (chordText: string) => void;
  initialInstrument?: string;
  initialTuning?: string;
}

const EnhancedChordTools: React.FC<EnhancedChordToolsProps> = ({
  onChordInsert,
  initialInstrument = 'guitar',
  initialTuning = 'standard'
}) => {
  // États principaux
  const [selectedInstrument, setSelectedInstrument] = useState<string>(initialInstrument);
  const [selectedTuning, setSelectedTuning] = useState<string>(initialTuning);
  const [selectedKey, setSelectedKey] = useState<string>('C');
  const [selectedSuffix, setSelectedSuffix] = useState<string>('major');
  const [selectedPosition, setSelectedPosition] = useState<number>(0);
  const [selectedStyle, setSelectedStyle] = useState<string>('classical'); // Pour piano
  const [startingFret, setStartingFret] = useState<number>(0); // Position de départ sur le manche
  const [customChordBuilder, setCustomChordBuilder] = useState<{
    frets: number[];
    fingers: number[];
    name: string;
    isBuilding: boolean;
  }>({ frets: [], fingers: [], name: '', isBuilding: false });
  
  // États de filtrage et recherche
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all');
  
  // États de lecture MIDI
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [playMode, setPlayMode] = useState<'chord' | 'arpeggio'>('chord');
  const [arpeggioPattern, setArpeggioPattern] = useState<string>('ascending');
  const [midiPlayer, setMidiPlayer] = useState<MidiChordPlayer | null>(null);
  
  // États d'interface
  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [viewMode, setViewMode] = useState<'grid' | 'single'>('single');
  const [showCreateDialog, setShowCreateDialog] = useState<boolean>(false);
  const [showSaveDialog, setShowSaveDialog] = useState<boolean>(false);
  
  // États des données
  const [loadedLibraries, setLoadedLibraries] = useState<Record<string, ChordLibrary>>({});
  const [customChords, setCustomChords] = useState<CustomChord[]>([]);
  const [currentChord, setCurrentChord] = useState<ChordPosition | null>(null);
  
  // Références
  const diagramRef = useRef<HTMLDivElement>(null);

  // Configuration des instruments et accordages
  const INSTRUMENT_CONFIGS = {
    guitar: {
      name: 'Guitare',
      icon: Guitar,
      tunings: {
        standard: { name: 'Standard (EADGBE)', file: 'guitar_complete_extended.json' },
        'open-g': { name: 'Open G (DGDGBD)', file: 'guitar_open_g_complete.json' },
        'drop-d': { name: 'Drop D (DADGBE)', file: 'guitar_drop_d_complete.json' },
        'dadgad': { name: 'DADGAD', file: 'guitar_dadgad_tuning.json' },
        'open-d': { name: 'Open D (DADF#AD)', file: 'guitar_open_d.json' },
        'open-e': { name: 'Open E (EBEG#BE)', file: 'guitar_open_e.json' },
        'open-a': { name: 'Open A (EAEAC#E)', file: 'guitar_open_a.json' }
      }
    },
    ukulele: {
      name: 'Ukulélé',
      icon: Music,
      tunings: {
        gcea: { name: 'GCEA (Standard)', file: 'ukulele_complete_all_keys.json' },
        'gcea-complete': { name: 'GCEA Complet', file: 'ukulele_gcea_complete.json' }
      }
    },
    piano: {
      name: 'Piano',
      icon: Piano,
      tunings: {
        standard: { name: 'Standard', file: 'piano.json' }
      }
    },
    mandolin: {
      name: 'Mandoline',
      icon: Music,
      tunings: {
        gdae: { name: 'GDAE (Standard)', file: 'mandolin_gdae_tuning.json' }
      }
    },
    banjo: {
      name: 'Banjo',
      icon: Music,
      tunings: {
        '5string': { name: '5 Cordes (gDGBD)', file: 'banjo_5string_complete.json' },
        standard: { name: 'Standard', file: 'banjo.json' }
      }
    },
    bouzouki: {
      name: 'Bouzouki',
      icon: Music,
      tunings: {
        gdae: { name: 'GDAE', file: 'bouzouki_gdae_complete.json' }
      }
    }
  };

  // Initialisation du lecteur MIDI
  useEffect(() => {
    const initMidi = () => {
      try {
        const player = new MidiChordPlayer();
        setMidiPlayer(player);
      } catch (error) {
        console.warn('MIDI non disponible:', error);
      }
    };
    initMidi();
  }, []);

  // Chargement des bibliothèques d'accords
  useEffect(() => {
    const loadChordLibraries = async () => {
      const libraries: Record<string, ChordLibrary> = {};
      
      try {
        for (const [instrument, config] of Object.entries(INSTRUMENT_CONFIGS)) {
          for (const [tuning, tuningConfig] of Object.entries(config.tunings)) {
            const key = `${instrument}-${tuning}`;
            try {
              const response = await fetch(`/lib/chords/${tuningConfig.file}`);
              if (response.ok) {
                const data = await response.json();
                libraries[key] = data;
              }
            } catch (error) {
              console.warn(`Erreur lors du chargement de ${tuningConfig.file}:`, error);
            }
          }
        }
        
        setLoadedLibraries(libraries);
      } catch (error) {
        console.error('Erreur lors du chargement des bibliothèques:', error);
      }
    };

    loadChordLibraries();
  }, []);

  // Chargement des accords personnalisés depuis le localStorage
  useEffect(() => {
    const savedChords = localStorage.getItem('customChords');
    if (savedChords) {
      try {
        setCustomChords(JSON.parse(savedChords));
      } catch (error) {
        console.error('Erreur lors du chargement des accords personnalisés:', error);
      }
    }
  }, []);

  // Bibliothèque active
  const activeLibrary = loadedLibraries[`${selectedInstrument}-${selectedTuning}`];

  // Obtenir l'accord actuel
  const getCurrentChord = (): ChordPosition | null => {
    // Si on est en mode création d'accord personnalisé
    if (customChordBuilder.isBuilding && customChordBuilder.frets.length > 0) {
      return {
        frets: customChordBuilder.frets,
        fingers: customChordBuilder.fingers,
        midi: [], // À calculer
        difficulty: 'intermediate' as const,
        baseFret: startingFret,
        position: 'custom',
        notes: [] // À calculer
      };
    }
    
    if (!activeLibrary?.chords[selectedKey]) return null;
    
    const variations = activeLibrary.chords[selectedKey];
    const variation = variations.find(v => v.suffix === selectedSuffix);
    
    if (!variation) return null;
    
    // Pour le piano, utiliser les styles
    if (selectedInstrument === 'piano' && variation.styles && selectedStyle) {
      const stylePositions = variation.styles[selectedStyle];
      if (stylePositions && stylePositions[selectedPosition]) {
        return {
          ...stylePositions[selectedPosition],
          baseFret: startingFret
        };
      }
    }
    
    // Pour les autres instruments
    if (variation.positions && variation.positions[selectedPosition]) {
      return {
        ...variation.positions[selectedPosition],
        baseFret: startingFret || variation.positions[selectedPosition].baseFret
      };
    }
    
    return null;
  };

  // Fonctions pour la création d'accords personnalisés
  const startCustomChordBuilder = () => {
    const strings = activeLibrary?.strings || 6;
    setCustomChordBuilder({
      frets: new Array(strings).fill(-1),
      fingers: new Array(strings).fill(0),
      name: '',
      isBuilding: true
    });
  };

  const updateCustomChordFret = (stringIndex: number, fret: number) => {
    if (!customChordBuilder.isBuilding) return;
    
    const newFrets = [...customChordBuilder.frets];
    newFrets[stringIndex] = fret;
    
    setCustomChordBuilder(prev => ({
      ...prev,
      frets: newFrets
    }));
  };

  const updateCustomChordFinger = (stringIndex: number, finger: number) => {
    if (!customChordBuilder.isBuilding) return;
    
    const newFingers = [...customChordBuilder.fingers];
    newFingers[stringIndex] = finger;
    
    setCustomChordBuilder(prev => ({
      ...prev,
      fingers: newFingers
    }));
  };

  const saveCustomChordFromBuilder = () => {
    if (!customChordBuilder.isBuilding || !customChordBuilder.name) return;
    
    const newChord: CustomChord = {
      id: Date.now().toString(),
      name: customChordBuilder.name,
      instrument: selectedInstrument,
      tuning: selectedTuning,
      position: {
        frets: customChordBuilder.frets,
        fingers: customChordBuilder.fingers,
        midi: [], // À calculer
        difficulty: 'intermediate' as const,
        baseFret: startingFret,
        position: 'custom',
        notes: [] // À calculer
      },
      created: new Date().toISOString(),
      tags: ['custom']
    };
    
    const updatedChords = [...customChords, newChord];
    setCustomChords(updatedChords);
    localStorage.setItem('customChords', JSON.stringify(updatedChords));
    
    // Réinitialiser le builder
    setCustomChordBuilder({ frets: [], fingers: [], name: '', isBuilding: false });
  };

  const cancelCustomChordBuilder = () => {
    setCustomChordBuilder({ frets: [], fingers: [], name: '', isBuilding: false });
  };

  // Rendu du diagramme d'accord pour guitare/ukulélé/etc.
  const renderStringedInstrumentDiagram = (chord: ChordPosition) => {
    if (!activeLibrary || !chord.frets) return null;

    const strings = activeLibrary.strings || 6;
    const frets = chord.frets;
    const fingers = chord.fingers || [];
    const barres = chord.barres || [];
    const scale = zoomLevel / 100;
    const baseFret = chord.baseFret || startingFret;
    const isCustomBuilding = customChordBuilder.isBuilding;
    
    // Fonction pour gérer le clic sur une case
    const handleFretClick = (stringIndex: number, fretNumber: number) => {
      if (!isCustomBuilding) return;
      
      const actualFret = fretNumber === 0 ? 0 : fretNumber + baseFret;
      updateCustomChordFret(stringIndex, actualFret);
    };
    
    // Fonction pour gérer le clic droit (doigt)
    const handleFretRightClick = (e: React.MouseEvent, stringIndex: number, fretNumber: number) => {
      e.preventDefault();
      if (!isCustomBuilding) return;
      
      const currentFinger = customChordBuilder.fingers[stringIndex] || 0;
      const nextFinger = currentFinger >= 4 ? 0 : currentFinger + 1;
      updateCustomChordFinger(stringIndex, nextFinger);
    };

    return (
      <div className="chord-diagram bg-white border rounded-lg p-4 shadow-sm" style={{ transform: `scale(${scale})`, transformOrigin: 'top left' }}>
        <svg width="200" height="240" viewBox="0 0 200 240">
          {/* Cordes */}
          {Array.from({ length: strings }, (_, i) => (
            <line
              key={`string-${i}`}
              x1={30 + i * 28}
              y1={40}
              x2={30 + i * 28}
              y2={200}
              stroke="#333"
              strokeWidth="1"
            />
          ))}
          
          {/* Frettes */}
          {Array.from({ length: 6 }, (_, i) => (
            <line
              key={`fret-${i}`}
              x1={30}
              y1={40 + i * 26}
              x2={30 + (strings - 1) * 28}
              y2={40 + i * 26}
              stroke="#333"
              strokeWidth={i === 0 ? "3" : "1"}
            />
          ))}
          
          {/* Zones cliquables pour la création d'accords personnalisés */}
          {isCustomBuilding && Array.from({ length: strings }, (_, stringIndex) => 
            Array.from({ length: 6 }, (_, fretIndex) => (
              <rect
                key={`clickable-${stringIndex}-${fretIndex}`}
                x={30 + stringIndex * 28 - 12}
                y={fretIndex === 0 ? 8 : 40 + fretIndex * 26 - 13 - 12}
                width={24}
                height={24}
                fill="transparent"
                className="cursor-pointer hover:fill-blue-100 hover:fill-opacity-50"
                onClick={() => handleFretClick(stringIndex, fretIndex)}
                onContextMenu={(e) => handleFretRightClick(e, stringIndex, fretIndex)}
              />
            ))
          )}
          
          {/* Barrés */}
          {barres.map((barre, index) => {
            const fretY = 40 + (barre.fret - (chord.baseFret || 0) + 1) * 26 - 13;
            const startX = 30 + (barre.fromString - 1) * 28;
            const endX = 30 + (barre.toString - 1) * 28;
            
            return (
              <g key={`barre-${index}`}>
                <line
                  x1={startX}
                  y1={fretY}
                  x2={endX}
                  y2={fretY}
                  stroke="#e74c3c"
                  strokeWidth="8"
                  strokeLinecap="round"
                />
                <text
                  x={startX - 15}
                  y={fretY + 4}
                  fontSize="12"
                  fill="#e74c3c"
                  fontWeight="bold"
                >
                  {barre.fret}
                </text>
              </g>
            );
          })}
          
          {/* Doigtés */}
          {frets.map((fret, stringIndex) => {
            if (fret === -1) {
              return (
                <g key={`finger-${stringIndex}`}>
                  <text
                    x={30 + stringIndex * 28}
                    y={25}
                    fontSize="16"
                    textAnchor="middle"
                    fill={isCustomBuilding ? "#f59e0b" : "#e74c3c"}
                    fontWeight="bold"
                  >
                    ×
                  </text>
                </g>
              );
            } else if (fret === 0) {
              return (
                <g key={`finger-${stringIndex}`}>
                  <circle
                    cx={30 + stringIndex * 28}
                    cy={20}
                    r="8"
                    fill="none"
                    stroke={isCustomBuilding ? "#f59e0b" : "#27ae60"}
                    strokeWidth="2"
                  />
                </g>
              );
            } else {
              const fretY = 40 + (fret - (chord.baseFret || 0) + 1) * 26 - 13;
              const isInBarre = barres.some(barre => 
                barre.fret === fret && 
                stringIndex + 1 >= barre.fromString && 
                stringIndex + 1 <= barre.toString
              );
              
              if (isInBarre) return null;
              
              return (
                <g key={`finger-${stringIndex}`}>
                  <circle
                    cx={30 + stringIndex * 28}
                    cy={fretY}
                    r="10"
                    fill={isCustomBuilding ? "#f59e0b" : "#3498db"}
                    stroke="#2980b9"
                    strokeWidth="2"
                  />
                  <text
                    x={30 + stringIndex * 28}
                    y={fretY + 4}
                    fontSize="12"
                    textAnchor="middle"
                    fill="white"
                    fontWeight="bold"
                  >
                    {fingers[stringIndex] || ''}
                  </text>
                </g>
              );
            }
          })}
          
          {/* Numéros de frettes */}
          {chord.baseFret && chord.baseFret > 0 && (
            <>
              <text x="10" y="70" fontSize="12" fill="#666">{chord.baseFret}</text>
              <text x="10" y="96" fontSize="12" fill="#666">{chord.baseFret + 1}</text>
              <text x="10" y="122" fontSize="12" fill="#666">{chord.baseFret + 2}</text>
              <text x="10" y="148" fontSize="12" fill="#666">{chord.baseFret + 3}</text>
              <text x="10" y="174" fontSize="12" fill="#666">{chord.baseFret + 4}</text>
            </>
          )}
          
          {/* Notes */}
          <text x="100" y="230" fontSize="10" textAnchor="middle" fill="#666">
            {chord.notes.filter(note => note !== '').join(' - ')}
          </text>
        </svg>
        
        {/* Instructions pour la création d'accords personnalisés */}
        {isCustomBuilding && (
          <div className="text-sm text-gray-600 text-center max-w-md mt-2">
            <p><strong>Clic gauche:</strong> Placer/déplacer le doigt</p>
            <p><strong>Clic droit:</strong> Changer le numéro de doigt (1-4)</p>
            <p><strong>Case vide:</strong> Corde non jouée</p>
          </div>
        )}
      </div>
    );
  };

  // Rendu du diagramme de piano
  const renderPianoDiagram = (chord: ChordPosition) => {
    if (!chord.keys && !chord.midi) return null;

    const scale = zoomLevel / 100;
    const whiteKeys = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
    const blackKeys = ['C#', 'D#', '', 'F#', 'G#', 'A#', ''];
    
    // Convertir les notes MIDI en touches de piano
    const activeKeys = chord.keys || chord.midi.map(note => {
      const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
      return noteNames[note % 12];
    });

    return (
      <div className="piano-diagram bg-white border rounded-lg p-4 shadow-sm" style={{ transform: `scale(${scale})`, transformOrigin: 'top left' }}>
        <svg width="300" height="120" viewBox="0 0 300 120">
          {/* Touches blanches */}
          {whiteKeys.map((key, index) => {
            const isActive = activeKeys.includes(key);
            return (
              <rect
                key={`white-${index}`}
                x={index * 40}
                y={20}
                width="38"
                height="80"
                fill={isActive ? '#3498db' : '#ffffff'}
                stroke="#333"
                strokeWidth="1"
              />
            );
          })}
          
          {/* Touches noires */}
          {blackKeys.map((key, index) => {
            if (!key) return null;
            const isActive = activeKeys.includes(key);
            const xPos = index * 40 + 25;
            return (
              <rect
                key={`black-${index}`}
                x={xPos}
                y={20}
                width="26"
                height="50"
                fill={isActive ? '#e74c3c' : '#333'}
                stroke="#000"
                strokeWidth="1"
              />
            );
          })}
          
          {/* Labels des notes */}
          <text x="150" y="115" fontSize="12" textAnchor="middle" fill="#666">
            {chord.notes.join(' - ')}
          </text>
        </svg>
        
        {/* Informations supplémentaires pour piano */}
        {chord.style && (
          <div className="mt-2 text-sm text-gray-600">
            <span className="font-medium">Style:</span> {chord.style}
            {chord.inversion !== undefined && (
              <span className="ml-4"><span className="font-medium">Inversion:</span> {chord.inversion}</span>
            )}
          </div>
        )}
      </div>
    );
  };

  // Jouer l'accord ou l'arpège
  const playChord = async (chord: ChordPosition) => {
    if (!midiPlayer || !chord) return;

    setIsPlaying(true);
    try {
      // Ensure chord has required properties for MIDI player
      const midiChord = {
        frets: chord.frets || [],
        fingers: chord.fingers || [],
        barres: chord.barres || [],
        midi: chord.midi,
        difficulty: chord.difficulty,
        baseFret: chord.baseFret || 0
      };
      
      if (playMode === 'chord') {
        await midiPlayer.playChord(midiChord, 2000);
      } else {
        await midiPlayer.playArpeggio(midiChord, arpeggioPattern as any, 100, 2000);
      }
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  // Arrêter la lecture
  const stopPlayback = () => {
    if (midiPlayer) {
      midiPlayer.stopAll();
      setIsPlaying(false);
    }
  };

  // Sauvegarder un accord personnalisé
  const saveCustomChord = (name: string, tags: string[]) => {
    const chord = getCurrentChord();
    if (!chord) return;

    const customChord: CustomChord = {
      id: Date.now().toString(),
      name,
      instrument: selectedInstrument,
      tuning: selectedTuning,
      position: chord,
      created: new Date().toISOString(),
      tags
    };

    const updatedChords = [...customChords, customChord];
    setCustomChords(updatedChords);
    localStorage.setItem('customChords', JSON.stringify(updatedChords));
    setShowSaveDialog(false);
  };

  // Insérer l'accord dans le bloc paroles
  const insertChordInLyrics = () => {
    const chord = getCurrentChord();
    if (!chord || !onChordInsert) return;

    const chordName = `${selectedKey}${selectedSuffix === 'major' ? '' : selectedSuffix}`;
    onChordInsert(`[${chordName}]`);
  };

  // Exporter l'accord
  const exportChord = () => {
    const chord = getCurrentChord();
    if (!chord) return;

    const exportData = {
      name: `${selectedKey}${selectedSuffix === 'major' ? '' : selectedSuffix}`,
      instrument: selectedInstrument,
      tuning: selectedTuning,
      position: chord,
      exported: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${exportData.name}-${selectedInstrument}-${selectedTuning}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const currentChordData = getCurrentChord();

  if (!activeLibrary) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-6">
          <div className="text-center">
            <Music className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">Chargement des bibliothèques d'accords...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* En-tête avec contrôles principaux */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {React.createElement(INSTRUMENT_CONFIGS[selectedInstrument as keyof typeof INSTRUMENT_CONFIGS]?.icon || Music, { className: "h-6 w-6" })}
              Outils d'Accords Avancés
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={() => setShowCreateDialog(true)}>
                <Plus className="w-4 h-4 mr-1" /> Créer
              </Button>
              <Button size="sm" variant="outline" onClick={() => setShowSaveDialog(true)} disabled={!currentChordData}>
                <Save className="w-4 h-4 mr-1" /> Sauver
              </Button>
              <Button size="sm" variant="outline" onClick={exportChord} disabled={!currentChordData}>
                <Download className="w-4 h-4 mr-1" /> Export
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Sélection d'instrument */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Instrument</Label>
              <Select value={selectedInstrument} onValueChange={setSelectedInstrument}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(INSTRUMENT_CONFIGS).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center gap-2">
                        {React.createElement(config.icon, { className: "w-4 h-4" })}
                        {config.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection d'accordage */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Accordage</Label>
              <Select value={selectedTuning} onValueChange={setSelectedTuning}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(INSTRUMENT_CONFIGS[selectedInstrument as keyof typeof INSTRUMENT_CONFIGS]?.tunings || {}).map(([key, tuning]) => (
                    <SelectItem key={key} value={key}>
                      {tuning.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection de tonalité */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Tonalité</Label>
              <Select value={selectedKey} onValueChange={setSelectedKey}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {activeLibrary.keys.map(key => (
                    <SelectItem key={key} value={key}>{key}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection de type d'accord */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Type d'accord</Label>
              <Select value={selectedSuffix} onValueChange={setSelectedSuffix}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {activeLibrary.suffixes.map(suffix => (
                    <SelectItem key={suffix} value={suffix}>
                      {suffix === 'major' ? 'Majeur' : 
                       suffix === 'minor' ? 'Mineur' : 
                       suffix === '7' ? 'Septième' :
                       suffix === 'maj7' ? 'Majeur 7' :
                       suffix === 'm7' ? 'Mineur 7' :
                       suffix === 'sus2' ? 'Sus2' :
                       suffix === 'sus4' ? 'Sus4' :
                       suffix === 'dim' ? 'Diminué' :
                       suffix === 'aug' ? 'Augmenté' :
                       suffix === '5' ? 'Power Chord' :
                       suffix}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection de la frette de départ */}
            {selectedInstrument !== 'piano' && (
              <div>
                <Label className="text-sm font-medium mb-2 block">Frette de départ</Label>
                <Select value={startingFret.toString()} onValueChange={(value) => setStartingFret(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 13 }, (_, i) => (
                      <SelectItem key={i} value={i.toString()}>
                        {i === 0 ? 'Tête de manche' : `${i}ème frette`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Style pour piano */}
          {selectedInstrument === 'piano' && activeLibrary.styles && (
            <div className="mt-4">
              <Label className="text-sm font-medium mb-2 block">Style</Label>
              <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {activeLibrary.styles.map(style => (
                    <SelectItem key={style} value={style}>
                      {style.charAt(0).toUpperCase() + style.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contenu principal */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Diagramme d'accord */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>
                {activeLibrary.chords[selectedKey]?.find(v => v.suffix === selectedSuffix)?.name || `${selectedKey}${selectedSuffix}`}
              </span>
              <div className="flex items-center gap-2">
                {currentChordData && (
                  <Badge variant={currentChordData.difficulty === 'beginner' ? 'default' : 
                                 currentChordData.difficulty === 'intermediate' ? 'secondary' : 'destructive'}>
                    {currentChordData.difficulty === 'beginner' ? 'Débutant' :
                     currentChordData.difficulty === 'intermediate' ? 'Intermédiaire' : 'Avancé'}
                  </Badge>
                )}
                <div className="flex gap-1">
                  <Button size="sm" variant="outline" onClick={() => setZoomLevel(Math.max(50, zoomLevel - 25))}>
                    <ZoomOut className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setZoomLevel(Math.min(200, zoomLevel + 25))}>
                    <ZoomIn className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div ref={diagramRef} className="overflow-auto">
              {currentChordData && (
                selectedInstrument === 'piano' 
                  ? renderPianoDiagram(currentChordData)
                  : renderStringedInstrumentDiagram(currentChordData)
              )}
            </div>
            
            {/* Contrôles de position */}
            {currentChordData && (
              <div className="mt-4 space-y-2">
                <Label className="text-sm font-medium">Position ({selectedPosition + 1})</Label>
                <div className="flex items-center gap-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setSelectedPosition(Math.max(0, selectedPosition - 1))}
                    disabled={selectedPosition === 0}
                  >
                    ←
                  </Button>
                  <span className="text-sm text-gray-600 min-w-[100px] text-center">
                    Position {selectedPosition + 1}
                  </span>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setSelectedPosition(selectedPosition + 1)}
                    disabled={!activeLibrary.chords[selectedKey]?.find(v => v.suffix === selectedSuffix)?.positions?.[selectedPosition + 1]}
                  >
                    →
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contrôles et actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              Contrôles et Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Lecture MIDI */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Lecture MIDI</Label>
              <div className="flex gap-2">
                <Select value={playMode} onValueChange={(value: 'chord' | 'arpeggio') => setPlayMode(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chord">Accord</SelectItem>
                    <SelectItem value="arpeggio">Arpège</SelectItem>
                  </SelectContent>
                </Select>
                
                {playMode === 'arpeggio' && (
                  <Select value={arpeggioPattern} onValueChange={setArpeggioPattern}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ascending">Montant</SelectItem>
                      <SelectItem value="descending">Descendant</SelectItem>
                      <SelectItem value="alternating">Alterné</SelectItem>
                      <SelectItem value="up">Haut</SelectItem>
                      <SelectItem value="down">Bas</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => currentChordData && playChord(currentChordData)}
                  disabled={isPlaying || !midiPlayer || !currentChordData}
                  className="flex-1"
                >
                  {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                  {playMode === 'chord' ? 'Jouer Accord' : 'Jouer Arpège'}
                </Button>
                
                {isPlaying && (
                  <Button onClick={stopPlayback} variant="outline">
                    <Pause className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Actions</Label>
              {!customChordBuilder.isBuilding ? (
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant="outline" 
                    onClick={insertChordInLyrics}
                    disabled={!currentChordData || !onChordInsert}
                  >
                    <FileText className="w-4 h-4 mr-1" />
                    Insérer
                  </Button>
                  <Button variant="outline" onClick={exportChord} disabled={!currentChordData}>
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </Button>
                  <Button variant="outline" onClick={() => setShowSaveDialog(true)} disabled={!currentChordData}>
                    <Save className="w-4 h-4 mr-1" />
                    Sauver
                  </Button>
                  <Button variant="outline" onClick={() => navigator.clipboard.writeText(JSON.stringify(currentChordData))} disabled={!currentChordData}>
                    <Copy className="w-4 h-4 mr-1" />
                    Copier
                  </Button>
                  {selectedInstrument !== 'piano' && (
                    <Button
                      onClick={startCustomChordBuilder}
                      variant="outline"
                      className="bg-amber-50 hover:bg-amber-100 border-amber-300 col-span-2"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Créer accord personnalisé
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  <Input
                    placeholder="Nom de l'accord"
                    value={customChordBuilder.name}
                    onChange={(e) => setCustomChordBuilder(prev => ({ ...prev, name: e.target.value }))}
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={saveCustomChordFromBuilder}
                      disabled={!customChordBuilder.name || customChordBuilder.frets.every(f => f === -1)}
                      className="bg-green-600 hover:bg-green-700 flex-1"
                    >
                      <Save className="w-4 h-4 mr-1" />
                      Sauvegarder
                    </Button>
                    <Button
                      onClick={cancelCustomChordBuilder}
                      variant="outline"
                      className="flex-1"
                    >
                      Annuler
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Zoom */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Zoom ({zoomLevel}%)</Label>
              <Slider
                value={[zoomLevel]}
                onValueChange={(value) => setZoomLevel(value[0])}
                min={50}
                max={200}
                step={25}
                className="w-full"
              />
            </div>

            {/* Informations sur l'accord */}
            {currentChordData && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Informations</Label>
                <div className="text-sm text-gray-600 space-y-1">
                  <div><span className="font-medium">Notes:</span> {currentChordData.notes.join(', ')}</div>
                  <div><span className="font-medium">Difficulté:</span> {currentChordData.difficulty}</div>
                  {currentChordData.baseFret && <div><span className="font-medium">Frette de base:</span> {currentChordData.baseFret}</div>}
                  {selectedInstrument === 'piano' && currentChordData.style && (
                    <div><span className="font-medium">Style:</span> {currentChordData.style}</div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Accords personnalisés */}
      {customChords.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Accords Personnalisés</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {customChords.map(chord => (
                <Card key={chord.id} className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{chord.name}</h4>
                    <Button size="sm" variant="outline" onClick={() => {
                      const updatedChords = customChords.filter(c => c.id !== chord.id);
                      setCustomChords(updatedChords);
                      localStorage.setItem('customChords', JSON.stringify(updatedChords));
                    }}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="text-sm text-gray-600">
                    <div>{chord.instrument} - {chord.tuning}</div>
                    <div>{new Date(chord.created).toLocaleDateString()}</div>
                    {chord.tags.length > 0 && (
                      <div className="flex gap-1 mt-1">
                        {chord.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">{tag}</Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialog de sauvegarde */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sauvegarder l'accord</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="chord-name">Nom de l'accord</Label>
              <Input 
                id="chord-name" 
                placeholder={`${selectedKey}${selectedSuffix === 'major' ? '' : selectedSuffix}`}
              />
            </div>
            <div>
              <Label htmlFor="chord-tags">Tags (séparés par des virgules)</Label>
              <Input 
                id="chord-tags" 
                placeholder="rock, débutant, position haute"
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={() => {
                const nameInput = document.getElementById('chord-name') as HTMLInputElement;
                const tagsInput = document.getElementById('chord-tags') as HTMLInputElement;
                const name = nameInput?.value || `${selectedKey}${selectedSuffix === 'major' ? '' : selectedSuffix}`;
                const tags = tagsInput?.value.split(',').map(tag => tag.trim()).filter(tag => tag);
                saveCustomChord(name, tags);
              }}>
                Sauvegarder
              </Button>
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Annuler
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedChordTools;