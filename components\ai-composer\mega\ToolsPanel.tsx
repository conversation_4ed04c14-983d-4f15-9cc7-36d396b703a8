'use client';

import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import RhymeGenerator from './tools/RhymeGenerator';
import ChordProgressionSelector from './tools/ChordProgressionSelector';
import SyllableCounter from './tools/SyllableCounter';
import QuickTools from './tools/QuickTools';

// Define UserProfileForSidebar inline for now, ideally this comes from a shared types file
interface UserProfileForSidebar {
  email?: string | null | undefined;
  username?: string | null | undefined;
  avatarUrl?: string | null | undefined;
  firstName?: string | null | undefined;
  lastName?: string | null | undefined;
  bio?: string | null | undefined;
  id?: string | null | undefined;
}

// Types
interface ToolsPanelProps {
  onInsertText?: (text: string) => void;
  userProfile?: UserProfileForSidebar | null; // Added userProfile prop
}

export const ToolsPanel: React.FC<ToolsPanelProps> = ({ onInsertText, userProfile }) => {
  return (
    <ScrollArea className="h-full w-full">
      <div className="p-4 space-y-6">
        <QuickTools onInsertText={onInsertText} />
        <RhymeGenerator />
        <ChordProgressionSelector onInsertText={onInsertText} />
        <SyllableCounter />
      </div>
    </ScrollArea>
  );
};

export default ToolsPanel;