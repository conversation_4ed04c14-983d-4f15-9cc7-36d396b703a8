'use client';

import React, { useState, useCallback, useMemo, memo, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Music, Plus, Trash2, Edit3, ArrowUp, ArrowDown,
  Play, Pause, Clock, BarChart3, Target, Shuffle, Volume2, Settings,
  Brain, Wand2, Download, Upload, Copy, Save, FileText, Zap,
  TrendingUp, Activity, Layers, Sparkles, RefreshCcw, Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/hooks/use-toast';

interface SongSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  duration: number;
  startTime: number;
  key?: string;
  tempo?: number;
  chords?: string[];
}

interface SongStructure {
  sections: SongSection[];
  totalDuration: number;
  key: string;
  tempo: number;
  timeSignature: string;
}

interface AIAnalysis {
  structureScore: number;
  suggestions: string[];
  energyFlow: Array<{ time: number; energy: number }>;
  keyChanges: Array<{ time: number; key: string }>;
  tempoChanges: Array<{ time: number; tempo: number }>;
}

interface ExportOptions {
  format: 'json' | 'midi' | 'txt' | 'pdf';
  includeChords: boolean;
  includeLyrics: boolean;
  includeStructure: boolean;
}

interface UnifiedSongStructureTimelineProps {
  structure?: SongStructure;
  onStructureChange?: (structure: SongStructure) => void;
  onSectionSelect?: (sectionId: string) => void;
  selectedSectionId?: string;
  audioUrl?: string;
  isPlaying?: boolean;
  currentTime?: number;
  onPlayPause?: () => void;
  onSeek?: (time: number) => void;
  viewMode?: 'list' | 'timeline';
  onAIAnalyze?: (structure: SongStructure) => Promise<AIAnalysis>;
  onExport?: (options: ExportOptions) => Promise<void>;
  onImport?: (file: File) => Promise<SongStructure>;
  aiAnalysis?: AIAnalysis;
}

const SECTION_TYPES = [
  { value: 'intro', label: 'Intro', color: 'bg-blue-500', lightColor: 'bg-blue-100', textColor: 'text-blue-800' },
  { value: 'verse', label: 'Couplet', color: 'bg-green-500', lightColor: 'bg-green-100', textColor: 'text-green-800' },
  { value: 'pre-chorus', label: 'Pré-refrain', color: 'bg-yellow-500', lightColor: 'bg-yellow-100', textColor: 'text-yellow-800' },
  { value: 'chorus', label: 'Refrain', color: 'bg-red-500', lightColor: 'bg-red-100', textColor: 'text-red-800' },
  { value: 'bridge', label: 'Pont', color: 'bg-purple-500', lightColor: 'bg-purple-100', textColor: 'text-purple-800' },
  { value: 'outro', label: 'Outro', color: 'bg-gray-500', lightColor: 'bg-gray-100', textColor: 'text-gray-800' },
  { value: 'coda', label: 'Coda', color: 'bg-indigo-500', lightColor: 'bg-indigo-100', textColor: 'text-indigo-800' }
] as const;

const STRUCTURE_TEMPLATES = [
  {
    name: 'Pop Standard',
    description: 'Structure pop classique avec intro, couplets, refrains et pont',
    sections: [
      { type: 'intro', duration: 8 },
      { type: 'verse', duration: 16 },
      { type: 'chorus', duration: 16 },
      { type: 'verse', duration: 16 },
      { type: 'chorus', duration: 16 },
      { type: 'bridge', duration: 8 },
      { type: 'chorus', duration: 16 },
      { type: 'outro', duration: 8 }
    ]
  },
  {
    name: 'Ballade',
    description: 'Structure de ballade avec développement progressif',
    sections: [
      { type: 'intro', duration: 12 },
      { type: 'verse', duration: 20 },
      { type: 'pre-chorus', duration: 8 },
      { type: 'chorus', duration: 20 },
      { type: 'verse', duration: 20 },
      { type: 'pre-chorus', duration: 8 },
      { type: 'chorus', duration: 20 },
      { type: 'bridge', duration: 16 },
      { type: 'chorus', duration: 20 },
      { type: 'outro', duration: 16 }
    ]
  },
  {
    name: 'Rock Énergique',
    description: 'Structure rock avec sections courtes et dynamiques',
    sections: [
      { type: 'intro', duration: 4 },
      { type: 'verse', duration: 12 },
      { type: 'chorus', duration: 12 },
      { type: 'verse', duration: 12 },
      { type: 'chorus', duration: 12 },
      { type: 'bridge', duration: 8 },
      { type: 'chorus', duration: 12 },
      { type: 'chorus', duration: 12 },
      { type: 'outro', duration: 4 }
    ]
  }
];

const AI_PROMPTS = [
  {
    id: 'analyze-structure',
    title: 'Analyser la structure',
    description: 'Analyser la cohérence et l\'efficacité de la structure',
    prompt: 'Analyse cette structure de chanson et donne des suggestions d\'amélioration'
  },
  {
    id: 'suggest-arrangement',
    title: 'Suggérer un arrangement',
    description: 'Proposer des idées d\'arrangement pour chaque section',
    prompt: 'Suggère des idées d\'arrangement musical pour cette structure'
  },
  {
    id: 'optimize-flow',
    title: 'Optimiser le flow',
    description: 'Améliorer la fluidité et les transitions',
    prompt: 'Comment améliorer les transitions et le flow de cette structure ?'
  },
  {
    id: 'generate-variations',
    title: 'Générer des variations',
    description: 'Créer des variations de la structure actuelle',
    prompt: 'Crée 3 variations de cette structure en gardant l\'essence musicale'
  }
];

const DEFAULT_STRUCTURE: SongStructure = {
  sections: [
    { id: '1', type: 'intro', title: 'Intro', duration: 8, startTime: 0 },
    { id: '2', type: 'verse', title: 'Couplet 1', duration: 16, startTime: 8 },
    { id: '3', type: 'chorus', title: 'Refrain', duration: 16, startTime: 24 },
    { id: '4', type: 'verse', title: 'Couplet 2', duration: 16, startTime: 40 },
    { id: '5', type: 'chorus', title: 'Refrain', duration: 16, startTime: 56 },
    { id: '6', type: 'outro', title: 'Outro', duration: 8, startTime: 72 }
  ],
  totalDuration: 80,
  key: 'C',
  tempo: 120,
  timeSignature: '4/4'
};

const UnifiedSongStructureTimelineComponent = ({
  structure = DEFAULT_STRUCTURE,
  onStructureChange,
  onSectionSelect,
  selectedSectionId,
  audioUrl,
  isPlaying = false,
  currentTime = 0,
  onPlayPause,
  onSeek,
  viewMode = 'list',
  onAIAnalyze,
  onExport,
  onImport,
  aiAnalysis
}: UnifiedSongStructureTimelineProps) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [draggedSection, setDraggedSection] = useState<string | null>(null);
  const [resizingSection, setResizingSection] = useState<string | null>(null);
  const [currentViewMode, setCurrentViewMode] = useState<'list' | 'timeline'>(viewMode);
  const timelineRef = useRef<HTMLDivElement>(null);
  const [timelineWidth, setTimelineWidth] = useState(800);
  
  // Nouveaux états pour les fonctionnalités avancées
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [isAIAnalyzing, setIsAIAnalyzing] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'json',
    includeChords: true,
    includeLyrics: true,
    includeStructure: true
  });
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Recalculer les startTime basés sur les durées
  const recalculateStartTimes = useCallback((sections: SongSection[]): SongSection[] => {
    let currentTime = 0;
    return sections.map(section => {
      const updatedSection = { ...section, startTime: currentTime };
      currentTime += section.duration;
      return updatedSection;
    });
  }, []);

  // Calculer les données de timeline
  const timelineData = useMemo(() => {
    if (!structure || !structure.sections) {
      return [];
    }
    const pixelsPerSecond = timelineWidth / structure.totalDuration;
    return structure.sections.map(section => ({
      ...section,
      left: section.startTime * pixelsPerSecond,
      width: section.duration * pixelsPerSecond
    }));
  }, [structure, timelineWidth]);

  const getSectionTypeInfo = useCallback((type: string) => {
    return SECTION_TYPES.find(t => t.value === type) || SECTION_TYPES[0];
  }, []);

  const addSection = useCallback((type: SongSection['type'], insertAfter?: string) => {
    const insertIndex = insertAfter 
      ? structure.sections.findIndex(s => s.id === insertAfter) + 1
      : structure.sections.length;
    
    const newSection: SongSection = {
      id: Date.now().toString(),
      type,
      title: getSectionTypeInfo(type).label,
      duration: 16,
      startTime: 0 // Will be recalculated
    };
    
    const newSections = [...structure.sections];
    newSections.splice(insertIndex, 0, newSection);
    const recalculatedSections = recalculateStartTimes(newSections);
    
    const newStructure = {
      ...structure,
      sections: recalculatedSections,
      totalDuration: structure.totalDuration + 16
    };
    
    onStructureChange?.(newStructure);
  }, [structure, onStructureChange, getSectionTypeInfo, recalculateStartTimes]);

  const removeSection = useCallback((sectionId: string) => {
    const section = structure.sections.find(s => s.id === sectionId);
    const newSections = structure.sections.filter(s => s.id !== sectionId);
    const recalculatedSections = recalculateStartTimes(newSections);
    
    const newStructure = {
      ...structure,
      sections: recalculatedSections,
      totalDuration: structure.totalDuration - (section?.duration || 0)
    };
    
    onStructureChange?.(newStructure);
  }, [structure, onStructureChange, recalculateStartTimes]);

  const updateSectionDuration = useCallback((sectionId: string, newDuration: number) => {
    const sectionIndex = structure.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) return;

    const newSections = [...structure.sections];
    const oldDuration = newSections[sectionIndex].duration;
    const durationDiff = newDuration - oldDuration;
    
    newSections[sectionIndex].duration = newDuration;
    const recalculatedSections = recalculateStartTimes(newSections);
    
    const newStructure = {
      ...structure,
      sections: recalculatedSections,
      totalDuration: structure.totalDuration + durationDiff
    };
    
    onStructureChange?.(newStructure);
  }, [structure, onStructureChange, recalculateStartTimes]);

  const moveSectionUp = useCallback((index: number) => {
    if (index === 0) return;
    
    const newSections = [...structure.sections];
    [newSections[index - 1], newSections[index]] = [newSections[index], newSections[index - 1]];
    const recalculatedSections = recalculateStartTimes(newSections);
    
    onStructureChange?.({
      ...structure,
      sections: recalculatedSections
    });
  }, [structure, onStructureChange, recalculateStartTimes]);

  const moveSectionDown = useCallback((index: number) => {
    if (index === structure.sections.length - 1) return;
    
    const newSections = [...structure.sections];
    [newSections[index], newSections[index + 1]] = [newSections[index + 1], newSections[index]];
    const recalculatedSections = recalculateStartTimes(newSections);
    
    onStructureChange?.({
      ...structure,
      sections: recalculatedSections
    });
  }, [structure, onStructureChange, recalculateStartTimes]);

  const formatDuration = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Nouvelles fonctions avancées
  const handleAIAnalyze = useCallback(async () => {
    if (!onAIAnalyze) return;
    
    setIsAIAnalyzing(true);
    try {
      await onAIAnalyze(structure);
      toast({
        title: "Analyse IA terminée",
        description: "L'analyse de la structure a été effectuée avec succès"
      });
    } catch (error) {
      toast({
        title: "Erreur d'analyse",
        description: "Impossible d'analyser la structure",
        variant: "destructive"
      });
    } finally {
      setIsAIAnalyzing(false);
    }
  }, [onAIAnalyze, structure]);

  const handleExport = useCallback(async () => {
    if (!onExport) return;
    
    try {
      await onExport(exportOptions);
      setShowExportDialog(false);
      toast({
        title: "Export réussi",
        description: `Structure exportée au format ${exportOptions.format.toUpperCase()}`
      });
    } catch (error) {
      toast({
        title: "Erreur d'export",
        description: "Impossible d'exporter la structure",
        variant: "destructive"
      });
    }
  }, [onExport, exportOptions]);

  const handleImport = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !onImport) return;
    
    try {
      const importedStructure = await onImport(file);
      onStructureChange?.(importedStructure);
      toast({
        title: "Import réussi",
        description: "Structure importée avec succès"
      });
    } catch (error) {
      toast({
        title: "Erreur d'import",
        description: "Impossible d'importer le fichier",
        variant: "destructive"
      });
    }
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onImport, onStructureChange]);

  const applyTemplate = useCallback((templateName: string) => {
    const template = STRUCTURE_TEMPLATES.find(t => t.name === templateName);
    if (!template) return;
    
    const newSections: SongSection[] = template.sections.map((section, index) => ({
      id: `section-${Date.now()}-${index}`,
      type: section.type as SongSection['type'],
      title: getSectionTypeInfo(section.type).label + (index > 0 && template.sections.slice(0, index).some(s => s.type === section.type) ? ` ${index + 1}` : ''),
      duration: section.duration,
      startTime: 0 // Will be recalculated
    }));
    
    const recalculatedSections = recalculateStartTimes(newSections);
    const totalDuration = recalculatedSections.reduce((sum, section) => sum + section.duration, 0);
    
    const newStructure: SongStructure = {
      ...structure,
      sections: recalculatedSections,
      totalDuration
    };
    
    onStructureChange?.(newStructure);
    setShowTemplateDialog(false);
    
    toast({
      title: "Template appliqué",
      description: `Structure "${templateName}" appliquée avec succès`
    });
  }, [structure, onStructureChange, getSectionTypeInfo, recalculateStartTimes]);

  const duplicateSection = useCallback((sectionId: string) => {
    const sectionIndex = structure.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) return;
    
    const originalSection = structure.sections[sectionIndex];
    const duplicatedSection: SongSection = {
      ...originalSection,
      id: `${originalSection.id}-copy-${Date.now()}`,
      title: `${originalSection.title} (Copie)`
    };
    
    const newSections = [...structure.sections];
    newSections.splice(sectionIndex + 1, 0, duplicatedSection);
    const recalculatedSections = recalculateStartTimes(newSections);
    
    const newStructure = {
      ...structure,
      sections: recalculatedSections,
      totalDuration: structure.totalDuration + duplicatedSection.duration
    };
    
    onStructureChange?.(newStructure);
    
    toast({
      title: "Section dupliquée",
      description: `${originalSection.title} a été dupliquée`
    });
  }, [structure, onStructureChange, recalculateStartTimes]);

  const handleTimelineClick = useCallback((event: React.MouseEvent) => {
    if (!timelineRef.current || !onSeek) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickTime = (clickX / timelineWidth) * structure.totalDuration;
    
    onSeek(Math.max(0, Math.min(clickTime, structure.totalDuration)));
  }, [timelineWidth, structure.totalDuration, onSeek]);

  // Mise à jour de la largeur de timeline
  useEffect(() => {
    const updateTimelineWidth = () => {
      if (timelineRef.current) {
        setTimelineWidth(timelineRef.current.offsetWidth - 40); // padding
      }
    };

    updateTimelineWidth();
    window.addEventListener('resize', updateTimelineWidth);
    return () => window.removeEventListener('resize', updateTimelineWidth);
  }, []);

  const renderListView = () => (
    <ScrollArea className="h-[400px]">
      <div className="space-y-2 p-2">
        {structure.sections.map((section, index) => {
          const typeInfo = getSectionTypeInfo(section.type);
          const isSelected = selectedSectionId === section.id;
          
          return (
            <div
              key={section.id}
              className={cn(
                "p-3 rounded-lg border cursor-pointer transition-all",
                isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"
              )}
              onClick={() => onSectionSelect?.(section.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className={cn(typeInfo.lightColor, typeInfo.textColor)}>
                    {typeInfo.label}
                  </Badge>
                  <span className="font-medium">{section.title}</span>
                  <span className="text-sm text-muted-foreground">
                    {formatDuration(section.duration)}
                  </span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      moveSectionUp(index);
                    }}
                    disabled={index === 0}
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      moveSectionDown(index);
                    }}
                    disabled={index === structure.sections.length - 1}
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingSection(section.id);
                    }}
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeSection(section.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {editingSection === section.id && (
                <div className="mt-3 pt-3 border-t space-y-2">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm">Durée (secondes):</Label>
                    <Input
                      type="number"
                      value={section.duration}
                      onChange={(e) => updateSectionDuration(section.id, parseInt(e.target.value) || 16)}
                      className="w-20"
                    />
                    <Button
                      size="sm"
                      onClick={() => setEditingSection(null)}
                    >
                      OK
                    </Button>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );

  const renderTimelineView = () => (
    <div className="space-y-4">
      {/* Contrôles audio */}
      {audioUrl && (
        <div className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
          <Button
            variant="outline"
            size="sm"
            onClick={onPlayPause}
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
          <span className="text-sm font-mono">
            {formatDuration(currentTime)} / {formatDuration(structure.totalDuration)}
          </span>
        </div>
      )}
      
      {/* Timeline visuelle */}
      <div className="relative">
        <div
          ref={timelineRef}
          className="relative h-20 bg-muted/30 rounded-lg cursor-pointer overflow-hidden"
          onClick={handleTimelineClick}
        >
          {/* Sections */}
          {timelineData.map((section) => {
            const typeInfo = getSectionTypeInfo(section.type);
            const isSelected = selectedSectionId === section.id;
            
            return (
              <div
                key={section.id}
                className={cn(
                  "absolute top-2 bottom-2 rounded border-2 transition-all cursor-pointer",
                  typeInfo.color,
                  isSelected ? "border-white shadow-lg" : "border-transparent"
                )}
                style={{
                  left: `${section.left}px`,
                  width: `${section.width}px`
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  onSectionSelect?.(section.id);
                }}
              >
                <div className="p-1 text-white text-xs font-medium truncate">
                  {section.title}
                </div>
                <div className="px-1 text-white text-xs opacity-75">
                  {formatDuration(section.duration)}
                </div>
              </div>
            );
          })}
          
          {/* Curseur de lecture */}
          {audioUrl && (
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10"
              style={{
                left: `${(currentTime / structure.totalDuration) * timelineWidth}px`
              }}
            />
          )}
        </div>
        
        {/* Échelle de temps */}
        <div className="flex justify-between text-xs text-muted-foreground mt-1">
          <span>0:00</span>
          <span>{formatDuration(structure.totalDuration)}</span>
        </div>
      </div>
      
      {/* Liste des sections avec édition rapide */}
      <ScrollArea className="h-[200px]">
        <div className="space-y-1">
          {structure.sections.map((section, index) => {
            const typeInfo = getSectionTypeInfo(section.type);
            const isSelected = selectedSectionId === section.id;
            
            return (
              <div
                key={section.id}
                className={cn(
                  "flex items-center gap-2 p-2 rounded cursor-pointer",
                  isSelected ? "bg-primary/10" : "hover:bg-muted/50"
                )}
                onClick={() => onSectionSelect?.(section.id)}
              >
                <Badge className={cn(typeInfo.lightColor, typeInfo.textColor)} variant="outline">
                  {typeInfo.label}
                </Badge>
                <span className="flex-1 text-sm">{section.title}</span>
                <span className="text-xs text-muted-foreground">
                  {formatDuration(section.startTime)} - {formatDuration(section.startTime + section.duration)}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    duplicateSection(section.id);
                  }}
                  title="Dupliquer la section"
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeSection(section.id);
                  }}
                  title="Supprimer la section"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Music className="h-5 w-5" />
            Structure & Timeline
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              {formatDuration(structure.totalDuration)}
            </div>
            <Separator orientation="vertical" className="h-4" />
            <Tabs value={currentViewMode} onValueChange={(value) => setCurrentViewMode(value as 'list' | 'timeline')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="list" className="flex items-center gap-1">
                  <BarChart3 className="h-3 w-3" />
                  Liste
                </TabsTrigger>
                <TabsTrigger value="timeline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Timeline
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm">
            <Badge variant="outline">{structure.key} majeur</Badge>
            <Badge variant="outline">{structure.tempo} BPM</Badge>
            <Badge variant="outline">{structure.timeSignature}</Badge>
          </div>
          
          {/* Barre d'outils avancée */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTemplateDialog(true)}
              className="flex items-center gap-1"
            >
              <Wand2 className="h-3 w-3" />
              Templates
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleAIAnalyze}
              disabled={isAIAnalyzing}
              className="flex items-center gap-1"
            >
              {isAIAnalyzing ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <Brain className="h-3 w-3" />
              )}
              Analyser IA
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-1"
            >
              <Upload className="h-3 w-3" />
              Importer
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExportDialog(true)}
              className="flex items-center gap-1"
            >
              <Download className="h-3 w-3" />
              Exporter
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.xml,.midi"
              onChange={handleImport}
              className="hidden"
            />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Menu d'ajout de sections */}
        <div className="flex flex-wrap gap-2">
          {SECTION_TYPES.map((type) => (
            <Button
              key={type.value}
              variant="outline"
              size="sm"
              onClick={() => addSection(type.value as SongSection['type'])}
              className="flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              {type.label}
            </Button>
          ))}
        </div>
        
        <Separator />
        
        {/* Contenu selon le mode de vue */}
        <Tabs value={currentViewMode} className="w-full">
          <TabsContent value="list" className="mt-0">
            {renderListView()}
          </TabsContent>
          <TabsContent value="timeline" className="mt-0">
            {renderTimelineView()}
          </TabsContent>
        </Tabs>
      </CardContent>
      
      {/* Dialogue des templates */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              Templates de Structure
            </DialogTitle>
            <DialogDescription>
              Choisissez un template prédéfini pour votre structure de chanson
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4">
            {STRUCTURE_TEMPLATES.map((template) => (
              <div
                key={template.name}
                className={cn(
                  "p-4 border rounded-lg cursor-pointer transition-all hover:bg-muted/50",
                  selectedTemplate === template.name ? "border-primary bg-primary/5" : "border-border"
                )}
                onClick={() => setSelectedTemplate(template.name)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{template.name}</h3>
                  <Badge variant="outline">{formatDuration(template.sections.reduce((sum, s) => sum + s.duration, 0))}</Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-3">{template.description}</p>
                <div className="flex flex-wrap gap-1">
                  {template.sections.map((section, index) => {
                    const typeInfo = getSectionTypeInfo(section.type);
                    return (
                      <Badge
                        key={index}
                        className={cn(typeInfo.lightColor, typeInfo.textColor)}
                        variant="outline"
                      >
                        {typeInfo.label} ({formatDuration(section.duration)})
                      </Badge>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTemplateDialog(false)}>
              Annuler
            </Button>
            <Button
              onClick={() => selectedTemplate && applyTemplate(selectedTemplate)}
              disabled={!selectedTemplate}
            >
              Appliquer le Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Dialogue d'export */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Exporter la Structure
            </DialogTitle>
            <DialogDescription>
              Configurez les options d'export pour votre structure
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="export-format">Format d'export</Label>
              <Select
                value={exportOptions.format}
                onValueChange={(value) => setExportOptions(prev => ({ ...prev, format: value as ExportOptions['format'] }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="xml">XML</SelectItem>
                  <SelectItem value="midi">MIDI</SelectItem>
                  <SelectItem value="txt">Texte</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-3">
              <Label>Éléments à inclure</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-structure"
                    checked={exportOptions.includeStructure}
                    onCheckedChange={(checked: boolean) => setExportOptions(prev => ({ ...prev, includeStructure: !!checked }))}
                  />
                  <Label htmlFor="include-structure">Structure des sections</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-chords"
                    checked={exportOptions.includeChords}
                    onCheckedChange={(checked: boolean) => setExportOptions(prev => ({ ...prev, includeChords: !!checked }))}
                  />
                  <Label htmlFor="include-chords">Accords</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-lyrics"
                    checked={exportOptions.includeLyrics}
                    onCheckedChange={(checked: boolean) => setExportOptions(prev => ({ ...prev, includeLyrics: !!checked }))}
                  />
                  <Label htmlFor="include-lyrics">Paroles</Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExportDialog(false)}>
              Annuler
            </Button>
            <Button onClick={handleExport}>
              Exporter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export const UnifiedSongStructureTimeline = memo(UnifiedSongStructureTimelineComponent);
export default UnifiedSongStructureTimeline;