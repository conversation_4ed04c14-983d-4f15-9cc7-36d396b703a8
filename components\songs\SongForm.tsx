"use client";

// React et hooks essentiels
import React, { 
  useEffect, 
  useState, 
  useCallback, 
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo
} from 'react';

// Navigation
import { useRouter } from 'next/navigation';

// Validation et formulaires
import { useForm, FormProvider as RHFFormProvider, useFormContext, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  useFieldArray, 
  SubmitHandler, 
  UseFormReturn,
  Control,
  FieldErrors,
  UseFormSetValue,
  UseFormGetValues,
  UseFormWatch
} from 'react-hook-form';

// Gestion des fichiers (Drag & Drop)
import useLocalFileManagement, { type LocalFileState } from './hooks/useLocalFileManagement'; // Import custom hooks
// import { type AudioRecorderHandle } from '@/components/AudioRecorder'; // Removed duplicate import
import { useSongVersioning, type LocalSongVersion, type SongVersion } from './hooks/useSongVersioning'; // Adjusted path if necessary
import { useSongFormActions } from './hooks/useSongFormActions'; // Import the new hook
import type Quill from 'quill'; 

// Utilitaires
import debounce from 'lodash/debounce';

import { cn } from '@/lib/utils';
import { format, isValid as isValidDate, parseISO } from 'date-fns';
import { createClientComponentClient, User, SupabaseClient } from '@supabase/auth-helpers-nextjs';
import { toast, useSonner } from "sonner"; // Replaced useToast with sonner, added useSonner
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import type { Album } from "@/types/album"; // Should now resolve
import type { Song } from '@/components/songs/song-schema';

import SongVault, { type SongVaultHandle } from '@/components/songs/SongVault';
import { SongFormGeneralInfoTab } from './SongFormGeneralInfoTab';
import { SongFormProductionDetailsTab } from './SongFormProductionDetailsTab';
import { SongFormHeader } from './SongFormHeader';
import { SongFormLyricsChordTab } from './SongFormLyricsChordTab'; // For lyrics and chords
import { EnhancedLyricsChordEditor } from "./EnhancedLyricsChordEditor";
import { SongFormStructureTab } from './SongFormStructureTab'; // For song structure
import SongFormAiTab from './SongFormAiTab'; // For AI settings
import { SongFormAdvancedTab } from './SongFormAdvancedTab';
import { SongFormFooter } from './SongFormFooter';
import { SongVaultDisplay } from './SongVaultDisplay';
import { SongFormModals } from './SongFormModals';
import AudioRecorder, { AudioRecorderHandle } from '@/components/AudioRecorder';

// Placeholder UI Components (remove later when actual components are available)
// const SongFormHeader: React.FC<any> = (props) => <div {...props}>SongFormHeader Placeholder</div>; // Actual component is imported
// const SongFormAudioFilesTab: React.FC<any> = (props) => <div {...props}>SongFormAudioFilesTab Placeholder</div>; // Actual component is imported
// const SongFormSettingsTab: React.FC<any> = (props) => <div {...props}>SongFormSettingsTab Placeholder</div>; // Actual component is imported
import { SongFormCoverArtCard } from './SongFormCoverArtCard'; // Import new component
import { SongFormAudioCard } from './SongFormAudioCard'; // Import new component
import { fr } from 'date-fns/locale';
import { v4 as uuidv4 } from 'uuid';

// Composants UI (ShadCN)
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { DatePicker } from "@/components/ui/date-picker";
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

// Icônes (Lucide React)
import {
  Edit3,
  ExternalLink,
  Eye,
  EyeOff,
  PanelRightOpen,
  PanelRightClose,
  FileAudio,
  FileText,
  GanttChartSquare,
  Globe,
  GripVertical,
  History, // Was 'Versions', replaced with 'History'
  ImagePlus,
  Info,
  KeyRound,
  Layers,
  Link,
  ListMusic,
  Loader2,
  LoaderIcon, // Added LoaderIcon
  Lock,
  Maximize,
  Mic,
  Mic2,
  Minimize,
  Music,
  Music2,
  Music3,
  Music4,
  Palette,
  Paperclip,
  PauseCircle,
  Pencil,
  PlayCircle,
  PlusCircle,
  Podcast,
  Redo2,
  RotateCcw,
  Save,
  Search,
  Send,
  Settings2,
  Share2,
  Sheet,
  Sparkles,
  SplitSquareHorizontal,
  SquarePen,
  Tags,
  Trash2,
  Undo2,
  UploadCloud,
  UserPlus,
  Users,
  Verified,
  Volume2,
  Wand2,
  X,
  XCircle
} from 'lucide-react';

// Import schema and types
import { songSchema, SongFormValues, AiConfig, AiHistoryItem } from './song-schema'; // Added AiConfig, AiHistoryItem

// Import options
import {
  LANGUAGE_OPTIONS,
  SONG_STATUS_OPTIONS,
  ATTRIBUTION_TYPE_OPTIONS,
  GENRES_OPTIONS,
  SUBGENRES_OPTIONS,
  MOODS_OPTIONS,
  THEMES_OPTIONS,
  INSTRUMENTS_OPTIONS,
  MUSICAL_KEY_OPTIONS
} from './song-options';

// Props du composant SongForm
export interface SongFormProps {
  initialSongData?: Song | null;
  albumsData?: { id: string; title: string; }[];
  isLoadingAlbums?: boolean;
  onFormSubmit: (data: SongFormValues, coverArtFile?: File | null, audioFile?: File | null) => Promise<void>;
  isSubmitting?: boolean; // Renamed from isSubmittingGlobal
  mode?: 'create' | 'edit';
  onCancel?: () => void;
  onDelete?: (songId: string) => Promise<void>;
  currentUserId: string;
  supabaseClient: SupabaseClient;
  lastSavedTimestamp?: string;
}

// Interface pour les méthodes exposées par la ref du composant
export interface SongFormHandle {
  submit: () => Promise<void>;
  resetForm: (values?: Partial<SongFormValues>) => void;
  getFormValues: () => SongFormValues;
  setFieldValue: <K extends keyof SongFormValues>(name: K, value: SongFormValues[K]) => void;
  getVaultActions: () => { saveCurrentVersion: () => void; loadVersion: (versionId: string) => void; deleteVersion: (versionId: string) => void; savePendingItems: (songId: string, userId: string) => Promise<void>; };
}

// Types pour les sections du formulaire (pour la navigation par onglets)
export type FormSection = 'main' | 'production-details' | 'lyrics-chords' | 'structure' | 'ai';

// Placeholder for ChordInstrument type (if needed elsewhere, consider moving to a types file)
export type ChordInstrument = {
  name: string;
  diagrams: { fret: number, string: number, finger?: number }[];
};

// Helper function to parse chord strings (JSON or space-separated)
const parseChordString = (chordsString: string | null | undefined): any[] => {
  if (!chordsString || typeof chordsString !== 'string' || chordsString.trim() === '') {
    return [];
  }
  try {
    const parsed = JSON.parse(chordsString);
    if (Array.isArray(parsed)) {
      return parsed; // Assuming it's an array of chord objects
    }
    // If it's not an array, or some other JSON structure, treat as simple string list
  } catch (e) {
    // Not a valid JSON array, fall through to string splitting
  }
  // Fallback for space-separated or comma-separated simple chord names
  const potentialChords = chordsString.split(/[\s,]+/);
  return potentialChords.filter(name => name.trim() !== '').map(name => ({
    name: name.trim(),
    diagram: null // Or some default diagram structure
  }));
};

// Main component
export const SongForm = forwardRef<SongFormHandle, SongFormProps>(({
  initialSongData,
  albumsData = [],
  isLoadingAlbums = false,
  onFormSubmit,
  isSubmitting = false, // Corrected: use isSubmitting directly from props
  mode = 'create',
  onCancel,
  currentUserId,
  supabaseClient,
  onDelete,
  lastSavedTimestamp: lastSavedTimestampFromProps
}, ref): JSX.Element => {
  const router = useRouter();
  const supabase = supabaseClient || createClientComponentClient();
  const [user, setUser] = useState<User | null>(null);

  // isSubmitting is now directly available from props

  // Refs for file inputs
  const coverArtInputRef = useRef<HTMLInputElement>(null);
  const audioInputRef = useRef<HTMLInputElement>(null);

  // State for recorded audio
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  const [recordedAudioPreviewUrl, setRecordedAudioPreviewUrl] = useState<string | null>(null);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialSongData?.audio_url || null);
  const [isAudioRecordingActive, setIsAudioRecordingActive] = useState<boolean>(false);

  // Refs for form reset logic based on mode/initialData change
  const isVersionJustLoadedRef = useRef<boolean>(false);
  const prevModeRef = useRef<string | undefined>(mode || (initialSongData?.id ? 'edit' : 'create'));
  const prevInitialSongIdRef = useRef<string | null | undefined>(initialSongData?.id || null);

  // AI Feature Placeholders (to be properly implemented)
  const editorInstanceForAi = useRef<Quill | null>(null);

  // State for local file handling (cover art, audio)
  const [localCoverArtFile, setLocalCoverArtFile] = useState<LocalFileState>({ file: null, previewUrl: null, error: null, uploadProgress: 0, isUploading: false });
  const [localAudioFile, setLocalAudioFile] = useState<LocalFileState & { duration: number | null }>({ file: null, previewUrl: null, error: null, uploadProgress: 0, isUploading: false, duration: null });

  // UI State
  const [activeTab, setActiveTab] = useState<FormSection>('main'); // Changed 'general' to 'main'
  const [isVaultPanelCollapsed, setIsVaultPanelCollapsed] = useState<boolean>(true);
  const [lastSavedTimestamp, setLastSavedTimestamp] = useState<Date | null>(lastSavedTimestampFromProps ? new Date(lastSavedTimestampFromProps) : null);

  // Versioning State
  const [currentSongVersions, setCurrentSongVersions] = useState<LocalSongVersion[]>([]);
  const [activeVersionId, setActiveVersionId] = useState<string | null>(null);
  const [isSubmittingVersion, setIsSubmittingVersion] = useState(false);
  const [isLoadingDeleteVersion, setIsLoadingDeleteVersion] = useState(false);
  const [isLoadingUpdateVersion, setIsLoadingUpdateVersion] = useState(false);

  // AI Feature States
  const [aiConfigState, setAiConfigState] = useState<any | null>(null); // Changed AIConfig to any (placeholder)
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>('');
  const [aiHistoryState, setAiHistoryState] = useState<any[]>([]); // Replace any with actual type
  const [showAiHistory, setShowAiHistory] = useState<boolean>(false);

  // Placeholder Handlers will be moved after methodsTest destructuring

  const defaultSongFormValues = useMemo((): SongFormValues => ({
    title: '',
    artist: '',
    artist_name: null,
    duration_ms: null,
    bpm: null,
    musical_key: null,
    time_signature: null,
    genre: [], // Default to empty array for TEXT[] type
    subgenre: [],
    moods: [],
    lyrics: '', // Use empty string for nullable text fields for controlled inputs
    // composer_name: null, // Removed as it's not in SongFormValues type
    writers: [],
    producers: [],
    bloc_note: '',
    right_column_notepad: '',
    status: 'draft', // Sensible default
    progress_data: {},
    slug: '',
    attribution_type: null,
    tuning_frequency: null,
    description: '',
    lyrics_language: null,
    custom_css: '',
    chords: '',
    structure: '',
    notes: '',
    is_public: false,
    is_favorite: false,
    is_incomplete: true,
    is_cover: false,
    is_instrumental: false,
    is_explicit: false,
    is_archived: false,
    release_date: null,
    audio_url: null,
    cover_art_url: null,
    cover_art_file_name: null,
    audio_file_name: null,
    creator_user_id: currentUserId || '', // Ensure this is set
    allow_comments: true,
    allow_downloads: false,
    band_id: null,
    tags: [],
    themes: [],
    instruments: [],
    featured_artists: [],
    contributors: [],
    plays: 0,

    iswc_code: null,
    editor_data: {},
    isrc_code: null,
    upc_code: null,
    album_id: null,
    copyright_notice: '',
    publisher_name: '',
    record_label: '',
    licensing_info: '',
    language: null,
    parental_advisory: null,
    recording_date: null,
    recording_status: null,
    mastering_status: null,
    mixing_engineer: null,
    mastering_engineer: null,
    mixing_status: null,
    ai_collaboration_level: null,
    artwork_credits: '',
    tablature: '',
    performance_notes: '',
    song_versions: [],
    audio_file_versions: [],
    external_links: [],
    completion_percentage: 0,
    creation_process_type: null,
    custom_fields: {},
    lyrics_sync_data: null,
    chords_diagrams: {},
    privacy_settings: { allow_embedding: true, allow_download: false },
    collaborators: [],
    split_sheet: null,
    last_played_at: null,
    play_count: 0,
    rating_average: 0,
    rating_count: 0,
    comments_count: 0, // Added missing default
    shares_count: 0,   // Added missing default
    downloads_count: 0 // Added missing default
  }), [currentUserId]);

// ...

// Process initial values for the form, merging with defaults
const processedInitialValues = useMemo(() => {
  if (!initialSongData) { // Create mode
    return { ...defaultSongFormValues, creator_user_id: currentUserId || '', genre: defaultSongFormValues.genre || [] };
  }
  // Edit mode: merge initialSongData with defaults
  const mergedValues = {
    ...defaultSongFormValues,
    ...initialSongData,
    // Ensure creator_user_id is correctly sourced from initial data if available, otherwise from defaults (which includes currentUserId)
    creator_user_id: initialSongData.creator_user_id || defaultSongFormValues.creator_user_id,
    // Ensure genre is an array. If initialSongData.genre is null/undefined, use defaultSongFormValues.genre
    genre: Array.isArray(initialSongData.genre)
      ? initialSongData.genre
      : (initialSongData.genre ? [String(initialSongData.genre)] : (defaultSongFormValues.genre || [])),
    bpm: initialSongData.bpm ? Number(initialSongData.bpm) : (defaultSongFormValues.bpm || null),
    duration_ms: initialSongData.duration_ms ? Number(initialSongData.duration_ms) : (defaultSongFormValues.duration_ms || null),
  };
  // Filter out any keys from mergedValues that are not actual keys of SongFormValues (from defaultSongFormValues as reference)
  // This prevents passing unknown fields to RHF if initialSongData has extra properties (e.g., from DB joins).
  const allowedKeys = Object.keys(defaultSongFormValues);
  const filteredValues = Object.fromEntries(
    Object.entries(mergedValues).filter(([key]) => allowedKeys.includes(key))
  );
  return filteredValues as SongFormValues;
}, [initialSongData, defaultSongFormValues]); // Depends on initialSongData and the memoized defaultSongFormValues

/*
  const methods = useForm<SongFormValues>({
    resolver: zodResolver(songSchema),
    defaultValues: processedInitialValues,
    mode: 'onChange',
  });

  const { 
    watch, 
    control, 
    setValue, 
    handleSubmit, 
    reset: resetFormHook, 
    formState: { isDirty, errors: formErrors, isSubmitting: isFormSubmittingRHF },
    trigger,
    getValues
  } = methods;
*/

const methodsTest = useForm<SongFormValues>({
  resolver: zodResolver(songSchema),
  defaultValues: processedInitialValues,
  mode: 'onChange',
});

const {
  watch: watchTest,
  control: controlTest,
  setValue: setValueTest,
  handleSubmit: handleSubmitTest,
  reset: resetFormHookTest,
  formState: formStateTest,
  trigger: triggerTest,
  getValues: getValuesTest,
} = methodsTest;

// Watched values for dynamic UI updates
const watchedTitle = watchTest('title', initialSongData?.title || '');
const watchedArtist = watchTest('artist_name', initialSongData?.artist_name || ''); // Assuming artist_name is what's intended for display
const watchedLyrics = watchTest('lyrics', initialSongData?.lyrics || '');
const watchedChords = watchTest('chords', initialSongData?.chords || '');

// URLs for display (cover art and audio)
const displayCoverArtUrl = useMemo(() => {
  if (localCoverArtFile?.previewUrl) return localCoverArtFile.previewUrl;
  if (initialSongData?.cover_art_url) return initialSongData.cover_art_url;
  return null;
}, [localCoverArtFile, initialSongData?.cover_art_url]);

const displayAudioUrl = useMemo(() => {
  if (localAudioFile?.previewUrl) return localAudioFile.previewUrl;
  if (initialSongData?.audio_file_url) return initialSongData.audio_file_url;
  return null;
}, [localAudioFile, initialSongData?.audio_file_url]);

// Ref for the hidden submit button
const submitButtonRef = useRef<HTMLButtonElement>(null);
const songVaultActionsRef = useRef<SongVaultHandle>(null);

  useImperativeHandle(ref, () => ({
    submit: async () => {
      submitButtonRef.current?.click();
    },
    resetForm: (values?: Partial<SongFormValues>) => {
      resetFormHookTest(values);
      setLocalCoverArtFile({ file: null, previewUrl: null, error: null, uploadProgress: 0, isUploading: false });
      setLocalAudioFile({ file: null, previewUrl: null, error: null, uploadProgress: 0, isUploading: false, duration: null });
      setActiveTab('main');
      console.log('Form reset via ref.');
    },
    getFormValues: () => getValuesTest(),
    setFieldValue: <K extends keyof SongFormValues>(name: K, value: SongFormValues[K]) => {
      setValueTest(name, value, { shouldValidate: true, shouldDirty: true });
    },
    getVaultActions: () => ({
      saveCurrentVersion: () => handleSaveNewVersion(),
      loadVersion: (versionId: string) => handleLoadVersion(versionId),
      deleteVersion: (versionId: string) => handleDeleteVersionWrapper(versionId),
      savePendingItems: async (songId: string, userId: string) => { console.warn("savePendingItems not implemented"); return Promise.resolve(); },
    }),
    saveCurrentVersion: () => handleSaveNewVersion(),
    loadVersion: (versionId: string) => handleLoadVersion(versionId),
    deleteVersion: (versionId: string) => handleDeleteVersionWrapper(versionId),
    savePendingItems: async (songId: string, userId: string) => { console.warn("savePendingItems not implemented"); return Promise.resolve(); },
  }));

// Placeholder handlers for file inputs and audio recording
const handleCoverArtSelect = (file: File | null) => {
  if (file) {
    const previewUrl = URL.createObjectURL(file);
    setLocalCoverArtFile({ file, previewUrl, error: null, uploadProgress: 0, isUploading: false });
    setValueTest('cover_art_url', previewUrl, { shouldDirty: true });
  }
};

const handleClearCoverArt = () => {
  setLocalCoverArtFile({ file: null, previewUrl: null, error: null, uploadProgress: 0, isUploading: false });
  setValueTest('cover_art_url', null, { shouldDirty: true });
  if (coverArtInputRef.current) {
    coverArtInputRef.current.value = '';
  }
};

const handleAudioFileSelect = (file: File | null) => {
  if (file) {
    const previewUrl = URL.createObjectURL(file);
    const audio = new Audio(previewUrl);
    audio.onloadedmetadata = () => {
      setLocalAudioFile({ file, previewUrl, error: null, uploadProgress: 0, isUploading: false, duration: audio.duration });
      setValueTest('audio_url', previewUrl, { shouldDirty: true });
      setValueTest('duration_ms', audio.duration * 1000, { shouldDirty: true });
    };
  }
};

const handleClearAudio = () => {
  setLocalAudioFile({ file: null, previewUrl: null, error: null, uploadProgress: 0, isUploading: false, duration: null });
  setValueTest('audio_url', null, { shouldDirty: true });
  if (audioInputRef.current) {
    audioInputRef.current.value = '';
  }
};

const handleClearRecordedAudio = () => {
  setRecordedAudioBlob(null);
  setRecordedAudioPreviewUrl(null);
  setValueTest('audio_url', null, { shouldDirty: true });
};

const handleClearAllAudio = () => {
  handleClearAudio();
  handleClearRecordedAudio();
};

const addAiHistory = (item: any) => {
    setAiHistoryState(prev => [...prev, item]);
};

const onRecordingComplete = (audioBlob: Blob | null, previewUrl?: string | null) => {
    setRecordedAudioBlob(audioBlob);
    setRecordedAudioPreviewUrl(previewUrl || null);
    if (previewUrl) {
        setValueTest('audio_url', previewUrl, { shouldDirty: true });
    }
};

const onRecordingError = (error: string) => { 
  console.error('Recording error:', error);
  // Optionally, show a toast notification to the user
};

const handleLyricsChange = (content: string) => { 
  setValueTest('lyrics', content, { shouldDirty: true }); 
};

// Main form submission logic (placeholder)
const handleRHFSubmitWrapper = async (data: SongFormValues) => {
  console.warn('LOGIC MISSING: handleRHFSubmitWrapper called with:', data);
  const audioFileToSubmit = localAudioFile.file
    ? localAudioFile.file
    : recordedAudioBlob
    ? new File([recordedAudioBlob], 'recorded_audio.webm', { type: recordedAudioBlob.type })
    : null;
  await onFormSubmit(data, localCoverArtFile.file, audioFileToSubmit);
};

const handleRHFSubmitError = (errors: any) => { 
  console.warn('Form submission error:', errors); 
  // TODO: Add user-friendly error notifications (e.g., toast)
};

// Versioning handlers
const handleLoadVersion = async (versionId: string) => { console.warn('LOGIC MISSING: handleLoadVersion called with:', versionId); return Promise.resolve(); };

const handleDeleteVersionWrapper = async (versionId: string): Promise<void> => {
  const version = currentSongVersions.find(v => v.id === versionId);
  if (version) {
    setVersionToDelete(version);
    setIsConfirmDeleteVersionModalOpen(true);
  } else {
    console.error(`Version with id ${versionId} not found.`);
  }
};

const handleConfirmDeleteVersion = async () => {
  if (versionToDelete) {
    console.warn(`LOGIC MISSING: Deleting version: ${versionToDelete.id}`);
    // Actual deletion logic would go here, for now it's a placeholder.
    // await onDeleteVersion(versionToDelete.id);
    setIsConfirmDeleteVersionModalOpen(false);
    setVersionToDelete(null);
  }
};

const handleSaveNewVersion = async () => { console.warn('LOGIC MISSING: handleSaveNewVersion called'); return Promise.resolve(); };
const handleUpdateVersionDetails = async (versionId: string, newName: string, newNotes: string) => { console.warn('LOGIC MISSING: handleUpdateVersionDetails called with:', versionId, newName, newNotes); return Promise.resolve(); };

// Modal States
const [isSaveVersionModalOpen, setIsSaveVersionModalOpen] = useState(false);
const [newVersionName, setNewVersionName] = useState('');
const [newVersionNotes, setNewVersionNotes] = useState('');
const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState(false);
const [songToDelete, setSongToDelete] = useState<string | null>(null); // For deleting the whole song
const [isConfirmDeleteVersionModalOpen, setIsConfirmDeleteVersionModalOpen] = useState(false);
const [versionToDelete, setVersionToDelete] = useState<LocalSongVersion | null>(null);

const handleOpenSaveVersionModal = () => {
  setNewVersionName(`Version ${currentSongVersions.length + 1}`);
  setNewVersionNotes('');
  setIsSaveVersionModalOpen(true);
};

return (
    <RHFFormProvider {...methodsTest}>
      <form onSubmit={handleSubmitTest(handleRHFSubmitWrapper, handleRHFSubmitError)} className="space-y-6">
        
        <SongFormHeader
          mode={mode}
          songTitle={watchedTitle}
          artistName={watchedArtist || ''}
          coverArtUrl={displayCoverArtUrl}
          onSave={() => submitButtonRef.current?.click()}
          isSubmitting={isSubmitting || formStateTest.isSubmitting}
          isDirty={formStateTest.isDirty}
          lastSavedTimestamp={lastSavedTimestamp}
          isVaultPanelCollapsed={isVaultPanelCollapsed}
          onToggleVaultPanel={() => setIsVaultPanelCollapsed(prev => !prev)}
          setValue={setValueTest}
          localAudioFile={localAudioFile}
          handleAudioFileSelect={handleAudioFileSelect}
          handleClearAudio={handleClearAudio}
          handleClearAllAudio={handleClearAllAudio}
          recordedAudioBlob={recordedAudioBlob}
          recordedAudioPreviewUrl={recordedAudioPreviewUrl}
          onRecordingComplete={onRecordingComplete}
          onRecordingError={onRecordingError}
          handleClearRecordedAudio={handleClearRecordedAudio}
          localCoverArtFile={localCoverArtFile}
          onCoverArtSelect={handleCoverArtSelect}
          onClearCoverArt={handleClearCoverArt}
          coverArtInputRef={coverArtInputRef}
        />

        {/* Hidden submit button for form submission via ref */}
        <button type="submit" ref={submitButtonRef} style={{ display: 'none' }} />

        {/* Main Content Layout */}
        <div className="flex flex-col xl:flex-row gap-6 w-full">
          {/* Main Content Panel */}
          <div className="flex-grow min-w-0">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as FormSection)} className="w-full">
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-1 sm:gap-2 mb-6 h-auto p-1">
                <TabsTrigger value="main">Général</TabsTrigger>
                <TabsTrigger value="production-details">Production</TabsTrigger>
                <TabsTrigger value="lyrics-chords">Paroles & Accords</TabsTrigger>
                <TabsTrigger value="structure">Structure</TabsTrigger>
                <TabsTrigger value="ai">Assistant IA</TabsTrigger>
              </TabsList>

              <TabsContent value="main" className="space-y-6">
                <SongFormGeneralInfoTab
                  control={controlTest}
                  errors={formStateTest.errors}
                  watch={watchTest}
                  albumsData={albumsData}
                  isLoadingAlbums={isLoadingAlbums}
                />
              </TabsContent>

              <TabsContent value="production-details" className="space-y-6">
                <SongFormProductionDetailsTab control={controlTest} errors={formStateTest.errors} />
              </TabsContent>

              <TabsContent value="lyrics-chords" className="space-y-6">
                <SongFormLyricsChordTab
                  control={controlTest}
                  errors={formStateTest.errors}
                  lyricsContent={watchedLyrics || ''}
                  onLyricsChange={handleLyricsChange}
                  quillRef={editorInstanceForAi}
                  aiConfig={aiConfigState}
                  aiGeneralPrompt={aiGeneralPrompt}
                  addAiHistory={addAiHistory}
                  aiHistory={aiHistoryState}
                  showAiHistory={showAiHistory}
                  setShowAiHistory={setShowAiHistory}
                  formControl={controlTest}
                  songId={initialSongData?.id}
                />
              </TabsContent>
              <TabsContent value="structure" className="space-y-6">
                <SongFormStructureTab control={controlTest} errors={formStateTest.errors} />
              </TabsContent>

              <TabsContent value="ai" className="space-y-6">
                <SongFormAiTab control={controlTest} watch={watchTest} setValue={setValueTest} />
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Panel (Collapsible Vault) */}
          <Collapsible
            open={!isVaultPanelCollapsed}
            onOpenChange={(isOpen) => setIsVaultPanelCollapsed(!isOpen)}
            className="w-full xl:w-[380px] xl:max-w-sm xl:min-w-[300px]"
          >
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="w-full xl:hidden mb-4">
                {isVaultPanelCollapsed ? <><PanelRightOpen className="mr-2 h-4 w-4" /> Afficher le coffre-fort</> : <><PanelRightClose className="mr-2 h-4 w-4" /> Masquer le coffre-fort</>}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="xl:sticky xl:top-16 space-y-4">
              <SongVaultDisplay
                songId={initialSongData?.id || 'new-song'}
                versions={currentSongVersions}
                currentVersionId={activeVersionId}
                onLoadVersion={handleLoadVersion}
                onDeleteVersion={handleDeleteVersionWrapper}
                onSaveNewVersion={handleSaveNewVersion}
                isLoadingSaveVersion={isSubmittingVersion}
                isLoadingDeleteVersion={isLoadingDeleteVersion}
                onUpdateVersion={handleUpdateVersionDetails}
                isLoadingUpdateVersion={isLoadingUpdateVersion}
                songVaultActionsRef={songVaultActionsRef}
              />
            </CollapsibleContent>
          </Collapsible>
        </div>

        <SongFormFooter
          mode={mode}
          onCancel={onCancel ? onCancel : () => router.back()}
          onSaveNewVersion={handleOpenSaveVersionModal}
          isSubmittingGlobal={isSubmitting || formStateTest.isSubmitting}
          isFormSubmitting={formStateTest.isSubmitting}
          lastSavedTimestamp={lastSavedTimestamp ? lastSavedTimestamp.toISOString() : null}
        />

        <SongFormModals
          isSaveVersionModalOpen={isSaveVersionModalOpen}
          setIsSaveVersionModalOpen={setIsSaveVersionModalOpen}
          newVersionName={newVersionName}
          setNewVersionName={setNewVersionName}
          newVersionNotes={newVersionNotes}
          setNewVersionNotes={setNewVersionNotes}
          currentSongVersionsLength={currentSongVersions.length}
          handleSaveNewVersion={handleSaveNewVersion}
          isSubmittingVersion={isSubmittingVersion}
          isConfirmDeleteVersionModalOpen={isConfirmDeleteVersionModalOpen}
          setIsConfirmDeleteVersionModalOpen={setIsConfirmDeleteVersionModalOpen}
          versionToDelete={versionToDelete}
          setVersionToDelete={setVersionToDelete}
          handleDeleteVersion={handleConfirmDeleteVersion}
          isLoadingDeleteVersion={isLoadingDeleteVersion}
        />

        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 p-4 bg-muted rounded-md">
            <summary className="cursor-pointer">Voir l'état du formulaire (développement)</summary>
            <pre className="text-xs overflow-auto">{JSON.stringify(formStateTest, null, 2)}</pre>
            <pre className="text-xs overflow-auto mt-2">{JSON.stringify(methodsTest.getValues(), null, 2)}</pre>
          </details>
        )}
      </form>
    </RHFFormProvider>
  );
});

SongForm.displayName = 'SongForm';

export default SongForm;
