'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, Settings, Zap, BarChart3, History, Lightbulb, 
  Wand2, Target, TrendingUp, AlertCircle, CheckCircle2,
  Sparkles, Music, FileText, Guitar, Palette, Clock,
  MessageSquare, Send, Download, Upload, Copy, Star
} from 'lucide-react';

// Import des composants IA existants
import { AiConfigMenu } from '@/components/ia/ai-config-menu';
import { AiQuickActions } from '@/components/ia/ai-quick-actions';
import { AIInsightsPanel } from '../AIInsightsPanel';

interface AIAssistantMegaProProps {
  // Configuration IA
  isConfigured: boolean;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: {
    provider: string;
    model: string;
    temperature: number;
  };
  setAiConfig?: (config: any) => void;
  
  // Données contextuelles
  currentSection: string;
  songSections: any[];
  styleConfig: any;
  lyricsContent: string;
  chordPlacements: any[];
  
  // Historique et résultats
  aiHistory: Array<{ role: string; content: string; timestamp?: string; type?: string }>;
  lastAiResult: string;
  setAiHistory: (history: any[]) => void;
  setLastAiResult: (result: string) => void;
  
  // Actions IA
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
  onGeneralSuggestions: () => Promise<void>;
  onMelodySuggestion: () => Promise<void>;
  onArrangementAdvice: () => Promise<void>;
  
  // Callbacks pour intégration
  onInsertToLyrics?: (text: string) => void;
  onApplyChordSuggestion?: (chord: any) => void;
  onApplyStructureSuggestion?: (structure: any) => void;
}

export const AIAssistantMegaPro: React.FC<AIAssistantMegaProProps> = ({
  isConfigured,
  aiLoading,
  aiError,
  aiConfig = { provider: 'openai', model: 'gpt-3.5-turbo', temperature: 0.7 },
  setAiConfig = () => {},
  currentSection,
  songSections,
  styleConfig,
  lyricsContent,
  chordPlacements,
  aiHistory,
  lastAiResult,
  setAiHistory,
  setLastAiResult,
  onAIGenerate,
  onGeneralSuggestions,
  onMelodySuggestion,
  onArrangementAdvice,
  onInsertToLyrics,
  onApplyChordSuggestion,
  onApplyStructureSuggestion
}) => {
  
  const [activeTab, setActiveTab] = useState('actions');
  const [chatInput, setChatInput] = useState('');
  const [selectedInsight, setSelectedInsight] = useState<string | null>(null);

  // Métriques calculées en temps réel
  const metrics = React.useMemo(() => {
    const totalWords = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const totalLines = lyricsContent.split('\n').length;
    const totalChords = chordPlacements.length;
    const sectionsCount = songSections.length;
    
    return {
      completionScore: Math.min(100, (totalWords / 100) * 100),
      structureScore: Math.min(100, (sectionsCount / 4) * 100),
      harmonyScore: Math.min(100, (totalChords / 8) * 100),
      creativityScore: Math.min(100, (aiHistory.length / 5) * 100),
      totalWords,
      totalLines,
      totalChords,
      sectionsCount
    };
  }, [lyricsContent, chordPlacements, songSections, aiHistory]);

  // Insights intelligents basés sur l'analyse
  const insights = React.useMemo(() => {
    const insights = [];
    
    if (metrics.completionScore < 50) {
      insights.push({
        id: 'completion',
        type: 'warning',
        title: 'Composition incomplète',
        description: 'Ajoutez plus de contenu pour enrichir votre chanson',
        action: 'Générer des paroles',
        priority: 'high'
      });
    }
    
    if (metrics.harmonyScore < 30) {
      insights.push({
        id: 'harmony',
        type: 'info',
        title: 'Peu d\'accords',
        description: 'Enrichissez l\'harmonie avec plus d\'accords',
        action: 'Suggérer des accords',
        priority: 'medium'
      });
    }
    
    if (metrics.structureScore > 80) {
      insights.push({
        id: 'structure',
        type: 'success',
        title: 'Bonne structure',
        description: 'Votre chanson a une structure équilibrée',
        action: 'Optimiser l\'arrangement',
        priority: 'low'
      });
    }
    
    return insights;
  }, [metrics]);

  // Gestionnaire pour le chat IA
  const handleChatSubmit = useCallback(async () => {
    if (!chatInput.trim() || !isConfigured) return;
    
    try {
      await onAIGenerate(chatInput, 'chat');
      setChatInput('');
    } catch (error) {
      console.error('Erreur chat IA:', error);
    }
  }, [chatInput, isConfigured, onAIGenerate]);

  // Gestionnaire pour appliquer un insight
  const handleApplyInsight = useCallback(async (insight: any) => {
    setSelectedInsight(insight.id);
    
    switch (insight.id) {
      case 'completion':
        await onGeneralSuggestions();
        break;
      case 'harmony':
        await onMelodySuggestion();
        break;
      case 'structure':
        await onArrangementAdvice();
        break;
    }
    
    setTimeout(() => setSelectedInsight(null), 2000);
  }, [onGeneralSuggestions, onMelodySuggestion, onArrangementAdvice]);

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950 dark:to-blue-950">
      {/* En-tête avec statut IA */}
      <div className="border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Assistant IA Mega Pro
              </h2>
              <div className="flex items-center gap-2">
                {isConfigured ? (
                  <Badge variant="default" className="gap-1 bg-green-500">
                    <CheckCircle2 className="h-3 w-3" />
                    Configuré
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="gap-1">
                    <AlertCircle className="h-3 w-3" />
                    Configuration requise
                  </Badge>
                )}
                {aiLoading && (
                  <Badge variant="secondary" className="gap-1">
                    <Sparkles className="h-3 w-3 animate-spin" />
                    Génération...
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          {/* Score global */}
          <div className="text-right">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round((metrics.completionScore + metrics.structureScore + metrics.harmonyScore + metrics.creativityScore) / 4)}%
            </div>
            <div className="text-xs text-muted-foreground">Score global</div>
          </div>
        </div>
      </div>

      {/* Contenu principal avec tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="border-b bg-white/50 dark:bg-gray-900/50 px-4 py-2">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="actions" className="gap-1">
                <Zap className="h-3 w-3" />
                Actions
              </TabsTrigger>
              <TabsTrigger value="insights" className="gap-1">
                <BarChart3 className="h-3 w-3" />
                Insights
              </TabsTrigger>
              <TabsTrigger value="chat" className="gap-1">
                <MessageSquare className="h-3 w-3" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="history" className="gap-1">
                <History className="h-3 w-3" />
                Historique
              </TabsTrigger>
              <TabsTrigger value="config" className="gap-1">
                <Settings className="h-3 w-3" />
                Config
              </TabsTrigger>
            </TabsList>
          </div>
          
          <div className="flex-1 overflow-hidden">
            {/* Tab Actions Rapides */}
            <TabsContent value="actions" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {!isConfigured && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Configurez votre provider IA pour utiliser les fonctionnalités avancées.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <AiQuickActions
                    onGeneralSuggestions={onGeneralSuggestions}
                    onMelodySuggestion={onMelodySuggestion}
                    onArrangementAdvice={onArrangementAdvice}
                    isConfigured={isConfigured}
                    aiConfig={aiConfig}
                    setAiConfig={setAiConfig}
                    loading={aiLoading}
                    lastResult={lastAiResult}
                    error={aiError}
                    iaHistory={aiHistory}
                    generalPrompt=""
                    onEditGeneralPrompt={() => {}}
                  />
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Tab Insights & Métriques */}
            <TabsContent value="insights" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {/* Métriques visuelles */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-sm">
                        <TrendingUp className="h-4 w-4" />
                        Métriques de Composition
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Complétude</span>
                          <span>{metrics.completionScore}%</span>
                        </div>
                        <Progress value={metrics.completionScore} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Structure</span>
                          <span>{metrics.structureScore}%</span>
                        </div>
                        <Progress value={metrics.structureScore} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Harmonie</span>
                          <span>{metrics.harmonyScore}%</span>
                        </div>
                        <Progress value={metrics.harmonyScore} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Créativité IA</span>
                          <span>{metrics.creativityScore}%</span>
                        </div>
                        <Progress value={metrics.creativityScore} className="h-2" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Insights intelligents */}
                  <div className="space-y-2">
                    <h3 className="font-medium text-sm">Suggestions Intelligentes</h3>
                    {insights.map((insight) => (
                      <Card 
                        key={insight.id}
                        className={`cursor-pointer transition-all ${
                          selectedInsight === insight.id ? 'ring-2 ring-purple-500' : ''
                        }`}
                        onClick={() => handleApplyInsight(insight)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge 
                                  variant={insight.type === 'success' ? 'default' : 
                                          insight.type === 'warning' ? 'destructive' : 'secondary'}
                                  className="text-xs"
                                >
                                  {insight.priority}
                                </Badge>
                                <span className="font-medium text-sm">{insight.title}</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{insight.description}</p>
                            </div>
                            <Button variant="ghost" size="sm" className="text-xs">
                              {insight.action}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Tab Chat IA */}
            <TabsContent value="chat" className="h-full m-0 p-4">
              <div className="h-full flex flex-col">
                <div className="flex-1 mb-4">
                  <ScrollArea className="h-full border rounded-lg p-3 bg-white/50">
                    {aiHistory.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Commencez une conversation avec l'IA</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {aiHistory.slice(-10).map((message, index) => (
                          <div 
                            key={index}
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div 
                              className={`max-w-[80%] p-3 rounded-lg text-sm ${
                                message.role === 'user' 
                                  ? 'bg-purple-500 text-white' 
                                  : 'bg-white border'
                              }`}
                            >
                              {message.content}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
                
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleChatSubmit()}
                    placeholder="Posez une question à l'IA..."
                    className="flex-1 px-3 py-2 border rounded-lg text-sm"
                    disabled={!isConfigured || aiLoading}
                  />
                  <Button 
                    onClick={handleChatSubmit}
                    disabled={!isConfigured || aiLoading || !chatInput.trim()}
                    size="sm"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Tab Historique */}
            <TabsContent value="history" className="h-full m-0 p-4">
              <AIInsightsPanel
                aiHistory={aiHistory}
                lastResult={lastAiResult}
                songSections={songSections}
                styleConfig={styleConfig}
              />
            </TabsContent>

            {/* Tab Configuration */}
            <TabsContent value="config" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <AiConfigMenu inlineMode={true} />
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};
