"use client";

import { useState, useEffect, useTransition, useMemo } from 'react';
import Link from "next/link";
import { createBrowserClient } from "@/lib/supabase/client";
import { useUser } from '@/contexts/user-context';
import { Button } from "@/components/ui/button";
import { AlbumCard } from "@/components/albums/album-card";
import { Plus, Music, Loader2, LayoutGrid, ListFilter, Search as SearchIcon, ListMusic as ListMusicIcon } from "lucide-react";
import { Album } from "@/types";
import { toast } from 'sonner';
import { toggleAlbumVisibility, deleteAlbum } from './actions';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export type AlbumWithDetails = Album & {
  songs_count: number;
  total_duration_ms: number;
  like_count: number;
  view_count: number;
  profiles: any;
};

export default function AlbumsPage() {
  const supabase = createBrowserClient();
  const { user } = useUser();
  const [allAlbums, setAllAlbums] = useState<AlbumWithDetails[]>([]);
  const [displayedAlbums, setDisplayedAlbums] = useState<AlbumWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [updatingAlbumId, setUpdatingAlbumId] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('created_at-desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    const fetchAlbums = async () => {
      if (!supabase) {
        console.warn('ALBUMS PAGE - Supabase client not ready in fetchAlbums');
        toast.error("Client Supabase non initialisé.");
        return;
      }
      
      setIsLoading(true);
      setAllAlbums([]); // Reset albums before fetching

      // Get user first
      const { data: userData, error: authError } = await supabase.auth.getUser();
      
      console.log('ALBUMS PAGE - User data from supabase.auth.getUser():', userData);
      console.log('ALBUMS PAGE - Auth error from supabase.auth.getUser():', authError);

      if (authError || !userData || !userData.user) {
        const errorMsg = authError ? authError.message : 'Utilisateur non connecté ou session invalide.';
        console.error('ALBUMS PAGE - Erreur récupération utilisateur ou utilisateur non connecté:', errorMsg);
        toast.error(errorMsg);
        setIsLoading(false);
        return;
      }
      
      // Now we are sure userData.user exists
      const currentUser = userData.user;
      console.log('ALBUMS PAGE - ID Utilisateur pour RPC:', currentUser.id);

      // Fetch albums
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_albums_for_user', { p_user_id: currentUser.id });

      console.log('ALBUMS PAGE - Réponse RPC data:', rpcData);
      console.log('ALBUMS PAGE - Réponse RPC error:', rpcError);

      if (rpcError) {
        console.error('ALBUMS PAGE - Erreur RPC get_albums_for_user:', rpcError.message);
        toast.error('Impossible de charger les albums: ' + rpcError.message);
      } else {
        setAllAlbums((rpcData as AlbumWithDetails[]) || []);
      }
      setIsLoading(false);
    };

    fetchAlbums();
  }, [user?.id, supabase]);

  useEffect(() => {
    let filtered = [...allAlbums];

    if (searchTerm) {
      filtered = filtered.filter(a =>
        a.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    const [key, direction] = sortOption.split('-');
    filtered.sort((a, b) => {
      const valA = a[key as keyof AlbumWithDetails];
      const valB = b[key as keyof AlbumWithDetails];

      if (valA === null || valA === undefined) return 1;
      if (valB === null || valB === undefined) return -1;

      if (typeof valA === 'string' && typeof valB === 'string') {
        return direction === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
      }

      if (valA < valB) return direction === 'asc' ? -1 : 1;
      if (valA > valB) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    setDisplayedAlbums(filtered);
  }, [allAlbums, searchTerm, sortOption]);

  const handleToggleVisibility = (albumId: string, currentIsPublic: boolean) => {
    setUpdatingAlbumId(albumId);
    startTransition(async () => {
      const result = await toggleAlbumVisibility(albumId, currentIsPublic);
      if (result.error) {
        console.error('ALBUMS PAGE - Erreur toggleAlbumVisibility:', result.error);
toast.error(result.error);
      } else {
        console.log('ALBUMS PAGE - Visibilité de l\'album mise à jour avec succès.');
        toast.success(`Visibilité de l'album mise à jour.`);
        setAllAlbums((prev: AlbumWithDetails[]) => 
          prev.map((a: AlbumWithDetails) => a.id === albumId ? { ...a, is_public: result.newIsPublic! } : a)
        );
      }
      setUpdatingAlbumId(null);
    });
  };

  const handleDeleteAlbum = async (albumId: string) => {
    const originalAlbums = [...allAlbums];
    
    setAllAlbums(prev => prev.filter(a => a.id !== albumId));

    const result = await deleteAlbum(albumId);

    if (result.error) {
      toast.error(result.error);
      setAllAlbums(originalAlbums);
    } else {
      toast.success("Album supprimé avec succès.");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <Loader2 className="w-12 h-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div className="flex items-center gap-3">
          <Music className="w-8 h-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Mes Albums</h1>
            <p className="text-muted-foreground">Gérez vos albums, EPs et singles.</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/albums/create">
              <Plus className="w-4 h-4 mr-2" />
              Créer un album
            </Link>
          </Button>
        </div>
      </div>

      <div className="bg-card border rounded-lg p-4 mb-6">
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="relative w-full sm:w-auto sm:flex-grow">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
            <Input
              placeholder="Rechercher par titre..."
              className="pl-10 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-4 w-full sm:w-auto">
            <Select value={sortOption} onValueChange={setSortOption}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Trier par" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at-desc">Plus récents</SelectItem>
                <SelectItem value="created_at-asc">Plus anciens</SelectItem>
                <SelectItem value="title-asc">Titre (A-Z)</SelectItem>
                <SelectItem value="title-desc">Titre (Z-A)</SelectItem>
                <SelectItem value="songs_count-desc">Plus de morceaux</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant={viewMode === 'grid' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('grid')}>
                      <LayoutGrid className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Vue Grille</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant={viewMode === 'list' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('list')}>
                      <ListMusicIcon className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Vue Liste</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>

      {displayedAlbums.length === 0 && !isLoading ? (
        <div className="text-center py-16 border-2 border-dashed rounded-lg">
          <Music className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Aucun album trouvé</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Créez votre premier album ou affinez votre recherche.
          </p>
          <div className="mt-6">
            <Button asChild>
              <Link href="/albums/create">
                <Plus className="mr-2 h-4 w-4" /> Créer un album
              </Link>
            </Button>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        <div 
          className="grid gap-4 md:gap-6"
          style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))' }}
        >
          {displayedAlbums.map((album) => (
            <AlbumCard 
              key={album.id} 
              album={album} 
              onToggleVisibility={handleToggleVisibility}
              isVisibilityUpdating={updatingAlbumId === album.id}
              onDelete={handleDeleteAlbum}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 border-2 border-dashed rounded-lg">
           <h3 className="mt-4 text-lg font-medium">Vue Liste en construction</h3>
        </div>
      )}
    </div>
  );
}