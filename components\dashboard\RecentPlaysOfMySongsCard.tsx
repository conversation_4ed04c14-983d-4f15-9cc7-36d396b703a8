"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Headphones, Clock, Music, ExternalLink } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

interface PlayOfMySong {
  id: string
  song_id: string
  song_title: string
  song_cover_url: string | null
  created_at: string
  listener_id: string | null
  listener_username: string | null
  listener_avatar_url: string | null
}

interface RecentPlaysOfMySongsCardProps {
  recentPlays: PlayOfMySong[]
}

export function RecentPlaysOfMySongsCard({ recentPlays }: RecentPlaysOfMySongsCardProps) {
  // Fonction pour formater la date en "il y a X jours/heures"
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true,
        locale: fr
      })
    } catch (error) {
      return "Date inconnue"
    }
  }

  // Fonction pour obtenir les initiales à partir du nom d'utilisateur
  const getInitials = (username: string | null) => {
    if (!username) return "?"
    return username.charAt(0).toUpperCase()
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-md font-medium flex items-center">
              <Headphones className="h-4 w-4 mr-2" />
              Écoutes récentes
            </CardTitle>
            <CardDescription>Les dernières écoutes de vos morceaux</CardDescription>
          </div>
          {recentPlays.length > 0 && (
            <Button variant="outline" size="sm" asChild>
              <Link href="/stats">
                Statistiques
              </Link>
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {recentPlays && recentPlays.length > 0 ? (
          <div className="space-y-4">
            {recentPlays.map((play) => (
              <div key={play.id} className="flex items-center space-x-4">
                <div className="h-10 w-10 rounded-md overflow-hidden bg-muted flex-shrink-0">
                  {play.song_cover_url ? (
                    <img
                      src={play.song_cover_url}
                      alt={play.song_title}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-secondary">
                      <Music className="h-5 w-5 text-muted-foreground" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{play.song_title}</p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatDate(play.created_at)}
                    {play.listener_username && (
                      <>
                        <span className="mx-1">•</span>
                        <span className="flex items-center">
                          <Avatar className="h-4 w-4 mr-1">
                            <AvatarImage src={play.listener_avatar_url || ""} alt={play.listener_username} />
                            <AvatarFallback className="text-[8px]">{getInitials(play.listener_username)}</AvatarFallback>
                          </Avatar>
                          {play.listener_username}
                        </span>
                      </>
                    )}
                  </div>
                </div>
                <Button variant="outline" size="sm" asChild className="flex-shrink-0">
                  <Link href={`/song/${play.song_id}`}>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            <p>Aucune écoute récente</p>
            <p className="text-xs mt-1">Partagez votre musique pour obtenir plus d'écoutes</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
