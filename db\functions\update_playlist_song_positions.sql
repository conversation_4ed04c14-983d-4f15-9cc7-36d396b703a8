CREATE OR REPLACE FUNCTION update_playlist_song_positions(
  p_playlist_id UUID,
  p_song_ids UUID[],
  p_requesting_user_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  playlist_owner_id UUID;
  i INTEGER;
BEGIN
  -- Verify if the playlist exists and if the requesting user is the owner
  SELECT user_id INTO playlist_owner_id FROM public.playlists WHERE id = p_playlist_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Playlist not found: %', p_playlist_id;
  END IF;

  IF playlist_owner_id != p_requesting_user_id THEN
    RAISE EXCEPTION 'Permission denied to modify playlist: %', p_playlist_id;
  END IF;

  -- Update song positions
  -- Assumes p_song_ids contains ALL song_ids for the playlist in the new order.
  -- Positions will be 0-indexed based on the array index.
  FOR i IN 1..array_length(p_song_ids, 1) LOOP
    UPDATE public.playlist_songs
    SET position = i - 1 -- Use 'i' if you prefer 1-indexed positions
    WHERE playlist_id = p_playlist_id AND song_id = p_song_ids[i];
  END LOOP;

END;
$$;
