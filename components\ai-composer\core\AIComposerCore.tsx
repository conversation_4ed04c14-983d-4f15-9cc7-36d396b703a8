'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useUser } from '@/contexts/user-context';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';
import { useAIComposerConfig } from '@/hooks/useAIComposerConfig';

// Types pour l'état principal
export interface ChordPosition {
  id: string;
  position: number;
  chord: string;
  instrument: string;
  tuning?: string;
  fret?: number;
  fingering?: number[];
  preview?: {
    svg?: string;
    audio?: string;
  };
}

export interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: ChordPosition[];
  aiSuggestions?: string[];
  key?: string;
  tempo?: number;
  duration?: number;
  startTime?: number;
  lyrics?: any[];
}

export interface StyleConfig {
  genres: string[];
  moods: string[];
  instrumentation: string[];
  capo: number;
  tuningFrequency: number;
  bpm: number;
  timeSignature: string;
  key: string;
  mode: string;
}

export interface CurrentSong {
  id?: string;
  title: string;
  artist: string;
  key: string;
  musical_key?: string;
  tempo: number;
  bpm?: number;
  timeSignature: string;
  time_signature?: string;
  genre?: string;
  moods?: string[];
  instruments?: string[];
  structure?: string;
  chords?: string;
  lyrics?: string;
  creator_user_id?: string;
  is_incomplete?: boolean;
  is_public?: boolean;
  ai_assistance_level?: string;
  editor_data?: any;
  created_at?: string;
  updated_at?: string;
}

// Hook principal pour l'état AI Composer
export const useAIComposerCore = (songId?: string) => {
  const router = useRouter();
  const supabase = createClientComponentClient();
  const user = useUser();
  
  // Configuration IA
  const { 
    config: aiConfig, 
    saveConfig: saveAiConfig, 
    callAI, 
    isLoading: aiLoading, 
    error: aiError, 
    isConfigured 
  } = useAIComposerConfig();

  // États principaux
  const [activeTab, setActiveTab] = useState('compose');
  const [currentSong, setCurrentSong] = useState<CurrentSong>({
    title: 'Nouvelle Composition',
    artist: '',
    key: 'C',
    tempo: 120,
    timeSignature: '4/4'
  });

  // Configuration style/thème
  const [styleConfig, setStyleConfig] = useState<StyleConfig>({
    genres: [],
    moods: [],
    instrumentation: [],
    capo: 0,
    tuningFrequency: 440,
    bpm: 120,
    timeSignature: '4/4',
    key: 'C',
    mode: 'major'
  });

  // États pour les paroles et structure
  const [lyricsContent, setLyricsContent] = useState('');
  const [songSections, setSongSections] = useState<LyricsSection[]>([
    {
      id: '1',
      type: 'verse',
      title: 'Couplet 1',
      content: '',
      chords: []
    },
    {
      id: '2', 
      type: 'chorus',
      title: 'Refrain',
      content: '',
      chords: []
    }
  ]);

  // Fonction pour synchroniser currentSong et styleConfig
  const syncSongData = useCallback((updates: Partial<CurrentSong>) => {
    setCurrentSong(prev => ({ ...prev, ...updates }));
    
    // Synchroniser avec styleConfig si nécessaire
    if (updates.key || updates.tempo || updates.timeSignature) {
      setStyleConfig(prev => ({
        ...prev,
        key: updates.key || prev.key,
        bpm: updates.tempo || prev.bpm,
        timeSignature: updates.timeSignature || prev.timeSignature
      }));
    }
  }, []);

  const [selectedSection, setSelectedSection] = useState<string>('1');
  const [isPlaying, setIsPlaying] = useState(false);
  const [saving, setSaving] = useState(false);

  // Historique IA
  const [aiHistory, setAiHistory] = useState<{ role: string; content: string }[]>([]);
  const [lastAiResult, setLastAiResult] = useState<string>('');
  const [generalPrompt, setGeneralPrompt] = useState<string>(
    'Vous êtes un assistant musical expert. Aidez-moi à composer et améliorer ma musique.'
  );

  // Fonction pour parser les paroles en sections
  const parseLyricsIntoSections = useCallback((lyrics: string): LyricsSection[] => {
    const lines = lyrics.split('\n');
    const sections: LyricsSection[] = [];
    let currentSection: LyricsSection | null = null;
    
    lines.forEach((line, index) => {
      // Détecter les titres de section [Verse], [Chorus], etc.
      const sectionMatch = line.match(/^\[(.+)\]$/);
      if (sectionMatch) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          id: `section-${Date.now()}-${index}`,
          type: getSectionType(sectionMatch[1]),
          title: sectionMatch[1],
          content: '',
          chords: []
        };
      } else if (currentSection && line.trim()) {
        // Ajouter le contenu à la section courante
        if (currentSection.content) {
          currentSection.content += '\n';
        }
        currentSection.content += line;
      }
    });
    
    if (currentSection) {
      sections.push(currentSection);
    }
    
    // Si aucune section n'a été trouvée, créer une section par défaut
    if (sections.length === 0 && lyrics.trim()) {
      sections.push({
        id: `section-${Date.now()}`,
        type: 'verse',
        title: 'Verse 1',
        content: lyrics,
        chords: []
      });
    }
    
    return sections;
  }, []);

  // Fonction pour déterminer le type de section
  const getSectionType = useCallback((title: string): LyricsSection['type'] => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('chorus') || lowerTitle.includes('refrain')) return 'chorus';
    if (lowerTitle.includes('verse') || lowerTitle.includes('couplet')) return 'verse';
    if (lowerTitle.includes('bridge') || lowerTitle.includes('pont')) return 'bridge';
    if (lowerTitle.includes('intro')) return 'intro';
    if (lowerTitle.includes('outro')) return 'outro';
    return 'verse'; // Par défaut
  }, []);

  // Chargement des données de la chanson
  useEffect(() => {
    const loadSongData = async () => {
      if (songId && supabase && user) {
        try {
          const { data: song, error } = await supabase
            .from('songs')
            .select('*')
            .eq('id', songId)
            .single();
          
          if (error) throw error;
          
          if (song) {
            // Mettre à jour l'état de la chanson avec tous les champs
            setCurrentSong({
              id: song.id,
              title: song.title || 'Sans titre',
              artist: song.artist || '',
              key: song.key || song.musical_key || 'C',
              musical_key: song.musical_key || song.key || 'C',
              tempo: song.bpm || song.tempo || 120,
              bpm: song.bpm || song.tempo || 120,
              timeSignature: song.time_signature || '4/4',
              time_signature: song.time_signature || '4/4',
              genre: song.genre,
              moods: song.moods || [],
              instruments: song.instruments || [],
              structure: song.structure,
              chords: song.chords,
              lyrics: song.lyrics,
              creator_user_id: song.creator_user_id,
              is_incomplete: song.is_incomplete,
              is_public: song.is_public,
              ai_assistance_level: song.ai_assistance_level,
              editor_data: song.editor_data,
              created_at: song.created_at,
              updated_at: song.updated_at
            });
            
            // Mettre à jour le contenu des paroles
            if (song.lyrics) {
              setLyricsContent(song.lyrics);
              
              // Parser les sections depuis les paroles
              const sections = parseLyricsIntoSections(song.lyrics);
              setSongSections(sections);
              if (sections.length > 0) {
                setSelectedSection(sections[0].id);
              }
            }
            
            // Charger les données AI Composer depuis editor_data
            if (song.editor_data?.ai_composer) {
              const aiData = song.editor_data.ai_composer;
              
              if (aiData.sections) {
                setSongSections(aiData.sections);
              }
              
              if (aiData.styleConfig) {
                setStyleConfig(prev => ({
                  ...prev,
                  ...aiData.styleConfig,
                  // S'assurer que les valeurs de base sont synchronisées
                  key: song.key || song.musical_key || prev.key,
                  bpm: song.bpm || song.tempo || prev.bpm,
                  timeSignature: song.time_signature || prev.timeSignature,
                  genres: song.genre ? [song.genre] : (aiData.styleConfig.genres || prev.genres),
                  moods: song.moods || aiData.styleConfig.moods || prev.moods,
                  instrumentation: song.instruments || aiData.styleConfig.instrumentation || prev.instrumentation
                }));
              } else {
                // Synchroniser styleConfig avec les données de base de la chanson
                setStyleConfig(prev => ({
                  ...prev,
                  key: song.key || song.musical_key || prev.key,
                  bpm: song.bpm || song.tempo || prev.bpm,
                  timeSignature: song.time_signature || prev.timeSignature,
                  genres: song.genre ? [song.genre] : prev.genres,
                  moods: song.moods || prev.moods,
                  instrumentation: song.instruments || prev.instrumentation
                }));
              }
            } else {
              // Pas de données AI Composer, synchroniser avec les données de base
              setStyleConfig(prev => ({
                ...prev,
                key: song.key || song.musical_key || prev.key,
                bpm: song.bpm || song.tempo || prev.bpm,
                timeSignature: song.time_signature || prev.timeSignature,
                genres: song.genre ? [song.genre] : prev.genres,
                moods: song.moods || prev.moods,
                instrumentation: song.instruments || prev.instrumentation
              }));
            }
          }
        } catch (error) {
          console.error('Erreur lors du chargement de la chanson:', error);
          toast({
            title: 'Erreur',
            description: 'Erreur lors du chargement de la chanson.',
            variant: 'destructive'
          });
        }
      }
    };
    
    loadSongData();
  }, [songId, supabase, user, parseLyricsIntoSections]);

  // Fonctions utilitaires pour extraire les données
  const extractChordProgressions = useCallback(() => {
    const progressions: string[] = [];
    songSections.forEach(section => {
      section.chords.forEach(chord => {
        if (!progressions.includes(chord.chord)) {
          progressions.push(chord.chord);
        }
      });
    });
    return progressions;
  }, [songSections]);
  
  const extractSongStructure = useCallback(() => {
    return songSections.map(section => ({
      type: section.type,
      title: section.title,
      duration: section.content.split('\n').length * 4, // Estimation
      hasChords: section.chords.length > 0
    }));
  }, [songSections]);

  return {
    // États
    activeTab,
    setActiveTab,
    currentSong,
    setCurrentSong,
    styleConfig,
    setStyleConfig,
    lyricsContent,
    setLyricsContent,
    songSections,
    setSongSections,
    selectedSection,
    setSelectedSection,
    isPlaying,
    setIsPlaying,
    saving,
    setSaving,
    aiHistory,
    setAiHistory,
    lastAiResult,
    setLastAiResult,
    generalPrompt,
    setGeneralPrompt,
    
    // Configuration IA
    aiConfig,
    saveAiConfig,
    callAI,
    aiLoading,
    aiError,
    isConfigured,
    
    // Utilitaires
    parseLyricsIntoSections,
    getSectionType,
    extractChordProgressions,
    extractSongStructure,
    syncSongData,
    
    // Contexte
    router,
    supabase,
    user,
    songId
  };
};
