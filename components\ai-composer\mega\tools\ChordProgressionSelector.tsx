'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Music, Shuffle } from 'lucide-react';

interface ChordProgression {
  id: string;
  name: string;
  chords: string[];
  key: string;
  description: string;
  genre: string;
}

const CHORD_PROGRESSIONS: ChordProgression[] = [
  {
    id: 'pop-1',
    name: 'Pop Classique',
    chords: ['C', 'Am', 'F', 'G'],
    key: 'C',
    description: 'Progression I-vi-IV-V très populaire',
    genre: 'Pop'
  },
  {
    id: 'blues-1',
    name: 'Blues 12 mesures',
    chords: ['C7', 'C7', 'C7', 'C7', 'F7', 'F7', 'C7', 'C7', 'G7', 'F7', 'C7', 'G7'],
    key: 'C',
    description: 'Structure blues classique en 12 mesures',
    genre: 'Blues'
  },
  {
    id: 'jazz-1',
    name: 'ii-V-I Jazz',
    chords: ['Dm7', 'G7', 'Cmaj7'],
    key: 'C',
    description: 'Progression jazz fondamentale',
    genre: 'Jazz'
  },
  {
    id: 'rock-1',
    name: 'Rock Puissant',
    chords: ['E5', 'A5', 'D5', 'C5'],
    key: 'E',
    description: 'Progression rock avec power chords',
    genre: 'Rock'
  }
];

interface ChordProgressionSelectorProps {
  onInsertText?: (text: string) => void;
}

export const ChordProgressionSelector: React.FC<ChordProgressionSelectorProps> = ({ onInsertText }) => {
  const [selectedProgressionId, setSelectedProgressionId] = useState<string>('');
  const [transpositionSteps, setTranspositionSteps] = useState(0);

  const transposeChord = useCallback((chord: string, steps: number): string => {
    const noteMap: { [key: string]: number } = {
      'C': 0, 'C#': 1, 'Db': 1, 'D': 2, 'D#': 3, 'Eb': 3, 'E': 4,
      'F': 5, 'F#': 6, 'Gb': 6, 'G': 7, 'G#': 8, 'Ab': 8, 'A': 9,
      'A#': 10, 'Bb': 10, 'B': 11
    };
    const reverseMap = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
    const baseNote = chord.match(/^[A-G][#b]?/)?.[0] || 'C';
    const suffix = chord.replace(baseNote, '');
    const currentIndex = noteMap[baseNote] || 0;
    const newIndex = (currentIndex + steps + 12) % 12;
    return reverseMap[newIndex] + suffix;
  }, []);

  const selectedProgression = useMemo(() => {
    return CHORD_PROGRESSIONS.find(p => p.id === selectedProgressionId);
  }, [selectedProgressionId]);

  const transposedChords = useMemo(() => {
    if (!selectedProgression) return [];
    return selectedProgression.chords.map(chord => transposeChord(chord, transpositionSteps));
  }, [selectedProgression, transpositionSteps, transposeChord]);

  const handleInsertProgression = useCallback(() => {
    if (transposedChords.length > 0) {
      onInsertText?.(`| ${transposedChords.join(' | ')} |`);
    }
  }, [transposedChords, onInsertText]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Music className="mr-2 h-5 w-5 text-purple-400" />
          Progressions d'Accords
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="progression-select">Choisir une progression</Label>
            <Select value={selectedProgressionId} onValueChange={setSelectedProgressionId}>
              <SelectTrigger id="progression-select">
                <SelectValue placeholder="Sélectionner..." />
              </SelectTrigger>
              <SelectContent>
                {CHORD_PROGRESSIONS.map(prog => (
                  <SelectItem key={prog.id} value={prog.id}>
                    {prog.name} ({prog.genre})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedProgression && (
            <>
              <div>
                <Label>Transposition: {transpositionSteps > 0 ? `+${transpositionSteps}` : transpositionSteps} demi-tons</Label>
                <Slider
                  min={-6}
                  max={6}
                  step={1}
                  value={[transpositionSteps]}
                  onValueChange={(value) => setTranspositionSteps(value[0])}
                />
              </div>
              <div>
                <Label>Aperçu</Label>
                <div className="mt-1 p-2 border rounded-md bg-muted text-center font-mono tracking-wider">
                  {transposedChords.join(' - ')}
                </div>
              </div>
              <Button onClick={handleInsertProgression} className="w-full">
                Insérer la Progression
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ChordProgressionSelector;
