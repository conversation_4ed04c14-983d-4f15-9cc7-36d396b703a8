import { create } from 'zustand';
import { Song } from '@/components/songs/song-schema';

interface PlayerState {
  currentSong: Song | null;
  isPlaying: boolean;
  volume: number;
  setCurrentSong: (song: Song) => void;
  togglePlay: () => void;
  setVolume: (volume: number) => void;
  reset: () => void;
}

export const usePlayerStore = create<PlayerState>((set) => ({
  currentSong: null,
  isPlaying: false,
  volume: 0.8,
  setCurrentSong: (song) => set({ currentSong: song, isPlaying: true }),
  togglePlay: () => set((state) => ({ isPlaying: !state.isPlaying })),
  setVolume: (volume) => set({ volume: Math.max(0, Math.min(1, volume)) }),
  reset: () => set({ currentSong: null, isPlaying: false }),
}));
