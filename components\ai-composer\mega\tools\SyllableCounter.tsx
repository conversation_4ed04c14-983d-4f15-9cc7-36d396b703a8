'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calculator } from 'lucide-react';

export const SyllableCounter: React.FC = () => {
  const [textToAnalyze, setTextToAnalyze] = useState('');
  const [syllableResult, setSyllableResult] = useState<number | null>(null);

  const countSyllables = useCallback((text: string): number => {
    if (!text) return 0;
    // This is a simplified algorithm for French. A more robust library might be needed for high accuracy.
    const words = text.toLowerCase().replace(/[^a-zàâäéèêëïîôöùûüÿç\s]/g, '').trim().split(/\s+/);
    let totalSyllables = 0;

    words.forEach(word => {
      if (word.length === 0) return;
      let syllables = (word.match(/[aeiouyàâäéèêëïîôöùûüÿ]/g) || []).length;
      if (word.endsWith('e') && syllables > 1 && !(word.match(/[aeiouyàâäéèêëïîôöùûüÿ]/g) || []).slice(-2, -1)[0]) {
        syllables--;
      }
      if (word.endsWith('es') && syllables > 1) {
          syllables--;
      }
      if (word.match(/([aeiouyàâäéèêëïîôöùûüÿ])\1/)) { // diphtongues
        syllables--;
      }
      if (syllables === 0) {
        syllables = 1;
      }
      totalSyllables += syllables;
    });

    return totalSyllables;
  }, []);

  const handleAnalyzeSyllables = useCallback(() => {
    const count = countSyllables(textToAnalyze);
    setSyllableResult(count);
  }, [textToAnalyze, countSyllables]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Calculator className="mr-2 h-5 w-5 text-green-400" />
          Compteur de Syllabes
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="syllable-input">Texte à analyser...</Label>
            <Textarea
              id="syllable-input"
              placeholder="Entrez votre texte ici..."
              value={textToAnalyze}
              onChange={(e) => setTextToAnalyze(e.target.value)}
              className="mt-1"
            />
          </div>
          <Button onClick={handleAnalyzeSyllables} className="w-full">Analyser</Button>
          {syllableResult !== null && (
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Résultat</p>
              <p className="text-2xl font-bold">{syllableResult} syllabes</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SyllableCounter;
