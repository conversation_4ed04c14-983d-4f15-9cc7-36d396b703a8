'use client';

import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, Lightbulb, TrendingUp, Target, Zap, RefreshCcw,
  Music, Palette, BarChart3, Clock, Star, AlertCircle,
  CheckCircle, ArrowRight, Sparkles, Wand2
} from 'lucide-react';

interface AIInsight {
  id: string;
  type: 'suggestion' | 'analysis' | 'improvement' | 'warning' | 'success';
  title: string;
  description: string;
  confidence: number;
  category: 'harmony' | 'melody' | 'rhythm' | 'structure' | 'lyrics' | 'production';
  actionable?: boolean;
  action?: () => void;
}

interface AnalysisMetric {
  label: string;
  value: number;
  max: number;
  status: 'good' | 'warning' | 'error';
  description: string;
}

interface AIInsightsPanelProps {
  insights?: AIInsight[];
  metrics?: AnalysisMetric[];
  isAnalyzing?: boolean;
  onRefreshInsights?: () => void;
  onApplyInsight?: (insightId: string) => void;
}

const MOCK_INSIGHTS: AIInsight[] = [
  {
    id: '1',
    type: 'suggestion',
    title: 'Progression harmonique enrichie',
    description: 'Essayez d\'ajouter un accord de passage entre le Am et le F pour créer plus de fluidité.',
    confidence: 85,
    category: 'harmony',
    actionable: true
  },
  {
    id: '2',
    type: 'analysis',
    title: 'Structure équilibrée',
    description: 'Votre chanson a une structure classique bien équilibrée avec des sections de durées appropriées.',
    confidence: 92,
    category: 'structure'
  },
  {
    id: '3',
    type: 'improvement',
    title: 'Variété rythmique',
    description: 'Considérez varier le pattern rythmique dans le pont pour créer plus de contraste.',
    confidence: 78,
    category: 'rhythm',
    actionable: true
  },
  {
    id: '4',
    type: 'warning',
    title: 'Répétition excessive',
    description: 'Le refrain se répète 4 fois, ce qui pourrait être trop pour maintenir l\'intérêt.',
    confidence: 70,
    category: 'structure',
    actionable: true
  }
];

const MOCK_METRICS: AnalysisMetric[] = [
  {
    label: 'Complexité harmonique',
    value: 75,
    max: 100,
    status: 'good',
    description: 'Bon équilibre entre simplicité et sophistication'
  },
  {
    label: 'Cohérence structurelle',
    value: 88,
    max: 100,
    status: 'good',
    description: 'Structure bien organisée et logique'
  },
  {
    label: 'Variété mélodique',
    value: 60,
    max: 100,
    status: 'warning',
    description: 'Pourrait bénéficier de plus de variation'
  },
  {
    label: 'Équilibre des sections',
    value: 82,
    max: 100,
    status: 'good',
    description: 'Durées des sections bien proportionnées'
  }
];

const AIInsightsPanelComponent = ({
  insights = MOCK_INSIGHTS,
  metrics = MOCK_METRICS,
  isAnalyzing = false,
  onRefreshInsights,
  onApplyInsight
}: AIInsightsPanelProps) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'confidence' | 'category' | 'type'>('confidence');

  const filteredInsights = useMemo(() => {
    return insights
      .filter(insight => selectedCategory === 'all' || insight.category === selectedCategory)
      .sort((a, b) => {
        if (sortBy === 'confidence') return b.confidence - a.confidence;
        if (sortBy === 'category') return a.category.localeCompare(b.category);
        return a.type.localeCompare(b.type);
      });
  }, [insights, selectedCategory, sortBy]);

  const categories = useMemo(() => {
    return Array.from(new Set(insights.map(i => i.category)));
  }, [insights]);

  const overallScore = useMemo(() => {
    return Math.round(metrics.reduce((acc, m) => acc + (m.value / m.max), 0) / metrics.length * 100);
  }, [metrics]);

  const getInsightIcon = useCallback((type: AIInsight['type']) => {
    switch (type) {
      case 'suggestion': return <Lightbulb className="h-4 w-4" />;
      case 'analysis': return <BarChart3 className="h-4 w-4" />;
      case 'improvement': return <TrendingUp className="h-4 w-4" />;
      case 'warning': return <AlertCircle className="h-4 w-4" />;
      case 'success': return <CheckCircle className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  }, []);

  const getInsightColor = useCallback((type: AIInsight['type']) => {
    switch (type) {
      case 'suggestion': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'analysis': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'improvement': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'success': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  const getMetricColor = useCallback((status: AnalysisMetric['status']) => {
    switch (status) {
      case 'good': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  }, []);

  const getCategoryIcon = useCallback((category: AIInsight['category']) => {
    switch (category) {
      case 'harmony': return <Music className="h-3 w-3" />;
      case 'melody': return <Sparkles className="h-3 w-3" />;
      case 'rhythm': return <Clock className="h-3 w-3" />;
      case 'structure': return <BarChart3 className="h-3 w-3" />;
      case 'lyrics': return <Wand2 className="h-3 w-3" />;
      case 'production': return <Palette className="h-3 w-3" />;
      default: return <Brain className="h-3 w-3" />;
    }
  }, []);

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Insights IA
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={overallScore >= 80 ? 'default' : overallScore >= 60 ? 'secondary' : 'destructive'}>
              Score: {overallScore}%
            </Badge>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onRefreshInsights}
              disabled={isAnalyzing}
              className="h-8"
            >
              <RefreshCcw className={`h-3 w-3 ${isAnalyzing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Métriques de performance */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Analyse de performance</h4>
          
          <div className="grid grid-cols-1 gap-3">
            {metrics.map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">{metric.label}</span>
                  <span className={`font-medium ${
                    metric.status === 'good' ? 'text-green-600' :
                    metric.status === 'warning' ? 'text-yellow-600' :
                    'text-red-600'
                  }`}>
                    {metric.value}%
                  </span>
                </div>
                
                <Progress 
                  value={metric.value} 
                  className="h-2"
                />
                
                <p className="text-xs text-muted-foreground">
                  {metric.description}
                </p>
              </div>
            ))}
          </div>
        </div>
        
        <Separator />
        
        {/* Filtres */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Insights</h4>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="text-xs border rounded px-2 py-1"
            >
              <option value="all">Toutes catégories</option>
              {categories.map(cat => (
                <option key={cat} value={cat}>
                  {cat.charAt(0).toUpperCase() + cat.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Liste des insights */}
        <ScrollArea className="h-[400px]">
          <div className="space-y-3">
            {isAnalyzing && (
              <div className="flex items-center justify-center py-8 text-muted-foreground">
                <RefreshCcw className="h-4 w-4 animate-spin mr-2" />
                Analyse en cours...
              </div>
            )}
            
            {!isAnalyzing && filteredInsights.map((insight) => {
              const Icon = getInsightIcon(insight.type);
              const CategoryIcon = getCategoryIcon(insight.category);
              
              return (
                <div
                  key={insight.id}
                  className="p-3 rounded-lg border border-border hover:border-primary/50 transition-colors"
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-full ${getInsightColor(insight.type)}`}>
                      {Icon}
                    </div>
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium text-sm">{insight.title}</h5>
                        
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="secondary" 
                            className="text-xs"
                          >
                            {CategoryIcon}
                            <span className="ml-1">{insight.category}</span>
                          </Badge>
                          
                          <Badge variant="outline" className="text-xs">
                            {insight.confidence}%
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        {insight.description}
                      </p>
                      
                      {insight.actionable && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onApplyInsight?.(insight.id)}
                          className="h-7 text-xs"
                        >
                          <ArrowRight className="h-3 w-3 mr-1" />
                          Appliquer
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {!isAnalyzing && filteredInsights.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                Aucun insight disponible pour cette catégorie.
              </div>
            )}
          </div>
        </ScrollArea>
        
        {/* Actions rapides */}
        <Separator />
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex-1">
            <Target className="h-3 w-3 mr-1" />
            Optimiser
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            <Zap className="h-3 w-3 mr-1" />
            Suggestions
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Mémoisation du composant pour éviter les re-rendus inutiles
export const AIInsightsPanel = memo(AIInsightsPanelComponent);