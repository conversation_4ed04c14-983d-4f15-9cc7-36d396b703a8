'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Lightbulb } from 'lucide-react';

interface RhymeResult {
  word: string;
  syllables: number;
  score: number;
}

const COMMON_RHYMES: { [key: string]: string[] } = {
  'amour': ['toujours', 'jour', 'pour', 'tour', 'retour', 'autour'],
  'vie': ['envie', 'folie', 'survie', 'harmonie', 'mélodie', 'nostalgie'],
  'cœur': ['bonheur', 'douleur', 'peur', 'heure', 'malheur', 'chaleur'],
  'temps': ['moment', 'instant', 'maintenant', 'souvent', 'vraiment', 'sentiment'],
  'nuit': ['bruit', 'fuit', 'suit', 'ruit', 'conduit', 'produit']
};

const countSyllables = (text: string): number => {
  const words = text.toLowerCase().replace(/[^a-zàâäéèêëïîôöùûüÿç\s]/g, '').split(/\s+/);
  let totalSyllables = 0;
  words.forEach(word => {
    if (word.length === 0) return;
    const vowels = word.match(/[aeiouyàâäéèêëïîôöùûüÿ]/g) || [];
    let syllables = vowels.length;
    if (word.endsWith('e') && syllables > 1) syllables--;
    if (word.includes('qu')) syllables--;
    if (syllables === 0) syllables = 1;
    totalSyllables += syllables;
  });
  return totalSyllables;
};

export const RhymeGenerator: React.FC = () => {
  const [rhymeInput, setRhymeInput] = useState('');
  const [rhymeResults, setRhymeResults] = useState<RhymeResult[]>([]);

  const handleFindRhymes = useCallback(() => {
    if (!rhymeInput.trim()) return;
    const input = rhymeInput.toLowerCase().trim();
    const rhymes = COMMON_RHYMES[input] || [];
    const results: RhymeResult[] = rhymes.map(word => ({
      word,
      syllables: countSyllables(word),
      score: Math.floor(Math.random() * 100)
    })).sort((a, b) => b.score - a.score);
    setRhymeResults(results);
  }, [rhymeInput]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Lightbulb className="mr-2 h-5 w-5 text-yellow-400" />
          Générateur de Rimes
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="rhyme-input">Mot à faire rimer...</Label>
            <div className="flex space-x-2 mt-1">
              <Input
                id="rhyme-input"
                placeholder="Ex: amour..."
                value={rhymeInput}
                onChange={(e) => setRhymeInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleFindRhymes()}
              />
              <Button onClick={handleFindRhymes}>Chercher</Button>
            </div>
          </div>
          {rhymeResults.length > 0 && (
            <div>
              <Label>Résultats</Label>
              <ScrollArea className="h-40 w-full rounded-md border p-2 mt-1">
                <div className="space-y-2">
                  {rhymeResults.map((rhyme, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="font-medium">{rhyme.word}</span>
                      <Badge variant="secondary">{rhyme.syllables} syll.</Badge>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RhymeGenerator;
