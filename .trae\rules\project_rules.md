LANCE TOUJOURS LE SERVEUR SUR LE PORT 8080 

# 🧩 Project Rules — MOUVIK

This project is a creative tool designed to help musicians, composers, and producers collaborate and explore musical ideas.

## 🌍 Context
- Project: **MOUVIK** (AI-assisted music platform)
- Tech Stack: Next.js (App Router), React 19, Tailwind CSS, Supabase (auth + DB + storage), Zustand, shadcn/ui, TypeScript
- Users: Musicians, artists, educators

## ✍️ Guidelines for AI Completions
- Follow schema definitions from `/docs/create-song-tech-reference.md`
- Adhere to Supabase schema conventions and typing found in `/docs/database-schema.md`
- Always prefer `zod` + `react-hook-form` for validation and forms
- Use helpers like `VisibilityToggle` and `LyricsEditorWithAI` when working on song editors
- Apply AI insights from `ai-reference.md` when generating lyrics or music structure
- Respect versioning logic based on `song_versions` and RPCs from the Supabase schema

## 🛠️ Coding Standards
- Components go in `components/`, should be small, focused, typed and reusable
- <PERSON><PERSON><PERSON> store per domain in `store/` with persistence if needed
- Contexts in `contexts/` only if cross-cutting concerns arise
- All files in `app/` follow the App Router convention: `layout.tsx`, `page.tsx`, `loading.tsx`, `error.tsx`

## 🧠 Supabase Rules
- Use RPCs whenever possible for complex data actions (`rpc_save_song_version`, `get_artist_stats`, etc.)
- Prefer filtered `views` or `functions` over fat client logic
- For analytics, respect the data model from `analytics_system.md`

## 📊 UI/UX Rules
- Audio player should respect waveform rendering, playback state, and analytics triggering
- Progressive enhancement: lazy load waveform, audio, and cover artwork
- Analytics and dashboard pages use `recharts`, `lucide-react`, `shadcn/ui`

## 🚀 Deployment
- Preview: Vercel Preview Deployments
- Production: Vercel Production Deployments

## 🧠 AI Usage (optional)
- All lyrics/text generation prompts should draw from musical context and artist metadata when available
- IA components should store config locally (e.g., selected model, provider)

---
This rule file evolves with the project. Please suggest optimizations!