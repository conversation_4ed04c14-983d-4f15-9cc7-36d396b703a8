"use client";

import { useState, useEffect, useRef, useTransition, useCallback, useMemo } from 'react';
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { useUser } from "@/contexts/user-context";
import { createBrowserClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { SongInfoPanel } from "@/components/songs/SongInfoPanel";
import { Music2 as SongIconMain, PlusCircle, LayoutGrid, ListFilter, Search as SearchIcon, Loader2, ListMusic as ListMusicIcon, ZoomIn, ZoomOut, Rows3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Slider } from "@/components/ui/slider";
import { SongCard } from '@/components/songs/song-card';
import { toggleSongVisibility } from './actions';
import { SongListItem } from '@/components/songs/song-list-item';
import { SongCardCompact } from '@/components/songs/song-card-compact';
import type { Song as SongType, UserProfile, Album } from "@/types";
import { cn } from '@/lib/utils';

// Type for data as it comes directly from the Supabase query
interface RawSongDataItem {
  id: string;
  title: string;
  creator_user_id: string;
  cover_art_url?: string;
  audio_url?: string;
  duration_ms?: number;
  bpm?: number;
  key?: string;
  genre?: string;
  description?: string;
  status?: string;
  is_ai_generated?: boolean;
  plays?: number;
  created_at: string;
  updated_at?: string;
  release_date?: string;
  artist_name?: string;
}

type FetchedSongForPage = SongType;

export default function UserSongsPage() {
  // Memoized Supabase client instance to ensure stable reference
  const supabase = useMemo(() => createBrowserClient(), []);
  const { user } = useUser();
  const router = useRouter();

  const [allSongs, setAllSongs] = useState<FetchedSongForPage[]>([]);
  const [displayedSongs, setDisplayedSongs] = useState<FetchedSongForPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [zoomedSong, setZoomedSong] = useState<FetchedSongForPage | null>(null);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('created_at_desc'); 
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [listItemDensity, setListItemDensity] = useState(1);
  const [updatingSongId, setUpdatingSongId] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();



  useEffect(() => {
    const fetchSongs = async () => {
      if (!user?.id) {
        console.log('[manage-songs] User not available, skipping fetch.');
        setAllSongs([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        const { data, error } = await supabase
          .from('songs')
          .select('*')
          .eq('creator_user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        console.log('[manage-songs] Successfully fetched songs:', data ? data.length : 0, 'songs');
        const transformedData: FetchedSongForPage[] = (data || []).map((rawSong: any) => ({
          id: rawSong.id,
          title: rawSong.title,
          user_id: rawSong.creator_user_id, // Map creator_user_id to user_id for SongCard
          creator_user_id: rawSong.creator_user_id, // Keep original if needed elsewhere
          created_at: rawSong.created_at,
          updated_at: rawSong.updated_at || rawSong.created_at,
          cover_art_url: rawSong.cover_art_url,
          audio_url: rawSong.audio_url,
          duration_ms: rawSong.duration_ms,
          bpm: rawSong.bpm,
          musical_key: rawSong.musical_key,
          genres: Array.isArray(rawSong.genre) ? rawSong.genre : (rawSong.genre ? [rawSong.genre] : []),
          mood: Array.isArray(rawSong.moods) ? rawSong.moods : (rawSong.moods ? [rawSong.moods] : []),
          description: rawSong.description,
          status: rawSong.status || 'draft',
          is_public: rawSong.is_public || false,
          is_explicit: rawSong.is_explicit || false,
          is_archived: rawSong.is_archived || false,
          editor_data: rawSong.editor_data || null,
          profiles: rawSong.profiles || null,
          artist_name: rawSong.artist_name || user?.display_name || user?.username || 'Artiste inconnu',
          album_id: rawSong.album_id || null,
          subgenre: Array.isArray(rawSong.subgenre) ? rawSong.subgenre : [],
          theme: Array.isArray(rawSong.theme) ? rawSong.theme : [],
          instrumentation: Array.isArray(rawSong.instrumentation) ? rawSong.instrumentation : [],
        }));
        setAllSongs(transformedData);

      } catch (error) {
        console.error("[manage-songs] Error fetching songs:", error);
        toast.error("Impossible de charger les morceaux.");
        setAllSongs([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSongs();
  }, [user?.id, supabase]); // supabase from useMemo is stable

  useEffect(() => {
    let filtered = [...allSongs];

    if (searchTerm) {
      filtered = filtered.filter(song =>
        song.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (song.albums && song.albums.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (song.genres && song.genres.some(g => g.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    }

    const [key, direction] = sortOption.split('-');
    filtered.sort((a, b) => {
      const valA = a[key as keyof FetchedSongForPage];
      const valB = b[key as keyof FetchedSongForPage];

      if (valA === null || valA === undefined) return 1;
      if (valB === null || valB === undefined) return -1;

      if (valA < valB) return direction === 'asc' ? -1 : 1;
      if (valA > valB) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    setDisplayedSongs(filtered);
  }, [allSongs, searchTerm, sortOption]);

  const handleStatusChange = useCallback(async (songId: string, newStatus: string) => {
    const { data, error } = await supabase
      .from('songs')
      .update({ status: newStatus })
      .eq('id', songId)
      .select('id, status')
      .single();

    if (error) {
      console.error("Error updating status:", error);
      toast.error("Erreur lors du changement de statut.");
    } else if (data) {
      toast.success(`Statut mis à jour : ${newStatus}`);
      setAllSongs(prevSongs => prevSongs.map(s => s.id === songId ? { ...s, status: data.status } : s));
    }
  }, [supabase]);

  const handleDeleteSong = useCallback(async (songId: string) => {
    const { error } = await supabase.from('songs').delete().eq('id', songId);
    if (error) {
      console.error("Error deleting song:", error);
      toast.error("Erreur lors de la suppression du morceau.");
    } else {
      toast.success("Morceau supprimé avec succès.");
      setAllSongs(prevSongs => prevSongs.filter(s => s.id !== songId));
    }
  }, [supabase]);
  
  const handleUpdateStatus = useCallback(async (songId: string, newStatus: { is_public: boolean; slug: string | null }) => {
    const { error } = await supabase
      .from('songs')
      .update({ 
        is_public: newStatus.is_public,
        slug: newStatus.slug 
      })
      .eq('id', songId);
      
    if (error) {
      console.error("Error updating song status:", error);
      toast.error("Erreur lors de la mise à jour du statut.");
    } else {
      setAllSongs(prevSongs => prevSongs.map(song => 
        song.id === songId 
          ? { ...song, is_public: newStatus.is_public, slug: newStatus.slug }
          : song
      ));
      toast.success(`Statut mis à jour : ${newStatus.is_public ? 'public' : 'privé'}`);
    }
  }, [supabase]);

  const handleToggleVisibility = useCallback((songId: string, currentIsPublic: boolean) => {
    setUpdatingSongId(songId);
    startTransition(async () => {
      const result = await toggleSongVisibility(songId, currentIsPublic);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(`Statut mis à jour : ${result.newIsPublic ? 'public' : 'privé'}`);
        // RevalidatePath in the action handles the data refresh, but an optimistic
        // update here provides a better user experience.
        setAllSongs(prevSongs =>
          prevSongs.map(s =>
            s.id === songId ? { ...s, is_public: result.newIsPublic! } : s
          )
        );
      }
      setUpdatingSongId(null);
    });
  }, [startTransition]); // toggleSongVisibility is stable (server action), toast, setAllSongs, setUpdatingSongId are stable setters

  const handleSongClick = useCallback((song: FetchedSongForPage) => {
    setZoomedSong(song);
  }, []); // setZoomedSong is stable

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <Loader2 className="w-12 h-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="container mx-auto p-4 md:p-6 lg:p-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <div className="flex items-center gap-3">
            <SongIconMain className="w-8 h-8 text-primary" />
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Mes Morceaux</h1>
              <p className="text-muted-foreground">Gérez votre catalogue de musique.</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => router.push('/manage-songs/create')}>
              <PlusCircle className="w-4 h-4 mr-2" />
              Nouveau morceau
            </Button>
          </div>
        </div>

        <div className="bg-card border rounded-lg p-4 mb-6">
          <div className="flex flex-col sm:flex-row items-center gap-4">
            <div className="relative w-full sm:w-auto sm:flex-grow">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                placeholder="Rechercher par titre, album, genre..."
                className="pl-10 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-4 w-full sm:w-auto">
              <Select value={sortOption} onValueChange={setSortOption}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Trier par" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at-desc">Plus récents</SelectItem>
                  <SelectItem value="created_at-asc">Plus anciens</SelectItem>
                  <SelectItem value="title-asc">Titre (A-Z)</SelectItem>
                  <SelectItem value="title-desc">Titre (Z-A)</SelectItem>
                  <SelectItem value="status-asc">Statut</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant={viewMode === 'grid' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('grid')}>
                        <LayoutGrid className="w-5 h-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Vue Grille</TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant={viewMode === 'list' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('list')}>
                        <ListMusicIcon className="w-5 h-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Vue Liste</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>



        {viewMode === 'list' && (
          <div className="flex items-center gap-2 mb-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger><Rows3 className="w-5 h-5 text-muted-foreground"/></TooltipTrigger>
                <TooltipContent>Densité d'affichage</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Slider
              value={[listItemDensity]}
              onValueChange={(value) => setListItemDensity(value[0])}
              max={2}
              min={0}
              step={1}
              className="w-32"
            />
          </div>
        )}

        {displayedSongs.length === 0 && !isLoading ? (
          <div className="text-center py-16 border-2 border-dashed rounded-lg">
            <SongIconMain className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">Aucun morceau trouvé</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Créez votre premier morceau pour commencer.
            </p>
            <div className="mt-6">
              <Button onClick={() => router.push('/songs/create/new-song')}>
                <PlusCircle className="mr-2 h-4 w-4" /> Nouveau morceau
              </Button>
            </div>
          </div>
        ) : viewMode === 'grid' ? (
          <div 
            className="grid gap-4 md:gap-6"
            style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))' }}
          >
            {displayedSongs.map((song: FetchedSongForPage) => (
              <div key={song.id} className="group relative cursor-pointer">
                <SongCard
                  onInfoClick={handleSongClick}
                  song={song}
                  onDelete={handleDeleteSong}
                  onUpdateStatus={handleUpdateStatus}
                  onToggleVisibility={handleToggleVisibility}
                  isVisibilityUpdating={updatingSongId === song.id}
                  size="md"
                  showExtendedInfo
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="border rounded-lg overflow-hidden">
            <div className="divide-y">
              {displayedSongs.map((song: FetchedSongForPage) =>
                listItemDensity === 0 ? (
                  <SongCardCompact
                    key={song.id}
                    song={song}
                    onDelete={handleDeleteSong}
                    onUpdateStatus={handleUpdateStatus}
                    onInfoClick={handleSongClick}
                  />
                ) : (
                  <div key={song.id}>
                    <SongListItem
                      song={song}
                      density={listItemDensity === 1 ? 1 : 2} // 1 for default, 2 for comfortable
                      onDelete={handleDeleteSong}
                      onUpdateStatus={handleUpdateStatus}
                      onInfoClick={handleSongClick}
                    />
                  </div>
                )
              )}
            </div>
          </div>
        )}
      </div>

      <Dialog open={!!zoomedSong} onOpenChange={(isOpen) => !isOpen && setZoomedSong(null)}>
        <DialogContent className="max-w-4xl h-[90vh] flex flex-col p-0">
          {zoomedSong && (
            <SongInfoPanel song={zoomedSong} />
          )}
        </DialogContent>
      </Dialog>

    </DndProvider>
  );
}
