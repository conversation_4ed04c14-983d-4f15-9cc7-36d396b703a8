import { InstrumentType, Diff<PERSON>ultyLevel, ChordCategory, ArpeggioPattern } from '@/components/chord-system/types/chord-system';
import { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';
import { AiGenerationType } from '@/types/ai-types';
import { ChordPlacementDisplay, ChordPlacementStorage } from './composer';
import { LayoutMode, PanelState, ViewMode } from '@/types/layout';



/**
 * Type for form data validation
 */
export type FormData = {
  title: string;
  artist: string;
  lyrics: string;
  chords: ChordPlacementStorage[];
  tempo: number;
  timeSignature: string;
  keySignature: string;
  // ... autres champs du formulaire
};

/**
 * Type for AI history entry
 */
export interface AIHistoryEntry {
  id: string;
  type: AiGenerationType;
  prompt: string;
  response: string;
  applied: boolean;
  timestamp: string;
}

/**
 * Type for composer store state
 */
export interface ComposerStoreState {
  currentSong: FormData;
  aiHistory: AIHistoryEntry[];
  selectedChord: ChordPlacementDisplay | null;
  isPlaying: boolean;
  playbackPosition: number;
  tempo: number;
  timeSignature: string;
  keySignature: string;
  // ... autres états
}

/**
 * Type for editor store state
 */
export interface EditorStoreState {
  layoutMode: LayoutMode;
  panelStates: {
    left: PanelState;
    right: PanelState;
    bottom: PanelState;
  };
  viewMode: ViewMode;
  // ... autres états
}
