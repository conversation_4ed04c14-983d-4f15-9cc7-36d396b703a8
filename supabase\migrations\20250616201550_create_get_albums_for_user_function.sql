DROP FUNCTION IF EXISTS get_albums_for_user(uuid);

CREATE OR REPLACE FUNCTION get_albums_for_user(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    created_at TIMESTAMPTZ,
    title TEXT,
    description TEXT,
    cover_url TEXT,
    is_public BOOLEAN,
    user_id UUID,
    artist_name TEXT,
    release_date DATE,
    slug TEXT,
    songs_count BIGINT,
    total_duration_ms BIGINT,
    like_count BIGINT,
    view_count BIGINT,
    profiles JSON
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.created_at,
        a.title,
        a.description,
        a.cover_url,
        a.is_public,
        a.user_id,
        p.display_name::TEXT AS artist_name,
        a.release_date,
        a.slug,
        COUNT(s.id)::BIGINT as songs_count,
        COALESCE(SUM(s.duration_ms), 0)::BIGINT as total_duration_ms,
        0::BIGINT as like_count,
        COALESCE(a.view_count, 0)::BIGINT as view_count,
        json_build_object(
            'id', p.id,
            'username', p.username,
            'display_name', p.display_name,
            'avatar_url', p.avatar_url
        ) as profiles
    FROM
        albums a
    LEFT JOIN
        songs s ON a.id = s.album_id -- Corrected join to songs table
    LEFT JOIN
        profiles p ON a.user_id = p.id
    WHERE
        a.user_id = p_user_id
    GROUP BY
        a.id, p.id;
END;
$$ LANGUAGE plpgsql;
