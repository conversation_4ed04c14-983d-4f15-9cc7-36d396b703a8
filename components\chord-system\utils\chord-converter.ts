import { 
  UnifiedChordPosition, 
  ChordPlacement, 
  InstrumentType, 
  DifficultyLevel, 
  ChordCategory 
} from '../types/chord-system';

/**
 * Convertit un chord placement en string pour le stockage
 * @param placement Placement d'accord à convertir
 * @returns String représentant le placement
 */
export function chordPlacementToString(placement: ChordPlacement): string {
  const chord = placement.chord;
  return JSON.stringify({
    id: chord.id,
    chord: chord.chord,
    instrument: chord.instrument,
    tuning: chord.tuning,
    frets: chord.frets,
    baseFret: chord.baseFret,
    difficulty: chord.difficulty,
    position: placement.beat,
    duration: placement.duration,
    emphasis: placement.emphasis,
    strum: placement.strum
  });
}

/**
 * Convertit une string de placement en chord placement
 * @param str String à convertir
 * @returns Placement d'accord
 */
export function stringToChord(str: string): ChordPlacement {
  const data = JSON.parse(str);
  return {
    chord: {
      id: data.id,
      chord: data.chord,
      instrument: data.instrument as InstrumentType,
      tuning: data.tuning,
      frets: data.frets,
      baseFret: data.baseFret,
      difficulty: data.difficulty as DifficultyLevel,
      category: data.category as ChordCategory,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    },
    beat: data.position,
    duration: data.duration,
    emphasis: data.emphasis,
    strum: data.strum
  };
}

/**
 * Convertit un chord placement en format unifié
 * @param placement Placement d'accord à convertir
 * @returns Placement d'accord unifié
 */
export function chordPlacementToUnified(placement: ChordPlacement): UnifiedChordPosition {
  return {
    ...placement.chord,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

/**
 * Convertit un chord unifié en string pour le stockage
 * @param chord Chord à convertir
 * @returns String représentant le chord
 */
export function chordToString(chord: UnifiedChordPosition): string {
  return JSON.stringify({
    id: chord.id,
    chord: chord.chord,
    instrument: chord.instrument,
    tuning: chord.tuning,
    frets: chord.frets,
    baseFret: chord.baseFret,
    difficulty: chord.difficulty,
    category: chord.category,
    createdAt: chord.createdAt,
    updatedAt: chord.updatedAt
  });
}
