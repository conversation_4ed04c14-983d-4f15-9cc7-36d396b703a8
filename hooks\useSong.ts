import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { createBrowserClient } from '@/lib/supabase/client';
import { useComposerStore } from '@/stores/composer';
import type { Song } from '@/components/songs/song-schema';
import { toast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { createSong } from '@/lib/actions/song.actions';
import { debounce } from 'lodash'; // Using lodash as lodash-es is not in dependencies

interface UseSongProps {
  songId?: string;
  userId?: string | null;
  initialData?: Song | null;
}

const AUTOSAVE_DEBOUNCE_MS = 3000;

export function useSong({ songId, userId, initialData }: UseSongProps) {
  const router = useRouter();
  // Créer le client Supabase directement sans useMemo pour éviter les dépendances circulaires
  const supabase = createBrowserClient();

    const songFromStore = useComposerStore((state) => state.currentSong);

  // Stabilise la référence de l'objet chanson pour éviter les re-rendus infinis
  // dans les composants enfants qui dépendent de la référence de l'objet (via useEffect, etc.).
  const currentSong = useMemo(() => songFromStore, [songFromStore]);
  const setCurrentSong = useComposerStore((state) => state.setCurrentSong);
  const updateSongFields = useComposerStore((state) => state.updateSongFields);
  const setUnsavedChanges = useComposerStore((state) => state.setUnsavedChanges);
  const hasUnsavedChanges = useComposerStore((state) => state.hasUnsavedChanges);

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const loadedSongIdRef = useRef<string | null>(null);

  const fetchSong = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const { data, error: fetchError } = await supabase
        .from('songs')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;
      if (data) {
        setCurrentSong(data as Song);
        setUnsavedChanges(false);
      }
    } catch (err: any) {
      console.error('Error fetching song:', err);
      setError('Failed to load song.');
      toast({ title: 'Error', description: 'Could not load the song.', variant: 'destructive' });
      router.push('/ai-composer-workspace'); // Or a dedicated error page/dashboard
    }
    setIsLoading(false);
  }, []); // Dépendances vides - les fonctions de store sont stables

  const createNewSong = useCallback(async () => {
    if (!userId) {
      toast({ title: 'Authentication Error', description: 'You must be logged in to create a song.', variant: 'destructive' });
      router.push('/login');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Call the centralized server action.
      const result = await createSong({
        title: 'Untitled Song',
      });

      if (result.success) {
        // The song is created. Navigate to the new song's workspace.
        router.push(`/ai-composer-workspace/${result.data.id}`);
      } else {
        // Handle server-side errors (DB, auth, validation, etc.)
        console.error('Error creating new song:', result.error, result.details);
        throw new Error(result.error);
      }
    } catch (err: any) {
      console.error('Error in createNewSong callback:', err);
      setError('Failed to create a new song.');
      toast({ title: 'Creation Error', description: `Could not create the song: ${err.message}`, variant: 'destructive' });
      setIsLoading(false); // Set loading to false only on error, as success navigates away.
    }
  }, [userId, router]);

  useEffect(() => {
    if (!songId) {
      setCurrentSong(null);
      setUnsavedChanges(false);
      setIsLoading(false);
      loadedSongIdRef.current = null;
      return;
    }

    if (loadedSongIdRef.current === songId) {
      setIsLoading(false);
      return;
    }

    if (initialData?.id === songId) {
      setCurrentSong(initialData);
      setUnsavedChanges(false);
      setIsLoading(false);
      loadedSongIdRef.current = songId;
      return;
    }

    setIsLoading(true);
    fetchSong(songId).then(() => {
      loadedSongIdRef.current = songId;
      setIsLoading(false);
    });
  }, [songId, initialData, fetchSong]); // fetchSong est maintenant stable

  const debouncedAutoSave = useCallback(
    debounce(async (fieldsToSave: Partial<Song>) => {
      const songToSave = useComposerStore.getState().currentSong;
      if (!songToSave || !songToSave.id || !userId) return;
      if (songToSave.creator_user_id !== userId) {
        toast({ title: 'Permission Denied', description: 'You can only save your own songs.', variant: 'destructive' });
        return;
      }
      setIsSaving(true);
      try {
        const { error: updateError } = await supabase
          .from('songs')
          .update(fieldsToSave)
          .eq('id', songToSave.id);
        if (updateError) throw updateError;
        setUnsavedChanges(false);
        toast({ title: 'Autosaved!', description: 'Your changes have been saved.' });
      } catch (err: any) {
        console.error('Autosave error:', err);
        toast({ title: 'Autosave Failed', description: 'Could not save changes automatically.', variant: 'destructive' });
      }
      setIsSaving(false);
    }, AUTOSAVE_DEBOUNCE_MS),
    [supabase, userId, setUnsavedChanges]
  );

  const handleFieldChange = useCallback((newFields: Partial<Song>) => {
    updateSongFields(newFields);
    setUnsavedChanges(true);
    debouncedAutoSave(newFields);
  }, [updateSongFields, setUnsavedChanges, debouncedAutoSave]);

  const transformNullsToUndefined = (obj: any) => {
    if (typeof obj !== 'object' || obj === null) return obj;
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => {
        if (value === null) {
          return [key, undefined];
        }
        return [key, value];
      })
    );
  };

  const saveSongVersion = useCallback(async (data: Partial<Song>, versionName?: string, versionNotes?: string) => {
    const transformedData = transformNullsToUndefined(data);
    const songToSave = { ...currentSong, ...transformedData };

    if (!songToSave || !songToSave.id || !userId) {
      toast({ title: 'Error', description: 'No song loaded or user not authenticated.', variant: 'destructive' });
      return;
    }
    if (songToSave.creator_user_id !== userId) {
      toast({ title: 'Permission Denied', description: 'You can only save your own songs.', variant: 'destructive' });
      return;
    }

    setIsSaving(true);
    try {
      const { error: updateError } = await supabase
        .from('songs')
        .update(transformedData)
        .eq('id', songToSave.id);
      if (updateError) throw updateError;

      const { data: versionData, error: rpcError } = await supabase.rpc('rpc_save_song_version', {
        p_song_id: songToSave.id,
        p_version_name: versionName || `Version ${new Date().toLocaleString()}`,
        p_song_data: songToSave, // Pass the whole song object as JSONB
        p_notes: versionNotes || null
      });
      if (rpcError) throw rpcError;

      setCurrentSong(songToSave as Song);
      setUnsavedChanges(false);
      toast({ title: 'Song Saved', description: `Version "${versionName || 'New Version'}" saved successfully.` });
    } catch (err: any) {
      console.error('Error saving song version:', err);
      setError('Failed to save song version.');
      toast({ title: 'Save Error', description: `Could not save the song version. ${err.message || ''}`, variant: 'destructive' });
    }
    setIsSaving(false);
  }, [currentSong, supabase, userId, setCurrentSong, setUnsavedChanges]);

  return {
    currentSong,
    isLoading,
    isSaving,
    error,
    hasUnsavedChanges,
    handleFieldChange,
    saveSongVersion,
    fetchSong, // Exposing fetchSong if manual refresh is needed
    createNewSong, // Exposing createNewSong if needed for e.g. 'New Song' button
  };
}
