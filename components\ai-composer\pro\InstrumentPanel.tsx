'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Guitar, Piano, Drum, Music } from 'lucide-react';

interface InstrumentPanelProps {
  studioMode: 'compose' | 'arrange' | 'mix' | 'master';
  songSections: any[];
  selectedSection: string;
  availableInstruments: any[];
  styleConfig: any;
  setStyleConfig: (config: any) => void;
}

export const InstrumentPanel: React.FC<InstrumentPanelProps> = ({
  studioMode,
  songSections,
  selectedSection,
  availableInstruments,
  styleConfig,
  setStyleConfig
}) => {
  
  return (
    <div className="h-full p-4">
      <div className="text-center">
        <Music className="h-12 w-12 text-slate-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Panneau Instruments</h3>
        <p className="text-slate-400 mb-4">Fonctionnalité en développement</p>
        <Card className="bg-slate-700/50 border-slate-600">
          <CardContent className="p-4">
            <div className="text-sm text-slate-300">
              <p className="mb-2">Prochainement disponible :</p>
              <ul className="text-left space-y-1 text-slate-400">
                <li>• Bibliothèque d'instruments</li>
                <li>• Accordages personnalisés</li>
                <li>• Diagrammes interactifs</li>
                <li>• Patterns et riffs</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
