"use client";

import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { SidebarProvider } from "@/components/ui/sidebar-engine";
import { AudioProvider } from "@/contexts/audio-context";
import GlobalAudioPlayer from "@/components/audio/global-audio-player";
import { useAudioPlayerStore, PlayerMode } from "@/lib/stores/audioPlayerStore";
import { UserProvider } from "@/contexts/user-context";
import type { UserProfileForSidebar } from "@/components/sidebar";
import { AIProvider } from "@/components/providers/AIProviderContext";
import { createBrowserClient } from "@/lib/supabase/client";
import { getUserProfileForSidebar } from "@/lib/supabase/queries/user";
import type { User } from '@supabase/supabase-js';

interface ClientLayoutProps {
  children: React.ReactNode;
  initialUser?: UserProfileForSidebar | null;
}

const PLAYER_HEIGHTS: Record<PlayerMode, number> = {
  micro: 48,
  compact: 72,
  normal: 96,
  large: 0, // Large mode is an overlay, no padding needed for main content
};

export function ClientLayout({ children, initialUser }: ClientLayoutProps) {
  const [currentUser, setCurrentUser] = useState<UserProfileForSidebar | null>(initialUser || null);
  const [isLoading, setIsLoading] = useState<boolean>(!initialUser);
  const [error, setError] = useState<string | null>(null);
  const supabase = useMemo(() => createBrowserClient(), []);

  const { playerMode, currentSong } = useAudioPlayerStore();
  const bottomPadding = currentSong ? PLAYER_HEIGHTS[playerMode] : 0;

  // Ref to hold the current user to avoid stale closures in callbacks.
  const currentUserRef = useRef(currentUser);
  currentUserRef.current = currentUser;

  useEffect(() => {
    let isMounted = true;

    const updateUserProfileInternal = async (authUser: User | null) => {
      if (!isMounted) return; // Early exit if unmounted

      const currentProfile = currentUserRef.current;
      const currentAuthUserId = authUser?.id;

      try {
        if (currentAuthUserId) {
          // If the auth user ID is different from the current profile ID, fetch a new profile.
          if (currentAuthUserId !== currentProfile?.id) {
            setIsLoading(true);
            const profile = await getUserProfileForSidebar(supabase, currentAuthUserId);
            if (isMounted) setCurrentUser(profile);
          } else {
            // The user is the same, no need to re-fetch.
            // This branch is useful if you want to avoid re-fetching on every auth event for the same user.
          }
        } else {
          // If there is no authenticated user, clear the profile.
          if (isMounted) setCurrentUser(null); // This is a synchronous update.
        }
      } catch (err) {
        console.error('ClientLayout: Error updating user profile:', err);
        if (isMounted) setError('Failed to load user profile.');
        if (isMounted) setCurrentUser(null); // Clear user on error
      } finally {
        // Ensure isLoading is false after all operations.
        if (isMounted) setIsLoading(false);
      }
    };

    const handleAuthChange = (newAuthUser: User | null) => {
      if (!isMounted) return;
      updateUserProfileInternal(newAuthUser);
    };

    // Initial check
    supabase.auth.getUser().then(({ data: { user } }) => {
      if (isMounted) {
        handleAuthChange(user);
      }
    });

    // Subscription
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (isMounted) {
        handleAuthChange(session?.user ?? null);
      }
    });

    return () => {
      isMounted = false;
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [supabase, currentUserRef]); // State setters (setCurrentUser, etc.) are stable. currentUserRef is stable.

  const userContextValue = useMemo(() => ({
    user: currentUser,
    isLoading,
    error
  }), [currentUser, isLoading, error]);

  return (
    <UserProvider value={userContextValue}>
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem
        disableTransitionOnChange
      >
        <AudioProvider>
          <SidebarProvider>
            <div className="flex flex-col h-screen">
            <main
              className="flex-1 overflow-y-auto transition-all duration-300 ease-in-out"
              style={{ paddingBottom: `${bottomPadding}px` }}
            >
              <AIProvider>
                {children}
              </AIProvider>
            </main>
            <GlobalAudioPlayer />
            </div>
          </SidebarProvider>
        </AudioProvider>
        <Toaster />
      </ThemeProvider>
    </UserProvider>
  );
}