import React from 'react';
import { Panel } from "react-resizable-panels";
import { PanelProps } from './types';
import { cn } from '@/lib/utils';

export const PanelBottom: React.FC<PanelProps> = ({ children, className }) => {
  return (
    <Panel
      collapsible
      defaultSize={25}
      minSize={20}
      maxSize={40}
      className={cn('h-full', className)}
    >
      {children}
    </Panel>
  );
};
