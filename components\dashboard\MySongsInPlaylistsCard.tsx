"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ListMusic, Clock, ExternalLink } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

interface PlaylistItem {
  playlist_id: string
  playlist_name: string
  song_id: string
  song_title: string
  username: string
  avatar_url: string | null
  added_at: string
}

interface MySongsInPlaylistsCardProps {
  playlistItems: PlaylistItem[]
}

export function MySongsInPlaylistsCard({ playlistItems }: MySongsInPlaylistsCardProps) {
  // Fonction pour formater la date en "il y a X jours/heures"
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true,
        locale: fr
      })
    } catch (error) {
      return "Date inconnue"
    }
  }

  // Fonction pour obtenir les initiales à partir du nom d'utilisateur
  const getInitials = (username: string) => {
    return username.charAt(0).toUpperCase()
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-md font-medium flex items-center">
              <ListMusic className="h-4 w-4 mr-2" />
              Vos morceaux dans des playlists
            </CardTitle>
            <CardDescription>Playlists où vos morceaux ont été ajoutés</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {playlistItems && playlistItems.length > 0 ? (
          <div className="space-y-4">
            {playlistItems.map((item, index) => (
              <div key={`${item.playlist_id}-${item.song_id}-${index}`} className="flex items-center space-x-4">
                <Avatar>
                  <AvatarImage src={item.avatar_url || ""} alt={item.username} />
                  <AvatarFallback>{getInitials(item.username)}</AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{item.playlist_name}</p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <span className="truncate">"{item.song_title}" ajouté par {item.username}</span>
                    <span className="mx-1">•</span>
                    <span className="flex items-center whitespace-nowrap">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDate(item.added_at)}
                    </span>
                  </div>
                </div>
                <Button variant="outline" size="sm" asChild className="flex-shrink-0">
                  <Link href={`/playlist/${item.playlist_id}`}>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            <p>Aucun morceau dans des playlists externes</p>
            <p className="text-xs mt-1">Partagez votre musique pour qu'elle soit ajoutée à des playlists</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
