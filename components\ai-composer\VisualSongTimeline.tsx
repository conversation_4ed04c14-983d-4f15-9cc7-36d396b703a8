'use client';

import React, { useState, useCallback, useMemo, memo, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Music, Plus, Trash2, Edit3, Play, Pause, Clock, 
  BarChart3, Target, Shuffle, Volume2, Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SongSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  duration: number; // en secondes
  startTime: number; // temps de début en secondes
  key?: string;
  tempo?: number;
  chords?: string[];
}

interface SongStructure {
  sections: SongSection[];
  totalDuration: number;
  key: string;
  tempo: number;
  timeSignature: string;
}

interface VisualSongTimelineProps {
  structure?: SongStructure;
  onStructureChange?: (structure: SongStructure) => void;
  onSectionSelect?: (sectionId: string) => void;
  selectedSectionId?: string;
  audioUrl?: string;
  isPlaying?: boolean;
  currentTime?: number;
  onPlayPause?: () => void;
  onSeek?: (time: number) => void;
}

const SECTION_TYPES = [
  { value: 'intro', label: 'Intro', color: 'bg-blue-500', textColor: 'text-white' },
  { value: 'verse', label: 'Couplet', color: 'bg-green-500', textColor: 'text-white' },
  { value: 'pre-chorus', label: 'Pré-refrain', color: 'bg-yellow-500', textColor: 'text-white' },
  { value: 'chorus', label: 'Refrain', color: 'bg-red-500', textColor: 'text-white' },
  { value: 'bridge', label: 'Pont', color: 'bg-purple-500', textColor: 'text-white' },
  { value: 'outro', label: 'Outro', color: 'bg-gray-500', textColor: 'text-white' },
  { value: 'coda', label: 'Coda', color: 'bg-indigo-500', textColor: 'text-white' }
] as const;

const DEFAULT_STRUCTURE: SongStructure = {
  sections: [
    { id: '1', type: 'intro', title: 'Intro', duration: 8, startTime: 0 },
    { id: '2', type: 'verse', title: 'Couplet 1', duration: 16, startTime: 8 },
    { id: '3', type: 'chorus', title: 'Refrain', duration: 16, startTime: 24 },
    { id: '4', type: 'verse', title: 'Couplet 2', duration: 16, startTime: 40 },
    { id: '5', type: 'chorus', title: 'Refrain', duration: 16, startTime: 56 },
    { id: '6', type: 'outro', title: 'Outro', duration: 8, startTime: 72 }
  ],
  totalDuration: 80,
  key: 'C',
  tempo: 120,
  timeSignature: '4/4'
};

const VisualSongTimelineComponent = ({
  structure = DEFAULT_STRUCTURE,
  onStructureChange,
  onSectionSelect,
  selectedSectionId,
  audioUrl,
  isPlaying = false,
  currentTime = 0,
  onPlayPause,
  onSeek
}: VisualSongTimelineProps) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [draggedSection, setDraggedSection] = useState<string | null>(null);
  const [resizingSection, setResizingSection] = useState<string | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const [timelineWidth, setTimelineWidth] = useState(800);

  // Calculer les positions et largeurs des sections
  const timelineData = useMemo(() => {
    if (!structure || !structure.sections) {
      return [];
    }
    const pixelsPerSecond = timelineWidth / structure.totalDuration;
    return structure.sections.map(section => ({
      ...section,
      left: section.startTime * pixelsPerSecond,
      width: section.duration * pixelsPerSecond
    }));
  }, [structure, timelineWidth]);

  const getSectionTypeInfo = useCallback((type: string) => {
    return SECTION_TYPES.find(t => t.value === type) || SECTION_TYPES[0];
  }, []);

  const updateSectionDuration = useCallback((sectionId: string, newDuration: number) => {
    if (!structure || !structure.sections) return;
    
    const sectionIndex = structure.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) return;

    const newSections = [...structure.sections];
    const oldDuration = newSections[sectionIndex].duration;
    const durationDiff = newDuration - oldDuration;
    
    newSections[sectionIndex].duration = newDuration;
    
    // Ajuster les temps de début des sections suivantes
    for (let i = sectionIndex + 1; i < newSections.length; i++) {
      newSections[i].startTime += durationDiff;
    }

    const newStructure = {
      ...structure,
      sections: newSections,
      totalDuration: structure.totalDuration + durationDiff
    };
    
    onStructureChange?.(newStructure);
  }, [structure, onStructureChange]);

  const addSection = useCallback((type: SongSection['type'], insertAfter?: string) => {
    if (!structure || !structure.sections) return;
    
    const insertIndex = insertAfter 
      ? structure.sections.findIndex(s => s.id === insertAfter) + 1
      : structure.sections.length;
    
    const startTime = insertAfter
      ? structure.sections.find(s => s.id === insertAfter)!.startTime + structure.sections.find(s => s.id === insertAfter)!.duration
      : structure.totalDuration;

    const newSection: SongSection = {
      id: Date.now().toString(),
      type,
      title: getSectionTypeInfo(type).label,
      duration: 16,
      startTime
    };
    
    const newSections = [...structure.sections];
    newSections.splice(insertIndex, 0, newSection);
    
    // Ajuster les temps de début des sections suivantes
    for (let i = insertIndex + 1; i < newSections.length; i++) {
      newSections[i].startTime += 16;
    }
    
    const newStructure = {
      ...structure,
      sections: newSections,
      totalDuration: structure.totalDuration + 16
    };
    
    onStructureChange?.(newStructure);
  }, [structure, onStructureChange, getSectionTypeInfo]);

  const removeSection = useCallback((sectionId: string) => {
    if (!structure || !structure.sections) return;
    
    const sectionIndex = structure.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) return;
    
    const removedSection = structure.sections[sectionIndex];
    const newSections = structure.sections.filter(s => s.id !== sectionId);
    
    // Ajuster les temps de début des sections suivantes
    for (let i = sectionIndex; i < newSections.length; i++) {
      newSections[i].startTime -= removedSection.duration;
    }
    
    const newStructure = {
      ...structure,
      sections: newSections,
      totalDuration: structure.totalDuration - removedSection.duration
    };
    
    onStructureChange?.(newStructure);
  }, [structure, onStructureChange]);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const handleTimelineClick = useCallback((e: React.MouseEvent) => {
    if (!timelineRef.current || !onSeek) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const clickedTime = (x / timelineWidth) * structure.totalDuration;
    
    onSeek(clickedTime);
  }, [timelineWidth, structure.totalDuration, onSeek]);

  // Mise à jour de la largeur de la timeline
  useEffect(() => {
    const updateWidth = () => {
      if (timelineRef.current) {
        setTimelineWidth(timelineRef.current.offsetWidth);
      }
    };
    
    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  const currentTimePosition = useMemo(() => {
    return (currentTime / structure.totalDuration) * timelineWidth;
  }, [currentTime, structure.totalDuration, timelineWidth]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Music className="h-5 w-5" />
            Timeline Visuelle
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onPlayPause}
              disabled={!audioUrl}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Badge variant="secondary">
              {formatTime(currentTime)} / {formatTime(structure.totalDuration)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Timeline principale */}
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>0:00</span>
            <span>{formatTime(structure.totalDuration)}</span>
          </div>
          
          <div 
            ref={timelineRef}
            className="relative h-20 bg-muted/20 rounded-lg border cursor-pointer overflow-hidden"
            onClick={handleTimelineClick}
          >
            {/* Sections */}
            {timelineData.map((section) => {
              const typeInfo = getSectionTypeInfo(section.type);
              const isSelected = selectedSectionId === section.id;
              
              return (
                <div
                  key={section.id}
                  className={cn(
                    "absolute top-0 h-full border-r border-white/20 transition-all duration-200 cursor-pointer group",
                    typeInfo.color,
                    isSelected && "ring-2 ring-white ring-offset-2",
                    "hover:brightness-110"
                  )}
                  style={{
                    left: `${section.left}px`,
                    width: `${section.width}px`
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onSectionSelect?.(section.id);
                  }}
                >
                  <div className={cn("p-2 h-full flex flex-col justify-center", typeInfo.textColor)}>
                    <div className="text-xs font-medium truncate">
                      {section.title}
                    </div>
                    <div className="text-xs opacity-80">
                      {formatTime(section.duration)}
                    </div>
                  </div>
                  
                  {/* Poignée de redimensionnement */}
                  <div 
                    className="absolute right-0 top-0 w-2 h-full bg-white/20 cursor-ew-resize opacity-0 group-hover:opacity-100 transition-opacity"
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      setResizingSection(section.id);
                    }}
                  />
                </div>
              );
            })}
            
            {/* Curseur de lecture */}
            {audioUrl && (
              <div 
                className="absolute top-0 w-0.5 h-full bg-red-500 z-10 pointer-events-none"
                style={{ left: `${currentTimePosition}px` }}
              >
                <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full" />
              </div>
            )}
          </div>
        </div>

        {/* Contrôles de section sélectionnée */}
        {selectedSectionId && structure && structure.sections && (() => {
          const selectedSection = structure.sections.find(s => s.id === selectedSectionId);
          if (!selectedSection) return null;
          
          return (
            <Card className="p-4 bg-muted/50">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Section sélectionnée: {selectedSection.title}</h4>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => removeSection(selectedSection.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Durée (secondes)</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        value={[selectedSection.duration]}
                        onValueChange={([value]) => updateSectionDuration(selectedSection.id, value)}
                        min={4}
                        max={60}
                        step={2}
                        className="flex-1"
                      />
                      <Input
                        type="number"
                        value={selectedSection.duration}
                        onChange={(e) => updateSectionDuration(selectedSection.id, parseInt(e.target.value) || 4)}
                        min={4}
                        max={60}
                        className="w-16"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Temps de début</Label>
                    <Input
                      value={formatTime(selectedSection.startTime)}
                      readOnly
                      className="bg-muted"
                    />
                  </div>
                </div>
              </div>
            </Card>
          );
        })()}

        {/* Boutons d'ajout de sections */}
        <div className="space-y-2">
          <Label>Ajouter une section</Label>
          <div className="flex flex-wrap gap-2">
            {SECTION_TYPES.map((type) => (
              <Button
                key={type.value}
                variant="outline"
                size="sm"
                onClick={() => addSection(type.value as SongSection['type'])}
                className="text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                {type.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Informations globales */}
        {structure && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold">{structure.sections?.length || 0}</div>
              <div className="text-sm text-muted-foreground">Sections</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{formatTime(structure.totalDuration || 0)}</div>
              <div className="text-sm text-muted-foreground">Durée totale</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{structure.tempo || 120} BPM</div>
              <div className="text-sm text-muted-foreground">Tempo</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const VisualSongTimeline = memo(VisualSongTimelineComponent);
export default VisualSongTimeline;