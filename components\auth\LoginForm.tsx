/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientComponentClient();

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (signInError) {
      setError(signInError.message);
    } else {
      router.push('/dashboard'); // Rediriger vers le dashboard après connexion réussie
      router.refresh(); // Pour s'assurer que la session est prise en compte
    }
  };

  return (
    <form onSubmit={handleLogin} className="space-y-4 p-6 bg-neutral-800 rounded-lg shadow-xl w-full max-w-md">
      <h2 className="text-2xl font-bold text-center text-white">Connexion</h2>
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-neutral-300">
          Adresse Email
        </label>
        <input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="mt-1 block w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-white"
        />
      </div>
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-neutral-300">
          Mot de passe
        </label>
        <input
          id="password"
          name="password"
          type="password"
          autoComplete="current-password"
          required
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="mt-1 block w-full px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-white"
        />
      </div>
      {error && <p className="text-sm text-red-500">{error}</p>}
      <div>
        <button
          type="submit"
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Se connecter
        </button>
      </div>
      <p className="text-sm text-center text-neutral-400">
        Pas encore de compte ?{' '}
        <a href="/auth/signup" className="font-medium text-indigo-400 hover:text-indigo-300">
          S'inscrire
        </a>
      </p>
    </form>
  );
}
