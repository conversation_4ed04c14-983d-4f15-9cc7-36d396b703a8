"use client"

import { createContext, useContext, type ReactNode, useEffect, useMemo } from "react"
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore"
import type { Song } from "@/types"
import { createBrowserClient } from "@/lib/supabase/client"

interface AudioContextType {
  currentSong: (Song & { artist?: string }) | null
  isPlaying: boolean
  queue: (Song & { artist?: string })[]
  playSong: (song: Song & { artist?: string }) => void
  pauseSong: () => void
  resumeSong: () => void
  nextSong: () => void
  previousSong: () => void
  addToQueue: (song: Song & { artist?: string }) => void
  clearQueue: () => void
  setQueue: (songs: (Song & { artist?: string })[]) => void
}

const AudioContext = createContext<AudioContextType | undefined>(undefined)

export function AudioProvider({ children }: { children: ReactNode }) {
  // Zustand store selectors
  const storeCurrentSong = useAudioPlayerStore(state => state.currentSong);
  const storeIsPlaying = useAudioPlayerStore(state => state.isPlaying);
  const storeQueue = useAudioPlayerStore(state => state.queue);

  // Zustand store actions
  const storePlaySong = useAudioPlayerStore(state => state.playSong);
  const storePauseSong = useAudioPlayerStore(state => state.pauseSong);
  const storeTogglePlayPause = useAudioPlayerStore(state => state.togglePlayPause); // For resume
  const storeNextSong = useAudioPlayerStore(state => state.nextSong);
  const storePreviousSong = useAudioPlayerStore(state => state.previousSong);
  const storeAddToQueue = useAudioPlayerStore(state => state.addToQueue);
  const storeClearQueue = useAudioPlayerStore(state => state.clearQueue);
  const storeSetQueue = useAudioPlayerStore(state => state.setQueue);
  const storeSetCurrentSongDetails = useAudioPlayerStore(state => state.setCurrentSongDetails); // Nouvelle action pour charger sans jouer

  const supabase = useMemo(() => createBrowserClient(), []);

  useEffect(() => {
    const loadInitialSong = async () => {
      const lastPlayedSongId = localStorage.getItem("lastPlayedSongId");
      let songLoaded = false;

      if (lastPlayedSongId) {
        try {
          const { data: songData, error } = await supabase
            .from("songs")
            .select(`
              *,
              profiles ( id, username, display_name )
            `)
            .eq("id", lastPlayedSongId)
            .single();

          if (error) throw error;

          if (songData) {
            const profileData = Array.isArray(songData.profiles) ? songData.profiles[0] : songData.profiles;
            storeSetCurrentSongDetails({ // Load the song details via store without playing.
              ...songData,
              artist: profileData?.display_name || profileData?.username || "Unknown Artist",
              profiles: undefined 
            });
            songLoaded = true;
          }
        } catch (error) {
          console.error("Error loading last played song:", error);
          localStorage.removeItem("lastPlayedSongId"); // Clear invalid ID
        }
      }

      if (!songLoaded) {
        // Fetch the most recently added song as a default
        try {
          const { data: defaultSongData, error: defaultError } = await supabase
            .from("songs")
            .select(`
              *,
              profiles ( id, username, display_name )
            `)
            .order("created_at", { ascending: false })
            .limit(1)
            .single();

          if (defaultError) throw defaultError;

          if (defaultSongData) {
            const profileData = Array.isArray(defaultSongData.profiles) ? defaultSongData.profiles[0] : defaultSongData.profiles;
            storeSetCurrentSongDetails({ // Load the song details via store without playing
              ...defaultSongData,
              artist: profileData?.display_name || profileData?.username || "Unknown Artist",
              profiles: undefined
            });
          }
        } catch (error) {
          console.error("Error loading default song:", error);
        }
      }
    };

    loadInitialSong();
  }, [supabase, storeSetCurrentSongDetails]);

  const playSong = (song: Song & { artist?: string }) => {
    storePlaySong(song);
    // The store's playSong should ideally handle this localStorage logic.
    // For now, keeping it here to ensure functionality isn't lost immediately.
    if (song && song.id) {
      localStorage.setItem("lastPlayedSongId", song.id.toString());
    }
  };

  const pauseSong = () => {
    storePauseSong();
  };

  const resumeSong = () => {
    // togglePlayPause will resume if paused and currentSong is set in store
    storeTogglePlayPause(); 
  };

  const nextSong = () => {
    storeNextSong();
    // localStorage for next song should be handled within storeNextSong if needed
  };

  const previousSong = () => {
    storePreviousSong();
    // localStorage for previous song should be handled within storePreviousSong if needed
  };

  const addToQueue = (song: Song & { artist?: string }) => {
    storeAddToQueue(song);
  };

  const clearQueue = () => {
    storeClearQueue();
  };

  const contextValue = useMemo(() => ({
    currentSong: storeCurrentSong,
    isPlaying: storeIsPlaying,
    queue: storeQueue,
    playSong,
    pauseSong,
    resumeSong,
    nextSong,
    previousSong,
    addToQueue,
    clearQueue,
    setQueue: storeSetQueue,
  }), [
    storeCurrentSong,
    storeIsPlaying,
    storeQueue,
    playSong, // playSong is a callback defined in AudioProvider, depends on storePlaySong
    storePauseSong, // pauseSong depends on this
    storeTogglePlayPause, // resumeSong depends on this
    storeNextSong, // nextSong depends on this
    storePreviousSong, // previousSong depends on this
    storeAddToQueue, // addToQueue depends on this
    storeClearQueue, // clearQueue depends on this
    storeSetQueue
  ]);

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
    </AudioContext.Provider>
  )
}

export function useAudio() {
  const context = useContext(AudioContext)
  if (context === undefined) {
    throw new Error("useAudio must be used within an AudioProvider")
  }
  return context
}
