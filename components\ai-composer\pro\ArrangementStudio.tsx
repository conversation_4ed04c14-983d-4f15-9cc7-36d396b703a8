'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Layers, Piano, Drum } from 'lucide-react';

interface ArrangementStudioProps {
  activeTab: string;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  availableInstruments: any[];
}

export const ArrangementStudio: React.FC<ArrangementStudioProps> = ({
  activeTab,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  availableInstruments
}) => {
  
  return (
    <div className="h-full p-6 flex items-center justify-center">
      <div className="text-center">
        <Layers className="h-16 w-16 text-slate-400 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-white mb-2">Studio d'Arrangement</h3>
        <p className="text-slate-400 mb-4">Fonctionnalité en développement</p>
        <Card className="bg-slate-700/50 border-slate-600 max-w-md">
          <CardContent className="p-4">
            <div className="text-sm text-slate-300">
              <p className="mb-2">Prochainement disponible :</p>
              <ul className="text-left space-y-1 text-slate-400">
                <li>• Éditeur de structure avancé</li>
                <li>• Arrangement multi-instruments</li>
                <li>• Patterns rythmiques</li>
                <li>• Dynamiques et variations</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
