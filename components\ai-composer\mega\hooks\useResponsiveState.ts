'use client';

import { useState, useEffect, useCallback } from 'react';

// Types pour l'état responsive
export type ScreenSize = 'mobile' | 'tablet' | 'desktop';
export type ViewMode = 'focus' | 'complete' | 'analysis';
export type PanelState = 'hidden' | 'collapsed' | 'expanded';

interface ResponsivePreferences {
  viewMode: ViewMode;
  aiPanelState: PanelState;
  activityPanelState: PanelState;
  autoAdaptLayout: boolean;
  showHelpTips: boolean;
  preferredZoomLevel: number;
  enableAnimations: boolean;
  compactMode: boolean;
}

interface ResponsiveState {
  screenSize: ScreenSize;
  viewMode: ViewMode;
  aiPanelState: PanelState;
  activityPanelState: PanelState;
  isFullscreen: boolean;
  showMobileMenu: boolean;
  preferences: ResponsivePreferences;
}

const DEFAULT_PREFERENCES: ResponsivePreferences = {
  viewMode: 'complete',
  aiPanelState: 'expanded',
  activityPanelState: 'collapsed',
  autoAdaptLayout: true,
  showHelpTips: true,
  preferredZoomLevel: 100,
  enableAnimations: true,
  compactMode: false
};

const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024
};

/**
 * Hook personnalisé pour gérer l'état responsive de l'interface AI Composer
 */
export function useResponsiveState() {
  // État d'hydratation pour éviter les erreurs SSR
  const [isHydrated, setIsHydrated] = useState(false);
  
  // État principal
  const [state, setState] = useState<ResponsiveState>({
    screenSize: 'desktop',
    viewMode: 'complete',
    aiPanelState: 'expanded',
    activityPanelState: 'collapsed',
    isFullscreen: false,
    showMobileMenu: false,
    preferences: DEFAULT_PREFERENCES
  });

  // Détection de la taille d'écran
  const detectScreenSize = useCallback((): ScreenSize => {
    if (typeof window === 'undefined') return 'desktop';
    
    const width = window.innerWidth;
    if (width < BREAKPOINTS.mobile) return 'mobile';
    if (width < BREAKPOINTS.tablet) return 'tablet';
    return 'desktop';
  }, []);

  // Charger les préférences depuis localStorage
  const loadPreferences = useCallback((): ResponsivePreferences => {
    if (typeof window === 'undefined') return DEFAULT_PREFERENCES;
    
    try {
      const saved = localStorage.getItem('ai-composer-responsive-preferences');
      if (saved) {
        const parsed = JSON.parse(saved);
        return { ...DEFAULT_PREFERENCES, ...parsed };
      }
    } catch (error) {
      console.warn('Erreur lors du chargement des préférences:', error);
    }
    
    return DEFAULT_PREFERENCES;
  }, []);

  // Sauvegarder les préférences
  const savePreferences = useCallback((preferences: ResponsivePreferences) => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem('ai-composer-responsive-preferences', JSON.stringify(preferences));
    } catch (error) {
      console.warn('Erreur lors de la sauvegarde des préférences:', error);
    }
  }, []);

  // Adaptation automatique selon la taille d'écran
  const adaptToScreenSize = useCallback((screenSize: ScreenSize, preferences: ResponsivePreferences) => {
    if (!preferences.autoAdaptLayout) return {};

    switch (screenSize) {
      case 'mobile':
        return {
          viewMode: 'focus' as ViewMode,
          aiPanelState: 'hidden' as PanelState,
          activityPanelState: 'hidden' as PanelState
        };
      
      case 'tablet':
        return {
          viewMode: 'complete' as ViewMode,
          aiPanelState: 'collapsed' as PanelState,
          activityPanelState: 'hidden' as PanelState
        };
      
      case 'desktop':
        return {
          viewMode: preferences.viewMode,
          aiPanelState: preferences.aiPanelState,
          activityPanelState: preferences.activityPanelState
        };
      
      default:
        return {};
    }
  }, []);

  // Hydratation côté client
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Initialisation après hydratation
  useEffect(() => {
    if (!isHydrated) return;
    
    const preferences = loadPreferences();
    const screenSize = detectScreenSize();
    const adaptedState = adaptToScreenSize(screenSize, preferences);

    setState(prev => ({
      ...prev,
      screenSize,
      preferences,
      ...adaptedState
    }));
  }, [isHydrated, detectScreenSize, loadPreferences, adaptToScreenSize]);

  // Écoute des changements de taille d'écran
  useEffect(() => {
    if (!isHydrated) return;
    
    const handleResize = () => {
      const newScreenSize = detectScreenSize();
      if (newScreenSize !== state.screenSize) {
        const adaptedState = adaptToScreenSize(newScreenSize, state.preferences);
        
        setState(prev => ({
          ...prev,
          screenSize: newScreenSize,
          ...adaptedState
        }));
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isHydrated, state.screenSize, state.preferences, detectScreenSize, adaptToScreenSize]);

  // Actions pour modifier l'état
  const actions = {
    // Mode d'affichage
    setViewMode: useCallback((viewMode: ViewMode) => {
      setState(prev => {
        const newPreferences = { ...prev.preferences, viewMode };
        savePreferences(newPreferences);
        return {
          ...prev,
          viewMode,
          preferences: newPreferences
        };
      });
    }, [savePreferences]),

    // État des panneaux
    setAiPanelState: useCallback((aiPanelState: PanelState) => {
      setState(prev => {
        const newPreferences = { ...prev.preferences, aiPanelState };
        savePreferences(newPreferences);
        return {
          ...prev,
          aiPanelState,
          preferences: newPreferences
        };
      });
    }, [savePreferences]),

    setActivityPanelState: useCallback((activityPanelState: PanelState) => {
      setState(prev => {
        const newPreferences = { ...prev.preferences, activityPanelState };
        savePreferences(newPreferences);
        return {
          ...prev,
          activityPanelState,
          preferences: newPreferences
        };
      });
    }, [savePreferences]),

    // Plein écran
    toggleFullscreen: useCallback(() => {
      setState(prev => ({
        ...prev,
        isFullscreen: !prev.isFullscreen
      }));
    }, []),

    setFullscreen: useCallback((isFullscreen: boolean) => {
      setState(prev => ({
        ...prev,
        isFullscreen
      }));
    }, []),

    // Menu mobile
    toggleMobileMenu: useCallback(() => {
      setState(prev => ({
        ...prev,
        showMobileMenu: !prev.showMobileMenu
      }));
    }, []),

    setShowMobileMenu: useCallback((showMobileMenu: boolean) => {
      setState(prev => ({
        ...prev,
        showMobileMenu
      }));
    }, []),

    // Préférences
    updatePreferences: useCallback((updates: Partial<ResponsivePreferences>) => {
      setState(prev => {
        const newPreferences = { ...prev.preferences, ...updates };
        savePreferences(newPreferences);
        
        // Réappliquer l'adaptation si nécessaire
        const adaptedState = updates.autoAdaptLayout !== false 
          ? adaptToScreenSize(prev.screenSize, newPreferences)
          : {};
        
        return {
          ...prev,
          preferences: newPreferences,
          ...adaptedState
        };
      });
    }, [savePreferences, adaptToScreenSize]),

    // Réinitialiser les préférences
    resetPreferences: useCallback(() => {
      const adaptedState = adaptToScreenSize(state.screenSize, DEFAULT_PREFERENCES);
      savePreferences(DEFAULT_PREFERENCES);
      
      setState(prev => ({
        ...prev,
        preferences: DEFAULT_PREFERENCES,
        ...adaptedState
      }));
    }, [state.screenSize, savePreferences, adaptToScreenSize]),

    // Basculer l'adaptation automatique
    toggleAutoAdapt: useCallback(() => {
      setState(prev => {
        const newAutoAdapt = !prev.preferences.autoAdaptLayout;
        const newPreferences = { ...prev.preferences, autoAdaptLayout: newAutoAdapt };
        savePreferences(newPreferences);
        
        const adaptedState = newAutoAdapt 
          ? adaptToScreenSize(prev.screenSize, newPreferences)
          : {};
        
        return {
          ...prev,
          preferences: newPreferences,
          ...adaptedState
        };
      });
    }, [savePreferences, adaptToScreenSize]),

    // Optimisations pour mobile
    optimizeForMobile: useCallback(() => {
      setState(prev => {
        const mobileOptimizations = {
          viewMode: 'focus' as ViewMode,
          aiPanelState: 'hidden' as PanelState,
          activityPanelState: 'hidden' as PanelState,
          compactMode: true,
          enableAnimations: false
        };
        
        const newPreferences = { ...prev.preferences, ...mobileOptimizations };
        savePreferences(newPreferences);
        
        return {
          ...prev,
          ...mobileOptimizations,
          preferences: newPreferences
        };
      });
    }, [savePreferences]),

    // Optimisations pour desktop
    optimizeForDesktop: useCallback(() => {
      setState(prev => {
        const desktopOptimizations = {
          viewMode: 'complete' as ViewMode,
          aiPanelState: 'expanded' as PanelState,
          activityPanelState: 'collapsed' as PanelState,
          compactMode: false,
          enableAnimations: true
        };
        
        const newPreferences = { ...prev.preferences, ...desktopOptimizations };
        savePreferences(newPreferences);
        
        return {
          ...prev,
          ...desktopOptimizations,
          preferences: newPreferences
        };
      });
    }, [savePreferences])
  };

  // Utilitaires
  const utils = {
    // Vérifier si on est sur mobile
    isMobile: state.screenSize === 'mobile',
    
    // Vérifier si on est sur tablet
    isTablet: state.screenSize === 'tablet',
    
    // Vérifier si on est sur desktop
    isDesktop: state.screenSize === 'desktop',
    
    // Vérifier si les panneaux sont visibles
    hasVisiblePanels: state.aiPanelState !== 'hidden' || state.activityPanelState !== 'hidden',
    
    // Obtenir la largeur disponible pour le contenu principal
    getContentWidth: () => {
      let width = 100;
      
      if (state.aiPanelState === 'expanded') width -= 25;
      else if (state.aiPanelState === 'collapsed') width -= 5;
      
      if (state.activityPanelState === 'expanded') width -= 20;
      else if (state.activityPanelState === 'collapsed') width -= 5;
      
      return Math.max(width, 50); // Minimum 50%
    },
    
    // Vérifier si l'interface est en mode compact
    isCompactMode: state.preferences.compactMode || state.screenSize === 'mobile',
    
    // Obtenir les classes CSS pour l'adaptation
    getResponsiveClasses: () => {
      const classes = [];
      
      classes.push(`screen-${state.screenSize}`);
      classes.push(`view-${state.viewMode}`);
      
      if (state.isFullscreen) classes.push('fullscreen');
      if (state.preferences.compactMode) classes.push('compact');
      if (!state.preferences.enableAnimations) classes.push('no-animations');
      
      return classes.join(' ');
    },
    
    // Obtenir la configuration optimale pour la taille d'écran actuelle
    getOptimalConfig: () => {
      return adaptToScreenSize(state.screenSize, state.preferences);
    }
  };

  return {
    // État actuel
    ...state,
    
    // Actions
    ...actions,
    
    // Utilitaires
    ...utils
  };
}

export default useResponsiveState;