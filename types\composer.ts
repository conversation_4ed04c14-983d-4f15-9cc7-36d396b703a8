import type { UnifiedChordPosition, ArpeggioPattern } from '@/components/chord-system/types/chord-system';


/**
 * @fileoverview Shared type definitions for the AI Composer workspace.
 * @version 1.1.0
 * @date 2025-06-21
 */

/**
 * Defines the display mode for the editor.
 * - `text-only`: Shows only the lyrics text.
 * - `chords-only`: Shows only the chord grid/sequence.
 * - `hybrid`: Shows lyrics with chord symbols overlaid.
 */
export type DisplayMode = 'text-only' | 'chords-only' | 'hybrid';

/**
 * Type for chord placement with string chord (for storage)
 */
export type ChordPlacementStorage = {
  id: string;
  chord: string;
  position: number;
  textPosition: number;
  lineNumber: number;
  wordIndex: number;
  timestamp: string;
  metadata?: {
    beat?: number;
    measure?: number;
    emphasis?: 'strong' | 'medium' | 'weak';
  };
};

/**
 * Type for chord placement with UnifiedChordPosition (for display)
 */
export type ChordPlacementDisplay = {
  id: string;
  chord: UnifiedChordPosition;
  position: number;
  textPosition: number;
  lineNumber: number;
  wordIndex: number;
  timestamp: string;
  beat: number;
  duration: number;
  emphasis?: 'strong' | 'medium' | 'weak';
  strum?: ArpeggioPattern;
  metadata?: {
    measure?: number;
  };
};

/**
 * Utility type to convert between storage and display formats
 */
export type ChordPlacement = ChordPlacementStorage | ChordPlacementDisplay;

/**
 * State of the chord system
 */
export interface ChordSystemState {
  chords: ChordPlacement[];
  currentChord: ChordPlacement | null;
  selectedChord: ChordPlacement | null;
  isPlaying: boolean;
  playbackPosition: number;
  tempo: number;
  timeSignature: string;
  keySignature: string;
}

/**
 * Actions available on the chord system
 */
export interface ChordSystemActions {
  addChord: (chord: ChordPlacement) => void;
  removeChord: (chordId: string) => void;
  updateChord: (chordId: string, updates: Partial<ChordPlacement>) => void;
  playChord: (chordId: string) => void;
  stopChord: () => void;
  setTempo: (tempo: number) => void;
  setTimeSignature: (signature: string) => void;
  setKeySignature: (key: string) => void;
}

/**
 * Type for chord system state with actions
 */
export type ChordSystemStateWithActions = ChordSystemState & {
  actions: ChordSystemActions;
};

/**
 * Types for instruments and difficulties
 */
import type { InstrumentType, DifficultyLevel } from '@/components/chord-system/types/chord-system';

// Utilisation des types existants depuis chord-system.ts
export { InstrumentType, DifficultyLevel };

// Re-exporting for convenience so other components only need to import from this file.
export type { UnifiedChordPosition };
