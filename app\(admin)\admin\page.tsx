import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { ShieldCheck } from "lucide-react";
import { PlanLimitsManager } from "@/components/admin/plan-limits-manager";
import { CreationCostsManager } from "@/components/admin/creation-costs-manager";
import { CommentSettingsManager } from "@/components/admin/comment-settings-manager"; // Added import

export default function AdminDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center">
          <ShieldCheck className="mr-3 h-8 w-8 text-primary" />
          Panneau d'Administration
        </h1>
        {/* Add any global admin actions here if needed */}
      </div>
      
      <p className="text-muted-foreground">
        Bienvenue dans le panneau d'administration. G<PERSON>rez les utilisateurs, les plans, le contenu et les paramètres de la plateforme.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Gestion des Utilisateurs</CardTitle>
            <CardDescription>Voir et modifier les rôles, abonnements, et quotas des utilisateurs.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">(Interface de gestion des utilisateurs à implémenter ici)</p>
            {/* Example: Link to a future user management page */}
            {/* <Button asChild variant="outline" className="mt-4">
              <Link href="/admin/users">Gérer les utilisateurs</Link>
            </Button> */}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gestion des Plans d'Abonnement</CardTitle>
            <CardDescription>Configurer les limites et fonctionnalités pour chaque plan.</CardDescription>
          </CardHeader>
          <CardContent>
            <PlanLimitsManager />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gestion des Coûts de Création</CardTitle>
            <CardDescription>Définir les coûts en pièces pour créer playlists, albums, etc.</CardDescription>
          </CardHeader>
          <CardContent>
            <CreationCostsManager />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Modération de Contenu</CardTitle>
            <CardDescription>Examiner et modérer le contenu soumis par les utilisateurs.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">(Interface de modération à implémenter ici)</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Paramètres des Commentaires</CardTitle>
            <CardDescription>Configurer la pagination des sections de commentaires.</CardDescription>
          </CardHeader>
          <CardContent>
            <CommentSettingsManager />
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2 lg:col-span-3">
          <CardHeader>
            <CardTitle>Statistiques de la Plateforme</CardTitle>
            <CardDescription>Vue d'ensemble de l'activité et de la croissance de la plateforme.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">(Graphiques et statistiques globales à implémenter ici)</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
