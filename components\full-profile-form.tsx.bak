"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { type User } from "@supabase/supabase-js";
import { getSupabaseClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { Loader2, Save, EyeIcon as ViewPublicProfileIcon, UserCircle2, Music, Link2 as LinkIcon, Settings2 } from "lucide-react";
import { MultiSelect } from "@/components/ui/multi-select"; // ASSUMPTION: This component exists
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { 
  instrumentationOptions, 
  tagOptions as profileTagOptions,
  genreOptions as globalGenreOptions, 
  influenceOptions as globalInfluenceOptions,
  languageOptions as globalLanguageOptions,
  rolePrimaryOptions as globalRolePrimaryOptions,
  roleSecondaryOptions,
  dawOptions as globalDawOptions,
  osOptions,
  countryOptions
} from '@/lib/constants/song-options';

// --- Types (Copied from provided code) ---
export interface InstrumentPlayed {
  name: string;
  experience_years: number;
}

export interface SocialLink {
  platform: string;
  url: string;
}

export type MultiSelectOption = {
  value: string;
  label: string;
};

const socialPlatformOptionsForLogic: MultiSelectOption[] = [
  { value: "spotify", label: "Spotify" }, { value: "youtube", label: "YouTube" },
  { value: "soundcloud", label: "SoundCloud" }, { value: "tiktok", label: "TikTok" },
  { value: "instagram", label: "Instagram" }, { value: "facebook", label: "Facebook" },
  { value: "twitter", label: "Twitter / X" }, { value: "bandcamp", label: "Bandcamp" },
  { value: "udio", label: "Udio" }, { value: "suno", label: "Suno" },
  { value: "autre", label: "Autre site web" },
];

const aiUsageLevelOptions: MultiSelectOption[] = [
  { value: "none", label: "Aucune" }, { value: "light", label: "Légère" },
  { value: "moderate", label: "Modérée" }, { value: "heavy", label: "Intensive" },
];

export type RolePrimaryEnum = string; 
export type UserRoleEnum = 'user' | 'moderator' | 'admin';
export type SubscriptionEnum = 'free' | 'pro' | 'studio';
export type AiUsageEnum = 'none' | 'light' | 'moderate' | 'heavy';
export type MonetizationEnum = 'hobby' | 'streaming' | 'sync' | 'teaching';
export type DawEnum = string;

interface Profile {
  id: string; email: string; username: string; full_name: string; display_name: string;
  avatar_url: string | null; header_url?: string | null; bio: string; website: string;
  location_city?: string; location_country?: string; social_links: SocialLink[];
  genres: string[]; influences: string[]; tags: string[]; is_artist: boolean; 
  record_label: string; equipment: string; spoken_languages: string[];
  instruments_played: InstrumentPlayed[]; is_profile_public: boolean;
  show_stats_publicly: boolean; allow_collaboration_requests: boolean;
  receive_email_notifications: boolean; updated_at?: string;
  role_primary: RolePrimaryEnum; roles_secondary: string[];
  operating_systems?: string[]; subscription_tier: SubscriptionEnum;
  user_role: UserRoleEnum; status_badges: string[]; main_instruments: string[];
  monetization_goals: MonetizationEnum; open_to_collab: boolean;
  primary_daw: DawEnum | null; other_daws: string[];
  ai_usage_level: AiUsageEnum; ai_usage_percent: number | null;
  ai_tools: string[]; coins_balance: number; ia_credits: number;
  marketing_opt_in: boolean;
}

const initialProfileState: Profile = {
  id: "", email: "", username: "", full_name: "", display_name: "", avatar_url: null, header_url: null,
  bio: "", website: "", location_city: "", location_country: "", social_links: [], genres: [], influences: [], tags: [],
  is_artist: false, record_label: "", equipment: "", spoken_languages: [], instruments_played: [],
  is_profile_public: true, show_stats_publicly: true, allow_collaboration_requests: true, receive_email_notifications: true,
  role_primary: 'listener', roles_secondary: [], operating_systems: [], subscription_tier: 'free',
  user_role: 'user', status_badges: [], main_instruments: [], monetization_goals: 'hobby',
  open_to_collab: false, primary_daw: null, other_daws: [], ai_usage_level: 'none',
  ai_usage_percent: null, ai_tools: [], coins_balance: 0, ia_credits: 0, marketing_opt_in: false,
};

// Define Props interface
interface FullProfileFormProps {
  user: User | null; // Accept the Supabase User object
}

export const FullProfileForm: React.FC<FullProfileFormProps> = ({ user }) => {
  const router = useRouter();
  const supabase = getSupabaseClient();
  const { toast } = useToast();
  const [userLoading, setUserLoading] = useState(true);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<Profile>(initialProfileState);
  const [initialProfile, setInitialProfile] = useState<Profile>(initialProfileState);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [headerPreview, setHeaderPreview] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [headerFile, setHeaderFile] = useState<File | null>(null);
  const [customRolePrimary, setCustomRolePrimary] = useState<string>("");
  const [hasChanges, setHasChanges] = useState(false);
  const avatarInputRef = React.useRef<HTMLInputElement>(null);
  const headerInputRef = React.useRef<HTMLInputElement>(null);

  // --- Hooks (Copied and adapted) ---
  useEffect(() => {
    // Fetch User Session Logic...
    const fetchUserSession = async () => {
        setUserLoading(true);
        const { data: { session } } = await supabase.auth.getSession();
        const activeUser = session?.user ?? null;
        setUserLoading(false);
      };
      fetchUserSession();
      const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
        const activeUser = session?.user ?? null;
        setUserLoading(false);
      });
      return () => { authListener?.subscription.unsubscribe(); };
  }, [supabase]);

  const fetchProfile = useCallback(async () => {
    // Fetch Profile Logic...
    if (!user) {
        if (!userLoading) setLoading(false);
        return;
      }
      setLoading(true);
      try {
        const { data, error: fetchError } = await supabase.from("profiles").select("*").eq("id", user.id).single();
        if (fetchError || !data) {
          // Handle error or new user...
          if (fetchError && fetchError.code !== 'PGRST116') {
               console.error("Error fetching profile:", fetchError);
               toast({ title: "Erreur chargement profil", description: fetchError.message, variant: "destructive" });
          } else {
              // Welcome message for new profile setup
              // toast({ title: "Bienvenue !", description: "Complétez votre profil pour commencer.", variant: "default" });
          }
          setProfile(prev => ({ ...initialProfileState, id: user.id, email: user.email || '' }));
        } else {
          // Parse and set profile data...
           let parsedSocialLinks: SocialLink[] = [];
           if (Array.isArray(data.social_links)) {
               parsedSocialLinks = data.social_links.filter( (sl: any) => sl && typeof sl.platform === 'string' && typeof sl.url === 'string');
           } else if (typeof data.social_links === 'object' && data.social_links !== null) {
               Object.entries(data.social_links).forEach(([platform, url]) => {
                   if (typeof url === 'string' && url.trim() !== '') {
                       parsedSocialLinks.push({ platform: platform.toLowerCase(), url });
                   }
               });
           }
          setProfile({
            id: data.id || user.id, email: user.email || data.email || '',
            username: data.username || '', full_name: data.full_name || '',
            display_name: data.display_name || '', avatar_url: data.avatar_url || null,
            header_url: data.header_url || null, bio: data.bio || '',
            website: data.website || '', location_city: data.location_city || '',
            location_country: data.location_country || '', social_links: parsedSocialLinks,
            genres: Array.isArray(data.genres) ? data.genres : [],
            influences: Array.isArray(data.influences) ? data.influences : [],
            tags: Array.isArray(data.tags) ? data.tags : [],
            is_artist: typeof data.is_artist === 'boolean' ? data.is_artist : false,
            record_label: data.record_label || '', equipment: data.equipment || '',
            spoken_languages: Array.isArray(data.spoken_languages) ? data.spoken_languages : [],
            instruments_played: Array.isArray(data.instruments_played) ? data.instruments_played.filter( (inst: any) => typeof inst === 'object' && inst !== null && typeof inst.name === 'string' && typeof inst.experience_years === 'number') : [],
            is_profile_public: typeof data.is_profile_public === 'boolean' ? data.is_profile_public : true,
            show_stats_publicly: typeof data.show_stats_publicly === 'boolean' ? data.show_stats_publicly : true,
            allow_collaboration_requests: typeof data.allow_collaboration_requests === 'boolean' ? data.allow_collaboration_requests : true,
            receive_email_notifications: typeof data.receive_email_notifications === 'boolean' ? data.receive_email_notifications : true,
            updated_at: data.updated_at || undefined,
            role_primary: data.role_primary || 'listener',
            roles_secondary: Array.isArray(data.roles_secondary) ? data.roles_secondary : [],
            operating_systems: Array.isArray(data.operating_systems) ? data.operating_systems : [],
            subscription_tier: data.subscription_tier || 'free',
            user_role: data.user_role || 'user',
            status_badges: Array.isArray(data.status_badges) ? data.status_badges : [],
            main_instruments: Array.isArray(data.main_instruments) ? data.main_instruments : [],
            monetization_goals: data.monetization_goals || 'hobby',
            open_to_collab: typeof data.open_to_collab === 'boolean' ? data.open_to_collab : false,
            primary_daw: data.primary_daw || null,
            other_daws: Array.isArray(data.other_daws) ? data.other_daws : [],
            ai_usage_level: data.ai_usage_level || 'none',
            ai_usage_percent: typeof data.ai_usage_percent === 'number' ? data.ai_usage_percent : null,
            ai_tools: Array.isArray(data.ai_tools) ? data.ai_tools : [],
            coins_balance: typeof data.coins_balance === 'number' ? data.coins_balance : 0,
            ia_credits: typeof data.ia_credits === 'number' ? data.ia_credits : 0,
            marketing_opt_in: typeof data.marketing_opt_in === 'boolean' ? data.marketing_opt_in : false,
          });
        }
      } catch (error: any) {
        console.error("Unexpected error in fetchProfile:", error);
        toast({ title: "Erreur Profil", description: "Une erreur inattendue est survenue lors du chargement du profil.", variant: "destructive" });
      } finally {
        setLoading(false);
      }
  }, [user, userLoading, supabase, toast]);

  useEffect(() => {
    if (!userLoading && user) {
      fetchProfile();
    } else if (!userLoading && !user) {
      setLoading(false);
      setProfile(initialProfileState);
    }
  }, [user, userLoading, fetchProfile]);

  useEffect(() => {
    setHasChanges(JSON.stringify(profile) !== JSON.stringify(initialProfile));
  }, [profile, initialProfile]);

  // --- Handlers (Copied and adapted) ---
  const handleAvatarChange = (event: ChangeEvent<HTMLInputElement>) => {
    // Avatar change logic...
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) {
        toast({ title: "Fichier trop volumineux", description: "L'image de l'avatar ne doit pas dépasser 2Mo.", variant: "destructive" });
        setAvatarFile(null);
        if (avatarInputRef.current) avatarInputRef.current.value = "";
        return;
      }
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => { setAvatarPreview(reader.result as string); };
      reader.readAsDataURL(file);
    } else { setAvatarFile(null); }
  };

  // Ajout : handler robuste pour supprimer la bannière
  const handleRemoveHeader = () => {
    setHeaderFile(null);
    setHeaderPreview(null);
    setProfile(prev => ({ ...prev, header_url: null }));
    if (headerInputRef.current) headerInputRef.current.value = "";
  };

  // Ajout : handler robuste pour annuler les modifications
  const handleCancel = () => {
    setProfile(initialProfile); // Reset to original data
    setAvatarPreview(null); // Clear avatar preview if any
    setHeaderPreview(null); // Clear header preview if any
    setAvatarFile(null);
    setHeaderFile(null);
    setCustomRolePrimary(''); // Simplified reset
    toast({ title: "Modifications annulées", variant: "default" });
  };

  // Ajout : handler robuste pour sauvegarder le profil
  const handleSubmit = async (event?: React.FormEvent<HTMLFormElement>) => {
    if (event) event.preventDefault();
    if (!user || !user.id) {
      toast({ title: "Erreur Session", description: "Session utilisateur invalide.", variant: "destructive" });
      return;
    }
    setSaving(true);
    let newAvatarUrl = profile.avatar_url;
    let newHeaderUrl = profile.header_url;
    try {
      // Upload avatar si besoin
      if (avatarFile) {
        const avatarPath = `${user.id}/avatar-${Date.now()}.${avatarFile.name.split('.').pop()}`;
        const { error: avatarUploadError } = await supabase.storage.from('avatars').upload(avatarPath, avatarFile, { upsert: true });
        if (avatarUploadError) throw new Error(`Erreur upload avatar: ${avatarUploadError.message}`);
        const { data: avatarPublicUrlData } = supabase.storage.from('avatars').getPublicUrl(avatarPath);
        newAvatarUrl = avatarPublicUrlData?.publicUrl || null;
      } else if (profile.avatar_url === null && avatarPreview === null) {
        newAvatarUrl = null;
      }
      // Upload header si besoin
      if (headerFile) {
        const headerPath = `${user.id}/header-${Date.now()}.${headerFile.name.split('.').pop()}`;
        const { error: headerUploadError } = await supabase.storage.from('profile-headers').upload(headerPath, headerFile, { upsert: true });
        if (headerUploadError) throw new Error(`Erreur upload bannière: ${headerUploadError.message}`);
        const { data: headerPublicUrlData } = supabase.storage.from('profile-headers').getPublicUrl(headerPath);
        newHeaderUrl = headerPublicUrlData?.publicUrl || null;
      } else if (profile.header_url === null && headerPreview === null) {
        newHeaderUrl = null;
      }
      // Prépare les données à sauvegarder
      let finalRolePrimary = profile.role_primary;
      if (profile.role_primary === 'other' && customRolePrimary.trim() !== "") {
        finalRolePrimary = customRolePrimary.trim();
      }
      const profileDataToSave = { ...profile, id: user.id, avatar_url: newAvatarUrl, header_url: newHeaderUrl, role_primary: finalRolePrimary };
      // Vérifie si le profil existe déjà
      const { data: existingProfile, error: checkError } = await supabase.from("profiles").select("id").eq("id", user.id).maybeSingle();
      if (checkError && checkError.code !== 'PGRST116') throw checkError;
      let upsertError;
      if (existingProfile) {
        const { error } = await supabase.from("profiles").update({ ...profileDataToSave, updated_at: new Date().toISOString() }).eq("id", user.id);
        upsertError = error;
      } else {
        const { error } = await supabase.from("profiles").insert({ ...profileDataToSave, id: user.id, updated_at: new Date().toISOString() });
        upsertError = error;
      }
      if (upsertError) throw upsertError;
      toast({ title: "Profil sauvegardé", description: "Vos modifications ont été enregistrées.", variant: "default" });
      setInitialProfile(profileDataToSave);
      setHasChanges(false);
      if (avatarFile) setAvatarFile(null);
      if (headerFile) setHeaderFile(null);
    } catch (error: any) {
      console.error("Error saving profile:", error);
      toast({ title: "Erreur Sauvegarde", description: error.message, variant: "destructive" });
    } finally {
      setSaving(false);
    }
  };

  const handleRemoveAvatar = () => {
    // Remove avatar logic...
    setAvatarFile(null); setAvatarPreview(null); setProfile(prev => ({ ...prev, avatar_url: null }));
    if (avatarInputRef.current) { avatarInputRef.current.value = ""; }
  };
  
  const handleHeaderChange = (event: ChangeEvent<HTMLInputElement>) => {
    // Header change logic...
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast({ title: "Fichier trop volumineux", description: "L'image de bannière ne doit pas dépasser 5Mo.", variant: "destructive" });
        setHeaderFile(null);
        if (headerInputRef.current) headerInputRef.current.value = "";
        return;
      }
      setHeaderFile(file);
      const reader = new FileReader();
      reader.onloadend = () => { setHeaderPreview(reader.result as string); };
      reader.readAsDataURL(file);
    } else { setHeaderFile(null); }
    toast({ title: "Modifications annulées", variant: "default" });
  };

  // --- Render Logic ---
  if (loading || userLoading) {
    return <div className="flex items-center justify-center min-h-[400px]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }
  if (!user && !userLoading) {
     return <div className="flex items-center justify-center min-h-[400px]"><p>Veuillez vous connecter pour modifier votre profil.</p></div>;
  }

  const currentProfile = profile.id ? profile : {...initialProfileState, id: user?.id || "", email: user?.email || ""};

  return (
    <form onSubmit={handleSubmit} id="profile-edit-form">
      {/* Profile Header Section - Reuse from provided code */}
      <div className="mb-8 p-6 bg-card dark:bg-slate-800 rounded-lg shadow-md space-y-6 border dark:border-slate-700">
        {/* Avatar and Banner Upload UI ... will add in next step */}
         <div className="flex flex-col md:flex-row gap-6 items-start">
            <div className="space-y-2 flex-shrink-0">
              <Label>Avatar</Label>
              <div className="flex flex-col gap-2 items-center md:items-start">
                {/* Avatar Preview/Placeholder */}
                {(avatarPreview || currentProfile.avatar_url) ? (
                  <img src={avatarPreview || currentProfile.avatar_url || undefined} alt="Aperçu Avatar" className="w-32 h-32 md:w-40 md:h-40 rounded-full object-cover bg-muted ring-2 ring-primary/50" />
                ) : (
                  <div className="w-32 h-32 md:w-40 md:h-40 rounded-full bg-muted flex items-center justify-center text-muted-foreground ring-1 ring-border">
                    <UserCircle2 className="h-16 w-16 md:h-20 md:w-20" />
                  </div>
                )}
                {/* Avatar Buttons */}
                 <div className="flex items-center gap-2">
                  <input type="file" accept="image/*" onChange={handleAvatarChange} className="hidden" ref={avatarInputRef} id="avatarUploadHeader" />
                  <Button type="button" size="sm" variant="outline" onClick={() => avatarInputRef.current?.click()}>Changer</Button>
                  {(avatarPreview || currentProfile.avatar_url) && (
                    <Button type="button" variant="ghost" size="sm" onClick={handleRemoveAvatar} className="text-destructive hover:text-destructive-foreground">Supprimer</Button>
                  )}
                </div>
              </div>
            </div>
            <div className="space-y-4 flex-grow">
              {/* Banner Upload UI */}
               <div>
                <Label>Image de Bannière</Label>
                <div className="mt-1 aspect-[3/1] w-full rounded-lg bg-muted ring-1 ring-border relative overflow-hidden">
                 {(headerPreview || currentProfile.header_url) ? (
                    <img src={headerPreview || currentProfile.header_url || undefined} alt="Aperçu Bannière" className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                      <span>Pas de bannière (Ratio 3:1 recommandé)</span>
                    </div>
                  )}
                </div>
                 <div className="flex items-center gap-2 mt-2">
                    <input type="file" accept="image/*" onChange={handleHeaderChange} className="hidden" ref={headerInputRef} id="headerUploadHeader" />
                    <Button type="button" size="sm" variant="outline" onClick={() => headerInputRef.current?.click()}>Changer la bannière</Button>
                    {(headerPreview || currentProfile.header_url) && (
                      <Button type="button" variant="ghost" size="sm" onClick={handleRemoveHeader} className="text-destructive hover:text-destructive-foreground">Supprimer</Button>
                    )}
                </div>
              </div>
              {/* Display Name and Username */}
               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                 <div className="space-y-1">
                   <Label htmlFor="header_display_name">Nom d'affichage</Label>
                   <div className="flex items-center gap-2">
                     <Input id="header_display_name" value={currentProfile.display_name} onChange={(e) => setProfile({ ...profile, display_name: e.target.value })} placeholder="Votre nom public" className="text-lg flex-grow" />
                     <Badge variant={ currentProfile.subscription_tier === 'pro' || currentProfile.subscription_tier === 'studio' ? 'default' : 'secondary'} className="capitalize whitespace-nowrap">
                       {currentProfile.subscription_tier}
                       {currentProfile.user_role === 'admin' && ' Admin'}
                     </Badge>
                   </div>
                 </div>
                 <div className="space-y-1">
                   <Label htmlFor="header_username">Nom d'utilisateur (non modifiable)</Label>
                   <Input id="header_username" value={currentProfile.username} disabled className="text-lg bg-muted/50" />
                   {currentProfile.username && 
                    <div className="flex items-center text-xs text-muted-foreground pt-1 gap-2">
                      <span>Profil public :</span>
                      <Link href={`/artists/${currentProfile.username}`} target="_blank" rel="noopener noreferrer" passHref legacyBehavior>
                         <a className="flex items-center gap-1 hover:text-primary underline"><ViewPublicProfileIcon className="h-3 w-3"/>/artists/{currentProfile.username}</a>
                      </Link>
                     </div>
                   }
                 </div>
               </div>
            </div>
          </div>
      </div>
      
      {/* Tabs for different profile sections */}
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 mb-6 border-b rounded-none">
          <TabsTrigger value="general" className="pb-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary"><UserCircle2 className="mr-2 h-4 w-4"/>Général</TabsTrigger>
          <TabsTrigger value="artist" className="pb-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary"><Music className="mr-2 h-4 w-4"/>Détails Artiste</TabsTrigger>
          <TabsTrigger value="account" className="pb-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary"><Settings2 className="mr-2 h-4 w-4"/>Compte & Préférences</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="mt-6 space-y-6 p-4 border dark:border-slate-700 rounded-lg bg-card dark:bg-slate-800">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="full_name">Nom complet (optionnel)</Label>
              <Input id="full_name" placeholder="Prénom Nom" value={currentProfile.full_name} onChange={(e) => setProfile({ ...profile, full_name: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Site web personnel</Label>
              <Input id="website" type="url" placeholder="https://example.com" value={currentProfile.website} onChange={(e) => setProfile({ ...profile, website: e.target.value })} />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">Biographie</Label>
            <Textarea id="bio" placeholder="Parlez un peu de vous..." value={currentProfile.bio} onChange={(e) => setProfile({ ...profile, bio: e.target.value })} rows={4} maxLength={1000} className="min-h-[100px]"/>
            <p className="text-xs text-muted-foreground text-right">{currentProfile.bio.length} / 1000 caractères</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="location_city">Ville</Label>
              <Input id="location_city" placeholder="Ex: Paris" value={currentProfile.location_city} onChange={(e) => setProfile({ ...profile, location_city: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location_country">Pays</Label>
              <Select value={currentProfile.location_country || 'none'} onValueChange={(value) => setProfile({ ...profile, location_country: value })}>
                <SelectTrigger id="location_country">
                  <SelectValue placeholder="Sélectionnez un pays" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Aucun</SelectItem>
                  {countryOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Social Links - Dynamic Section */}
          <div className="space-y-4">
            <Label className="flex items-center gap-2"><LinkIcon className="h-4 w-4" /> Liens Sociaux / Musique</Label>
            {currentProfile.social_links.map((link, index) => (
              <div key={index} className="flex items-center gap-2">
                <Select
                  value={link.platform}
                  onValueChange={(value) => {
                    const newLinks = [...currentProfile.social_links];
                    newLinks[index].platform = value;
                    setProfile({ ...profile, social_links: newLinks });
                  }}
                >
                  <SelectTrigger className="w-[150px] flex-shrink-0">
                    <SelectValue placeholder="Plateforme" />
                  </SelectTrigger>
                  <SelectContent>
                    {socialPlatformOptionsForLogic.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  type="url"
                  placeholder="URL (https://...)"
                  value={link.url}
                  onChange={(e) => {
                    const newLinks = [...currentProfile.social_links];
                    newLinks[index].url = e.target.value;
                    setProfile({ ...profile, social_links: newLinks });
                  }}
                  className="flex-grow"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const newLinks = currentProfile.social_links.filter((_, i) => i !== index);
                    setProfile({ ...profile, social_links: newLinks });
                  }}
                  className="text-destructive hover:text-destructive-foreground"
                >
                  Supprimer
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                setProfile({ ...profile, social_links: [...currentProfile.social_links, { platform: "spotify", url: "" }] });
              }}
              disabled={currentProfile.social_links.length >= 10} // Limit links
            >
              Ajouter un lien
            </Button>
          </div>

        </TabsContent>

        <TabsContent value="artist" className="mt-6 space-y-6 p-4 border dark:border-slate-700 rounded-lg bg-card dark:bg-slate-800">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
               <Checkbox id="is_artist" checked={currentProfile.is_artist} onCheckedChange={(checked) => setProfile({ ...profile, is_artist: Boolean(checked) })} />
               <Label htmlFor="is_artist" className="font-medium">Je suis un artiste / créateur de musique</Label>
            </div>
            <p className="text-xs text-muted-foreground pl-6">Cochez cette case pour accéder aux champs spécifiques aux artistes.</p>
          </div>
          
          {currentProfile.is_artist && (
            <>
              {/* Roles */} 
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="role_primary">Rôle principal</Label>
                  <Select 
                    value={globalRolePrimaryOptions.some(opt => opt.value === currentProfile.role_primary) ? currentProfile.role_primary : 'other'}
                    onValueChange={(value: string) => {
                      setProfile({ ...profile, role_primary: value });
                      if (value !== 'other') setCustomRolePrimary('');
                    }}
                  >
                    <SelectTrigger id="role_primary">
                      <SelectValue placeholder="Sélectionnez votre rôle principal" />
                    </SelectTrigger>
                    <SelectContent>
                      {globalRolePrimaryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                      ))}
                      <SelectItem value="other">Autre (préciser)</SelectItem>
                    </SelectContent>
                  </Select>
                  {profile.role_primary === 'other' && (
                    <Input 
                      type="text"
                      placeholder="Précisez votre rôle principal"
                      value={customRolePrimary}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => setCustomRolePrimary(e.target.value)}
                      className="mt-2"
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="roles_secondary">Rôles secondaires</Label>
                  <MultiSelect
                    // No 'id' prop needed if Label htmlFor points here
                    options={roleSecondaryOptions}
                    selected={currentProfile.roles_secondary}
                    onChange={(selected: string[]) => setProfile({ ...profile, roles_secondary: selected })}
                    placeholder="Sélectionnez vos rôles secondaires..."
                    className="w-full" // Apply styling here
                  />
                </div>
              </div>

              {/* Genres & Influences */} 
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="genres">Genres Musicaux</Label>
                  <MultiSelect
                    options={globalGenreOptions}
                    selected={currentProfile.genres}
                    onChange={(selected: string[]) => setProfile({ ...profile, genres: selected })}
                    placeholder="Sélectionnez vos genres..."
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="influences">Influences</Label>
                  <MultiSelect
                    options={globalInfluenceOptions}
                    selected={currentProfile.influences}
                    onChange={(selected: string[]) => setProfile({ ...profile, influences: selected })}
                    placeholder="Sélectionnez vos influences..."
                    className="w-full"
                  />
                </div>
              </div>
              
              {/* Instruments Played */} 
              <div className="space-y-4">
                <Label>Instruments Joués</Label>
                {currentProfile.instruments_played.map((inst, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Select
                      value={inst.name}
                      onValueChange={(value: string) => {
                        const newInstruments = [...currentProfile.instruments_played];
                        newInstruments[index].name = value;
                        setProfile({ ...profile, instruments_played: newInstruments });
                      }}
                    >
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Instrument" />
                      </SelectTrigger>
                      <SelectContent>
                        {instrumentationOptions.map((opt) => (
                          <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input
                      type="number"
                      placeholder="Années Exp."
                      value={inst.experience_years}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => {
                        const newInstruments = [...currentProfile.instruments_played];
                        // Ensure value is non-negative
                        newInstruments[index].experience_years = Math.max(0, parseInt(e.target.value, 10) || 0);
                        setProfile({ ...profile, instruments_played: newInstruments });
                      }}
                      min="0"
                      className="w-[120px]"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newInstruments = currentProfile.instruments_played.filter((_, i) => i !== index);
                        setProfile({ ...profile, instruments_played: newInstruments });
                      }}
                      className="text-destructive hover:text-destructive-foreground"
                    >
                      Supprimer
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Avoid adding if max length reached
                    if (currentProfile.instruments_played.length < 10) {
                         setProfile({ ...profile, instruments_played: [...currentProfile.instruments_played, { name: instrumentationOptions[0]?.value || '', experience_years: 0 }] });
                    }
                  }}
                  disabled={currentProfile.instruments_played.length >= 10}
                >
                  Ajouter un instrument
                </Button>
              </div>

              {/* DAW & OS */} 
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="primary_daw">DAW Principal</Label>
                  <Select value={currentProfile.primary_daw || ''} onValueChange={(value: string) => setProfile({ ...profile, primary_daw: value || null })}>
                    <SelectTrigger id="primary_daw">
                      <SelectValue placeholder="Votre DAW principal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Aucun</SelectItem>
                      {globalDawOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="other_daws">Autres DAW utilisés</Label>
                  <MultiSelect
                    options={globalDawOptions}
                    selected={currentProfile.other_daws}
                    onChange={(selected: string[]) => setProfile({ ...profile, other_daws: selected })}
                    placeholder="Autres DAW..."
                    className="w-full"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="operating_systems">Systèmes d'exploitation</Label>
                <MultiSelect
                  options={osOptions}
                  selected={currentProfile.operating_systems || []}
                  onChange={(selected: string[]) => setProfile({ ...profile, operating_systems: selected })}
                  placeholder="Vos OS (Windows, macOS, Linux)..."
                  className="w-full"
                />
              </div>
              
              {/* Equipment & Label */} 
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="equipment">Équipement Principal</Label>
                  <Textarea
                    id="equipment"
                    placeholder="Listez votre équipement clé (micros, synthés, guitares...)"
                    value={currentProfile.equipment}
                    onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setProfile({ ...profile, equipment: e.target.value })}
                    rows={3}
                    maxLength={500}
                    className="min-h-[80px]"
                  />
                  <p className="text-xs text-muted-foreground text-right">{currentProfile.equipment.length} / 500 caractères</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="record_label">Label (si applicable)</Label>
                  <Input
                    id="record_label"
                    placeholder="Nom du label"
                    value={currentProfile.record_label}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => setProfile({ ...profile, record_label: e.target.value })}
                  />
                </div>
              </div>
              
              {/* Collaboration */} 
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="open_to_collab" checked={currentProfile.open_to_collab} onCheckedChange={(checked) => setProfile({ ...profile, open_to_collab: Boolean(checked) })} />
                  <Label htmlFor="open_to_collab" className="font-medium">Ouvert aux collaborations</Label>
                </div>
                <p className="text-xs text-muted-foreground pl-6">Indiquez si vous êtes intéressé par des projets collaboratifs.</p>
              </div>

              {/* AI Usage */} 
              <div className="p-4 border dark:border-slate-600 rounded-lg bg-muted/20 space-y-4">
                <h4 className="font-semibold text-base">Utilisation de l'IA</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="ai_usage_level">Niveau d'utilisation de l'IA dans votre musique</Label>
                    <Select value={currentProfile.ai_usage_level || 'none'} onValueChange={(value: string) => setProfile({ ...profile, ai_usage_level: value as AiUsageEnum })}>
                      <SelectTrigger id="ai_usage_level">
                        <SelectValue placeholder="Niveau d'utilisation IA" />
                      </SelectTrigger>
                      <SelectContent>
                        {aiUsageLevelOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ai_usage_percent">Pourcentage approx. (si pertinent)</Label>
                    <Input
                      id="ai_usage_percent"
                      type="number"
                      placeholder="%"
                      value={currentProfile.ai_usage_percent ?? ''}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => setProfile({ ...profile, ai_usage_percent: e.target.value ? parseInt(e.target.value) : null })}
                      min="0"
                      max="100"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ai_tools">Outils IA utilisés</Label>
                  <MultiSelect
                    options={[]} // Consider adding predefined common AI tool options later
                    selected={currentProfile.ai_tools}
                    onChange={(selected: string[]) => setProfile({ ...profile, ai_tools: selected })}
                    placeholder="Listez les outils IA (Suno, Udio, AIVA...)"
                    className="w-full"
                  />
                </div>
              </div>
            </> // End is_artist conditional fragment
          )} 
         </TabsContent>
 
         <TabsContent value="account"> 
           {/* TODO: Add content for Account & Preferences tab */}
           <div className="space-y-6 p-4 border dark:border-slate-700 rounded-lg bg-card dark:bg-slate-800">
             <h3 className="text-lg font-medium">Compte & Préférences</h3>
             <p className="text-sm text-muted-foreground">Gérez les paramètres de votre compte et vos préférences d'application.</p>
             {/* Add relevant account settings fields here later */} 
              <div>
                <Label>Changement de mot de passe (Fonctionnalité à venir)</Label>
                <p className="text-xs text-muted-foreground">Vous pourrez bientôt changer votre mot de passe ici.</p>
              </div>
              <div>
                <Label>Notifications par email (Fonctionnalité à venir)</Label>
                 <p className="text-xs text-muted-foreground">Configurez vos préférences de notification.</p>
              </div>
           </div>
         </TabsContent>
+
          </Tabs>
          
       <div className="flex justify-end gap-2 mt-8">
         <Button type="button" variant="outline" onClick={handleCancel} disabled={saving || !hasChanges}>
           Annuler
         </Button>
         <Button type="submit" disabled={saving || !hasChanges}>
           {saving ? 'Sauvegarde...' : 'Sauvegarder les modifications'}
         </Button>
        </div> 
      </form> 
  ); 
}; 
  
  export default FullProfileForm;
