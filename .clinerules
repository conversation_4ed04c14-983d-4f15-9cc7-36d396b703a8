# Mouvik Project - .clinerules

## Objectif de ce fichier
Ce fichier sert de journal d'apprentissage pour le projet Mouvik. Il capture les patrons de conception importants, les préférences de l'utilisateur, l'intelligence du projet et toute information clé qui aide à travailler plus efficacement sur ce projet.

## Conventions Générales
- **Langage principal du code :** TypeScript (pour Next.js)
- **Style de code :** Suivre les linters et formateurs configurés (ex: ESLint, Prettier).
- **Nommage des fichiers :** kebab-case pour les fichiers et dossiers (ex: `user-profile.tsx`, `api-routes`).
- **Nommage des composants React :** PascalCase (ex: `UserProfileCard`).
- **Nommage des fonctions/variables :** camelCase (ex: `getUserProfile`).
- **Gestion des dépendances :** Utiliser pnpm.

## Memory Bank
- La Memory Bank est CRUCIALE. Toujours la consulter en début de session.
- Mettre à jour la Memory Bank après des changements significatifs ou à la demande de l'utilisateur.
- Les fichiers `activeContext.md` et `progress.md` doivent être particulièrement soignés pour refléter l'état actuel.

## Technologies et Architecture
- **Frontend :** Next.js (App Router)
- **Backend :** Next.js API Routes / Server Actions
- **Base de données :** Supabase (PostgreSQL)
- **Authentification :** Supabase Auth
- **Stockage :** Supabase Storage
- **UI Components :** shadcn/ui, Tailwind CSS

## Workflow de Développement
1.  **Comprendre la tâche :** Lire la Memory Bank, clarifier les exigences.
2.  **Planifier :** Définir les étapes, identifier les fichiers à modifier/créer.
3.  **Implémenter :** Coder la solution, en respectant les conventions.
4.  **Tester :** (Idéalement) Ajouter des tests unitaires/intégration.
5.  **Documenter :** Mettre à jour la Memory Bank et ce fichier `.clinerules` si de nouveaux apprentissages émergent.

## Apprentissages Spécifiques au Projet (à compléter au fur et à mesure)
- ...

## Préférences Utilisateur (à compléter)
- L'utilisateur préfère des mises à jour fréquentes sur l'avancement.
- ...

## Points d'Attention Particuliers
- **Cache Supabase/PostgREST :** Être attentif aux erreurs PGRST200/PGRST204 après des modifications DDL. Un redémarrage du projet ou un rafraîchissement du schéma via le Dashboard Supabase peut être nécessaire.
- **Déploiement :** Suivre les procédures dans `deployment.md`.

## Débogage des Composants React et Affichage
- **Problème d'affichage (composant "n'apparaît pas" ou données incorrectes) :**
    1.  **Vérifier les props :** Inspecter les props passées au composant parent et au composant concerné. Utiliser `console.log` dans le composant parent pour voir les valeurs juste avant de les passer.
    2.  **Vérifier l'état interne :** Si le composant utilise un état interne (`useState`, `useReducer`), vérifier ses valeurs.
    3.  **Vérifier les appels de données :** Si le composant (ou son parent) récupère des données (ex: via une RPC Supabase, API fetch), s'assurer que l'appel réussit et que les données retournées sont celles attendues. `console.log` les résultats des appels.
    4.  **Conditions de rendu :** Examiner toute logique conditionnelle qui pourrait empêcher le rendu du composant ou d'une partie de celui-ci (ex: `&&`, ternaires, `if` statements).
    5.  **CSS / Styles :** Vérifier si des styles (CSS, Tailwind) masquent le composant (ex: `display: none`, `opacity: 0`, mauvais positionnement). Utiliser les outils de développement du navigateur.
    6.  **Erreurs console :** Toujours vérifier la console du navigateur pour des erreurs JavaScript qui pourraient interrompre le rendu.
    7.  **Chemin des fichiers :** S'assurer que les importations de composants et de modules sont correctes. Une erreur de chemin peut empêcher un composant d'être chargé.
    8.  **Hydratation (Next.js) :** Être attentif aux erreurs d'hydratation si le contenu rendu côté serveur diffère de celui côté client.
- **Carte de Profil (Dashboard) :**
    - Le composant principal est `components/dashboard/DashboardProfileCard.tsx`.
    - Il est utilisé dans `app/(authenticated)/dashboard/page.tsx`.
    - Les données proviennent de la RPC `get_dashboard_data`, spécifiquement du champ `user_profile`.
    - Un `console.log("Value of dashboardData after RPC call:", dashboardData);` est présent dans `app/(authenticated)/dashboard/page.tsx` pour aider au débogage des données reçues.

## Utilisation des Outils IA (Trae AI)
- Privilégier les outils d'édition de code pour les modifications.
- Utiliser `search_by_regex` pour localiser le code pertinent avant de visualiser des fichiers complets.
- S'assurer que les chemins de fichiers sont corrects avant d'utiliser `view_files` ou des outils d'édition.