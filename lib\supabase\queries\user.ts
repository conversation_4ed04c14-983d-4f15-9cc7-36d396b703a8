// lib/supabase/queries/user.ts
import { SupabaseClient, User } from '@supabase/supabase-js';
import { UserProfile } from '@/types/auth'; // Assuming this path is correct for UserProfileForSidebar

export async function getUserProfileForSidebar(
  supabase: SupabaseClient,
  authUserId: string, // Renamed for clarity, this is auth.users.id
  authUser: User | null // Pass the authenticated user to get email
): Promise<UserProfile | null> {
  console.log(`[getUserProfileForSidebar] Fetching profile for authUserId: ${authUserId}`);
  if (!authUserId) {
    console.warn('[getUserProfileForSidebar] No authUserId provided, returning null.');
    return null;
  }

  // Use select('*') for simplicity and to avoid RLS issues with specific columns
  console.log(`[getUserProfileForSidebar] PRE-QUERY for user ${authUserId}`);
  const { data: profileData, error } = await supabase
    .from('profiles')
    .select(`
      id,
      name: display_name,
      avatar_url,
      created_at,
      updated_at
    `)
    .eq('id', authUserId)
    .single();
  console.log(`[getUserProfileForSidebar] POST-QUERY for user ${authUserId} - Error:`, error, `Data:`, profileData);

  if (error) {
    console.error('[getUserProfileForSidebar] Supabase error fetching profile:', error.message);
    return null;
  }

  if (!profileData) {
    console.warn(`[getUserProfileForSidebar] No profile data found for user ${authUserId}, returning null.`);
    return null;
  }

  console.log(`[getUserProfileForSidebar] Successfully fetched profile data for user ${authUserId}. Constructing final object.`);
  
  // Construct the final profile, merging data from the profiles table and the auth user object
  const finalProfile: UserProfile = {
    ...profileData,
    id: authUserId, // Ensure the ID from the auth user is authoritative
    email: authUser?.email || '',
  };

  console.log(`[getUserProfileForSidebar] Returning final profile object for user ${authUserId}:`, finalProfile.name);
  return finalProfile;
}
