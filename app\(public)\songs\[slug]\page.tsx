"use client"; 

import { useState, useEffect, use<PERSON>emo, useCallback, useRef } from 'react';
import { WaveformPlayer } from '@/components/audio/waveform-player';
import { createBrowserClient } from '@/lib/supabase/client';
import Image from 'next/image';
import Link from 'next/link';
import { notFound, useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Music2, UserCircle, Disc, Clock, AlignLeft, Info, Mic2, GitFork, Palette, Guitar, Share2, Edit3, Heart, ListMusic, BarChart3, MessageCircle, ThumbsUp, ThumbsDown, Play, EyeIcon, Tag, CalendarDays, Users, Brain, Layers3, ListPlus, Fingerprint } from 'lucide-react'; 
import { Badge } from '@/components/ui/badge';
import { formatDuration, cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button'; 
import { PlayButton } from '@/components/audio/play-button'; 
import { LikeButton } from '@/components/social/like-button';
import { DislikeButton } from '@/components/social/dislike-button';
import { ResourceViewTracker } from '@/components/stats/resource-view-tracker';
import { ArtistCardDisplay } from '@/components/artists/artist-card-display';
import { CommentSection } from '@/components/comments/comment-section';
import { SimilarSongs } from '@/components/songs/similar-songs'; 
import { useUser } from '@/contexts/user-context'; 
import type { Song } from '@/components/songs/song-schema';
import type { Album, UserProfile } from '@/types'; 
import { useResourceInteractionStore, getResourceInteractionStoreState, type ResourceInteractionStoreState, type ResourceInteractionStatus, DEFAULT_RESOURCE_STATE, getResourceKey } from '@/lib/stores/resource-interaction-store';
import { shallow } from 'zustand/shallow';
import { Loader2 } from 'lucide-react'; // Added Loader2
import { SongInfoPanel } from '@/components/songs/SongInfoPanel';
import { useToast } from '@/hooks/use-toast';
import { SharePopover } from '@/components/shared/share-popover';
import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button';

import { Label } from '@/components/ui/label';
import { ResourceHeaderBanner } from '@/components/shared/resource-header-banner';
import { ResourceStatsDisplay } from '@/components/shared/ResourceStatsDisplay';
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from '@/components/ui/switch';

interface PublicSongData extends Song { 
  profiles: UserProfile | null;
  albums: Pick<Album, 'id' | 'title' | 'cover_url' | 'slug'> | null;
  like_count?: number; 
  view_count?: number;
  plays?: number; 
  dislike_count?: number; 
}

const UUID_REGEX = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-4[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/i;

async function getSongBySlugOrId(slugOrId: string, supabaseClient: any, currentUserId?: string): Promise<PublicSongData | null> {
  const supabase = supabaseClient;
  
  const selectString = `
    *,
    profiles:creator_user_id (id, username, display_name, avatar_url, bio, website, location_city, location_country),
    albums:album_id (id, title, cover_url, slug)
  `;

  let queryBuilder = supabase.from('songs').select(selectString);

  const isPotentialUuid = UUID_REGEX.test(slugOrId);

  if (isPotentialUuid) {
    console.log(`Attempting to fetch song by ID: ${slugOrId}`);
    queryBuilder = queryBuilder.eq('id', slugOrId);
  } else {
    console.log(`Attempting to fetch song by slug: ${slugOrId}`);
    queryBuilder = queryBuilder.eq('slug', slugOrId);
  }

  const { data: songData, error } = await queryBuilder
    .or(`is_public.eq.true${currentUserId ? `,creator_user_id.eq.${currentUserId}` : ''}`)
    .single();

  if (error || !songData) {
    console.error(`Error fetching public song by ${isPotentialUuid ? 'ID' : 'slug'} '${slugOrId}':`, error?.message);
    // If the primary fetch method (ID or slug) fails, we don't try the other one to prevent potential errors like 406 or incorrect matches.
    return null;
  }
  
  // Counts: Assuming these might be on songData or need RPC fallback
  let likeCount = songData.like_count || 0;
  let viewCount = songData.view_count || 0;
  let playCount = songData.plays || 0;
  let dislikeCount = songData.dislike_count || 0;

  // Fallback RPC calls for counts if not directly on songData - retain existing logic for now
  if (songData.like_count === undefined) {
    const { data: rpcLikeCount, error: likeError } = await supabase.rpc('get_like_count', { resource_id_param: songData.id, resource_type_param: 'song' });
    if (likeError) console.warn('Error fetching like_count via RPC:', likeError.message);
    else if (typeof rpcLikeCount === 'number') likeCount = rpcLikeCount;
  }
  if (songData.view_count === undefined) {
    const { data: rpcViewCount, error: viewError } = await supabase.rpc('get_view_count', { resource_id_param: songData.id, resource_type_param: 'song' });
    if (viewError) console.warn('Error fetching view_count via RPC:', viewError.message);
    else if (typeof rpcViewCount === 'number') viewCount = rpcViewCount;
  }
  // Add similar for plays and dislike_count if needed (original code only had these two examples)

  return {
    ...songData,
    profiles: Array.isArray(songData.profiles) ? songData.profiles[0] : songData.profiles,
    albums: Array.isArray(songData.albums) ? songData.albums[0] : songData.albums,
    like_count: likeCount,
    view_count: viewCount,
    plays: playCount,
    dislike_count: dislikeCount,
  } as PublicSongData;
}

export default function PublicSongPage({ params }: { params: { slug: string } }) {
  const supabase = useMemo(() => createBrowserClient(), []);
  const { user } = useUser();
  const router = useRouter();
  const { toast } = useToast();
  const [song, setSong] = useState<PublicSongData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);
  const [activeTab, setActiveTab] = useState("lyrics-chords");

  // --- Start Diagnostic Logging for fetchSongDataAndInteractions dependencies ---
  const prevParamsSlugRef = useRef<string>();
  const prevSupabaseRef = useRef<any>();
  const prevUserIdRef = useRef<string | undefined>();
  const prevStoreSetResourceStatusRef = useRef<any>();
  const prevNotFoundRef = useRef<any>();

  useEffect(() => {
    let changed = false;
    if (prevParamsSlugRef.current !== params.slug) {
      console.log('DIAGNOSTIC: params.slug changed from', prevParamsSlugRef.current, 'to', params.slug);
      changed = true;
    }
    if (prevSupabaseRef.current !== supabase) {
      console.log('DIAGNOSTIC: supabase reference changed.');
      changed = true;
    }
    if (prevUserIdRef.current !== user?.id) {
      console.log('DIAGNOSTIC: user?.id changed from', prevUserIdRef.current, 'to', user?.id);
      changed = true;
    }
    if (prevStoreSetResourceStatusRef.current !== storeSetResourceStatus) {
      console.log('DIAGNOSTIC: storeSetResourceStatus reference changed.');
      changed = true;
    }
    if (prevNotFoundRef.current !== notFound) {
      console.log('DIAGNOSTIC: notFound reference changed.');
      changed = true;
    }
    if (changed) {
      console.log('DIAGNOSTIC: At least one dependency of fetchSongDataAndInteractions changed.');
    } else {
      // console.log('DIAGNOSTIC: fetchSongDataAndInteractions dependencies appear stable this render.');
    }

    prevParamsSlugRef.current = params.slug;
    prevSupabaseRef.current = supabase;
    prevUserIdRef.current = user?.id;
    prevStoreSetResourceStatusRef.current = storeSetResourceStatus;
    prevNotFoundRef.current = notFound;
  }); // This useEffect runs on every render to log changes
  // --- End Diagnostic Logging ---

  const selectSetResourceStatus = useCallback((state: ResourceInteractionStoreState) => state.setResourceStatus, []);
  const storeSetResourceStatus = useResourceInteractionStore(selectSetResourceStatus);

  // Selector for resource-specific state from the store
  const currentResourceState = useResourceInteractionStore(
    (state: ResourceInteractionStoreState) => song ? state.resourceStates[getResourceKey('song', song.id)] || DEFAULT_RESOURCE_STATE : DEFAULT_RESOURCE_STATE
  );

  const storeLikeCount = currentResourceState.likeCount;
  const storeDislikeCount = currentResourceState.dislikeCount;

  const coverArtUrl = useMemo(() => {
    if (!song?.cover_art_url) return '/images/covers/mouvk.png';
    return song.cover_art_url;
  }, [song?.cover_art_url]);

  const artistDisplayName = useMemo(() => {
    return song?.profiles?.display_name || song?.artist_name || 'Artiste inconnu';
  }, [song?.profiles?.display_name, song?.artist_name]);

  const genresForBadges = useMemo(() => {
    if (!song?.genre) return [];
    const genreArray = Array.isArray(song.genre) ? song.genre : String(song.genre).split(',').map(g => g.trim()).filter(g => g);
    return genreArray;
  }, [song?.genre]);

  const preparedGenreBadges = useMemo(() => {
    return genresForBadges.map((genre, index) => (
      <Link key={`genre-header-${index}`} href={`/genres/${encodeURIComponent(genre)}`} passHref>
        <Badge variant="secondary" className="text-xs font-normal cursor-pointer hover:bg-primary/20 transition-colors">
          <Tag className="w-3 h-3 mr-1 opacity-70" />
          {genre}
        </Badge>
      </Link>
    ));
  }, [genresForBadges]);

  const musicalInfoLine = useMemo(() => {
    if (!song) return null;
    const infos = [
      song.key ? { value: song.key, icon: Music2 } : null,
      song.bpm ? { value: `${song.bpm} BPM`, icon: BarChart3 } : null,
      song.time_signature ? { value: song.time_signature, icon: GitFork } : null,
      typeof song.capo === 'number' ? { value: `Capo ${song.capo}`, icon: Layers3 } : null,
    ].filter(Boolean);

    return infos.length > 0 ? (
      <div className="flex flex-wrap gap-x-3 gap-y-1.5 items-center">
        {infos.map((info, index) => (
          info && (
            <Badge key={index} variant="outline" className="text-xs font-normal">
              {info.icon && <info.icon className="w-3 h-3 mr-1 opacity-70" />}
              {info.value}
            </Badge>
          )
        ))}
      </div>
    ) : null;
  }, [song?.key, song?.bpm, song?.time_signature, song?.capo]);

  const songForPlayer: Song | undefined = useMemo(() => {
    if (!song) return undefined;
    return {
      ...song,
      id: song.id,
      title: song.title,
      audio_url: song.audio_url ? String(song.audio_url) : '',
      cover_art_url: coverArtUrl ? String(coverArtUrl) : '', // Match updated Song type
      duration_ms: song.duration_ms || 0, // Match updated Song type
      artist_name: artistDisplayName,
      is_public: song.is_public ?? true,
      slug: song.slug ?? undefined,
      genres: Array.isArray(song.genres) ? song.genres : (song.genres ? [song.genres] : []),
      moods: Array.isArray(song.moods) ? song.moods : (song.moods ? [song.moods] : []),
      instrumentation: Array.isArray(song.instrumentation) ? song.instrumentation : (song.instrumentation ? [song.instrumentation] : []),
      lyrics: song.lyrics ?? '',
      bpm: song.bpm ?? undefined,
      key: song.key ?? undefined,
      ai_content_origin: song.ai_content_origin,
      is_explicit: song.is_explicit ?? false,
      status: song.status ?? 'published',
    };
  }, [song, coverArtUrl, artistDisplayName]);

  const handleTogglePublicStatus = useCallback(async (newPublicStatus: boolean) => {
    if (!song || !user || song.creator_user_id !== user.id || isTogglingStatus) return;

    setIsTogglingStatus(true);
    try {
      const { data: rpcData, error } = await supabase.rpc('toggle_song_public_status', {
        p_song_id: song.id,
        p_user_id: user.id
      });

      if (error) throw error;

      if (rpcData && rpcData.length > 0) {
        const { new_is_public, new_slug } = rpcData[0];
        setSong(prevSong => prevSong ? { ...prevSong, is_public: new_is_public, slug: new_slug } : null);
        toast({
          title: "Statut du morceau mis à jour",
          description: `Le morceau "${song.title}" est maintenant ${new_is_public ? 'public' : 'privé'}.`
        });
        // If slug changed and song is now public, router.replace might be needed if current URL is ID-based
        // Or if the slug in URL needs to update to new_slug
        if (new_is_public && new_slug && params.slug !== new_slug) {
          // router.replace(`/songs/${new_slug}`, { scroll: false });
          // Be cautious with immediate redirects, might be better to let user navigate or refresh
        }
      } else {
        throw new Error("Réponse invalide de la fonction RPC toggle_song_public_status.");
      }
    } catch (err: any) {
      console.error("Error toggling song status:", err);
      toast({ title: "Erreur", description: err.message || "Impossible de changer le statut du morceau.", variant: "destructive" });
    } finally {
      setIsTogglingStatus(false);
    }
  }, [song, user, supabase, toast, params.slug]);

  const handleLike = useCallback(async () => {
    if (!user) {
      toast({ title: 'Connexion requise', description: 'Vous devez être connecté pour aimer un morceau.', variant: 'destructive' });
      return;
    }
    if (!song) return;

    const currentStoreInteractionState = getResourceInteractionStoreState().resourceStates[getResourceKey('song', song.id)] || DEFAULT_RESOURCE_STATE;
    const intendedNewIsLiked = !currentStoreInteractionState.isLiked;

    try {
      await getResourceInteractionStoreState().toggleLike('song', song.id, async () => {
        let newIsLikedState = intendedNewIsLiked;
        let newIsDislikedState = currentStoreInteractionState.isDisliked;

        if (newIsLikedState) { // Liking
          await supabase.from('likes').insert({ resource_id: song.id, resource_type: 'song', user_id: user.id });
          if (currentStoreInteractionState.isDisliked) { // If it was disliked, undislike it
            await supabase.from('dislikes').delete().match({ resource_id: song.id, resource_type: 'song', user_id: user.id });
            newIsDislikedState = false;
          }
        } else { // Unliking
          await supabase.from('likes').delete().match({ resource_id: song.id, resource_type: 'song', user_id: user.id });
        }

        const { data: likeData } = await supabase.rpc('get_like_count', { resource_id_param: song.id, resource_type_param: 'song' });
        const { data: dislikeData } = await supabase.rpc('get_dislike_count', { resource_id_param: song.id, resource_type_param: 'song' });
        
        return {
          newIsLiked: newIsLikedState,
          newLikeCount: typeof likeData === 'number' ? likeData : (currentStoreInteractionState.likeCount + (newIsLikedState ? 1 : -1)),
          newIsDisliked: newIsDislikedState,
          newDislikeCount: typeof dislikeData === 'number' ? dislikeData : currentStoreInteractionState.dislikeCount,
        };
      });
    } catch (error) {
      // Error handling is done within the apiCall or by the store's catch block
      // Toast for failure is good here if the store doesn't show one
      console.error("Error during handleLike process:", error);
      toast({ title: 'Erreur', description: `L'action 'J'aime' n'a pas pu être complétée.`, variant: 'destructive' });
    }
  }, [user, song, supabase, toast]);

  const handleDislike = useCallback(async () => {
    if (!user) {
      toast({ title: 'Connexion requise', description: 'Vous devez être connecté pour ne pas aimer un morceau.', variant: 'destructive' });
      return;
    }
    if (!song) return;

    const currentStoreInteractionState = getResourceInteractionStoreState().resourceStates[getResourceKey('song', song.id)] || DEFAULT_RESOURCE_STATE;
    const intendedNewIsDisliked = !currentStoreInteractionState.isDisliked;

    try {
      await getResourceInteractionStoreState().toggleDislike('song', song.id, async () => {
        let newIsDislikedState = intendedNewIsDisliked;
        let newIsLikedState = currentStoreInteractionState.isLiked;

        if (newIsDislikedState) { // Disliking
          await supabase.from('dislikes').insert({ resource_id: song.id, resource_type: 'song', user_id: user.id });
          if (currentStoreInteractionState.isLiked) { // If it was liked, unlike it
            await supabase.from('likes').delete().match({ resource_id: song.id, resource_type: 'song', user_id: user.id });
            newIsLikedState = false;
          }
        } else { // Un-disliking
          await supabase.from('dislikes').delete().match({ resource_id: song.id, resource_type: 'song', user_id: user.id });
        }

        const { data: dislikeData } = await supabase.rpc('get_dislike_count', { resource_id_param: song.id, resource_type_param: 'song' });
        const { data: likeData } = await supabase.rpc('get_like_count', { resource_id_param: song.id, resource_type_param: 'song' });

        return {
          newIsDisliked: newIsDislikedState,
          newDislikeCount: typeof dislikeData === 'number' ? dislikeData : (currentStoreInteractionState.dislikeCount + (newIsDislikedState ? 1 : -1)),
          newIsLiked: newIsLikedState,
          newLikeCount: typeof likeData === 'number' ? likeData : currentStoreInteractionState.likeCount,
        };
      });
    } catch (error) {
      console.error("Error during handleDislike process:", error);
      toast({ title: 'Erreur', description: `L'action 'Je n'aime pas' n'a pas pu être complétée.`, variant: 'destructive' });
    }
  }, [user, song, supabase, toast]);

  const fetchSongDataAndInteractions = useCallback(async () => {
    setIsLoading(true);
    // Use params.slug for initial status key as song.id might not be available yet
    console.warn('setResourceStatus is not available - usePageStatus hook needs to be created/restored'); 
    try {
      const fetchedSong = await getSongBySlugOrId(params.slug, supabase, user?.id);
      // console.log('DEBUG: Fetched song data for user', user?.id, ':', JSON.stringify(fetchedSong, null, 2)); // Keep this for debugging if needed

      if (!fetchedSong) {
        console.warn(`Song not found for slug/ID: ${params.slug}. Triggering notFound.`);
        setSong(null);
        console.warn('setResourceStatus is not available - usePageStatus hook needs to be created/restored');
        notFound(); // Call notFound to render the 404 page
        return;
      }

      // Check for private song access
      if (!fetchedSong.is_public && fetchedSong.creator_user_id !== user?.id) {
        console.warn('Access denied: User trying to access private song.');
        setSong(null);
        console.warn('setResourceStatus is not available - usePageStatus hook needs to be created/restored');
        notFound(); // Call notFound
        return;
      }

      setSong(fetchedSong);

      let userIsLiked = false;
      let userIsDisliked = false;
      if (user && fetchedSong.id) {
        const { data: likeData } = await supabase
          .from('likes')
          .select('id', { count: 'exact' })
          .eq('resource_id', fetchedSong.id)
          .eq('resource_type', 'song')
          .eq('user_id', user.id)
          .maybeSingle(); // Check if a like record exists
        userIsLiked = !!likeData;

        const { data: dislikeData } = await supabase
          .from('dislikes')
          .select('id', { count: 'exact' })
          .eq('resource_id', fetchedSong.id)
          .eq('resource_type', 'song')
          .eq('user_id', user.id)
          .maybeSingle(); // Check if a dislike record exists
        userIsDisliked = !!dislikeData;
      }

      storeSetResourceStatus('song', fetchedSong.id, { // Use storeSetResourceStatus from useResourceInteractionStore
        isLiked: userIsLiked,
        isDisliked: userIsDisliked,
        likeCount: fetchedSong.like_count ?? 0,
        dislikeCount: fetchedSong.dislike_count ?? 0,
      });
      console.warn('setResourceStatus is not available - usePageStatus hook needs to be created/restored'); // General status for the page
    } catch (error) {
      console.error("Error fetching song data and interactions:", error);
      setSong(null);
      console.warn('setResourceStatus is not available - usePageStatus hook needs to be created/restored');
      // Consider a toast message for the user
      // toast({ title: "Erreur", description: "Impossible de charger les données du morceau.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [params.slug, supabase, user?.id, setIsLoading, setSong, storeSetResourceStatus]); // Added notFound to deps

  // Ref to track fetching status to prevent multiple initiations for the same slug
  const fetchStatusRef = useRef<{ slug: string | null; status: 'idle' | 'pending' | 'completed' }>({ slug: null, status: 'idle' });

  useEffect(() => {
    if (!params.slug) {
      fetchStatusRef.current = { slug: null, status: 'idle' };
      setSong(null); 
      setIsLoading(false); // Ensure loading is false if no slug
      return;
    }

    // If the current slug is different from the one in ref, or if status is idle, initiate fetch.
    if (params.slug !== fetchStatusRef.current.slug || fetchStatusRef.current.status === 'idle') {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`EFFECT: Slug changed or fetch idle. Initiating fetch for ${params.slug}. Current ref:`, JSON.stringify(fetchStatusRef.current));
      }
      fetchStatusRef.current = { slug: params.slug, status: 'pending' };
      // setIsLoading(true); // fetchSongDataAndInteractions sets this
      fetchSongDataAndInteractions()
        .then(() => {
          // Ensure this update is for the current slug being processed
          if (fetchStatusRef.current.slug === params.slug) {
            fetchStatusRef.current.status = 'completed';
            if (process.env.NODE_ENV !== 'production') {
              console.log(`EFFECT: Fetch completed for ${params.slug}. Ref status:`, fetchStatusRef.current.status);
            }
          }
        })
        .catch((error) => {
          console.error(`EFFECT: Fetch failed for ${params.slug}:`, error);
          // If fetch fails, reset status to allow retry if effect runs again for same slug (e.g. user navigates away and back)
          if (fetchStatusRef.current.slug === params.slug) {
            fetchStatusRef.current.status = 'idle'; 
          }
        });
    } else if (fetchStatusRef.current.status === 'pending') {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`EFFECT: Fetch already pending for ${params.slug}.`);
      }
    } else if (fetchStatusRef.current.status === 'completed') {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`EFFECT: Fetch already completed for ${params.slug}.`);
      }
      // If fetch is completed, ensure isLoading is false, in case it got stuck.
      // This can happen if fetchSongDataAndInteractions's finally block didn't run or setIsLoading(false) was missed.
      if (isLoading && song?.slug === params.slug) {
         setIsLoading(false);
      }
    }
  }, [params.slug, fetchSongDataAndInteractions, isLoading, song?.slug]); // Added isLoading and song?.slug for more robust condition checks

  // useEffect for incrementing view count
  useEffect(() => {
    if (song && song.id && user && user.id && supabase) { // Ensure song.id is present
      const incrementView = async () => {
        try {
          const { error: rpcError } = await supabase.rpc('increment_song_view_count_if_not_viewed_today', {
            p_song_id: song.id, // Use song.id
            p_user_id: user.id,
          });
          if (rpcError) {
            console.error('Error incrementing song view count:', rpcError.message);
          } else {
            if (process.env.NODE_ENV !== 'production') {
              console.log('Song view count incremented or already tracked for today for song:', song.id);
            }
          }
        } catch (callError: any) {
          console.error('RPC call to increment_song_view_count_if_not_viewed_today failed:', callError.message);
        }
      };
      incrementView();
    }
  }, [song?.id, user?.id, supabase]); // Dependencies: song.id, user.id, supabase





  
  console.log('PublicSongPage - song data:', song);
  if (song && song.audio_url) {
    console.log('PublicSongPage - song.audio_url:', song.audio_url);
  } else if (song) {
    console.warn('PublicSongPage - song.audio_url is missing or null');
  }

  if (isLoading) {
    return (
      <div className="container mx-auto max-w-6xl py-8 px-4">
        <Skeleton className="h-[250px] md:h-[350px] w-full mb-8" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-6">
            <Skeleton className="h-10 w-3/4" />
            <Skeleton className="h-8 w-1/2" />
            <div className="flex gap-4">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-10" />
              <Skeleton className="h-10 w-10" />
              <Skeleton className="h-10 w-10" />
            </div>
            <Skeleton className="h-40 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!song || !songForPlayer) {
    notFound();
  }

  const canEdit = user && song.creator_user_id === user.id;

  const publicPrivateToggle = canEdit ? (
    <div className="flex items-center space-x-2">
      <Switch
        id={`public-toggle-${song.id}`}
        checked={!!song.is_public}
        onCheckedChange={handleTogglePublicStatus}
        disabled={isTogglingStatus}
        aria-label={song.is_public ? "Rendre privé" : "Rendre public"}
      />
      <Label htmlFor={`public-toggle-${song.id}`} className="text-sm font-medium text-foreground cursor-pointer">
        {song.is_public ? 'Public' : 'Privé'}
      </Label>
      {isTogglingStatus && <Loader2 className="h-4 w-4 animate-spin text-primary" />}
    </div>
  ) : null;

  return (
    <>
      <ResourceViewTracker resourceType="song" resourceId={song.id} />
      <ResourceHeaderBanner
        coverUrl={coverArtUrl}
        defaultIcon={<Music2 className="w-24 h-24 text-muted-foreground" />}
        title={song.title}
        artistName={artistDisplayName}
        artistLink={song.profiles?.username ? `/u/${song.profiles.username}` : undefined}
        genreBadges={preparedGenreBadges.length > 0 ? preparedGenreBadges : null}
        topRightSlot={publicPrivateToggle}
        musicalInfoLine={
          <>
            {song.key && (
              <Badge variant="outline" className="text-xs font-normal"><Music2 className="w-3 h-3 mr-1 opacity-70" />{song.key}</Badge>
            )}
            {song.bpm && (
              <Badge variant="outline" className="text-xs font-normal"><Layers3 className="w-3 h-3 mr-1 opacity-70" />{song.bpm} BPM</Badge>
            )}
            {song.time_signature && (
              <Badge variant="outline" className="text-xs font-normal"><BarChart3 className="w-3 h-3 mr-1 opacity-70" />{song.time_signature}</Badge>
            )}
            {typeof song.capo === 'number' && song.capo !== null && (
              <Badge variant="outline" className="text-xs font-normal"><Guitar className="w-3 h-3 mr-1 opacity-70" />Capo {song.capo}</Badge>
            )}
          </>
        }
        waveform={ // Waveform passed directly to the header
          song.audio_url && song ? (
            <WaveformPlayer song={song} height={60} />
          ) : null
        }
        actionButtons={ // Buttons for interaction
          <>
            <LikeButton 
              resourceId={song.id} 
              resourceType='song' 
              size="sm"
              className="bg-transparent hover:bg-green-600/20 border-0 text-green-500 hover:text-green-400"
            />
            <DislikeButton 
              resourceId={song.id} 
              resourceType='song' 
              size="sm"
              className="bg-transparent hover:bg-red-600/20 border-0 text-red-500 hover:text-red-400"
            />
            {/* Placeholder for AddToPlaylistButton - Assuming such a component exists or will be created */}
            {user && (
              <AddToPlaylistButton songId={song.id} />
            )}
            <SharePopover
              resourceType="song"
              resourceSlug={song.slug || song.id}
              resourceTitle={song.title}
              triggerButton={
                <Button variant="ghost" size="icon" className="h-8 w-8" title="Partager">
                  <Share2 className="h-4 w-4" />
                </Button>
              }
            />
            {canEdit && (
              <Link href={`/manage-songs/${song.id}/edit`} passHref>
                <Button variant="ghost" size="icon" className="h-8 w-8" aria-label="Modifier le morceau">
                  <Edit3 className="h-4 w-4" />
                </Button>
              </Link>
            )}
          </>
        }
        stats={ 
          <ResourceStatsDisplay
            resourceType="song"
            viewCount={song.view_count}
            playCount={song.plays}
            // likeCount and dislikeCount removed as they are shown with action buttons
          />
        }
      />
      {/* Waveform Player and Stats are now rendered within ResourceHeaderBanner */}

      {/* The main content area, starting below the banner */}
      <div className="container mx-auto max-w-6xl py-8 px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-x-8 gap-y-6">
          {/* Colonne Principale (2/3) */}
          <div className="lg:col-span-2 space-y-6">

            <Tabs defaultValue="lyrics-chords" className="mt-8 w-full" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-3 md:grid-cols-4 mb-4">
                <TabsTrigger value="lyrics-chords">Paroles & Accords</TabsTrigger>
                <TabsTrigger value="information">Informations</TabsTrigger>
                <TabsTrigger value="comments">Commentaires ({song.comments_count || 0})</TabsTrigger>
              </TabsList>

              {/* Tab Content: Lyrics & Chords */}
              <TabsContent value="lyrics-chords">
                <Card>
                  <CardHeader>
                    <CardTitle>Paroles & Accords</CardTitle>
                  </CardHeader>
                  <CardContent className="prose dark:prose-invert max-w-none">
                    {song.lyrics ? (
                      <div dangerouslySetInnerHTML={{ __html: song.lyrics.replace(/\n/g, '<br />') }} />
                    ) : (
                      <p>Aucunes paroles disponibles pour ce morceau.</p>
                    )}
                    {/* TODO: Add chord display here if data available */}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab Content: Information */}
              <TabsContent value="information">
                <SongInfoPanel song={song} showTopBadges={false} />
              </TabsContent>

              {/* Tab Content: Comments */}
              <TabsContent value="comments">
                <CommentSection 
                  resourceType="song" 
                  resourceId={song.id} 
                  resourceCreatorId={song.creator_user_id} 
                  areCommentsPublic={song.is_public} 
                />
              </TabsContent>
            </Tabs>

          </div>

          {/* Colonne Latérale (1/3) */}
          <div className="lg:col-span-1 space-y-6">
            {song.profiles && (
              <ArtistCardDisplay artist={song.profiles} />
            )}
            {song.albums && (
              <Card>
                <CardHeader><CardTitle>Album</CardTitle></CardHeader>
                <CardContent>
                  <Link href={`/albums/${song.albums.slug || song.albums.id}`} className="flex items-center gap-4 hover:bg-muted/50 p-2 rounded-md">
                    <Image src={song.albums.cover_url || '/images/placeholder-album.svg'} alt={song.albums.title} width={64} height={64} className="rounded-md aspect-square object-cover" />
                    <div>
                      <p className="font-semibold">{song.albums.title}</p>
                      <p className="text-sm text-muted-foreground">Voir l'album</p>
                    </div>
                  </Link>
                </CardContent>
              </Card>
            )}
            <SimilarSongs currentSongId={song.id} currentSongGenres={song.genres ? (Array.isArray(song.genres) ? song.genres : [song.genres]) : []} currentSongArtistId={song.creator_user_id} />
            {/* TODO: Add other related content like 'Other songs by artist' */}
          </div>
        </div>
      </div>
    </>
  );
}

