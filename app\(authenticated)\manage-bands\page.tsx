import Link from "next/link"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Plus, Users, Music, Calendar, Edit, Heart, ThumbsDown } from "lucide-react" // Added Heart, ThumbsDown
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { InvitationCardActions } from "@/components/bands/invitation-actions" 
import { BandCardActions } from "@/components/bands/band-card-actions";

export default async function BandsPage() {
  const supabase = createSupabaseServerClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Step 1: Get band_ids the user is a member of
  const { data: userBandMemberships, error: memberError } = await supabase
    .from("band_members")
    .select("band_id")
    .eq("user_id", session?.user.id);

  let myBands: Band[] = [];
  console.log("User session:", session); 

  if (memberError) {
    console.error("Error fetching user band memberships:", memberError);
  } else if (userBandMemberships && userBandMemberships.length > 0) {
    const bandIds = userBandMemberships.map(mb => mb.band_id);
    console.log("User is member of band IDs:", bandIds); 
    
    // Step 2: Fetch details for these bands
    const { data: bandsData, error: bandsError } = await supabase
      .from("bands")
      .select(`
        id,
        name,
        description,
        avatar_url,
        cover_url,
        created_at,
        genres,
        moods,
        instrumentation,
        location,
        creator_id,
        slug,
        is_public,
        dislike_count, 
        follower_count, 
        albums (count),
        songs (count),
        band_members ( user_id )
      `)
      .in("id", bandIds);

    if (bandsError) {
      console.error("Error fetching user bands details:", bandsError);
    } else if (bandsData) {
      console.log("Raw bandsData from DB:", JSON.stringify(bandsData, null, 2));

      // Step 3: Collect all unique member user_ids
      const memberUserIds = new Set<string>();
      bandsData.forEach(band => {
        band.band_members?.forEach((member: any) => {
          if (member.user_id) {
            memberUserIds.add(member.user_id);
          }
        });
      });

      // Step 4: Fetch profiles for these user_ids
      let profilesMap = new Map<string, { avatar_url: string | null }>();
      if (memberUserIds.size > 0) {
        const { data: profilesData, error: profilesError } = await supabase
          .from("profiles")
          .select("id, avatar_url")
          .in("id", Array.from(memberUserIds));

        if (profilesError) {
          console.error("Error fetching member profiles:", profilesError);
        } else if (profilesData) {
          profilesData.forEach(profile => {
            profilesMap.set(profile.id, { avatar_url: profile.avatar_url });
          });
        }
      }
      console.log("Fetched profilesMap:", profilesMap);

      // Step 5: Transform bandsData to include profile avatars
      const transformedBands = bandsData.map(band => {
        const processedBandMembers = band.band_members?.map((member: any) => ({
          user_id: member.user_id,
          profiles: profilesMap.get(member.user_id) || null 
        })) || [];
        
        return {
          ...band,
          band_members: processedBandMembers
        };
      });
      console.log("Transformed bands before filter:", JSON.stringify(transformedBands, null, 2));
      myBands = transformedBands.filter(isBand);
      console.log("Filtered myBands:", JSON.stringify(myBands, null, 2));
    } else {
      console.log("No bandsData returned for the given band IDs.");
    }
  } else {
    console.log("User is not a member of any bands or userBandMemberships is empty.");
  }

  const { data: invitations } = await supabase
    .from("band_invitations")
    .select(`
      id,
      bands ( id, name, avatar_url ),
      role,
      created_at
    `)
    .eq("user_id", session?.user.id)
    .eq("status", "pending")

  const { data: recommendedBandsData } = await supabase.from("bands").select("id, name, description, avatar_url, cover_url, slug, genres, location, is_public").limit(4);
  const recommendedBands = (recommendedBandsData || []).filter(isBand);


  interface Band {
    id: string;
    name: string;
    description?: string | null;
    avatar_url?: string | null;
    cover_url?: string | null;
    created_at?: string;
    genres?: string[] | null;
    moods?: string[] | null;
    instrumentation?: string[] | null;
    location?: string | null;
    creator_id?: string; 
    is_public?: boolean; 
    slug?: string | null; 
    albums?: Array<{ count: number }>; 
    songs?: Array<{ count: number }>; 
    band_members?: Array<{ user_id: string; profiles?: { avatar_url: string | null } | null }>; 
    dislike_count?: number; // Added
    follower_count?: number; // Added
  }

  function isBand(obj: any): obj is Band {
    return (
      obj &&
      typeof obj === 'object' &&
      'id' in obj && typeof obj.id === 'string' &&
      'name' in obj && typeof obj.name === 'string' 
    );
  }

  interface Invitation {
    id: string;
    bands: Pick<Band, 'id' | 'name' | 'avatar_url'> | null; 
    role: string;
    created_at: string;
  }
  
  function extractBand(bandsData: unknown): Pick<Band, 'id' | 'name' | 'avatar_url'> | null {
     // Check if bandsData is an object and has 'id' and 'name' properties
    if (typeof bandsData === 'object' && bandsData !== null && 'id' in bandsData && 'name' in bandsData) {
        const band = bandsData as { id: string; name: string; avatar_url?: string | null };
        return { id: band.id, name: band.name, avatar_url: band.avatar_url };
    }
    // Check if bandsData is an array and the first element is a valid band object
    if (Array.isArray(bandsData) && bandsData.length > 0 && 
        typeof bandsData[0] === 'object' && bandsData[0] !== null && 
        'id' in bandsData[0] && 'name' in bandsData[0]) {
        const band = bandsData[0] as { id: string; name: string; avatar_url?: string | null };
        return { id: band.id, name: band.name, avatar_url: band.avatar_url };
    }
    return null;
  }

  return (
    <div className="min-h-screen w-full bg-transparent">
      <div className="frosted p-6 rounded-xl w-full">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between w-full mb-6">
            <h1 className="text-3xl font-bold tracking-tight">Gérer les Groupes</h1>
            <Button
              asChild
              size="lg"
              className="bg-primary text-white rounded-xl font-semibold shadow-md hover:bg-primary/90 transition-colors w-full max-w-xs"
            >
              <Link href="/manage-bands/create">
                <Plus className="mr-2 h-5 w-5" />
                Créer un groupe
              </Link>
            </Button>
          </div>

          <Tabs defaultValue="my-bands" className="w-full">
            <TabsList>
              <TabsTrigger value="my-bands">Mes groupes</TabsTrigger>
              <TabsTrigger value="invitations">
                Invitations {invitations?.length ? `(${invitations.length})` : ""}
              </TabsTrigger>
              <TabsTrigger value="discover">Découvrir</TabsTrigger>
            </TabsList>

            <TabsContent value="my-bands" className="mt-6 w-full">
              {myBands.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 w-full">
                  {myBands.map((band) => {
                    const userIsCreator = band.creator_id === session?.user.id;
                    const bandLink = band.slug ? `/bands/${band.slug}` : `/manage-bands/${band.id}`;
                    return (
                      <Card key={band.id} className="overflow-hidden h-full flex flex-col justify-between hover:shadow-lg transition-shadow w-full group relative">
                        <Link href={bandLink} className="flex flex-col h-full">
                          <div className="h-32 overflow-hidden">
                            <img
                              src={band.cover_url || "/placeholder.svg?height=200&width=400&query=music band"}
                              alt={band.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <CardHeader className="pb-2">
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage src={band.avatar_url || "/placeholder.svg?height=40&width=40&query=band logo"} />
                                <AvatarFallback>{band.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <CardTitle className="flex items-center">
                                  {band.name}
                                  {band.is_public !== undefined && (
                                    <span className={`ml-2 w-3 h-3 rounded-full ${band.is_public ? 'bg-green-500' : 'bg-red-500'}`} title={band.is_public ? 'Public' : 'Privé'}></span>
                                  )}
                                </CardTitle>
                                <CardDescription>
                                  {Array.isArray(band.genres) && band.genres.length > 0 ? band.genres.join(', ') : 'Genres non spécifiés'}
                                </CardDescription>
                                <div className="text-xs text-muted-foreground mt-1">
                                  <span>{band.albums?.[0]?.count || 0} album(s)</span> • <span>{band.songs?.[0]?.count || 0} morceau(x)</span>
                                  {band.follower_count !== undefined && band.follower_count > 0 && (
                                    <> • <span className="flex items-center gap-0.5"><Users className="h-3 w-3" /> {band.follower_count}</span></>
                                  )}
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pb-2 flex-grow">
                            <p className="text-sm line-clamp-2 mb-2">{band.description || "Pas de description."}</p>
                            {/* Member Avatars */}
                            {band.band_members && band.band_members.length > 0 && (
                              <div className="flex items-center -space-x-2 mb-2">
                                {band.band_members.slice(0, 4).map(member => (
                                  member.profiles?.avatar_url && (
                                    <Avatar key={member.user_id} className="h-6 w-6 border-2 border-background">
                                      <AvatarImage src={member.profiles.avatar_url} alt="Membre" />
                                      <AvatarFallback delayMs={600}>?</AvatarFallback>
                                    </Avatar>
                                  )
                                ))}
                                {band.band_members.length > 4 && (
                                  <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs text-muted-foreground border-2 border-background">
                                    +{band.band_members.length - 4}
                                  </div>
                                )}
                              </div>
                            )}
                          </CardContent>
                          <CardFooter className="pt-2 flex justify-between items-center text-xs text-muted-foreground">
                             {band.location && <Badge variant="outline" className="text-xs">{band.location}</Badge>}
                             <div className="flex items-center gap-2">
                               {/* like_count is not fetched in this list view, only dislike_count and follower_count from the table */}
                               {band.dislike_count !== undefined && band.dislike_count > 0 && (
                                 <span className="flex items-center gap-0.5"><ThumbsDown className="h-3 w-3" /> {band.dislike_count}</span>
                               )}
                             </div>
                          </CardFooter>
                        </Link>
                        <div className="absolute top-2 right-2">
                          {userIsCreator && (
                            <BandCardActions bandId={band.id} bandName={band.name} initialIsPublic={band.is_public} />
                          )}
                        </div>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Vous n'avez pas encore de groupe</h3>
                  <p className="mt-2 text-muted-foreground">
                    Créez un groupe ou rejoignez-en un pour commencer à collaborer
                  </p>
                  <Button className="mt-4" asChild>
                    <Link href="/manage-bands/create">
                      <Plus className="mr-2 h-4 w-4" />
                      Créer un groupe
                    </Link>
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="invitations" className="mt-6 w-full">
              {invitations?.length ? (
                <div className="space-y-4 w-full">
                  {invitations.map((invitation) => {
                    const band = extractBand(invitation.bands);
                    return (
                      <Card key={invitation.id} className="w-full">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage
                                  src={band?.avatar_url || "/placeholder.svg?height=40&width=40&query=band logo"}
                                />
                                <AvatarFallback>{band?.name ? band.name.charAt(0) : '?'}</AvatarFallback>
                              </Avatar>
                              <div>
                                <CardTitle>{band?.name || 'Groupe inconnu'}</CardTitle>
                                <CardDescription>Vous êtes invité en tant que {invitation.role}</CardDescription>
                              </div>
                            </div>
                            <Badge variant="outline" className="bg-primary/10">
                              {new Date(invitation.created_at).toLocaleDateString()}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardFooter>
                          <InvitationCardActions
                            invitationId={invitation.id}
                            bandId={band?.id || ""}
                            userId={session?.user.id || ""}
                            offeredRole={invitation.role}
                          />
                        </CardFooter>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Aucune invitation en attente</h3>
                  <p className="mt-2 text-muted-foreground">
                    Vous n'avez pas d'invitation à rejoindre un groupe pour le moment
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="discover" className="mt-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {recommendedBands.map((band) => ( 
                  <Card key={band.id} className="overflow-hidden hover:shadow-md transition-shadow">
                    <Link href={band.slug ? `/bands/${band.slug}` : `#`}>
                      <div className="h-32 overflow-hidden">
                        <img
                        src={band.cover_url || "/placeholder.svg?height=200&width=400&query=music band"}
                        alt={band.name}
                        className="w-full h-full object-cover"
                      />
                      </div>
                    </Link>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={band.avatar_url || "/placeholder.svg?height=40&width=40&query=band logo"} />
                          <AvatarFallback>{band.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle>{band.name}</CardTitle>
                          <CardDescription>
                            {Array.isArray(band.genres) && band.genres.length > 0 ? band.genres.join(', ') : 'N/A'} • {band.location || 'N/A'}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm line-clamp-2">{band.description || "Pas de description."}</p>
                    </CardContent>
                    <CardFooter>
                      <Button size="sm" asChild>
                        <Link href={band.slug ? `/bands/${band.slug}` : `#`}>
                           Voir le profil
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
