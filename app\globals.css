@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* --- PLAYER OVERRIDES --- */
  .player-bg {
    background: #000 !important;
    background-color: #000 !important;
    box-shadow: 0 0 32px 0 #000d;
    border-top: 1px solid rgba(255,255,255,0.10);
    z-index: 99;
  }
  .player-slider {
    background: #181818 !important;
    border-radius: 9999px;
    height: 1.2rem !important;
  }
  .player-slider .slider-thumb {
    background: #00fff0 !important;
    border: 3px solid #fff !important;
    box-shadow: 0 0 12px 2px #00fff088 !important;
    width: 1.6rem !important;
    height: 1.6rem !important;
    transition: transform 0.18s;
  }
  .player-slider .slider-thumb:hover {
    transform: scale(1.18);
  }
  .player-slider .slider-track {
    background: #222 !important;
  }
  .text-balance {
    text-wrap: balance;
  }
  /* Classe utilitaire pour effet frosted glass */
  .frosted {
    background: rgba(255, 255, 255, 0.18); /* Ajuste selon la palette extraite */
    border-radius: 1rem;
    border: 1px solid rgba(255,255,255,0.25);
    box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.10);
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    /* Optionnel : transition douce */
    transition: background 0.3s, box-shadow 0.3s;
  }
  /* --- MOUVIK DESIGN UTILITIES --- */

  .gradient-text {
    background: linear-gradient(90deg, var(--primary-teal), #4FFFB0);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .teal-glow {
    text-shadow: var(--glow-effect);
  }
  .teal-glow-box {
    box-shadow: var(--glow-effect);
  }
  .bg-glass {
    background: rgba(0, 18, 24, 0.7);
    border: 1px solid rgba(0, 206, 209, 0.2);
    backdrop-filter: blur(8px);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 180 100% 30%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 180 100% 30%;
    --radius: 0.5rem;

    --sidebar-background: 0 0% 85%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Variables CSS personnalisées ajoutées */
    --primary-teal: #00CED1;
    --dark-teal: #004a4b;
    --darker-teal: #002a2b;
    --darkest-bg: #001214;
    --glow-effect: 0 0 8px rgba(0, 206, 209, 0.5);
    --sidebar-width: 16rem; /* valeur par défaut pour la sidebar étendue */
    --sidebar-width-icon: 3rem; /* valeur pour la sidebar réduite (icônes) */
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 180 100% 40%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 180 100% 40%;

    --sidebar-background: 240 5.9% 6%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    position: relative;
    min-height: 100vh;
    @apply bg-background text-foreground;
  }
  body::before {
    content: '';
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    z-index: -1;
    background: url('/bg1.png') center center/cover no-repeat;
    filter: blur(16px) brightness(1.08) saturate(1.1);
    opacity: 0.93;
    pointer-events: none;
  }
}

/* Classe utilitaire pour effet frosted glass */
.frosted {
  background: rgba(24, 28, 35, 0.35); /* Frost noir doux, + opacité */
  border-radius: 1rem;
  border: 1px solid rgba(255,255,255,0.13);
  box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.07);
  backdrop-filter: blur(16px) saturate(120%);
  -webkit-backdrop-filter: blur(16px) saturate(120%);
  transition: background 0.3s, box-shadow 0.3s;
}

.card, .Card, .bg-background, .bg-card, .bg-secondary, .bg-muted, .bg-accent, .bg-destructive { /* Removed .bg-primary, .bg-background, and .bg-popover from this rule */
  background: rgba(24, 28, 35, 0.38) !important; /* Noir frosté subtil - retained for these elements */
  box-shadow: none !important;
  border-color: rgba(255,255,255,0.09) !important;
}

/* Custom style for general popovers to be less transparent */
.bg-popover {
  background: rgba(30, 35, 45, 0.85) !important; /* Darker, more opaque base, similar to ai-config-popover */
  color: hsl(var(--popover-foreground)) !important; /* Ensure text contrast */
  box-shadow: 0 6px 24px rgba(0,0,0,0.25) !important; /* Consistent shadow with ai-config */
  border: 1px solid rgba(255, 255, 255, 0.15) !important; /* Consistent border with ai-config */
  backdrop-filter: blur(12px) saturate(100%); /* Apply backdrop filter for consistency */
  -webkit-backdrop-filter: blur(12px) saturate(100%);
}

.frosted *[class*="bg-"],
.card *[class*="bg-"],
.Card *[class*="bg-"],
.bg-background *[class*="bg-"],
.bg-card *[class*="bg-"],
.bg-popover *[class*="bg-"],
.bg-secondary *[class*="bg-"],
.bg-muted *[class*="bg-"],
.bg-accent *[class*="bg-"],
.bg-primary *[class*="bg-"],
.bg-destructive *[class*="bg-"] {
  background: rgba(24, 28, 35, 0.22) !important; /* Un peu de noir, mais translucide */
}

/* React Quill Customization */
@layer components {
  /* Main wrapper for Quill (styled via className prop in the component) */
  /* This will already get the frosted background due to global .bg-white overrides */

  /* Toolbar */
  .ql-toolbar.ql-snow {
    border-bottom-width: 0;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    padding: 0.5rem;
    border-color: rgba(255,255,255,0.09) !important; 
  }

  /* Toolbar buttons and icons */
  .ql-snow .ql-formats button {
    color: hsl(var(--foreground));
    border-radius: 0.125rem; /* rounded-sm */
    transition: background-color 0.2s;
    width: 28px;
    height: 28px;
    padding: 4px;
  }
  .ql-snow .ql-formats button svg {
    width: 100%;
    height: 100%;
  }

  .ql-snow .ql-stroke,
  .ql-snow .ql-picker-label .ql-stroke {
    stroke: hsl(var(--foreground));
  }
  .ql-snow .ql-fill,
  .ql-snow .ql-picker-label .ql-fill {
    fill: hsl(var(--foreground));
  }
  
  .ql-snow button.ql-active,
  .ql-snow .ql-picker-label.ql-active,
  .ql-snow .ql-picker-item.ql-selected {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
  .ql-snow button.ql-active .ql-stroke,
  .ql-snow .ql-picker-label.ql-active .ql-stroke,
  .ql-snow .ql-picker-item.ql-selected .ql-stroke {
    stroke: hsl(var(--primary-foreground));
  }
   .ql-snow button.ql-active .ql-fill,
  .ql-snow .ql-picker-label.ql-active .ql-fill,
  .ql-snow .ql-picker-item.ql-selected .ql-fill {
    fill: hsl(var(--primary-foreground));
  }

  /* Dropdowns in Toolbar (Font, Size, Header etc.) */
  .ql-snow .ql-picker-label {
    color: hsl(var(--foreground));
    border-radius: 0.125rem; /* rounded-sm */
    padding-left: 0.5rem; /* pl-2 */
    padding-right: 0.25rem; /* pr-1 */
  }
  .ql-snow .ql-picker-options {
    border-radius: 0.375rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    border: none;
    padding: 0.25rem;
    background-color: hsl(var(--popover)) !important; 
    border-color: rgba(255,255,255,0.09) !important; 
  }
  .ql-snow .ql-picker-item {
    color: hsl(var(--foreground));
    border-radius: 0.125rem; /* rounded-sm */
    padding: 0.375rem; /* p-1.5 */
  }
  
  /* Editor Container (holds the .ql-editor) */
  .ql-container.ql-snow {
    border-top-width: 0;
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-color: rgba(255,255,255,0.09) !important;
  }

  /* Editor Area (where text is typed) */
  .ql-editor {
    color: hsl(var(--foreground));
    min-height: 180px; /* min-h-[180px] */
    padding: 1rem; /* p-4 */
    caret-color: hsl(var(--primary));
    line-height: 1.6;
  }
  .ql-editor.ql-blank::before {
    color: hsl(var(--muted-foreground));
    font-style: normal !important;
    left: 1rem;
    top: 1rem;
  }
  .ql-editor p, .ql-editor ol, .ql-editor ul, .ql-editor pre, .ql-editor blockquote, .ql-editor h1, .ql-editor h2, .ql-editor h3 {
    margin-bottom: 0.75rem;
  }

  /* Link Tooltip */
  .ql-snow .ql-tooltip {
    border-radius: 0.375rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    padding: 0.5rem;
    border: none;
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground));
    backdrop-filter: blur(12px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.2) !important;
  }
  .ql-snow .ql-tooltip input[type="text"] {
    background-color: hsl(var(--input));
    color: hsl(var(--foreground));
    border-radius: 0.125rem;
    border-width: 1px;
    padding: 0.375rem;
    width: 100%;
  }
  .ql-snow .ql-tooltip a.ql-preview {
    color: hsl(var(--primary));
    text-decoration: underline;
  }
  .ql-snow .ql-tooltip .ql-action, .ql-snow .ql-tooltip .ql-remove {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s;
    height: 2.25rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    margin-left: 0.5rem;
  }
  .ql-snow .ql-tooltip .ql-action {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
  .ql-snow .ql-tooltip .ql-remove {
    background-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
  }
}

/* Custom style for AI Config Popover Content */
.ai-config-popover-content {
  background-color: rgba(30, 35, 45, 0.85) !important; /* Darker, more opaque base */
  /* Ensure text color has good contrast */
  color: hsl(var(--popover-foreground)) !important; 
  /* Slightly more defined border to help it stand out */
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  /* Keep existing backdrop filter for consistency */
  backdrop-filter: blur(12px) saturate(100%); /* Slightly less saturate/blur if needed to reduce transparency impact */
  -webkit-backdrop-filter: blur(12px) saturate(100%);
  box-shadow: 0 6px 24px rgba(0,0,0,0.25) !important; /* Slightly stronger shadow */
}

/* Ajoutez à la fin du fichier */
.main-layout-container {
  width: 100% !important;
  max-width: none !important;
}

/* Force la largeur complète pour les pages d'édition */
.main-layout-container main {
  width: 100% !important;
  max-width: none !important;
}
