"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useF<PERSON>, SubmitH<PERSON>ler, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { createBrowserClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/use-toast';
import { Loader2, Save, ArrowLeft, ListMusic, ImageUp, Palette, Music2, Guitar, Eye, Trash2, GripVertical, Text, AlignLeft, Info } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { ImageUploader } from "@/components/ui/image-uploader";
import { MultiSelect } from "@/components/ui/multi-select";
import { genreOptions, moodOptions, instrumentationOptions } from '@/lib/constants/song-options';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus } from 'lucide-react';

// Corrected schema for editing a playlist
const playlistEditFormSchema = z.object({
  name: z.string().min(1, "Le nom est requis.").max(100, "Le nom ne doit pas dépasser 100 caractères."),
  description: z.string().max(500, "La description ne doit pas dépasser 500 caractères.").optional().nullable(),
  is_public: z.boolean().default(true),
  cover_art_url: z.string().url("URL de pochette invalide.").optional().nullable(),
  banner_url: z.string().url("URL de bannière invalide.").optional().nullable(),
  genres: z.array(z.string()).optional(),
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  are_comments_public: z.boolean().default(false).optional(),
});

type PlaylistEditFormData = z.infer<typeof playlistEditFormSchema>;

// Matches the RPC return for playlist details
interface PlaylistData {
  id: string;
  name: string;
  description: string | null;
  is_public: boolean;
  user_id: string;
  slug?: string | null;
  cover_art_url?: string | null;
  banner_url?: string | null;
  genres?: string[] | null;
  moods?: string[] | null;
  instrumentation?: string[] | null;
  are_comments_public?: boolean;
}

// Matches the RPC return for songs in the playlist
interface PlaylistSongEditItem {
  id: string; // This is the playlist_songs.id
  song_id: string;
  title: string;
  artist_name: string | null;
  cover_art_url: string | null;
  duration_ms: number | null;
  position: number;
}

// Matches the RPC return for available songs
interface AvailableSong {
    id: string;
    title: string;
    artist_name: string | null;
    cover_art_url: string | null;
    duration_ms: number | null;
}

export default function EditPlaylistPage() {
  const supabase = createBrowserClient();
  const { user } = useUser();
  const router = useRouter();
  const params = useParams();
  const playlistId = params.id as string;

  const [playlist, setPlaylist] = useState<PlaylistData | null>(null);
  const [playlistSongs, setPlaylistSongs] = useState<PlaylistSongEditItem[]>([]);
  const [availableSongs, setAvailableSongs] = useState<AvailableSong[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAddSongModalOpen, setIsAddSongModalOpen] = useState(false);
  const [songsToAddInModal, setSongsToAddInModal] = useState<Set<string>>(new Set());

  const { control, register, handleSubmit, setValue, formState: { errors }, watch, reset } = useForm<PlaylistEditFormData>({
    resolver: zodResolver(playlistEditFormSchema),
  });

  const fetchPlaylistData = useCallback(async () => {
    if (!user || !playlistId) return;

    setIsLoading(true);
    const { data, error } = await supabase.rpc('get_data_for_playlist_edit_page', {
      p_playlist_id: playlistId,
      p_requesting_user_id: user.id,
    });

    console.log('[DEBUG] RPC data for playlist edit page:', data);

    if (error || !data) {
      console.error('Error fetching data for edit page:', error);
      toast({ title: "Erreur", description: `Impossible de charger les données de la playlist.`, variant: "destructive" });
      router.push('/playlists');
      return;
    }

    const { playlistDetails, playlistSongs: initialSongs, availableSongs: userSongs } = data;

    console.log('[DEBUG] Initial playlistSongs from RPC:', initialSongs);
    console.log('[DEBUG] Available songs from RPC (userSongs):', userSongs);

    setPlaylist(playlistDetails);
    setPlaylistSongs(initialSongs || []);
    setAvailableSongs(userSongs || []);
    
    reset({
      name: playlistDetails.name,
      description: playlistDetails.description,
      is_public: playlistDetails.is_public,
      cover_art_url: playlistDetails.cover_art_url || '',
      banner_url: playlistDetails.banner_url || '',
      genres: playlistDetails.genres || [],
      moods: playlistDetails.moods || [],
      instrumentation: playlistDetails.instrumentation || [],
      are_comments_public: playlistDetails.are_comments_public ?? false,
    });

    setIsLoading(false);
  }, [user, playlistId, supabase, router, reset]);

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    if (!playlistId) {
        toast({title: "Erreur", description: "ID de playlist manquant.", variant: "destructive"});
        router.push('/playlists');
        return;
    }
    fetchPlaylistData();
  }, [user, playlistId, router, fetchPlaylistData]);

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    const items = Array.from(playlistSongs);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedItems = items.map((item, index) => ({ ...item, position: index + 1 }));
    setPlaylistSongs(updatedItems);
  };

  const handleConfirmAddSongsFromModal = () => {
    const newSongDetails = availableSongs
      .filter(s => songsToAddInModal.has(s.id) && !playlistSongs.some(ps => ps.song_id === s.id))
      .map((s) => ({
        id: `new-${s.id}-${Date.now()}`,
        song_id: s.id,
        title: s.title,
        artist_name: s.artist_name || "Inconnu",
        cover_art_url: s.cover_art_url,
        duration_ms: s.duration_ms,
        position: 0, // Position will be recalculated
      }));

    const combined = [...playlistSongs, ...newSongDetails];
    const updatedItems = combined.map((item, index) => ({ ...item, position: index + 1 }));

    setPlaylistSongs(updatedItems);
    setIsAddSongModalOpen(false);
    setSongsToAddInModal(new Set());
  };

  const handleDeleteSongFromPlaylist = (songIdToRemove: string, songTitle: string) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir retirer le morceau \"${songTitle}\" de cette playlist ?`)) {
      return;
    }
    const updatedSongs = playlistSongs
      .filter(s => s.id !== songIdToRemove)
      .map((item, index) => ({ ...item, position: index + 1 })); // Re-calculate position

    setPlaylistSongs(updatedSongs);
    toast({ title: "Morceau retiré", description: `\"${songTitle}\" a été retiré de la playlist.` });
  };

  const onSubmit: SubmitHandler<PlaylistEditFormData> = async (formData) => {
    if (!playlist || !user) return;
    setIsSubmitting(true);

    try {
      // 1. Update playlist details
      const { error: playlistUpdateError } = await supabase
        .from('playlists')
        .update({
          name: formData.name,
          description: formData.description,
          is_public: formData.is_public,
          cover_art_url: formData.cover_art_url || null,
          banner_url: formData.banner_url || null,
          genres: formData.genres?.length ? formData.genres : null,
          moods: formData.moods?.length ? formData.moods : null,
          instrumentation: formData.instrumentation?.length ? formData.instrumentation : null,
          are_comments_public: formData.are_comments_public,
          updated_at: new Date().toISOString(),
        })
        .eq('id', playlist.id);

      if (playlistUpdateError) throw playlistUpdateError;

      // 2. Clear existing songs for this playlist
      const { error: deleteError } = await supabase.from('playlist_songs').delete().eq('playlist_id', playlist.id);
      if (deleteError) throw deleteError;

      // 3. Insert the updated list of songs
      if (playlistSongs.length > 0) {
        const songsToInsert = playlistSongs.map(song => ({
          playlist_id: playlist.id,
          song_id: song.song_id,
          position: song.position,
          user_id: user.id,
        }));
        const { error: insertError } = await supabase.from('playlist_songs').insert(songsToInsert);
        if (insertError) throw insertError;
      }

      toast({ title: "Succès", description: `Playlist \"${formData.name}\" mise à jour.` });
      // Rester sur la page d'édition au lieu de rediriger
      router.refresh(); // Refresh pour mettre à jour les données

    } catch (error: any) {
      console.error("Error updating playlist:", error);
      toast({ title: "Erreur de mise à jour", description: error.message, variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div className="container py-8 flex justify-center items-center min-h-[calc(100vh-200px)]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }
  if (!playlist) {
    return <div className="container py-8 text-center">Playlist non trouvée ou accès non autorisé.</div>;
  }

  return (
    <div className="container max-w-4xl py-8 mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold flex items-center">
          <ListMusic className="mr-3 h-8 w-8 text-primary" />
          Modifier la Playlist
        </h1>
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/playlists/${playlistId}`}><ArrowLeft className="mr-2 h-4 w-4" />Annuler et voir</Link>
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Metadata Section */}
        <Card className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-1 space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Info className="mr-2 h-5 w-5 text-primary" />
                  Informations générales
                </h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="flex items-center"><Text className="mr-2 h-4 w-4 text-muted-foreground" />Nom</Label>
                    <Input id="name" {...register('name')} className="focus:ring-2 focus:ring-primary/20" />
                    {errors.name && <p className="text-sm text-destructive mt-1">{errors.name.message}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="flex items-center"><AlignLeft className="mr-2 h-4 w-4 text-muted-foreground" />Description</Label>
                    <Textarea id="description" {...register('description')} className="min-h-[120px] focus:ring-2 focus:ring-primary/20" />
                    {errors.description && <p className="text-sm text-destructive mt-1">{errors.description.message}</p>}
                  </div>

                  <div className="flex items-center space-x-2 pt-2">
                    <Controller
                      name="is_public"
                      control={control}
                      render={({ field }) => (
                        <Switch
                          id="is_public"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          aria-label="Rendre la playlist publique"
                        />
                      )}
                    />
                    <Label htmlFor="is_public">Playlist publique ?</Label>
                  </div>
                  {errors.is_public && <p className="text-sm text-destructive mt-1">{errors.is_public.message}</p>}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="cover_art_url" className="flex items-center"><ImageUp className="mr-2 h-4 w-4 text-muted-foreground" />Pochette</Label>
              <ImageUploader 
                onImageUploaded={(url) => setValue('cover_art_url', url, { shouldValidate: true })} 
                existingImageUrl={watch('cover_art_url') ?? undefined}
                bucketName="playlist-covers" 
                maxWidth={1200} maxHeight={1200} 
              />
              <p className="text-xs text-muted-foreground mt-1">Max 1200x1200px, 5MB. Ratio libre.</p>
              {errors.cover_art_url && <p className="text-sm text-destructive mt-1">{errors.cover_art_url.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="banner_url" className="flex items-center"><ImageUp className="mr-2 h-4 w-4 text-muted-foreground" />Bannière</Label>
              <ImageUploader 
                onImageUploaded={(url) => setValue('banner_url', url, { shouldValidate: true })} 
                existingImageUrl={watch('banner_url') ?? undefined}
                bucketName="playlist-banners" 
                maxWidth={1600} maxHeight={900}
              />
              <p className="text-xs text-muted-foreground mt-1">Max 1600x900px, 5MB. Ratio libre.</p>
              {errors.banner_url && <p className="text-sm text-destructive mt-1">{errors.banner_url.message}</p>}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="space-y-2">
              <Label htmlFor="genres" className="flex items-center"><Music2 className="mr-2 h-4 w-4 text-muted-foreground" />Genres</Label>
              <MultiSelect options={genreOptions} selected={watch('genres') || []} onChange={(selected) => setValue('genres', selected)} placeholder="Sélectionnez des genres" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="moods" className="flex items-center"><Palette className="mr-2 h-4 w-4 text-muted-foreground" />Ambiances</Label>
              <MultiSelect options={moodOptions} selected={watch('moods') || []} onChange={(selected) => setValue('moods', selected)} placeholder="Sélectionnez des ambiances" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="instrumentation" className="flex items-center"><Guitar className="mr-2 h-4 w-4 text-muted-foreground" />Instrumentation</Label>
              <MultiSelect options={instrumentationOptions} selected={watch('instrumentation') || []} onChange={(selected) => setValue('instrumentation', selected)} placeholder="Sélectionnez des instruments" />
            </div>
          </div>

          <div className="flex items-center space-x-2 pt-6">
            <Controller
              name="are_comments_public"
              control={control}
              render={({ field }) => (
                <Switch
                  id="are_comments_public"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  aria-label="Rendre les commentaires publics"
                />
              )}
            />
            <Label htmlFor="are_comments_public">Rendre les commentaires publics ?</Label>
          </div>
          {errors.are_comments_public && <p className="text-sm text-destructive mt-1">{errors.are_comments_public.message}</p>}
        </Card>
        {/* Section pour gérer les morceaux dans la playlist */}
        <Card className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Music2 className="mr-3 h-6 w-6 text-primary" />
              <h2 className="text-2xl font-semibold">Morceaux dans la playlist</h2>
            </div>
            <Dialog open={isAddSongModalOpen} onOpenChange={setIsAddSongModalOpen}>
              <DialogTrigger asChild>
                <Button><Plus className="mr-2 h-4 w-4" />Ajouter des morceaux</Button>
              </DialogTrigger>
                <DialogContent className="sm:max-w-[625px]">
                  <DialogHeader><DialogTitle>Ajouter des morceaux</DialogTitle><DialogDescription>Sélectionnez des morceaux de votre bibliothèque à ajouter à cette playlist.</DialogDescription></DialogHeader>
                  <div className="max-h-[60vh] overflow-y-auto p-1 space-y-1">
                    {(() => {
                      const songsToFilter = availableSongs || [];
                      const currentPlaylistSongIds = new Set((playlistSongs || []).map(ps => ps.song_id));
                      const songsToShowInModal = songsToFilter.filter(s => !currentPlaylistSongIds.has(s.id));
                      
                      console.log('[DEBUG] Modal - availableSongs (state):', availableSongs);
                      console.log('[DEBUG] Modal - playlistSongs (state):', playlistSongs);
                      console.log('[DEBUG] Modal - songsToShowInModal (filtered):', songsToShowInModal);
                      
                      if (songsToShowInModal.length > 0) {
                        return songsToShowInModal.map(song => (
                        <div key={song.id} className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-md border">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id={`song-modal-${song.id}`}
                              checked={songsToAddInModal.has(song.id)}
                              onCheckedChange={(checked) => {
                                setSongsToAddInModal(prev => {
                                  const newSet = new Set(prev);
                                  if (checked) newSet.add(song.id);
                                  else newSet.delete(song.id);
                                  return newSet;
                                });
                              }}
                            />
                            {song.cover_art_url && <Image src={song.cover_art_url} alt={song.title} width={40} height={40} className="rounded-md object-cover" />}
                            {!song.cover_art_url && <div className="w-10 h-10 rounded-md bg-muted flex items-center justify-center"><Music2 className="w-5 h-5 text-muted-foreground"/></div>}
                            <div>
                              <Label htmlFor={`song-modal-${song.id}`} className="font-medium cursor-pointer">{song.title}</Label>
                              <p className="text-xs text-muted-foreground">{song.artist_name || "Artiste inconnu"}</p>
                            </div>
                          </div>
                        </div>
                      ))
                      } else {
                        return <p className="text-sm text-muted-foreground text-center py-4">Tous vos morceaux publiés sont déjà dans cette playlist ou vous n'avez pas d'autres morceaux publiés.</p>;
                      }
                    })() /* Closing IIFE */
                    }
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => {setIsAddSongModalOpen(false); setSongsToAddInModal(new Set());}}>Annuler</Button>
                    <Button type="button" onClick={handleConfirmAddSongsFromModal} disabled={songsToAddInModal.size === 0}>Ajouter ({songsToAddInModal.size})</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            {playlistSongs.length > 0 ? (
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="playlistSongs">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                      {playlistSongs.map((song, index) => (
                        <Draggable key={song.id} draggableId={song.id} index={index}>
                          {(providedDraggable) => (
                            <Card
                              ref={providedDraggable.innerRef}
                              {...providedDraggable.draggableProps}
                              {...providedDraggable.dragHandleProps}
                              className="flex items-center p-3 gap-3 bg-card hover:bg-muted/80 transition-shadow"
                            >
                              <span className="text-sm text-muted-foreground w-6 text-center cursor-grab">{index + 1}</span>
                              {song.cover_art_url ? (
                                <Image src={song.cover_art_url} alt={song.title} width={40} height={40} className="rounded-md aspect-square object-cover" />
                              ) : (
                                <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                                  <Music2 className="h-5 w-5 text-muted-foreground" />
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate" title={song.title}>{song.title}</p>
                                <p className="text-xs text-muted-foreground truncate" title={song.artist_name || undefined}>{song.artist_name || "Artiste inconnu"}</p>
                              </div>
                              <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  onClick={() => handleDeleteSongFromPlaylist(song.id, song.title)} 
                                  title="Supprimer de la playlist"
                                  className="h-8 w-8"
                              >
                                  <Trash2 className="h-4 w-4 text-destructive"/>
                              </Button>
                            </Card>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">Cette playlist est vide. Ajoutez des morceaux depuis leurs pages respectives.</p>
            )}
        </Card>


        <div className="flex justify-end gap-4 pt-8 items-center"> {/* Increased top padding */}
          {playlist && (
            <Link 
              href={playlist.is_public && playlist.slug ? `/playlists/${playlist.slug}` : `/playlists/${playlist.id}`} 
              passHref 
              target="_blank" 
              rel="noopener noreferrer"
            >
              <Button type="button" variant="outline" disabled={isSubmitting}>
                <Eye className="mr-2 h-4 w-4" />
                Prévisualiser
              </Button>
            </Link>
          )}
          <Button type="button" variant="outline" onClick={() => router.push(`/playlists/${playlistId}`)} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sauvegarder les modifications
          </Button>
        </div>
      </form>
    </div>
  );
}
