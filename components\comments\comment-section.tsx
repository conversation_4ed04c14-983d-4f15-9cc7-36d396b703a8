"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { toast } from '@/hooks/use-toast';
import { Loader2, MessageSquare, Smile, Bold, Italic, Link2 as LinkIcon, MoreVertical, Eye, EyeOff, Trash2, Edit2, CornerDownRight, MessageCircle, List, ListOrdered, Quote } from 'lucide-react';
import Link from 'next/link';
import EmojiPicker, { EmojiClickData, Theme as EmojiTheme } from 'emoji-picker-react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import RichTextEditor from './rich-text-editor'; // Import the new editor
import { getCommentsPerPageSetting } from '@/lib/actions/comment.actions'; // Added missing import

interface CommentProfile {
  username: string | null;
  display_name: string | null;
  avatar_url: string | null;
}

interface Comment {
  id: string;
  content: string;
  created_at: string;
  user_id: string;
  parent_id: string | null;
  profiles: CommentProfile[] | null; // Supabase peut retourner un tableau pour une relation
  status?: string; 
  replies?: Comment[]; 
}

interface CommentSectionProps {
  resourceId: string;
  resourceType: 'album' | 'song' | 'playlist' | 'artist' | 'band'; 
  resourceCreatorId?: string | null; 
  isModeratorView?: boolean;
  areCommentsPublic?: boolean; // New prop for comment visibility
  isCurrentUserMember?: boolean; // New prop, relevant for private band comments
}

export function CommentSection({ 
  resourceId, 
  resourceType, 
  resourceCreatorId, 
  isModeratorView = false,
  areCommentsPublic = true, // Default to public if not provided, though pages should fetch and pass this
  isCurrentUserMember = false 
}: CommentSectionProps) {
  const supabase = getSupabaseClient();
  const { user } = useUser();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoadingComments, setIsLoadingComments] = useState(true);
  const [isPostingComment, setIsPostingComment] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showReplyEmojiPicker, setShowReplyEmojiPicker] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null); 
  const [replyContent, setReplyContent] = useState('');
  const newCommentTextareaRef = useRef<HTMLTextAreaElement>(null);
  const replyTextareaRef = useRef<HTMLTextAreaElement>(null);
  
  const [currentPage, setCurrentPage] = useState(1);
  // const COMMENTS_PER_PAGE = 10; // Will be replaced by dynamic state
  const [commentsPerPage, setCommentsPerPage] = useState(10); // Default, will be updated
  const [isLoadingLimit, setIsLoadingLimit] = useState(true);

  useEffect(() => {
    const fetchLimit = async () => {
      setIsLoadingLimit(true);
      const limit = await getCommentsPerPageSetting();
      setCommentsPerPage(limit);
      setIsLoadingLimit(false);
    };
    fetchLimit();
  }, []);

  const countVisibleComments = (commentsToCount: Comment[]): number => {
    let count = 0;
    for (const comment of commentsToCount) {
      if (comment.status === 'visible' || (isModeratorView && comment.status !== 'deleted_by_moderator')) {
        count++;
        if (comment.replies && comment.replies.length > 0) {
          count += countVisibleComments(comment.replies);
        }
      }
    }
    return count;
  };

  const onEmojiClick = (emojiData: EmojiClickData, event?: MouseEvent, isReply: boolean = false) => {
    const textareaRef = isReply ? replyTextareaRef : newCommentTextareaRef;
    const setText = isReply ? setReplyContent : setNewComment;
    
    if (textareaRef.current) {
      const { selectionStart, selectionEnd, value } = textareaRef.current;
      const newText = value.substring(0, selectionStart) + emojiData.emoji + value.substring(selectionEnd);
      setText(newText);
      setTimeout(() => { 
        textareaRef.current?.focus();
        textareaRef.current?.setSelectionRange(selectionStart + emojiData.emoji.length, selectionStart + emojiData.emoji.length);
      }, 0);
    } else { 
      setText(prev => prev + emojiData.emoji);
    }
    if (isReply) setShowReplyEmojiPicker(null); else setShowEmojiPicker(false);
  };
  
  // The applyMarkdown function is no longer needed as Tiptap handles formatting.
  // It can be removed or kept if there's a fallback mechanism planned.
  // For now, I will comment it out.
  /*
  const applyMarkdown = (syntax: 'bold' | 'italic' | 'link' | 'list-ul' | 'list-ol' | 'quote', isReply: boolean = false) => {
    const textareaRef = isReply ? replyTextareaRef : newCommentTextareaRef;
    const setText = isReply ? setReplyContent : setNewComment;
    
    if (!textareaRef.current) return;
    const { selectionStart, selectionEnd, value } = textareaRef.current;
    const selectedText = value.substring(selectionStart, selectionEnd);

    let textToInsert = '';
    let finalCursorPos = selectionStart;

    if (syntax === 'bold') {
      textToInsert = `**${selectedText || ''}**`;
      finalCursorPos = selectionStart + 2 + (selectedText ? selectedText.length : 0);
    } else if (syntax === 'italic') {
      textToInsert = `_${selectedText || ''}_`;
      finalCursorPos = selectionStart + 1 + (selectedText ? selectedText.length : 0);
    } else if (syntax === 'link') {
      const url = prompt("Entrez l'URL du lien :", "https://");
      if (!url) return; 
      textToInsert = `[${selectedText || 'texte du lien'}](${url})`;
      finalCursorPos = selectionStart + textToInsert.length;
    } else if (syntax === 'list-ul') {
        const lines = selectedText.split('\n').map(line => line ? `- ${line}` : '').join('\n');
        textToInsert = lines || '- ';
        finalCursorPos = selectionStart + textToInsert.length;
    } else if (syntax === 'list-ol') {
        const lines = selectedText.split('\n').map((line, i) => line ? `${i + 1}. ${line}` : '').join('\n');
        textToInsert = lines || '1. ';
        finalCursorPos = selectionStart + textToInsert.length;
    } else if (syntax === 'quote') {
        const lines = selectedText.split('\n').map(line => line ? `> ${line}` : '').join('\n');
        textToInsert = lines || '> ';
        finalCursorPos = selectionStart + textToInsert.length;
    }
    
    const newValue = value.substring(0, selectionStart) + textToInsert + value.substring(selectionEnd);
    setText(newValue);
    
    setTimeout(() => {
      textareaRef.current?.focus();
      textareaRef.current?.setSelectionRange(finalCursorPos, finalCursorPos);
    }, 0);
  };
  */

  const fetchCommentsWithReplies = useCallback(async () => {
    setIsLoadingComments(true);
    let query = supabase
      .from('comments')
      .select(`
        id, content, created_at, user_id, parent_id, status,
        profiles:user_id (username, display_name, avatar_url)
      `)
      .eq('resource_id', resourceId)
      .eq('resource_type', resourceType);

    if (!isModeratorView) {
      query = query.eq('status', 'visible');
    }
    query = query.order('created_at', { ascending: true });
    
    const { data: fetchedData, error: fetchError } = await query;

    if (fetchError) {
      console.error('Error fetching comments:', fetchError);
      toast({ title: "Erreur", description: "Impossible de charger les commentaires.", variant: "destructive" });
      setComments([]);
    } else {
      const commentsData = fetchedData || [];
      const commentsById: { [key: string]: Comment } = {};
      const rootComments: Comment[] = [];

      commentsData.forEach(comment => {
        commentsById[comment.id] = { ...comment, replies: [] } as Comment;
      });

      commentsData.forEach(comment => {
        if (comment.parent_id && commentsById[comment.parent_id]) {
          commentsById[comment.parent_id].replies?.push(commentsById[comment.id]);
        } else if (!comment.parent_id) {
          rootComments.push(commentsById[comment.id]);
        }
      });
      setComments(rootComments.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
    }
    setIsLoadingComments(false);
  }, [resourceId, resourceType, supabase, isModeratorView]);

  useEffect(() => {
    if (resourceId && resourceType) {
      fetchCommentsWithReplies();
    }
  }, [fetchCommentsWithReplies, resourceId, resourceType]);

  const handlePostComment = async (content: string, parentId: string | null = null) => {
    if (!user || !content.trim()) return;
    setIsPostingComment(true);

    const { error } = await supabase
      .from('comments')
      .insert({
        resource_id: resourceId,
        resource_type: resourceType,
        user_id: user.id,
        content: content.trim(),
        parent_id: parentId,
        status: 'visible',
      });

    if (error) {
      console.error('Error posting comment:', error);
      toast({ title: "Erreur", description: "Votre commentaire n'a pas pu être envoyé.", variant: "destructive" });
    } else {
      toast({ title: parentId ? "Réponse ajoutée !" : "Commentaire ajouté !" });
      if (parentId) {
        setReplyContent('');
        setReplyingTo(null);
      } else {
        setNewComment('');
      }
      fetchCommentsWithReplies(); 
    }
    setIsPostingComment(false);
  };

  const handleModerateComment = async (commentId: string, newStatus: string) => {
    if (!user) {
      toast({ title: "Non connecté", description: "Vous devez être connecté pour modérer.", variant: "destructive" });
      return;
    }

    const { data, error } = await supabase.rpc('moderate_comment_status', {
      p_comment_id: commentId,
      p_new_status: newStatus
    });

    if (error) {
      console.error("Error calling moderate_comment_status RPC:", error);
      toast({ title: "Erreur de modération", description: error.message, variant: "destructive" });
    } else if (typeof data === 'string' && data.startsWith('Error:')) {
      console.error("Error from moderate_comment_status RPC:", data);
      toast({ title: "Erreur de modération", description: data.substring(7), variant: "destructive" });
    }
     else {
      toast({ title: "Commentaire modéré !", description: data || undefined });
      fetchCommentsWithReplies(); 
    }
  };
  
  const renderComment = (comment: Comment, level: number = 0, index: number = 0): React.ReactNode => { // Added index parameter
    let profile: CommentProfile | null = null;
    if (comment.profiles) {
      if (Array.isArray(comment.profiles) && comment.profiles.length > 0) {
        profile = comment.profiles[0];
      } else if (!Array.isArray(comment.profiles)) {
        profile = comment.profiles as unknown as CommentProfile; 
      }
    }
    const displayName = profile?.display_name || profile?.username || 'Utilisateur Anonyme';
    const avatarUrl = profile?.avatar_url || undefined;
    const avatarFallback = displayName.charAt(0).toUpperCase() || 'U';
    const isHiddenByModerator = comment.status === 'hidden_by_moderator' && isModeratorView;
    const isDeletedByUser = comment.status === 'deleted_by_user';
    const isDeletedByModerator = comment.status === 'deleted_by_moderator';

    const parseMarkdown = (text: string): React.ReactNode => {
      if (!text) return '';
      
      const lines = text.split('\n');
      const elements: React.ReactNode[] = [];
      const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
      const spotifyTrackRegex = /(?:https?:\/\/)?open\.spotify\.com\/track\/([a-zA-Z0-9]{22})/;
      // Regex for SoundCloud: https://developers.soundcloud.com/docs/api/html5-widget#integration
      // Simple version: extract URL from iframe src or direct link
      const soundcloudRegex = /(?:https?:\/\/)?(?:w\.|www\.|)(?:soundcloud\.com\/player\/\?url=https\%3A\/\/api\.soundcloud\.com\/tracks\/(\d+).*|soundcloud\.com\/[\w\-]+\/[\w\-]+)/;
      // Regex for Vimeo: https://developer.vimeo.com/api/guides/oembed
      // Simple version: extract video ID from URL
      const vimeoRegex = /(?:https?:\/\/)?(?:www\.)?vimeo\.com\/(?:channels\/.+\/)?(\d+)/;


      lines.forEach((line, index) => {
        let processedLine = line;
        let embedAdded = false;

        const youtubeMatch = line.match(youtubeRegex);
        if (youtubeMatch && youtubeMatch[1]) {
          const videoId = youtubeMatch[1];
          elements.push(
            <div key={`yt-${index}`} className="my-2 aspect-video max-w-md mx-auto">
              <iframe width="100%" height="100%" src={`https://www.youtube.com/embed/${videoId}`} title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen className="rounded-md"></iframe>
            </div>
          );
          processedLine = processedLine.replace(youtubeMatch[0], '').trim();
          embedAdded = true;
        }

        const spotifyMatch = !embedAdded ? line.match(spotifyTrackRegex) : null;
        if (spotifyMatch && spotifyMatch[1]) {
          const trackId = spotifyMatch[1];
          elements.push(
            <div key={`sp-${index}`} className="my-2 max-w-md mx-auto">
              <iframe src={`https://open.spotify.com/embed/track/${trackId}`} width="100%" height="80" frameBorder="0" allowTransparency={true} allow="encrypted-media" className="rounded-md"></iframe>
            </div>
          );
          processedLine = processedLine.replace(spotifyMatch[0], '').trim();
          embedAdded = true;
        }

        const soundcloudMatch = !embedAdded ? line.match(soundcloudRegex) : null;
        if (soundcloudMatch) {
          // SoundCloud embed needs the full URL for the widget, or track ID for API-based embed
          // For simplicity, if it's a direct track URL, we can try to use the generic oEmbed-like player
          // If soundcloudMatch[1] is the track ID from the API URL:
          const soundcloudSrc = soundcloudMatch[1] 
            ? `https://w.soundcloud.com/player/?url=https%3A//api.soundcloud.com/tracks/${soundcloudMatch[1]}&color=%23ff5500&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true`
            : `https://w.soundcloud.com/player/?url=${encodeURIComponent(soundcloudMatch[0])}&color=%23ff5500&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true`;
          elements.push(
            <div key={`sc-${index}`} className="my-2 max-w-md mx-auto">
              <iframe width="100%" height="166" scrolling="no" frameBorder="no" allow="autoplay" src={soundcloudSrc} className="rounded-md"></iframe>
            </div>
          );
          processedLine = processedLine.replace(soundcloudMatch[0], '').trim();
          embedAdded = true;
        }

        const vimeoMatch = !embedAdded ? line.match(vimeoRegex) : null;
        if (vimeoMatch && vimeoMatch[1]) {
          const videoId = vimeoMatch[1];
          elements.push(
            <div key={`vm-${index}`} className="my-2 aspect-video max-w-md mx-auto">
              <iframe src={`https://player.vimeo.com/video/${videoId}`} width="100%" height="100%" frameBorder="0" allow="autoplay; fullscreen; picture-in-picture" allowFullScreen className="rounded-md"></iframe>
            </div>
          );
          processedLine = processedLine.replace(vimeoMatch[0], '').trim();
          embedAdded = true;
        }
        
        if (processedLine) {
          let html = processedLine;
          html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
          html = html.replace(/_(.*?)_/g, '<em>$1</em>');
          html = html.replace(/\[([^\]]+)\]\((https?:\/\/[^\s)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">$1</a>');
          // Add support for lists and blockquotes
          html = html.replace(/^\s*\*\s+(.*)$/gm, '<li>$1</li>'); // Unordered list
          html = html.replace(/^\s*\d+\.\s+(.*)$/gm, '<li>$1</li>'); // Ordered list
          html = html.replace(/^\s*>\s+(.*)$/gm, '<blockquote>$1</blockquote>'); // Blockquote

          elements.push(<span key={`text-${index}`} dangerouslySetInnerHTML={{ __html: html }} />);
        }
        
        if (index < lines.length - 1) {
          elements.push(<br key={`br-${index}`} />);
        }
      });
      return <>{elements}</>;
    };

    const isReply = level > 0;
    const replyIndentClass = isReply ? `ml-${level * 8}` : ''; // Increased indentation for replies
    // Changed reply background to light blue shades
    const replyBackgroundClass = isReply ? (index % 2 === 0 ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-blue-100 dark:bg-blue-800/30') : (index % 2 === 0 ? 'bg-card' : 'bg-muted/30');
    
    return (
      <div key={comment.id} className={`mb-4 ${replyIndentClass} relative`}>
        {isReply && (
          <CornerDownRight className="absolute -left-6 top-5 h-4 w-4 text-muted-foreground" />
        )}
        <div className={`p-4 rounded-lg shadow-sm ${replyBackgroundClass} ${isHiddenByModerator ? 'opacity-60 border border-yellow-500' : ''} ${isDeletedByUser || isDeletedByModerator ? 'opacity-40 border border-destructive' : ''}`}>
          <div className="flex gap-3">
            {profile?.username ? (
              <Link href={`/profile/${profile.username}`} passHref legacyBehavior>
                <a className="flex gap-3 items-start cursor-pointer">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={avatarUrl} alt={displayName} />
                    <AvatarFallback>{avatarFallback}</AvatarFallback>
                  </Avatar>
                  <div> {/* Wrapper for name and time to keep them together if name wraps */}
                    <span className={`font-semibold text-sm hover:underline ${isHiddenByModerator ? 'text-muted-foreground' : 'text-primary'}`}>{displayName}</span> {/* Added text-primary for username color */}
                    <span className={`text-xs ml-2 ${isHiddenByModerator ? 'text-muted-foreground/70' : 'text-muted-foreground'}`}>
                      {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true, locale: fr })}
                    </span>
                  </div>
                </a>
              </Link>
            ) : (
               <div className="flex gap-3 items-start">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={avatarUrl} alt={displayName} />
                  <AvatarFallback>{avatarFallback}</AvatarFallback>
                </Avatar>
                <div>
                  <span className={`font-semibold text-sm ${isHiddenByModerator ? 'text-muted-foreground' : 'text-primary'}`}>{displayName}</span> {/* Added text-primary for username color */}
                  <span className={`text-xs ml-2 ${isHiddenByModerator ? 'text-muted-foreground/70' : 'text-muted-foreground'}`}>
                    {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true, locale: fr })}
                  </span>
                </div>
              </div>
            )}
          </div>
          {/* Content is now part of the main flow, not inside the Link/div with avatar if linked */}
          <div className={`text-sm whitespace-pre-wrap mt-1 ml-11 ${isHiddenByModerator ? 'text-muted-foreground/80' : 'text-foreground/90'}`}>{parseMarkdown(comment.content)}</div>
          
          <div className="flex items-center gap-2 mt-2 ml-11">
                {user && !isHiddenByModerator && !isDeletedByUser && !isDeletedByModerator && (
                  <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={() => { setReplyingTo(comment.id); setReplyContent(''); }}>
                    <MessageCircle className="mr-1 h-3 w-3" /> Répondre
                  </Button>
                )}
                {isModeratorView && user?.id === resourceCreatorId && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6 ml-auto">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {comment.status === 'visible' && (
                        <DropdownMenuItem onClick={() => handleModerateComment(comment.id, 'hidden_by_moderator')}>
                          <EyeOff className="mr-2 h-4 w-4" /> Cacher
                        </DropdownMenuItem>
                      )}
                      {comment.status === 'hidden_by_moderator' && (
                         <DropdownMenuItem onClick={() => handleModerateComment(comment.id, 'visible')}>
                          <Eye className="mr-2 h-4 w-4" /> Afficher
                        </DropdownMenuItem>
                      )}
                      {(comment.status === 'visible' || comment.status === 'hidden_by_moderator') && (
                         <DropdownMenuItem className="text-destructive" onClick={() => handleModerateComment(comment.id, 'deleted_by_moderator')}>
                          <Trash2 className="mr-2 h-4 w-4" /> Supprimer par modérateur
                        </DropdownMenuItem>
                      )}
                       {comment.status === 'deleted_by_user' && (
                         <DropdownMenuItem className="text-destructive" onClick={() => handleModerateComment(comment.id, 'deleted_by_moderator')}>
                          <Trash2 className="mr-2 h-4 w-4" /> Supprimer définitivement
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
              {(comment.status === 'hidden_by_moderator' || comment.status === 'deleted_by_user' || comment.status === 'deleted_by_moderator') && (
                <Badge variant="outline" className={`text-xs mt-1 ml-11 ${comment.status === 'hidden_by_moderator' ? 'border-yellow-500 text-yellow-500' : 'border-destructive text-destructive'}`}> {/* Aligned badge */}
                  {comment.status === 'hidden_by_moderator' ? 'Caché par modérateur' : comment.status === 'deleted_by_user' ? 'Supprimé par l\'utilisateur' : 'Supprimé par modérateur'}
                </Badge>
              )}
        </div>


        {replyingTo === comment.id && user && (
          <div className="mt-3 ml-11 pl-2 border-l-2 border-muted">
            <RichTextEditor
              content={replyContent}
              onChange={setReplyContent}
              placeholder={`Répondre à ${displayName}...`}
            />
            <div className="flex justify-end items-center mt-2">
              <Button
                size="sm"
                onClick={() => handlePostComment(replyContent, comment.id)}
                disabled={isPostingComment || !replyContent.trim()}
              >
                {isPostingComment && <Loader2 className="mr-2 h-3 w-3 animate-spin" />}
                Envoyer
              </Button>
            </div>
          </div>
        )}

        {comment.replies && comment.replies.length > 0 && (
          <div className="mt-3 border-l-2 border-[hsl(var(--border))]/30 pl-4"> {/* Adjusted padding for replies */}
            {comment.replies.map((reply, replyIndex) => renderComment(reply, level + 1, replyIndex))}
          </div>
        )}
      </div>
    );
  };

  // Determine if comments should be displayed based on new props
  const canViewComments = () => {
    if (isModeratorView) return true; // Moderators always see comments
    if (areCommentsPublic) return true; // Public comments are visible to all (logged-in) users

    // Private comments logic
    if (resourceType === 'band') {
      return isCurrentUserMember; // Only band members see private band comments
    }
    // For other resource types, private comments are only for the creator (handled by isModeratorView)
    // Other logged-in users will see a message if not public and not creator.
    return false; 
  };

  if (!isModeratorView && !areCommentsPublic) {
    if (resourceType === 'band' && !isCurrentUserMember) {
      return (
        <section className="mt-12 p-4 border rounded-lg bg-muted/50">
          <p className="text-muted-foreground text-center">
            <MessageSquare className="inline-block mr-2 h-5 w-5" /> {/* Corrected to MessageSquare */}
            Les commentaires pour ce groupe sont réservés à ses membres.
          </p>
        </section>
      );
    }
    if (resourceType !== 'band') {
       return (
        <section className="mt-12 p-4 border rounded-lg bg-muted/50">
          <p className="text-muted-foreground text-center">
            <MessageSquare className="inline-block mr-2 h-5 w-5" /> {/* Corrected to MessageSquare */}
            Les commentaires pour cette ressource sont privés.
          </p>
        </section>
      );
    }
    // If it's a band, and user is a member, and comments are private, canViewComments() will be true later.
  }


  return (
    <section className="mt-12">
      <h2 className="text-2xl font-semibold mb-4 border-b pb-2 flex items-center">
        <MessageSquare className="mr-2 h-6 w-6" /> Commentaires ({canViewComments() ? countVisibleComments(comments) : 0}) {/* Corrected to MessageSquare */}
      </h2>
      
      {/* Input for new comment - only if user can view comments and is logged in */}
      {user && canViewComments() && (
        <div className="mb-6">
          <RichTextEditor
            content={newComment}
            onChange={setNewComment}
            placeholder="Ajoutez votre commentaire..."
          />
          <div className="flex justify-end items-center mt-2">
            <Button onClick={() => handlePostComment(newComment)} disabled={isPostingComment || !newComment.trim()}>
              {isPostingComment && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Commenter
            </Button>
          </div>
        </div>
      )}
      {!user && (
        <p className="text-sm text-muted-foreground mb-6">
          <Link href="/login" className="underline hover:text-primary">Connectez-vous</Link> pour laisser un commentaire.
        </p>
      )}

      {isLoadingComments || isLoadingLimit ? ( // Added isLoadingLimit to condition
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : comments.length > 0 ? (
        <>
          <div className="space-y-4">
            {comments
              .slice((currentPage - 1) * commentsPerPage, currentPage * commentsPerPage) 
              .map((comment, index) => renderComment(comment, 0, index))}
          </div>
          {comments.length > commentsPerPage && ( 
            <div className="mt-6 flex justify-center items-center gap-2">
              {/* Use dynamic commentsPerPage */}
              <Button 
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                variant="outline"
              >
                Précédent
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {currentPage} sur {Math.ceil(comments.length / commentsPerPage)} 
              </span>
              {/* Use dynamic commentsPerPage */}
              <Button 
                onClick={() => setCurrentPage(prev => Math.min(Math.ceil(comments.length / commentsPerPage), prev + 1))}
                disabled={currentPage === Math.ceil(comments.length / commentsPerPage)}
                variant="outline"
              >
                Suivant
              </Button>
            </div>
          )}
        </>
      ) : (
        <p className="text-muted-foreground text-center py-4">Aucun commentaire pour le moment. Soyez le premier !</p>
      )}
    </section>
  );
}
