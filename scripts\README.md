# Scripts de Migration Mouvik

Ce dossier contient les scripts pour gérer les migrations de la base de donn<PERSON>, notamment la migration de la table `tracks` vers `songs`.

## 📋 Fichiers Principaux

- `run-migration.ps1` - Script principal pour exécuter les migrations
- `config.ps1` - Configuration par défaut
- `config.local.ps1.example` - Exemple de configuration locale (à copier en `config.local.ps1`)
- `20240516_rename_tracks_to_songs.sql` - Script de migration principal
- `rollback_20240516_rename_tracks_to_songs.sql` - Script de rollback

## 🚀 Utilisation

### Prérequis

- PowerShell 5.1 ou supérieur
- Accès à la base de données PostgreSQL
- Droits d'administration sur la base de données
- Module `SqlServer` ou `PostgreSQL` installé selon votre base de données

### Configuration

1. <PERSON><PERSON><PERSON> le fichier `config.local.ps1.example` en `config.local.ps1`
   ```powershell
   Copy-Item config.local.ps1.example config.local.ps1
   ```

2. Modifiez les paramètres dans `config.local.ps1` selon votre environnement.
   - Pour des raisons de sécurité, il est recommandé d'utiliser des variables d'environnement pour les mots de passe.
   - Exemple : `$env:DB_PASSWORD = "votre_mot_de_passe"`

### Exécution d'une Migration

```powershell
# Se placer dans le dossier du projet
cd C:\chemin\vers\mouvik-5v0

# Exécuter la migration
.\scripts\run-migration.ps1 up

# Annuler la dernière migration (rollback)
.\scripts\run-migration.ps1 down
```

### Options Avancées

#### Variables d'environnement utiles

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `DB_PASSWORD` | Mot de passe de la base de données | - |
| `FORCE_BACKUP` | Forcer une sauvegarde avant migration | `$false` |
| `LOG_LEVEL` | Niveau de journalisation (DEBUG, INFO, WARN, ERROR) | `INFO` |

#### Exemples

```powershell
# Activer le mode debug
$env:LOG_LEVEL="DEBUG"
.\scripts\run-migration.ps1 up

# Forcer une sauvegarde avant migration
$env:FORCE_BACKUP=$true
.\scripts\run-migration.ps1 up

# Spécifier un mot de passe via variable d'environnement
$env:DB_PASSWORD = "mon_mot_de_passe_secret"
.\scripts\run-migration.ps1 up
```

## 🔒 Sécurité

- Ne commitez jamais le fichier `config.local.ps1` dans le contrôle de version
- Utilisez des variables d'environnement pour les informations sensibles
- Vérifiez les droits d'accès aux fichiers de configuration

## 🔄 Gestion des Erreurs

Le script inclut un système de journalisation complet :
- Les journaux sont enregistrés dans le fichier spécifié dans la configuration
- En cas d'erreur, une sauvegarde de la base de données est créée automatiquement
- Les erreurs sont affichées à l'écran et enregistrées dans le journal

## 🔒 Sécurité

- Ne commitez jamais le fichier `config.local.ps1` dans le dépôt
- Utilisez des variables d'environnement pour les mots de passe en production
- Limitez les droits de l'utilisateur de la base de données au strict nécessaire

## 🐛 Dépannage

### Erreur de connexion à la base de données

1. Vérifiez que le serveur est en cours d'exécution
2. Vérifiez les identifiants dans `config.local.ps1`
3. Vérifiez que le pare-feu autorise les connexions au port de la base de données

### Problèmes de permissions

Assurez-vous que l'utilisateur a les droits nécessaires pour :
- Créer/modifier des tables
- Exécuter des requêtes DDL
- Sauvegarder/restaurer la base de données

## 📚 Documentation Supplémentaire

- [Guide de Migration](../docs/MIGRATION_GUIDE.md)
- [Structure de la Base de Données](../docs/UPDATED_DATABASE_STRUCTURE.md)
