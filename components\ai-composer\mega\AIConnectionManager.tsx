'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle2, AlertCircle, Loader2, Wifi, WifiOff, 
  Settings, Zap, Brain, TestTube, RefreshCw, Info
} from 'lucide-react';

interface AIConnectionManagerProps {
  aiConfig: any;
  setAiConfig: (config: any) => void;
  onTestConnection: (config: any) => Promise<boolean>;
}

interface ConnectionTest {
  status: 'idle' | 'testing' | 'success' | 'error';
  message: string;
  latency?: number;
  model?: string;
}

const AI_PROVIDERS = [
  {
    id: 'ollama',
    name: '<PERSON><PERSON><PERSON> (Local)',
    defaultUrl: 'http://localhost:11434',
    models: ['llama2', 'codellama', 'mistral', 'neural-chat', 'starling-lm'],
    testEndpoint: '/api/tags'
  },
  {
    id: 'openai',
    name: 'OpenAI',
    defaultUrl: 'https://api.openai.com/v1',
    models: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
    testEndpoint: '/models'
  },
  {
    id: 'anthropic',
    name: 'Anthropic Claude',
    defaultUrl: 'https://api.anthropic.com/v1',
    models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    testEndpoint: '/messages'
  },
  {
    id: 'custom',
    name: 'API Personnalisée',
    defaultUrl: '',
    models: [],
    testEndpoint: '/health'
  }
];

export const AIConnectionManager: React.FC<AIConnectionManagerProps> = ({
  aiConfig,
  setAiConfig,
  onTestConnection
}) => {
  
  const [connectionTest, setConnectionTest] = useState<ConnectionTest>({
    status: 'idle',
    message: 'Prêt à tester la connexion'
  });
  
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  const currentProvider = AI_PROVIDERS.find(p => p.id === aiConfig?.provider) || AI_PROVIDERS[0];

  // Test de connexion avancé
  const handleTestConnection = useCallback(async () => {
    setConnectionTest({ status: 'testing', message: 'Test de connexion en cours...' });
    
    const startTime = Date.now();
    
    try {
      // Test 1: Connectivité de base
      setConnectionTest({ status: 'testing', message: '🔗 Test de connectivité...' });
      
      const response = await fetch(`${aiConfig.apiUrl}${currentProvider.testEndpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(aiConfig.apiKey && { 'Authorization': `Bearer ${aiConfig.apiKey}` })
        },
        signal: AbortSignal.timeout(10000) // 10s timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Test 2: Récupération des modèles
      setConnectionTest({ status: 'testing', message: '🧠 Récupération des modèles...' });
      
      let models = [];
      if (aiConfig.provider === 'ollama') {
        const data = await response.json();
        models = data.models?.map((m: any) => m.name) || [];
      } else {
        models = currentProvider.models;
      }
      
      setAvailableModels(models);

      // Test 3: Test de génération simple
      setConnectionTest({ status: 'testing', message: '✨ Test de génération...' });
      
      const testResult = await onTestConnection(aiConfig);
      
      const latency = Date.now() - startTime;
      
      if (testResult) {
        setConnectionTest({
          status: 'success',
          message: `✅ Connexion réussie ! Latence: ${latency}ms`,
          latency,
          model: aiConfig.model
        });
      } else {
        throw new Error('Test de génération échoué');
      }
      
    } catch (error: any) {
      const latency = Date.now() - startTime;
      setConnectionTest({
        status: 'error',
        message: `❌ Erreur: ${error.message}`,
        latency
      });
    }
  }, [aiConfig, currentProvider, onTestConnection]);

  // Chargement automatique des modèles
  const loadModels = useCallback(async () => {
    if (!aiConfig?.apiUrl || aiConfig.provider !== 'ollama') return;
    
    setIsLoadingModels(true);
    try {
      const response = await fetch(`${aiConfig.apiUrl}/api/tags`);
      if (response.ok) {
        const data = await response.json();
        const models = data.models?.map((m: any) => m.name) || [];
        setAvailableModels(models);
      }
    } catch (error) {
      console.error('Erreur chargement modèles:', error);
    } finally {
      setIsLoadingModels(false);
    }
  }, [aiConfig]);

  // Auto-test au changement de config
  useEffect(() => {
    if (aiConfig?.apiUrl && aiConfig?.provider) {
      loadModels();
    }
  }, [aiConfig?.apiUrl, aiConfig?.provider, loadModels]);

  // Gestionnaire de changement de provider
  const handleProviderChange = (providerId: string) => {
    const provider = AI_PROVIDERS.find(p => p.id === providerId);
    if (!provider) return;
    
    setAiConfig({
      ...aiConfig,
      provider: providerId,
      apiUrl: provider.defaultUrl,
      model: provider.models[0] || ''
    });
    
    setConnectionTest({ status: 'idle', message: 'Configuration mise à jour' });
    setAvailableModels(provider.models);
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Configuration IA
          {connectionTest.status === 'success' && (
            <Badge variant="default" className="gap-1 bg-green-500">
              <CheckCircle2 className="h-3 w-3" />
              Connecté
            </Badge>
          )}
          {connectionTest.status === 'error' && (
            <Badge variant="destructive" className="gap-1">
              <AlertCircle className="h-3 w-3" />
              Erreur
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Sélection du provider */}
        <div>
          <Label className="text-white">Fournisseur IA</Label>
          <Select value={aiConfig?.provider || 'ollama'} onValueChange={handleProviderChange}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {AI_PROVIDERS.map(provider => (
                <SelectItem key={provider.id} value={provider.id}>
                  <div className="flex items-center gap-2">
                    {provider.id === 'ollama' && <Wifi className="h-4 w-4" />}
                    {provider.id === 'openai' && <Zap className="h-4 w-4" />}
                    {provider.id === 'anthropic' && <Brain className="h-4 w-4" />}
                    {provider.id === 'custom' && <Settings className="h-4 w-4" />}
                    {provider.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* URL de l'API */}
        <div>
          <Label className="text-white">URL de l'API</Label>
          <Input
            value={aiConfig?.apiUrl || ''}
            onChange={(e) => setAiConfig({...aiConfig, apiUrl: e.target.value})}
            className="bg-slate-700 border-slate-600 text-white"
            placeholder={currentProvider.defaultUrl}
          />
        </div>

        {/* Clé API (si nécessaire) */}
        {(aiConfig?.provider === 'openai' || aiConfig?.provider === 'anthropic' || aiConfig?.provider === 'custom') && (
          <div>
            <Label className="text-white">Clé API</Label>
            <Input
              type="password"
              value={aiConfig?.apiKey || ''}
              onChange={(e) => setAiConfig({...aiConfig, apiKey: e.target.value})}
              className="bg-slate-700 border-slate-600 text-white"
              placeholder="Votre clé API..."
            />
          </div>
        )}

        {/* Modèle */}
        <div>
          <div className="flex items-center justify-between">
            <Label className="text-white">Modèle</Label>
            {aiConfig?.provider === 'ollama' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={loadModels}
                disabled={isLoadingModels}
                className="h-6 w-6 p-0"
              >
                <RefreshCw className={`h-3 w-3 ${isLoadingModels ? 'animate-spin' : ''}`} />
              </Button>
            )}
          </div>
          
          <Select 
            value={aiConfig?.model || ''} 
            onValueChange={(value) => setAiConfig({...aiConfig, model: value})}
          >
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
              <SelectValue placeholder="Choisir un modèle..." />
            </SelectTrigger>
            <SelectContent>
              {availableModels.map(model => (
                <SelectItem key={model} value={model}>{model}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Paramètres avancés */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-white">Température</Label>
            <Input
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={aiConfig?.temperature || 0.7}
              onChange={(e) => setAiConfig({...aiConfig, temperature: parseFloat(e.target.value)})}
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>
          <div>
            <Label className="text-white">Max Tokens</Label>
            <Input
              type="number"
              min="100"
              max="4000"
              value={aiConfig?.maxTokens || 1000}
              onChange={(e) => setAiConfig({...aiConfig, maxTokens: parseInt(e.target.value)})}
              className="bg-slate-700 border-slate-600 text-white"
            />
          </div>
        </div>

        {/* Test de connexion */}
        <div className="space-y-3">
          <Button
            onClick={handleTestConnection}
            disabled={connectionTest.status === 'testing' || !aiConfig?.apiUrl}
            className="w-full gap-2"
          >
            {connectionTest.status === 'testing' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <TestTube className="h-4 w-4" />
            )}
            Tester la connexion
          </Button>

          {/* Résultat du test */}
          <div className={`p-3 rounded-lg border ${
            connectionTest.status === 'success' ? 'bg-green-500/10 border-green-500/20' :
            connectionTest.status === 'error' ? 'bg-red-500/10 border-red-500/20' :
            connectionTest.status === 'testing' ? 'bg-blue-500/10 border-blue-500/20' :
            'bg-slate-500/10 border-slate-500/20'
          }`}>
            <div className="flex items-center gap-2">
              {connectionTest.status === 'testing' && <Loader2 className="h-4 w-4 animate-spin text-blue-400" />}
              {connectionTest.status === 'success' && <CheckCircle2 className="h-4 w-4 text-green-400" />}
              {connectionTest.status === 'error' && <AlertCircle className="h-4 w-4 text-red-400" />}
              {connectionTest.status === 'idle' && <Info className="h-4 w-4 text-slate-400" />}
              
              <span className={`text-sm ${
                connectionTest.status === 'success' ? 'text-green-300' :
                connectionTest.status === 'error' ? 'text-red-300' :
                connectionTest.status === 'testing' ? 'text-blue-300' :
                'text-slate-300'
              }`}>
                {connectionTest.message}
              </span>
            </div>
            
            {connectionTest.latency && (
              <div className="mt-2 text-xs text-slate-400">
                Latence: {connectionTest.latency}ms
                {connectionTest.model && ` • Modèle: ${connectionTest.model}`}
              </div>
            )}
          </div>
        </div>

        {/* Informations du provider */}
        <div className="bg-slate-700/30 rounded-lg p-3">
          <h4 className="text-sm font-medium text-white mb-2">Informations</h4>
          <div className="text-xs text-slate-400 space-y-1">
            <div>Provider: {currentProvider.name}</div>
            <div>Endpoint: {currentProvider.testEndpoint}</div>
            {availableModels.length > 0 && (
              <div>Modèles disponibles: {availableModels.length}</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
