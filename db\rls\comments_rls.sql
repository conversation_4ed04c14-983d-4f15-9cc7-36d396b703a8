-- Enable <PERSON><PERSON> on the comments table if not already enabled
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to ensure a clean state
DROP POLICY IF EXISTS "Allow authenticated users to insert comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to update content of their own comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to mark their own comments as deleted_by_user" ON public.comments;
DROP POLICY IF EXISTS "Allow public read access to visible comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to see their own non-visible comments" ON public.comments;
-- The moderation policy is dropped and recreated in comment_moderation_helpers.sql

-- Policy: Allow authenticated users to insert comments
CREATE POLICY "Allow authenticated users to insert comments"
ON public.comments
FOR INSERT
TO authenticated 
WITH CHECK (auth.uid() = user_id); 

-- Policy: Allow users to update the CONTENT of their own comments.
-- This policy is ONLY for content changes, not status changes by the user themselves.
DROP POLICY IF EXISTS "Allow users to update content of their own comments" ON public.comments;
CREATE POLICY "Allow users to update content of their own comments"
ON public.comments
FOR UPDATE
TO authenticated
USING (
    auth.uid() = user_id AND 
    status <> 'deleted_by_moderator'::text AND
    NOT public.can_moderate_comment(id, auth.uid()) -- This policy does not apply if user can moderate
) 
WITH CHECK (
    auth.uid() = user_id AND
    status <> 'deleted_by_moderator'::text 
    -- Attempting to ensure status is not changed by this policy.
    -- PostgreSQL RLS does not directly support OLD.column in WITH CHECK for UPDATE in a simple way.
    -- This check implicitly means that if this policy is the *only* one allowing the update,
    -- the status must not be 'deleted_by_moderator'.
    -- The actual prevention of status change relies on other policies being more specific
    -- or the client sending only 'content' for update.
    -- For true column-level update security, use triggers or update via SECURITY DEFINER functions.
);

-- Policy: Allow users to mark their OWN comments as 'deleted_by_user'.
-- This is a specific status update action by the comment author.
DROP POLICY IF EXISTS "Allow users to mark their own comments as deleted_by_user" ON public.comments;
CREATE POLICY "Allow users to mark their own comments as deleted_by_user"
ON public.comments
FOR UPDATE
TO authenticated
USING (
    auth.uid() = user_id AND
    status <> 'deleted_by_moderator'::text AND
    NOT public.can_moderate_comment(id, auth.uid()) -- Does not apply if user is also a moderator
)
WITH CHECK (
    auth.uid() = user_id AND
    status = 'deleted_by_user'::text -- The new status must be 'deleted_by_user'
    -- Assuming content is not changed by this specific user action.
    -- For stricter control, this should be a dedicated function.
);

-- Policy: Allow public read access to 'visible' comments
CREATE POLICY "Allow public read access to visible comments"
ON public.comments
FOR SELECT
USING (status = 'visible'::text);

-- Policy: Allow authenticated users to see their own comments regardless of status, unless deleted by moderator
CREATE POLICY "Allow users to see their own non-visible comments"
ON public.comments
FOR SELECT
TO authenticated
USING (auth.uid() = user_id AND status <> 'deleted_by_moderator'::text);

-- The policy "Allow resource creators to update comments on their resources" 
-- (for moderation actions like changing status to 'hidden_by_moderator' or 'visible' or 'deleted_by_moderator')
-- is defined in 'db/functions/comment_moderation_helpers.sql'.
-- It should handle all status updates by moderators.
