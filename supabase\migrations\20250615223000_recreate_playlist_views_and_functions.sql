-- Migration script to fix missing playlist features

-- Step 1: Add missing columns to the `playlists` table
ALTER TABLE public.playlists
ADD COLUMN IF NOT EXISTS cover_art_url TEXT,
ADD COLUMN IF NOT EXISTS banner_url TEXT,
ADD COLUMN IF NOT EXISTS genres TEXT[],
ADD COLUMN IF NOT EXISTS moods TEXT[],
ADD COLUMN IF NOT EXISTS instrumentation TEXT[],
ADD COLUMN IF NOT EXISTS slug TEXT;

-- Generate slug for existing playlists that don't have one
UPDATE public.playlists
SET slug = lower(regexp_replace(name, '\s+', '-', 'g'))
WHERE slug IS NULL;

-- Step 2: Add `position` and `created_at` columns to the `playlist_songs` table
ALTER TABLE public.playlist_songs
ADD COLUMN IF NOT EXISTS position INTEGER,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT now();

-- Backfill position for existing playlist songs based on their creation order (approximated by song_id)
WITH ranked_songs AS (
  SELECT
    playlist_id,
    song_id,
    row_number() OVER (PARTITION BY playlist_id ORDER BY song_id) as rn
  FROM public.playlist_songs
  WHERE position IS NULL
)
UPDATE public.playlist_songs ps
SET position = rs.rn
FROM ranked_songs rs
WHERE ps.playlist_id = rs.playlist_id AND ps.song_id = rs.song_id;


-- Step 3: Create the `update_playlist_song_positions` RPC function
DROP FUNCTION IF EXISTS public.update_playlist_song_positions(uuid, uuid[], uuid);
CREATE OR REPLACE FUNCTION public.update_playlist_song_positions(p_playlist_id uuid, p_song_ids uuid[], p_requesting_user_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_playlist_owner_id uuid;
BEGIN
  -- Ensure the requesting user owns the playlist
  SELECT user_id INTO v_playlist_owner_id FROM public.playlists WHERE id = p_playlist_id;

  IF v_playlist_owner_id IS DISTINCT FROM p_requesting_user_id THEN
    RAISE EXCEPTION 'Permission denied to modify this playlist';
  END IF;

  -- Loop through the provided song IDs and update their position
  FOR i IN 1..array_length(p_song_ids, 1) LOOP
    UPDATE public.playlist_songs
    SET position = i
    WHERE playlist_id = p_playlist_id AND song_id = p_song_ids[i];
  END LOOP;
END;
$$;


-- Step 4: Create the `get_playlist_details_for_view` RPC function
-- This function retrieves all necessary details for the playlist view page.
DROP FUNCTION IF EXISTS public.get_playlist_details_for_view(uuid, uuid);
CREATE OR REPLACE FUNCTION public.get_playlist_details_for_view(p_playlist_id uuid, p_requesting_user_id uuid DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    v_playlist_record RECORD;
BEGIN
    -- Fetch the playlist and its owner
    SELECT p.*, pr.username, pr.display_name, pr.avatar_url
    INTO v_playlist_record
    FROM public.playlists p
    JOIN public.profiles pr ON p.user_id = pr.id
    WHERE p.id = p_playlist_id;

    -- If not found, return null
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;

    -- Check for access rights
    IF v_playlist_record.is_public = FALSE AND v_playlist_record.user_id IS DISTINCT FROM p_requesting_user_id THEN
        RETURN NULL; -- Access denied
    END IF;

    -- Build and return the JSON object
    RETURN json_build_object(
        'id', v_playlist_record.id,
        'name', v_playlist_record.name,
        'description', v_playlist_record.description,
        'is_public', v_playlist_record.is_public,
        'cover_art_url', v_playlist_record.cover_art_url,
        'user_id', v_playlist_record.user_id,
        'created_at', v_playlist_record.created_at,
        'updated_at', v_playlist_record.updated_at,
        'slug', v_playlist_record.slug,
        'profiles', json_build_object(
            'id', v_playlist_record.user_id,
            'username', v_playlist_record.username,
            'display_name', v_playlist_record.display_name,
            'avatar_url', v_playlist_record.avatar_url
        ),
        'playlist_songs', (
             SELECT COALESCE(json_agg(
                json_build_object(
                    'id', s.id,
                    'title', s.title,
                    'artist_name', s.artist_name,
                    'audio_url', s.audio_url,
                    'cover_art_url', s.cover_art_url,
                    'duration_ms', s.duration_ms,
                    'position', ps.position,
                    'profiles', (
                        SELECT json_build_object(
                            'id', pr.id,
                            'username', pr.username,
                            'display_name', pr.display_name
                        )
                        FROM public.profiles pr
                        WHERE pr.id = s.creator_user_id
                    )
                ) ORDER BY ps.position
            ), '[]'::json)
            FROM public.playlist_songs ps
            JOIN public.songs s ON ps.song_id = s.id
            WHERE ps.playlist_id = v_playlist_record.id
        )
    );
END;
$$;

-- Note: The view `playlist_songs_view` is no longer needed as the logic is encapsulated
-- within the `get_playlist_details_for_view` function for better performance and security.

-- Step 5: Create the `get_playlists_for_user` RPC function
-- This function retrieves all playlists for a given user, along with creator and song details.
CREATE OR REPLACE FUNCTION public.get_playlists_for_user(p_user_id uuid)
RETURNS json
LANGUAGE sql
STABLE
AS $$
  SELECT
    COALESCE(
      json_agg(
        json_build_object(
          'id', p.id,
          'name', p.name,
          'description', p.description,
          'is_public', p.is_public,
          'cover_art_url', p.cover_art_url,
          'user_id', p.user_id,
          'created_at', p.created_at,
          'updated_at', p.updated_at,
          'slug', p.slug,
          'profiles', json_build_object(
            'id', pr.id,
            'username', pr.username,
            'display_name', pr.display_name,
            'avatar_url', pr.avatar_url
          ),
          'songs', (
            SELECT COALESCE(json_agg(s.*), '[]'::json)
            FROM public.playlist_songs ps
            JOIN public.songs s ON ps.song_id = s.id
            WHERE ps.playlist_id = p.id
          )
        )
      ORDER BY p.created_at DESC
      ),
      '[]'::json
    )
  FROM
    public.playlists p
  JOIN
    public.profiles pr ON p.user_id = pr.id
  WHERE
    p.user_id = p_user_id;
$$;

-- Step 6: Create RPC for the playlist edit page
CREATE OR REPLACE FUNCTION public.get_data_for_playlist_edit_page(p_playlist_id uuid, p_requesting_user_id uuid)
RETURNS json
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    v_playlist_owner_id uuid;
    v_playlist_details json;
    v_playlist_songs json;
    v_available_songs json;
BEGIN
    -- 1. Get playlist details and verify ownership
    SELECT
        json_build_object(
            'id', p.id,
            'name', p.name,
            'description', p.description,
            'is_public', p.is_public,
            'cover_art_url', p.cover_art_url,
            'banner_url', p.banner_url,
            'genres', p.genres, -- Added missing fields from schema
            'moods', p.moods, -- Added missing fields from schema
            'instrumentation', p.instrumentation, -- Added missing fields from schema
            'are_comments_public', p.are_comments_public, -- Added missing fields from schema
            'user_id', p.user_id
        ),
        p.user_id
    INTO v_playlist_details, v_playlist_owner_id
    FROM public.playlists p
    WHERE p.id = p_playlist_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Playlist not found';
    END IF;

    IF v_playlist_owner_id IS DISTINCT FROM p_requesting_user_id THEN
        RAISE EXCEPTION 'Permission denied';
    END IF;

    -- 2. Get songs already in the playlist
    SELECT COALESCE(json_agg(
        json_build_object(
            'id', ps.id, -- Utilise playlist_songs.id
            'song_id', s.id, -- Ajout de song_id
            'title', s.title,
            'artist_name', s.artist_name,
            'cover_art_url', s.cover_art_url, -- Nom corrigé
            'duration_ms', s.duration_ms,
            'position', ps.position, -- Ajout de la position
            'profiles', (SELECT json_build_object('display_name', pr.display_name, 'username', pr.username) FROM public.profiles pr WHERE pr.id = s.creator_user_id)
        ) ORDER BY ps.position
    ), '[]'::json)
    INTO v_playlist_songs
    FROM public.playlist_songs ps
    JOIN public.songs s ON ps.song_id = s.id
    WHERE ps.playlist_id = p_playlist_id;

    -- 3. Get all songs available for the user to add
    SELECT COALESCE(json_agg(
        json_build_object(
            'id', s.id,
            'title', s.title,
            'artist_name', s.artist_name,
            'cover_art_url', s.cover_art_url, -- Nom corrigé
            'duration_ms', s.duration_ms,
            'profiles', (SELECT json_build_object('display_name', pr.display_name, 'username', pr.username) FROM public.profiles pr WHERE pr.id = s.creator_user_id)
        ) ORDER BY s.title
    ), '[]'::json)
    INTO v_available_songs
    FROM public.songs s
    WHERE s.creator_user_id = p_requesting_user_id; -- Filtre s.status = 'published' retiré

    -- 4. Combine and return
    RETURN json_build_object(
        'playlistDetails', v_playlist_details,
        'playlistSongs', v_playlist_songs,
        'availableSongs', v_available_songs
    );
END;
$$;
