import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ClientLayout } from "@/components/client-layout"
import { createSupabaseServerClient } from "@/lib/supabase/server" // Added
import type { UserProfileForSidebar } from "@/components/sidebar";
import { getUserProfileForSidebar } from "@/lib/supabase/queries/user";
// import dynamic from 'next/dynamic'; // Added for dynamic import

// const StagewiseToolbar = dynamic(() => 
//   import('@stagewise/toolbar-next').then(mod => mod.StagewiseToolbar),
//   { ssr: false }
// ); // Added for Stagewise toolbar

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "MOUVIK - Plateforme de composition musicale assistée par IA",
  description: "<PERSON><PERSON>ez, partagez et découvrez de la musique avec l'aide de l'IA",
  generator: 'v0.dev',
  icons: {
    icon: '/Mlogo.png',
  }
}

import { cookies as nextCookies } from 'next/headers'; // Import cookies for logging

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = nextCookies();
  console.log("RootLayout: All cookies from next/headers cookieStore:", JSON.stringify(Array.from(cookieStore.getAll())));

  const supabase = createSupabaseServerClient();
  const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

  if (authError) {
    console.error("RootLayout auth error:", authError.message);
  } else if (!authUser) {
    console.warn("RootLayout: No authenticated user found (authUser is null/undefined), but no specific authError object was returned.");
  } else {
    console.log("RootLayout: Authenticated user found, ID:", authUser.id);
  }

  let userObj: UserProfileForSidebar | null = null;

  if (authUser) {
    userObj = await getUserProfileForSidebar(supabase, authUser.id, authUser);
    if (!userObj) {
      console.warn(`RootLayout: Could not fetch sidebar profile for user ${authUser.id}. This might happen for new users or if profile data is missing. A minimal profile will be used.`)
    }
  }

  const stagewiseConfig = {
    plugins: []
  };

  return (
    <html lang="fr">
      <body className={inter.className}>
        <ClientLayout initialUser={userObj}>
          {children}
          {/* {process.env.NODE_ENV === 'development' && (
            <StagewiseToolbar config={stagewiseConfig} />
          )} */}
        </ClientLayout>
      </body>
    </html>
  )
}
