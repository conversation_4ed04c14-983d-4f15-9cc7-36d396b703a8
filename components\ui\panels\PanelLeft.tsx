import React from 'react';
import { Panel } from "react-resizable-panels";
import { PanelProps } from './types';
import { cn } from '@/lib/utils';

export const PanelLeft: React.FC<PanelProps> = ({ children, className }) => {
  return (
    <Panel
      collapsible
      defaultSize={20}
      minSize={15}
      maxSize={25}
      className={cn('h-full', className)}
    >
      {children}
    </Panel>
  );
};
