export interface ChordPlacementDisplay {
  chord: UnifiedChordPosition;
  textPosition: number;
  lineNumber: number;
  wordIndex: number;
  metadata?: {
    beat?: number;
    measure?: number;
    emphasis?: 'strong' | 'medium' | 'weak';
  };
}

export interface UnifiedChordPosition {
  root: string;
  type: string;
  bass?: string;
  frets: number[];
  fingers: number[];
  capo?: number;
  category: string;
  difficulty: number;
  pattern: string;
  instrument: string;
  tuning: string;
  source?: string;
}

export type ChordPlacement = ChordPlacementDisplay | {
  chord: string;
  textPosition: number;
  lineNumber: number;
  wordIndex: number;
};
