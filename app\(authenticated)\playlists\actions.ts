"use server";

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

const playlistIdSchema = z.string().uuid();

export async function togglePlaylistVisibility(playlistId: string, currentIsPublic: boolean) {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'Authentification requise.' };
  }

  if (!playlistIdSchema.safeParse(playlistId).success) {
    return { error: 'ID de playlist invalide.' };
  }

  const newIsPublic = !currentIsPublic;

  const { error } = await supabase
    .from('playlists')
    .update({ is_public: newIsPublic })
    .eq('id', playlistId)
    .eq('user_id', user.id);

  if (error) {
    console.error('Error toggling playlist visibility:', error);
    return { error: 'Impossible de mettre à jour la visibilité de la playlist.' };
  }

  revalidatePath('/playlists');
  revalidatePath(`/playlists/${playlistId}`);

  return { success: true, newIsPublic };
}

export async function duplicatePlaylist(playlistId: string): Promise<{ success: boolean; error?: string; newPlaylistId?: string }> {
  'use server';
  const supabase = createSupabaseServerClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: 'Unauthorized: You must be logged in to duplicate a playlist.' };
  }

  try {
    // Assumes an RPC function `duplicate_playlist` exists that takes the playlist ID and user ID,
    // duplicates it, and returns the new playlist's ID.
    const { data, error: rpcError } = await supabase.rpc('duplicate_playlist', {
      p_playlist_id: playlistId,
      p_user_id: user.id
    });

    if (rpcError) throw rpcError;

    revalidatePath('/playlists');
    // Assuming the RPC returns an object with the new ID, e.g., { new_playlist_id: '...' }
    const newPlaylistId = data?.[0]?.new_playlist_id || data?.id || null;

    return { success: true, newPlaylistId };

  } catch (error: any) {
    console.error('Error duplicating playlist:', error);
    return { success: false, error: error.message };
  }
}

export async function deletePlaylist(playlistId: string) {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'Authentification requise.' };
  }

  if (!playlistIdSchema.safeParse(playlistId).success) {
    return { error: 'ID de playlist invalide.' };
  }

  const { error } = await supabase
    .from('playlists')
    .delete()
    .eq('id', playlistId)
    .eq('user_id', user.id);

  if (error) {
    console.error('Error deleting playlist:', error);
    return { error: 'Impossible de supprimer la playlist.' };
  }

  revalidatePath('/playlists');

  return { success: true };
}
