'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, Sparkles, MessageSquare, History, Settings,
  Wand2, Music, Layers, Volume2, TrendingUp, Target,
  CheckCircle2, AlertCircle, Lightbulb, Zap, Send
} from 'lucide-react';

// Import des composants IA existants
import { AiConfigMenu } from '@/components/ia/ai-config-menu';

interface AIStudioAssistantProps {
  studioMode: 'compose' | 'arrange' | 'mix' | 'master';
  isConfigured: boolean;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: any;
  setAiConfig?: (config: any) => void;
  currentSection: string;
  songSections: any[];
  styleConfig: any;
  lyricsContent: string;
  aiHistory: any[];
  lastAiResult: string;
  setAiHistory: (history: any[]) => void;
  setLastAiResult: (result: string) => void;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
}

export const AIStudioAssistant: React.FC<AIStudioAssistantProps> = ({
  studioMode,
  isConfigured,
  aiLoading,
  aiError,
  aiConfig,
  setAiConfig,
  currentSection,
  songSections,
  styleConfig,
  lyricsContent,
  aiHistory,
  lastAiResult,
  setAiHistory,
  setLastAiResult,
  onAIGenerate
}) => {
  
  const [activeTab, setActiveTab] = useState('actions');
  const [chatInput, setChatInput] = useState('');

  // Actions IA spécialisées par mode studio
  const studioAIActions = {
    compose: [
      {
        id: 'lyrics-suggest',
        label: 'Suggérer paroles',
        icon: Wand2,
        prompt: `Écris des paroles créatives pour un ${songSections.find(s => s.id === currentSection)?.type || 'couplet'} dans le style ${styleConfig.genres?.[0] || 'pop'}`,
        color: 'bg-blue-500'
      },
      {
        id: 'melody-idea',
        label: 'Idée mélodie',
        icon: Music,
        prompt: 'Suggère une ligne mélodique qui s\'accorde avec ces paroles et ce style musical',
        color: 'bg-green-500'
      },
      {
        id: 'chord-progression',
        label: 'Progression d\'accords',
        icon: Target,
        prompt: `Suggère une progression d'accords en ${styleConfig.key || 'C'} majeur pour un ${songSections.find(s => s.id === currentSection)?.type || 'couplet'}`,
        color: 'bg-purple-500'
      }
    ],
    arrange: [
      {
        id: 'structure-optimize',
        label: 'Optimiser structure',
        icon: Layers,
        prompt: 'Analyse la structure de cette chanson et suggère des améliorations pour l\'arrangement',
        color: 'bg-green-500'
      },
      {
        id: 'instrument-suggest',
        label: 'Suggérer instruments',
        icon: Music,
        prompt: `Suggère des instruments qui s'accorderaient bien avec ce style ${styleConfig.genres?.[0] || 'pop'}`,
        color: 'bg-blue-500'
      },
      {
        id: 'dynamics-advice',
        label: 'Conseils dynamiques',
        icon: TrendingUp,
        prompt: 'Donne des conseils sur les dynamiques et les variations d\'intensité pour cette chanson',
        color: 'bg-orange-500'
      }
    ],
    mix: [
      {
        id: 'mix-balance',
        label: 'Balance mix',
        icon: Volume2,
        prompt: 'Donne des conseils pour équilibrer le mix de cette composition',
        color: 'bg-purple-500'
      },
      {
        id: 'effects-suggest',
        label: 'Suggérer effets',
        icon: Sparkles,
        prompt: 'Suggère des effets audio appropriés pour ce style musical',
        color: 'bg-blue-500'
      },
      {
        id: 'eq-advice',
        label: 'Conseils EQ',
        icon: Settings,
        prompt: 'Donne des conseils d\'égalisation pour les différents instruments',
        color: 'bg-green-500'
      }
    ],
    master: [
      {
        id: 'master-chain',
        label: 'Chaîne mastering',
        icon: Sparkles,
        prompt: 'Suggère une chaîne de mastering appropriée pour ce style musical',
        color: 'bg-orange-500'
      },
      {
        id: 'loudness-advice',
        label: 'Conseils loudness',
        icon: Volume2,
        prompt: 'Donne des conseils sur les niveaux de loudness et la dynamique finale',
        color: 'bg-red-500'
      },
      {
        id: 'final-polish',
        label: 'Finitions',
        icon: CheckCircle2,
        prompt: 'Suggère les dernières retouches pour finaliser cette production',
        color: 'bg-green-500'
      }
    ]
  };

  const currentActions = studioAIActions[studioMode] || [];

  // Métriques du projet
  const projectMetrics = React.useMemo(() => {
    const totalWords = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const totalSections = songSections.length;
    const completionScore = Math.min(100, (totalWords / 100) * 100);
    const structureScore = Math.min(100, (totalSections / 4) * 100);
    const aiUsageScore = Math.min(100, (aiHistory.length / 10) * 100);
    
    return {
      completion: completionScore,
      structure: structureScore,
      aiUsage: aiUsageScore,
      overall: Math.round((completionScore + structureScore + aiUsageScore) / 3)
    };
  }, [lyricsContent, songSections, aiHistory]);

  // Gestionnaire pour les actions IA
  const handleAIAction = useCallback(async (action: any) => {
    if (!isConfigured) return;
    
    try {
      let contextualPrompt = action.prompt;
      
      // Ajouter le contexte selon le mode
      if (studioMode === 'compose' && lyricsContent) {
        contextualPrompt += `\n\nContexte actuel:\n${lyricsContent}`;
      }
      
      contextualPrompt += `\n\nStyle: ${styleConfig.genres?.[0] || 'pop'}`;
      contextualPrompt += `\nTonalité: ${styleConfig.key || 'C'}`;
      contextualPrompt += `\nTempo: ${styleConfig.bpm || 120} BPM`;
      
      await onAIGenerate(contextualPrompt, action.id);
    } catch (error) {
      console.error('Erreur action IA:', error);
    }
  }, [isConfigured, studioMode, lyricsContent, styleConfig, onAIGenerate]);

  // Gestionnaire pour le chat
  const handleChatSubmit = useCallback(async () => {
    if (!chatInput.trim() || !isConfigured) return;
    
    try {
      await onAIGenerate(chatInput, 'chat');
      setChatInput('');
    } catch (error) {
      console.error('Erreur chat IA:', error);
    }
  }, [chatInput, isConfigured, onAIGenerate]);

  return (
    <div className="h-full flex flex-col bg-slate-800/30">
      {/* En-tête */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-white">Assistant IA Studio</h2>
              <p className="text-sm text-slate-400 capitalize">Mode {studioMode}</p>
            </div>
          </div>
          
          {/* Statut */}
          <div className="flex items-center gap-2">
            {isConfigured ? (
              <Badge variant="default" className="gap-1 bg-green-500">
                <CheckCircle2 className="h-3 w-3" />
                Actif
              </Badge>
            ) : (
              <Badge variant="destructive" className="gap-1">
                <AlertCircle className="h-3 w-3" />
                Config
              </Badge>
            )}
            {aiLoading && (
              <Badge variant="secondary" className="gap-1">
                <Sparkles className="h-3 w-3 animate-spin" />
                Génération...
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Score global */}
      <div className="border-b border-slate-700 bg-slate-800/20 p-4">
        <div className="text-center mb-3">
          <div className="text-2xl font-bold text-white">{projectMetrics.overall}%</div>
          <div className="text-sm text-slate-400">Score global du projet</div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-slate-400">Complétude</span>
            <span className="text-white">{projectMetrics.completion}%</span>
          </div>
          <Progress value={projectMetrics.completion} className="h-1" />
          
          <div className="flex justify-between text-sm">
            <span className="text-slate-400">Structure</span>
            <span className="text-white">{projectMetrics.structure}%</span>
          </div>
          <Progress value={projectMetrics.structure} className="h-1" />
          
          <div className="flex justify-between text-sm">
            <span className="text-slate-400">Usage IA</span>
            <span className="text-white">{projectMetrics.aiUsage}%</span>
          </div>
          <Progress value={projectMetrics.aiUsage} className="h-1" />
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700/50 m-2">
            <TabsTrigger value="actions" className="gap-1">
              <Zap className="h-3 w-3" />
              Actions
            </TabsTrigger>
            <TabsTrigger value="chat" className="gap-1">
              <MessageSquare className="h-3 w-3" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="history" className="gap-1">
              <History className="h-3 w-3" />
              Historique
            </TabsTrigger>
            <TabsTrigger value="config" className="gap-1">
              <Settings className="h-3 w-3" />
              Config
            </TabsTrigger>
          </TabsList>
          
          <div className="flex-1 overflow-hidden">
            {/* Actions spécialisées */}
            <TabsContent value="actions" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  {!isConfigured && (
                    <Card className="bg-orange-500/10 border-orange-500/20">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-2 text-orange-400 text-sm">
                          <AlertCircle className="h-4 w-4" />
                          Configurez votre IA pour utiliser ces fonctionnalités
                        </div>
                      </CardContent>
                    </Card>
                  )}
                  
                  <h3 className="text-sm font-medium text-white mb-3">
                    Actions pour le {studioMode === 'compose' ? 'composition' : 
                                   studioMode === 'arrange' ? 'arrangement' :
                                   studioMode === 'mix' ? 'mixage' : 'mastering'}
                  </h3>
                  
                  {currentActions.map((action) => {
                    const Icon = action.icon;
                    return (
                      <Button
                        key={action.id}
                        variant="outline"
                        size="sm"
                        onClick={() => handleAIAction(action)}
                        disabled={!isConfigured || aiLoading}
                        className={`w-full h-auto p-3 flex flex-col items-start gap-2 ${action.color} text-white border-slate-600 hover:border-slate-500`}
                      >
                        <div className="flex items-center gap-2 w-full">
                          <Icon className="h-4 w-4" />
                          <span className="font-medium">{action.label}</span>
                        </div>
                        <span className="text-xs opacity-80 text-left">
                          {action.prompt.substring(0, 80)}...
                        </span>
                      </Button>
                    );
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Chat IA */}
            <TabsContent value="chat" className="h-full m-0 p-4">
              <div className="h-full flex flex-col">
                <div className="flex-1 mb-4">
                  <ScrollArea className="h-full border border-slate-600 rounded-lg p-3 bg-slate-800/50">
                    {aiHistory.length === 0 ? (
                      <div className="text-center text-slate-400 py-8">
                        <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Commencez une conversation avec l'IA</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {aiHistory.slice(-10).map((message, index) => (
                          <div 
                            key={index}
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div 
                              className={`max-w-[80%] p-3 rounded-lg text-sm ${
                                message.role === 'user' 
                                  ? 'bg-blue-500 text-white' 
                                  : 'bg-slate-700 text-white border border-slate-600'
                              }`}
                            >
                              {message.content}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
                
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleChatSubmit()}
                    placeholder="Posez une question à l'IA..."
                    className="flex-1 px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white text-sm placeholder-slate-400"
                    disabled={!isConfigured || aiLoading}
                  />
                  <Button 
                    onClick={handleChatSubmit}
                    disabled={!isConfigured || aiLoading || !chatInput.trim()}
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Historique */}
            <TabsContent value="history" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-white">Historique des interactions</h3>
                  {aiHistory.length === 0 ? (
                    <div className="text-center text-slate-400 py-8">
                      <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>Aucun historique pour le moment</p>
                    </div>
                  ) : (
                    aiHistory.map((entry, index) => (
                      <Card key={index} className="bg-slate-700/50 border-slate-600">
                        <CardContent className="p-3">
                          <div className="text-xs text-slate-400 mb-1">
                            {entry.role === 'user' ? 'Vous' : 'IA'} • {new Date().toLocaleTimeString()}
                          </div>
                          <div className="text-sm text-white">
                            {entry.content.substring(0, 150)}
                            {entry.content.length > 150 && '...'}
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Configuration */}
            <TabsContent value="config" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <AiConfigMenu inlineMode={true} />
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};
