"use client";
import Link from "next/link"
import { <PERSON><PERSON>ronRight, UserPlus, Send } from "lucide-react" // Added Send
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from "@/components/ui/dialog" // Added Dialog components
import { Input } from "@/components/ui/input" // Added Input
import { Label } from "@/components/ui/label" // Added Label
import { useToast } from "@/hooks/use-toast" // Added useToast
import { useState } from "react" // Added useState
import { getSupabaseClient } from "@/lib/supabase/client" // Added Supabase client

interface ProfileForCard {
  id: string;
  name?: string | null;
  avatar_url?: string | null;
  username?: string | null;
}

interface MemberForCard {
  user_id: string;
  role: string | null;
  is_admin?: boolean; // Make optional if not always present
  joined_at: string;
  profiles: ProfileForCard | null;
  permissions?: string[] | null;
}

interface BandMembersProps {
  members: MemberForCard[] 
  bandId: string
  isAdmin: boolean
}

export function BandMembers({ members, bandId, isAdmin }: BandMembersProps) {
  const { toast } = useToast();
  const supabase = getSupabaseClient();
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState("member"); // Default role for invitation
  const [isInviting, setIsInviting] = useState(false);

  const handleSendInvitation = async () => {
    if (!inviteEmail) {
      toast({ title: "Erreur", description: "Veuillez entrer l'email de l'utilisateur à inviter.", variant: "destructive" });
      return;
    }
    setIsInviting(true);
    try {
      // 1. Find user by email to get their ID
      const { data: invitedUserData, error: userFetchError } = await supabase
        .from("profiles") // Assuming email is unique in 'profiles' or 'auth.users'
        .select("id")
        .eq("email", inviteEmail) // Or search in auth.users if profiles.email is not reliable
        .single();

      if (userFetchError || !invitedUserData) {
        throw new Error("Utilisateur non trouvé avec cet email.");
      }
      const invitedUserId = invitedUserData.id;

      // Check if user is already a member
      const { data: existingMember, error: checkError } = await supabase
        .from("band_members")
        .select("id")
        .eq("band_id", bandId)
        .eq("user_id", invitedUserId)
        .maybeSingle();

      if (checkError) throw checkError;
      if (existingMember) {
        toast({ title: "Déjà membre", description: "Cet utilisateur est déjà membre du groupe." }); // Removed variant: "info"
        setIsInviting(false);
        setIsInviteModalOpen(false);
        return;
      }
      
      // 2. Create invitation in band_invitations table
      const { error: invitationError } = await supabase
        .from("band_invitations")
        .insert({
          band_id: bandId,
          user_id: invitedUserId, // The user being invited
          invited_by: (await supabase.auth.getUser()).data.user?.id, // Current user sending invite
          role: inviteRole,
          status: "pending",
        });

      if (invitationError) throw invitationError;

      toast({ title: "Invitation envoyée", description: `Une invitation a été envoyée à ${inviteEmail} pour rejoindre le groupe en tant que ${inviteRole}.` });
      setIsInviteModalOpen(false);
      setInviteEmail("");
      setInviteRole("member");
    } catch (error: any) {
      toast({ title: "Erreur d'invitation", description: error.message, variant: "destructive" });
    } finally {
      setIsInviting(false);
    }
  };

  const getRoleBadgeVariant = (role: string | null) => { // Role can be null
    if (!role) return "outline";
    switch (role.toLowerCase()) {
      case "owner":
      case "admin": // Grouping admin and owner for similar visual weight
        return "default"
      case "admin":
        return "destructive"
      case "member":
        return "secondary"
      case "collaborator":
        return "outline"
      default:
        return "outline"
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Membres</CardTitle>
          <CardDescription>Membres actifs du groupe</CardDescription>
        </div>
        {members.length > 5 && (
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/bands/${bandId}/members`}>
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {members.slice(0, 5).map((member) => (
            <div key={member.user_id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage
                    src={member.profiles?.avatar_url || "/placeholder.svg?height=40&width=40&query=person"}
                  />
                  <AvatarFallback>{member.profiles?.name?.charAt(0) || "U"}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{member.profiles?.name}</p>
                  <p className="text-xs text-muted-foreground">
                    Depuis {new Date(member.joined_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <Badge variant={getRoleBadgeVariant(member.role)}>
                {member.role ? member.role.charAt(0).toUpperCase() + member.role.slice(1) : 'N/A'}
              </Badge>
            </div>
          ))}
        </div>

        {isAdmin && (
          <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full mt-4">
                <UserPlus className="mr-2 h-4 w-4" />
                Inviter un membre
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Inviter un membre</DialogTitle>
                <DialogDescription>
                  Entrez l'email de l'utilisateur et le rôle que vous souhaitez lui assigner dans le groupe.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2 pb-4">
                <div className="space-y-2">
                  <Label htmlFor="invite-email">Email de l'utilisateur</Label>
                  <Input 
                    id="invite-email" 
                    type="email" 
                    placeholder="<EMAIL>" 
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invite-role">Rôle dans le groupe</Label>
                  <Input 
                    id="invite-role" 
                    placeholder="Ex: Guitariste, Chanteur, Membre" 
                    value={inviteRole}
                    onChange={(e) => setInviteRole(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsInviteModalOpen(false)}>Annuler</Button>
                <Button onClick={handleSendInvitation} disabled={isInviting}>
                  {isInviting ? "Envoi en cours..." : <><Send className="mr-2 h-4 w-4" /> Envoyer l'invitation</>}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </CardContent>
    </Card>
  )
}
