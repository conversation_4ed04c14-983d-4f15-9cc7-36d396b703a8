import React from 'react';
import { EnhancedLyricsEditor } from '@/components/enhanced-lyrics-editor/EnhancedLyricsEditor';
import { ChordPlacement, ChordPlacementDisplay } from '@/types/composer';
import { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';
import { ResizablePanelGroup, ResizablePanel } from '@/components/ui/resizable';

interface StudioLayoutProps {
  quillRef?: React.RefObject<any>;
  value?: string;
  chords?: ChordPlacement[];
  onLyricsChange?: (lyrics: string) => void;
  onChordsChange?: (chords: ChordPlacement[]) => void;
}

export const StudioLayout: React.FC<StudioLayoutProps> = ({
  quillRef,
  value,
  chords,
  onLyricsChange,
  onChordsChange
}) => {
  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel>
        <EnhancedLyricsEditor
          quillRef={quillRef}
          value={value || ''}
          chords={chords || []}
          onChange={onLyricsChange || (() => {})}
          onChordsChange={onChordsChange || (() => {})}
          className="h-full flex-grow"
        />
      </ResizablePanel>
      <ResizablePanel>
        {/* Left panel content */}
      </ResizablePanel>
      <ResizablePanel>
        {/* Right panel content */}
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};
