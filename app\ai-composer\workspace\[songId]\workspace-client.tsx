'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Song } from '@/components/songs/song-schema';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { SongInfoHeaderComplete } from '@/components/ui/song-info-header';
import CreativeEditor from '@/src/components/editor/CreativeEditor';
import { Button } from '@/components/ui/button';
import SongVault from '@/components/songs/SongVault';
import { AiQuickActions } from '@/components/ui/ai-quick-actions';
import { LocalSongVersion } from '@/components/songs/hooks/useSongVersioning';
import { toast } from 'sonner';
import { updateSongLyrics } from '@/lib/actions/song.actions';
import { <PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';

// This type is defined inline in AiQuickActionsProps, so we define it here to match.
type AIConfig = { provider: string; model: string; temperature: number; };

interface WorkspaceClientProps {
  song: Song;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function WorkspaceClient({ song }: WorkspaceClientProps) {
  const router = useRouter();
  
  const editorRef = useRef<any>(null);
  const [versions, setVersions] = useState<LocalSongVersion[]>([]);
  const [activeVersionId, setActiveVersionId] = useState<string | null>(null);
  const [aiConfig, setAiConfig] = useState<AIConfig>({ model: 'claude-3', provider: 'anthropic', temperature: 0.7 });
  const [lyrics, setLyrics] = useState(song.lyrics || '');
  const debouncedLyrics = useDebounce(lyrics, 1500);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingUpdate, setIsLoadingUpdate] = useState(false);
    const [isLoadingDeleteVersion, setIsLoadingDeleteVersion] = useState(false);
  const initialLyrics = useRef(song.lyrics || '');

  // Loading states for AI actions
  const [isAiLoading, setIsAiLoading] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [lastAiResult, setLastAiResult] = useState<string | null>(null);

  const generateAiContent = async (prompt: string): Promise<string> => {
    console.warn('generateAiContent is a mock implementation.', prompt);
    // In a real scenario, you would call your AI service here.
    return Promise.resolve(`AI-generated content for: ${prompt}`);
  };

  const handleAiConfigChange = (cfg: Partial<AIConfig>) => {
    setAiConfig(prev => ({ ...prev, ...cfg }));
  };

  const handleLoadVersion = async (versionId: string) => {
    console.log('Load version:', versionId);
    // Here you would fetch version content and set it to lyrics state
    // setActiveVersionId(versionId);
  };
  const handleDeleteVersion = async (versionId: string) => console.log('Delete version:', versionId);
  const handleSaveNewVersion = async (name: string, notes: string, isMajor: boolean) => {
    console.log('Save new version:', { name, notes, isMajor });
    toast.success('New version saved successfully!');
  };
  const handleUpdateVersion = async (versionId: string, newName: string, newNotes: string) => {
    console.log('Update version:', { versionId, newName, newNotes });
  };

  const runAiAction = async (prompt: string, actionType: 'generate' | 'correct' | 'translate' | 'chords') => {
    setIsAiLoading(true);
    setAiError(null);
    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, ...aiConfig }),
      });

      if (!response.ok) {
        throw new Error(`Erreur de l'API : ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.text) {
        throw new Error("La réponse de l'IA est vide.");
      }
      
      setLastAiResult(data.text);

      // Mettre à jour l'éditeur en fonction de l'action
      switch (actionType) {
        case 'generate':
          setLyrics(prevContent => `${prevContent}\n\n${data.text}`);
          break;
        case 'correct':
        case 'translate':
        case 'chords':
          setLyrics(data.text);
          break;
        default:
          setLyrics(data.text);
      }

      toast.success(`L'action "${actionType}" a été complétée.`);
    } catch (err: any) {
      setAiError(err.message);
      toast.error(err.message);
    } finally {
      setIsAiLoading(false);
    }
  };

  const getEditorText = () => {
    return lyrics;
  };

  const handleGenerate = (customPrompt?: string) => {
    const prompt = customPrompt || `Continue d\'écrire les paroles suivantes :\n\n${getEditorText()}`;
    generateAiContent(prompt);
  };

  const handleCorrect = () => {
    const prompt = `Corrige les fautes d\'orthographe et de grammaire dans le texte suivant sans en altérer le sens ou le style :\n\n${getEditorText()}`;
    generateAiContent(prompt);
  };

  const handleTranslate = (lang: string) => {
    const prompt = `Traduis le texte suivant en ${lang} :\n\n${getEditorText()}`;
    generateAiContent(prompt);
  };

    const saveChanges = useCallback(async (newLyrics: string) => {
    setSaveStatus('saving');
    try {
      const result = await updateSongLyrics({ songId: song.id, lyrics: newLyrics });
      if (result.error) {
        throw new Error(result.error);
      }
      setSaveStatus('saved');
      initialLyrics.current = newLyrics;
    } catch (error) {
      console.error(error);
      setSaveStatus('error');
      toast.error('Erreur de sauvegarde.');
    }
  }, [song.id]);

  useEffect(() => {
    if (debouncedLyrics !== initialLyrics.current) {
      saveChanges(debouncedLyrics);
    }
  }, [debouncedLyrics, saveChanges]);

const handleSuggestChords = () => {
    const prompt = `Analyse les paroles suivantes et suggère une progression d\'accords simple et efficace. Place les accords entre crochets, comme [Am] ou [G], juste avant la syllabe où l\'accord doit être joué. Voici les paroles :\n\n${getEditorText()}`;
    generateAiContent(prompt);
  };

  return (
    <div className="h-screen w-full flex flex-col bg-background">
                <div className="flex items-center gap-4 p-4 border-b">
          <Button variant="ghost" size="icon" onClick={() => router.push('/ai-composer')}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-grow">
            <SongInfoHeaderComplete song={song} />
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground w-40 justify-end">
            {saveStatus === 'saving' && <><Loader2 className="h-4 w-4 animate-spin mr-1" /><span>Sauvegarde...</span></>}
            {saveStatus === 'saved' && <span>Enregistré</span>}
            {saveStatus === 'error' && <span className="text-destructive">Erreur</span>}
          </div>
        </div>
                <ResizablePanelGroup direction="horizontal" className="flex-grow">
            <ResizablePanel defaultSize={65}>
                <div className="flex-1 flex flex-col relative">
                  <CreativeEditor
                    ref={editorRef}
                    value={lyrics}
                    onChange={setLyrics}
                  />
                </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={35}>
                <ResizablePanelGroup direction="vertical">
                    <ResizablePanel defaultSize={40}>
                        <div className="flex h-full items-center justify-center p-6">
                            <span className="font-semibold">Lecteur Audio (à venir)</span>
                        </div>
                    </ResizablePanel>
                    <ResizableHandle withHandle />
                    <ResizablePanel defaultSize={60}>
                        <div className="h-full p-2">
                            <Tabs defaultValue="versions" className="h-full flex flex-col">
                                <TabsList className="grid w-full grid-cols-2">
                                    <TabsTrigger value="versions">Versions</TabsTrigger>
                                    <TabsTrigger value="ai">Assistant IA</TabsTrigger>
                                </TabsList>
                                <TabsContent value="versions" className="flex-grow overflow-auto">
                                   {song.id && <SongVault
                                     songId={song.id}
                                     versions={versions}
                                     activeVersionId={activeVersionId}
                                     onLoadVersion={handleLoadVersion}
                                     onDeleteVersion={handleDeleteVersion}
                                     onSaveNewVersion={handleSaveNewVersion}
                                     onUpdateVersion={handleUpdateVersion}
                                     isLoadingDeleteVersion={isLoadingDeleteVersion}
                                     isLoadingSaveVersion={isSaving}
                                     isLoadingUpdateVersion={isLoadingUpdate}
                                   />}
                                </TabsContent>
                                <TabsContent value="ai" className="flex-grow">
                                    <AiQuickActions
                                      onGenerate={handleGenerate}
                                      onCorrect={handleCorrect}
                                      onTranslate={handleTranslate}
                                      onSuggestChords={handleSuggestChords}
                                      loading={isAiLoading}
                                      error={aiError || undefined}
                                      lastResult={lastAiResult || undefined}
                                      aiConfig={aiConfig}
                                      setAiConfig={handleAiConfigChange}
                                    />
                                </TabsContent>
                            </Tabs>
                        </div>
                    </ResizablePanel>
                </ResizablePanelGroup>
            </ResizablePanel>
        </ResizablePanelGroup>
    </div>
  );
}
