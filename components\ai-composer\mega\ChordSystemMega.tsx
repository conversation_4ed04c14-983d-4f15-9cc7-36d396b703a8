'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Guitar, Piano, Plus, X, Play, Volume2, Music, Pause,
  Hash, Target, Layers, RotateCcw, Wand2, Copy, Search,
  Filter, Settings, Shuffle, Download, Upload, Zap
} from 'lucide-react';

// Import des données d'accords et du player MIDI
import { MidiChordPlayer, ChordPosition } from '@/lib/chords/midi-chord-player';

interface ChordSystemMegaProps {
  selectedSection: string;
  sections: any[];
  setSections: (sections: any[]) => void;
  styleConfig: any;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
}

interface Chord {
  id: string;
  name: string;
  position: number;
  instrument: string;
  chordData: any;
  positionIndex: number;
}

// Instruments disponibles avec leurs fichiers de données
const AVAILABLE_INSTRUMENTS = [
  { id: 'guitar', name: 'Guitare', icon: Guitar, file: 'guitar_complete_extended.json' },
  { id: 'guitar_dadgad', name: 'Guitare DADGAD', icon: Guitar, file: 'guitar_dadgad_tuning.json' },
  { id: 'guitar_drop_d', name: 'Guitare Drop D', icon: Guitar, file: 'guitar_drop_d_complete.json' },
  { id: 'guitar_open_g', name: 'Guitare Open G', icon: Guitar, file: 'guitar_open_g_complete.json' },
  { id: 'ukulele', name: 'Ukulélé', icon: Music, file: 'ukulele_complete_all_keys.json' },
  { id: 'mandolin', name: 'Mandoline', icon: Music, file: 'mandolin_gdae_tuning.json' },
  { id: 'banjo', name: 'Banjo', icon: Music, file: 'banjo_5string_complete.json' },
  { id: 'piano', name: 'Piano', icon: Piano, file: 'piano.json' }
];

// Progressions d'accords populaires par genre
const CHORD_PROGRESSIONS = {
  'Pop/Rock': {
    'I-V-vi-IV': { chords: ['C', 'G', 'Am', 'F'], description: 'Progression pop classique' },
    'vi-IV-I-V': { chords: ['Am', 'F', 'C', 'G'], description: 'Progression émotionnelle' },
    'I-vi-IV-V': { chords: ['C', 'Am', 'F', 'G'], description: 'Progression vintage' },
    'vi-V-IV-V': { chords: ['Am', 'G', 'F', 'G'], description: 'Progression moderne' }
  },
  'Jazz': {
    'ii-V-I': { chords: ['Dm7', 'G7', 'Cmaj7'], description: 'Cadence jazz classique' },
    'I-vi-ii-V': { chords: ['Cmaj7', 'Am7', 'Dm7', 'G7'], description: 'Cycle des quintes' },
    'iii-vi-ii-V': { chords: ['Em7', 'Am7', 'Dm7', 'G7'], description: 'Progression jazz moderne' }
  },
  'Blues': {
    '12-bar blues': { chords: ['C7', 'C7', 'C7', 'C7', 'F7', 'F7', 'C7', 'C7', 'G7', 'F7', 'C7', 'G7'], description: 'Blues 12 mesures' },
    'Quick change': { chords: ['C7', 'F7', 'C7', 'C7'], description: 'Blues avec changement rapide' }
  },
  'Folk': {
    'I-IV-V': { chords: ['C', 'F', 'G'], description: 'Progression folk basique' },
    'vi-IV-I-V': { chords: ['Am', 'F', 'C', 'G'], description: 'Progression folk moderne' },
    'I-V-vi-IV': { chords: ['G', 'D', 'Em', 'C'], description: 'Progression country' }
  }
};

export const ChordSystemMega: React.FC<ChordSystemMegaProps> = ({
  selectedSection,
  sections,
  setSections,
  styleConfig,
  onAIGenerate
}) => {

  const [selectedInstrument, setSelectedInstrument] = useState('guitar');
  const [searchChord, setSearchChord] = useState('');
  const [selectedChord, setSelectedChord] = useState<string | null>(null);
  const [selectedPosition, setSelectedPosition] = useState(0);
  const [chordData, setChordData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [midiPlayer] = useState(() => new MidiChordPlayer());
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedGenre, setSelectedGenre] = useState('Pop/Rock');
  const [chordViewMode, setChordViewMode] = useState<'library' | 'progressions' | 'builder'>('library');

  const currentSection = sections.find(s => s.id === selectedSection);
  const sectionChords = currentSection?.chords || [];

  // Charger les données d'accords
  useEffect(() => {
    const loadChordData = async () => {
      setLoading(true);
      try {
        const instrument = AVAILABLE_INSTRUMENTS.find(i => i.id === selectedInstrument);
        if (instrument) {
          const response = await fetch(`/lib/chords/${instrument.file}`);
          const data = await response.json();
          setChordData(data);
        }
      } catch (error) {
        console.error('Erreur chargement accords:', error);
      } finally {
        setLoading(false);
      }
    };

    loadChordData();
  }, [selectedInstrument]);

  // Filtrer les accords selon la recherche
  const filteredChords = useMemo(() => {
    if (!chordData) return [];

    const chords = Object.keys(chordData);
    return chords.filter(chord =>
      chord.toLowerCase().includes(searchChord.toLowerCase())
    ).sort();
  }, [chordData, searchChord]);

  // Obtenir les positions d'un accord
  const getChordPositions = useCallback((chordName: string) => {
    if (!chordData || !chordData[chordName]) return [];
    return chordData[chordName];
  }, [chordData]);

  // Jouer un accord avec MIDI
  const handlePlayChord = useCallback(async (chordName: string, positionIndex: number = 0) => {
    if (!chordData || !chordData[chordName]) return;

    const positions = chordData[chordName];
    const position = positions[positionIndex];

    if (position && position.midi) {
      setIsPlaying(true);
      try {
        await midiPlayer.playChord(position);
        setTimeout(() => setIsPlaying(false), 2000);
      } catch (error) {
        console.error('Erreur lecture MIDI:', error);
        setIsPlaying(false);
      }
    }
  }, [chordData, midiPlayer]);

  // Ajouter un accord à la section
  const handleAddChord = useCallback((chordName: string, positionIndex: number = 0) => {
    if (!currentSection || !chordData) return;

    const positions = chordData[chordName];
    if (!positions || !positions[positionIndex]) return;

    const newChord: Chord = {
      id: `chord-${Date.now()}`,
      name: chordName,
      position: sectionChords.length,
      instrument: selectedInstrument,
      chordData: positions[positionIndex],
      positionIndex
    };

    const updatedSections = sections.map(section =>
      section.id === selectedSection
        ? { ...section, chords: [...sectionChords, newChord] }
        : section
    );

    setSections(updatedSections);
  }, [currentSection, selectedSection, sections, setSections, sectionChords, selectedInstrument, chordData]);

  // Supprimer un accord
  const handleRemoveChord = useCallback((chordId: string) => {
    const updatedSections = sections.map(section =>
      section.id === selectedSection
        ? { ...section, chords: sectionChords.filter(c => c.id !== chordId) }
        : section
    );

    setSections(updatedSections);
  }, [selectedSection, sections, setSections, sectionChords]);

  // Ajouter une progression d'accords
  const handleAddProgression = useCallback((progressionName: string) => {
    const chords = CHORD_PROGRESSIONS[progressionName];
    chords.forEach((chordName, index) => {
      setTimeout(() => handleAddChord(chordName, sectionChords.length + index), index * 100);
    });
  }, [handleAddChord, sectionChords.length]);

  // Actions IA pour les accords
  const handleChordAI = useCallback(async (action: string) => {
    const sectionType = currentSection?.type || 'verse';
    const key = styleConfig.key || 'C';
    const genre = styleConfig.genres?.[0] || 'pop';
    
    let prompt = '';
    
    switch (action) {
      case 'suggest':
        prompt = `Suggère une progression d'accords pour un ${sectionType} en ${key} majeur dans le style ${genre}. Donne 4-6 accords avec leur fonction harmonique.`;
        break;
      case 'improve':
        prompt = `Améliore cette progression d'accords : ${sectionChords.map(c => c.name).join(' - ')}. Suggère des variantes plus intéressantes en ${key} majeur.`;
        break;
      case 'transpose':
        prompt = `Transpose cette progression d'accords : ${sectionChords.map(c => c.name).join(' - ')} vers la tonalité de ${key} majeur.`;
        break;
      case 'analyze':
        prompt = `Analyse cette progression d'accords : ${sectionChords.map(c => c.name).join(' - ')}. Explique les fonctions harmoniques et l'effet émotionnel.`;
        break;
    }
    
    await onAIGenerate(prompt, 'chords');
  }, [currentSection, styleConfig, sectionChords, onAIGenerate]);

  return (
    <div className="h-full flex flex-col bg-slate-900/30">
      {/* En-tête */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Guitar className="h-6 w-6 text-blue-400" />
            <div>
              <h3 className="text-lg font-medium text-white">Système d'Accords Complet</h3>
              <p className="text-sm text-slate-400">
                {currentSection?.title || 'Aucune section'} • {filteredChords.length} accords disponibles
              </p>
            </div>
          </div>

          {/* Contrôles de lecture */}
          <div className="flex items-center gap-2">
            <Button
              variant={isPlaying ? "default" : "outline"}
              size="sm"
              disabled={!selectedChord}
              onClick={() => selectedChord && handlePlayChord(selectedChord, selectedPosition)}
              className="gap-1"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              {isPlaying ? 'Lecture...' : 'Jouer'}
            </Button>
          </div>
        </div>

        {/* Sélecteur d'instrument */}
        <div className="flex items-center gap-2 mb-4">
          <span className="text-sm text-slate-400 mr-2">Instrument:</span>
          {AVAILABLE_INSTRUMENTS.map((instrument) => {
            const Icon = instrument.icon;
            return (
              <Button
                key={instrument.id}
                variant={selectedInstrument === instrument.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedInstrument(instrument.id)}
                className="gap-1"
                disabled={loading}
              >
                <Icon className="h-4 w-4" />
                {instrument.name}
              </Button>
            );
          })}
        </div>

        {/* Modes de vue */}
        <Tabs value={chordViewMode} onValueChange={(value: any) => setChordViewMode(value)} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-slate-700/50">
            <TabsTrigger value="library" className="gap-1">
              <Music className="h-4 w-4" />
              Bibliothèque
            </TabsTrigger>
            <TabsTrigger value="progressions" className="gap-1">
              <Layers className="h-4 w-4" />
              Progressions
            </TabsTrigger>
            <TabsTrigger value="builder" className="gap-1">
              <Wand2 className="h-4 w-4" />
              Constructeur
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Accords de la section */}
      <div className="border-b border-slate-700 bg-slate-800/30 p-3">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-white">Accords de la section</h4>
          <Badge variant="outline" className="text-xs">
            {sectionChords.length} accords
          </Badge>
        </div>
        
        {sectionChords.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {sectionChords.map((chord) => (
              <div key={chord.id} className="flex items-center gap-1 bg-slate-700 rounded-lg p-2">
                <span className="text-white font-medium">{chord.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveChord(chord.id)}
                  className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-slate-400 py-4">
            <Music className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Aucun accord dans cette section</p>
          </div>
        )}
      </div>

      {/* Actions IA pour accords */}
      <div className="border-b border-slate-700 bg-slate-800/20 p-3">
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-400 mr-2">IA Accords:</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleChordAI('suggest')}
            className="gap-1 bg-blue-500 text-white border-slate-600"
          >
            <Wand2 className="h-3 w-3" />
            Suggérer
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleChordAI('improve')}
            className="gap-1 bg-green-500 text-white border-slate-600"
            disabled={sectionChords.length === 0}
          >
            <Target className="h-3 w-3" />
            Améliorer
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleChordAI('analyze')}
            className="gap-1 bg-purple-500 text-white border-slate-600"
            disabled={sectionChords.length === 0}
          >
            <Hash className="h-3 w-3" />
            Analyser
          </Button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-4">
            
            {/* Recherche d'accords */}
            <div>
              <h4 className="text-sm font-medium text-white mb-2">Bibliothèque d'accords</h4>
              <Input
                value={searchChord}
                onChange={(e) => setSearchChord(e.target.value)}
                placeholder="Rechercher un accord..."
                className="bg-slate-800 border-slate-600 text-white"
              />
            </div>

            {/* Liste des accords */}
            <div className="grid grid-cols-2 gap-2">
              {filteredChords.map((chordName) => {
                const chordData = CHORD_LIBRARY[selectedInstrument][chordName];
                return (
                  <Card 
                    key={chordName}
                    className={`cursor-pointer transition-all bg-slate-700/50 border-slate-600 hover:border-slate-500 ${
                      selectedChord === chordName ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedChord(selectedChord === chordName ? null : chordName)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-white">{chordName}</div>
                          <div className="text-xs text-slate-400">
                            {chordData.notes.join(' - ')}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddChord(chordName);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {/* Diagramme guitare */}
                      {selectedInstrument === 'guitar' && selectedChord === chordName && chordData.fingering && (
                        <div className="mt-2 pt-2 border-t border-slate-600">
                          <div className="text-xs text-slate-400 mb-1">Doigtés:</div>
                          <div className="flex gap-1">
                            {chordData.fingering.map((fret, index) => (
                              <div key={index} className="text-center">
                                <div className="w-6 h-6 bg-slate-600 rounded text-xs flex items-center justify-center text-white">
                                  {fret}
                                </div>
                                <div className="text-xs text-slate-500 mt-1">{index + 1}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Progressions populaires */}
            <div>
              <h4 className="text-sm font-medium text-white mb-2">Progressions populaires</h4>
              <div className="space-y-2">
                {Object.entries(CHORD_PROGRESSIONS).map(([name, chords]) => (
                  <Card key={name} className="bg-slate-700/50 border-slate-600">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-white text-sm">{name}</div>
                          <div className="text-xs text-slate-400">{chords.join(' - ')}</div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddProgression(name)}
                          className="gap-1"
                        >
                          <Plus className="h-3 w-3" />
                          Ajouter
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
