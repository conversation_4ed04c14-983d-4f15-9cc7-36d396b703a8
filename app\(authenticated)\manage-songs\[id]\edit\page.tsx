"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createBrowserClient } from '@supabase/ssr';
import { format } from 'date-fns'; 
// SupabaseClient is imported by @supabase/ssr, or ensure one consistent import
import type { SupabaseClient } from '@supabase/supabase-js'; 
import { useToast } from "@/hooks/use-toast"; 

// Using relative path to rule out alias issues
import Link from 'next/link'; // Import Link
import SongForm from '../../../../../components/songs/SongForm';
import { SongFormValues, songSchema } from '../../../../../components/songs/song-schema';
import { Song } from '@/types'; 
import { Button } from "@/components/ui/button";
import { Loader2 as LoadingSpinner, ExternalLink } from 'lucide-react'; // Import ExternalLink

// Helper for safe JSON parsing
const parseArrayField = (fieldData: any, defaultValue: any[] = []): any[] => {
  if (Array.isArray(fieldData)) {
    return fieldData; // Already an array
  }
  if (typeof fieldData === 'string') {
    try {
      const parsed = JSON.parse(fieldData);
      return Array.isArray(parsed) ? parsed : defaultValue;
    } catch (e) {
      // console.warn('Failed to parse JSON string for array field:', fieldData, e);
      return defaultValue; // Parsing failed
    }
  }
  return defaultValue; // Not an array or string, or other unexpected type
};

const safeJsonParse = (jsonString: string | null | undefined, defaultValue: any[] = []): any[] => {
  if (!jsonString || typeof jsonString !== 'string') {
    return defaultValue;
  }
  try {
    const parsed = JSON.parse(jsonString);
    return Array.isArray(parsed) ? parsed : defaultValue;
  } catch (e) {
    // console.warn('Failed to parse JSON string:', jsonString, e);
    return defaultValue;
  }
};

// Helper function to format an array for PostgreSQL text array literal
function formatArrayToPostgresTextArrayLiteral(arr: string[] | string | undefined | null): string | null {
  if (arr === undefined || arr === null) {
    return null;
  }

  let arrayToFormat: string[];

  if (typeof arr === 'string') {
    // If it's already a PG array literal like "{foo,bar}", pass it through.
    // This is a bit risky as it assumes valid PG array format.
    if (arr.startsWith('{') && arr.endsWith('}')) {
      // Basic validation: check if it's not just "{}" which should be null if empty
      if (arr === '{}') return null;
      return arr;
    }
    // Try to parse it as a JSON string array, e.g., "[\"rock\",\"pop\"]"
    try {
      const parsed = JSON.parse(arr);
      if (Array.isArray(parsed)) {
        arrayToFormat = parsed.map(String); // Ensure all elements are strings
      } else {
        // If parsed but not an array, treat as a single element array
        arrayToFormat = [String(parsed)];
      }
    } catch (e) {
      // Not a JSON string, treat as a single element to be put into an array
      arrayToFormat = [arr];
    }
  } else if (Array.isArray(arr)) {
    arrayToFormat = arr.map(String); // Ensure all elements are strings
  } else {
    // Unrecognized type, should not happen with TypeScript but as a fallback
    return null;
  }

  if (arrayToFormat.length === 0) {
    return null;
  }

  // Ensure all items are properly quoted and escaped
  return `{${arrayToFormat.map(item => `"${String(item).replace(/"/g, '""')}"`).join(',')}}`;
}

// Helper function to upload file to Supabase Storage
async function uploadFileToSupabase(
  supabaseClient: SupabaseClient,
  file: File,
  bucket: string,
  userId: string | null,
  toastFn: (options: { title: string; description: string; variant?: 'default' | 'destructive' }) => void // Removed 'success' variant
): Promise<string | null> {
  if (!userId) {
    console.error("[uploadFileToSupabase] User ID is null. Upload will be placed in a generic path.");
  }

  const timestamp = Date.now();
  const sanitizedFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9._-]/g, '');
  const filePath = `${userId || 'public_uploads'}/${timestamp}_${sanitizedFileName}`;

  console.log(`[uploadFileToSupabase] Attempting to upload ${file.name} to bucket '${bucket}' at path '${filePath}'`);

  const { error: uploadError } = await supabaseClient.storage
    .from(bucket)
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: true,
    });

  if (uploadError) {
    console.error(`[uploadFileToSupabase] Error uploading ${file.name} to ${bucket}:`, uploadError);
    toastFn({
      title: 'Erreur de téléversement',
      description: `Échec du téléversement de ${file.name}: ${uploadError.message}`,
      variant: 'destructive',
    });
    return null;
  }

  const { data: publicUrlData } = supabaseClient.storage
    .from(bucket)
    .getPublicUrl(filePath);

  if (!publicUrlData || !publicUrlData.publicUrl) {
    console.error(`[uploadFileToSupabase] Error getting public URL for ${filePath}`);
    toastFn({
      title: 'Erreur d\'URL publique',
      description: `Impossible d'obtenir l'URL publique pour ${file.name} après le téléversement.`,
      variant: 'destructive',
    });
    return null;
  }
  console.log(`[uploadFileToSupabase] File ${file.name} uploaded successfully. Public URL: ${publicUrlData.publicUrl}`);
  return publicUrlData.publicUrl;
}

// Helper function to generate a slug
const slugify = (text: string): string => {
  if (!text) return '';
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(/[^\w-]+/g, '') // Remove all non-word chars
    .replace(/--+/g, '-'); // Replace multiple - with single -
};

interface Album {
  id: string;
  title: string;
}

export default function EditSongPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const [songData, setSongData] = useState<Partial<SongFormValues & { slug?: string | null }> | null>(null);
  const [previewSlug, setPreviewSlug] = useState<string>('');
  const [isLoadingSongData, setIsLoadingSongData] = useState(true);
  const [userAlbums, setUserAlbums] = useState<Album[]>([]);
  const [isLoadingAlbums, setIsLoadingAlbums] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [coverArtFile, setCoverArtFile] = useState<File | null>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setCurrentUserId(user.id);
      } else {
        toast({ title: 'Erreur Authentification', description: 'Impossible de récupérer l\'utilisateur actuel.', variant: 'destructive' });
      }
    };
    fetchCurrentUser();
  }, [supabase.auth, toast]);

  const fetchSong = useCallback(async (songId: string) => {
    setIsLoadingSongData(true);
    console.log(`[EditSongPage] Fetching song with ID: ${songId}`);
    
    const { data, error } = await supabase
      .from('songs')
      .select(`
        *,
        albums (id, title)
      `)
      .eq('id', songId)
      .single();

    if (error) {
      console.error('[EditSongPage] Error fetching song:', error);
      toast({ title: 'Erreur de chargement', description: `Impossible de charger les données de la chanson: ${error.message}`, variant: 'destructive' });
      setSongData(null);
    } else if (data) {
      console.log('[EditSongPage] Raw data from Supabase for song:', data);
      const transformedData = {
        id: data.id, // Ensure ID is explicitly included
        ...(({ audio_url, cover_art_url, ...rest }) => rest)(data), // Spread data excluding audio_url and cover_art_url
        // Ensure audio_url and cover_art_url are explicitly typed as string or null, overriding any from ...data
        audio_url: typeof data.audio_url === 'string' ? data.audio_url : null,
        cover_art_url: typeof data.cover_art_url === 'string' ? data.cover_art_url : null,
        // Single TEXT fields from DB. Ensure they are null if empty or not a string.
        genre: typeof data.genre === 'string' && data.genre.trim() !== '' ? data.genre.trim() : null,
        subgenre: typeof data.subgenre === 'string' && data.subgenre.trim() !== '' ? data.subgenre.trim() : null, // Assuming subgenre is also TEXT
        musical_key: typeof data.musical_key === 'string' && data.musical_key.trim() !== '' ? data.musical_key.trim() : null,
        time_signature: typeof data.time_signature === 'string' && data.time_signature.trim() !== '' ? data.time_signature.trim() : null,
        // Array fields from DB (TEXT[])
        moods: parseArrayField(data.moods, []),
        tags: parseArrayField(data.tags, []),
        themes: parseArrayField(data.themes, []),
        instruments: parseArrayField(data.instruments, []),
        writers: parseArrayField(data.writers, []),
        producers: parseArrayField(data.producers, []),
        featured_artists: parseArrayField(data.featured_artists, []),
        gear_used: parseArrayField(data.gear_used, []),
        sample_credits: safeJsonParse(data.sample_credits, []), // sample_credits is JSONB

        album_id: data.albums?.id ?? data.album_id ?? null,
        // Ensure numeric fields are numbers, default to 0 or null
        bpm: data.bpm ?? null,
        duration_ms: data.duration_ms ?? null, // Allow null for optional numbers
        capo: data.capo ?? null,
        tuning_frequency: data.tuning_frequency ?? null,
        // Ensure text fields are strings, default to empty or null based on schema
        lyrics: data.lyrics ?? '',
        song_story: data.song_story || data.story || '', // Map story to song_story
        description: data.description ?? '',
        // composer_name: data.composer_name ?? '', // Already commented out for DB save, keep consistent for form load
        record_label: data.record_label ?? '',
        isrc_code: data.isrc_code ?? null,
        upc_code: data.upc_code || data.upc || '',
        lyrics_language: typeof data.lyrics_language === 'string' && data.lyrics_language.trim() !== '' ? data.lyrics_language.trim() : null,
        status: typeof data.status === 'string' && data.status.trim() !== '' ? data.status.trim() : null,
        // Boolean fields
        is_public: data.is_public ?? false,
        is_archived: data.is_archived ?? false,
        is_favorite: data.is_favorite ?? false,
        is_incomplete: data.is_incomplete ?? true,
        is_cover: data.is_cover ?? false,
        is_instrumental: data.is_instrumental ?? false,
        is_explicit: data.is_explicit ?? false,
        // Dates (should be processed by SongForm's useMemo, pass as is or null)
        release_date: data.release_date ? new Date(data.release_date) : null,
        // Audio and image URLs are handled at the beginning of this object to ensure correct typing (string | null) // Map image_url to cover_art_url
        // Slug for preview
        slug: data.slug ?? slugify(data.title ?? ''),
      };
      console.log('[EditSongPage] Transformed data (to be set as songData):', transformedData);
      setSongData(transformedData);
      setPreviewSlug(transformedData.slug || ''); // Update preview slug
    } else {
      setSongData(null); // No data found
    }
    setIsLoadingSongData(false);
  }, [supabase, toast]);

  const fetchUserAlbums = useCallback(async () => {
    if (!currentUserId) return;
    setIsLoadingAlbums(true);
    const { data, error } = await supabase
      .from('albums')
      .select('id, title')
      .eq('user_id', currentUserId);

    if (error) {
      console.error('Error fetching user albums:', error);
      toast({ title: 'Erreur', description: 'Impossible de charger les albums.', variant: 'destructive' });
    } else {
      setUserAlbums(data || []);
    }
    setIsLoadingAlbums(false);
  }, [supabase, currentUserId, toast]);

  useEffect(() => {
    if (params.id) {
      fetchSong(params.id);
    }
  }, [params.id, fetchSong]);

  useEffect(() => {
    if (currentUserId) {
      fetchUserAlbums();
    }
  }, [currentUserId, fetchUserAlbums]);

  const handleFormSubmit = useCallback(async (formData: SongFormValues, localCoverArtFile?: File | null, localAudioFile?: File | null) => {
    console.log('[EditSongPage] handleFormSubmit called. FormData:', formData, 'CoverArt:', localCoverArtFile, 'AudioFile:', localAudioFile);
    setIsSubmitting(true);
    console.log('[Edit Submit] Initial form values:', formData);
    console.log('[Edit Submit] Cover art file:', localCoverArtFile);
    console.log('[Edit Submit] Audio file:', localAudioFile);

    if (!params.id) {
      toast({ title: 'Erreur', description: 'ID de la chanson manquant.', variant: 'destructive' });
      setIsSubmitting(false);
      return;
    }

    if (!currentUserId) {
      toast({ title: 'Erreur Authentification', description: 'Utilisateur non identifié. Impossible de sauvegarder.', variant: 'destructive' });
      setIsSubmitting(false);
      return;
    }

    let finalCoverArtUrl = formData.cover_art_url;
    let finalAudioUrl = formData.audio_url;

    // Handle Cover Art Upload/Clear
    if (localCoverArtFile) {
      console.log('[Edit Submit] Uploading new cover art...');
      const uploadedCoverUrl = await uploadFileToSupabase(supabase, localCoverArtFile, 'song-covers', currentUserId, toast);
      if (uploadedCoverUrl) {
        finalCoverArtUrl = uploadedCoverUrl;
      } else {
        console.warn('[Edit Submit] Cover art upload failed. Using original or null.');
        finalCoverArtUrl = songData?.cover_art_url || null;
      }
    } else if (formData.cover_art_url === '' || formData.cover_art_url === null) {
      console.log('[Edit Submit] Cover art cleared by user.');
      finalCoverArtUrl = null;
      // TODO: Optionally delete songData?.cover_art_url from Supabase storage if it existed
    }

    // Handle Audio File Upload/Clear
    if (localAudioFile) {
      console.log('[Edit Submit] Uploading new audio file...');
      const uploadedAudioUrl = await uploadFileToSupabase(supabase, localAudioFile, 'audio', currentUserId, toast);
      if (uploadedAudioUrl) {
        finalAudioUrl = uploadedAudioUrl;
      } else {
        console.warn('[Edit Submit] Audio file upload failed. Using original or null.');
        finalAudioUrl = songData?.audio_url || null;
      }
    } else if (formData.audio_url === '' || formData.audio_url === null) {
      console.log('[Edit Submit] Audio file cleared by user.');
      finalAudioUrl = null;
      // TODO: Optionally delete songData?.audio_url from Supabase storage if it existed
    }

    // Final check to prevent saving blob URLs if they somehow persisted through failed uploads
    if (finalCoverArtUrl?.startsWith('blob:')) {
        console.warn('[Edit Submit] Cover art URL is a blob post-upload attempt; reverting to original or null.');
        finalCoverArtUrl = songData?.cover_art_url || null;
    }
    if (finalAudioUrl?.startsWith('blob:')) {
        console.warn('[Edit Submit] Audio URL is a blob post-upload attempt; reverting to original or null.');
        finalAudioUrl = songData?.audio_url || null;
    }

    const updatePayload: { [key: string]: any } = {
      // Single text fields
      genre: formatArrayToPostgresTextArrayLiteral(formData.genre) ?? null,
      subgenre: formatArrayToPostgresTextArrayLiteral(formData.subgenre),
      // Array fields that are stored as TEXT[] in DB
      moods: formatArrayToPostgresTextArrayLiteral(formData.moods),

      // Fields storing JSON strings of arrays
      production_notes: formData.production_notes ?? null,
      gear_used: formatArrayToPostgresTextArrayLiteral(formData.gear_used),
      // sample_credits: values.sample_credits ? JSON.stringify(values.sample_credits) : '[]', // Column 'sample_credits' does not exist, BDD has 'credits' (jsonb)
      remix_info: formData.remix_info ?? null,
      version_of_song_id: formData.version_of_song_id ?? null,
      is_archived: formData.is_archived ?? false,
      featured_artists: formatArrayToPostgresTextArrayLiteral(formData.featured_artists),
      tags: formatArrayToPostgresTextArrayLiteral(formData.tags),
      themes: formatArrayToPostgresTextArrayLiteral(formData.themes),
      instruments: formatArrayToPostgresTextArrayLiteral(formData.instruments),
      writers: formatArrayToPostgresTextArrayLiteral(formData.writers),
      producers: formatArrayToPostgresTextArrayLiteral(formData.producers),

      // Direct mapping from form values
      title: formData.title,
      artist_name: formData.artist_name, // Assuming form has artist_name, and DB has artist_name
      album_id: formData.album_id === '__NONE__' ? null : formData.album_id,
      bpm: formData.bpm,
      musical_key: formData.musical_key,
      duration_ms: formData.duration_ms,
      lyrics: formData.lyrics,
      song_story: formData.song_story,
      description: formData.description,
      // composer_name: values.composer_name, // Column does not exist in DB, commented out to fix save
      record_label: formData.record_label ?? null,
      isrc_code: formData.isrc_code ?? null,
      upc_code: formData.upc_code ?? null,
      lyrics_language: formData.lyrics_language,
      time_signature: formData.time_signature,
      status: formData.status,

      // Boolean fields
      is_public: formData.is_public ?? false,
      is_favorite: formData.is_favorite ?? false,
      is_incomplete: formData.is_incomplete ?? true,
      is_cover: formData.is_cover ?? false,
      is_instrumental: formData.is_instrumental ?? false,
      is_explicit: formData.is_explicit ?? false,

      // Date fields
      release_date: formData.release_date ? format(new Date(formData.release_date), 'yyyy-MM-dd') : null,

      // URLs
      audio_url: finalAudioUrl,
      cover_art_url: finalCoverArtUrl,

      // Slug generation (regenerate if title changed or slug was empty)
      slug: (songData?.title !== formData.title || !formData.slug || !songData?.slug) ? slugify(formData.title ?? '') : formData.slug,

      // Timestamp
      updated_at: new Date().toISOString(),
    };

    // Remove undefined properties to avoid issues with Supabase client
    Object.keys(updatePayload).forEach(key => {
      if (updatePayload[key] === undefined) {
        // Set to null if DB column allows, otherwise omit. For now, setting to null.
        updatePayload[key] = null;
      }
    });

    console.log('[Edit Submit] Final payload for Supabase:', updatePayload);

    const { error: updateError } = await supabase
      .from('songs')
      .update(updatePayload)
      .eq('id', params.id)
      .select() // Important to get the updated row back for local state update
      .single(); // Assuming we expect one row back

    if (updateError) {
      console.error('[Edit Submit] Error updating song:', updateError);
      toast({ title: 'Erreur de mise à jour', description: `Impossible de mettre à jour la chanson: ${updateError.message}`, variant: 'destructive' });
    } else if (updateError === null && updatePayload) { // Check if updateError is null and data exists
      console.log('[Edit Submit] Song updated successfully in DB. Response data:', updatePayload); // Log the data returned by .select()
      
      // Update local state to reflect changes, ensuring form values are preserved/updated
      const newSongStateForForm = {
        ...songData, // Spread existing non-form related data like original creator_id, etc.
        ...formData,   // Spread all current form values (these are already in form-friendly format, e.g., arrays)
        
        // Override with values that were processed or came from the server/upload
        audio_url: finalAudioUrl,
        cover_art_url: finalCoverArtUrl,
        slug: updatePayload.slug, // Use the slug from the payload sent to DB
        // The 'updated_at' field is in updatePayload, SongFormValues might not need it directly,
        // but songData state can hold it.
        // If the .select().single() returns the updated row (let's call it 'updatedDbRow')
        // then it's better to use fields from 'updatedDbRow' where appropriate.
        // For now, using a mix of `values` and `updatePayload` for simplicity.
        // Example: if DB transforms a value, `updatedDbRow.that_value` would be best.
      };

      // To ensure the local state `songData` (which feeds `SongForm`) has the correct array structures
      // for fields like genres, moods, etc., we rely on `values` which already has them as arrays.
      // The `updatePayload` had them as singular strings or JSON strings for the DB.
      // So, the spread of `...values` is crucial here.
      // @ts-ignore // If newSongStateForForm doesn't perfectly match Song type
      setSongData(newSongStateForForm);
      setPreviewSlug(updatePayload.slug || '');

      toast({ title: 'Succès', description: 'Morceau mis à jour avec succès.' });
      // router.push('/manage-songs'); // Optional: redirect after save
    }

    setIsSubmitting(false);
  }, [supabase, params, currentUserId, toast, songData, setIsSubmitting, setSongData, setPreviewSlug, uploadFileToSupabase, formatArrayToPostgresTextArrayLiteral, slugify, format]);


// Conditional rendering for loading states
  if (isLoadingSongData || isLoadingAlbums || (!currentUserId && params.id)) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner className="h-12 w-12 animate-spin text-blue-500" />
      </div>
    );
  }

  if (!songData && !isLoadingSongData) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Chanson non trouvée</h1>
        <p className="mb-4">Impossible de charger les détails de la chanson.</p>
        <Link href="/manage-songs" passHref>
          <Button variant="outline">Retourner à la gestion</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Modifier: {songData?.title || 'Chanson'}</h1>
        {(songData?.slug) && (
          <Link href={`/songs/${songData.slug}`} target="_blank" passHref>
            <Button variant="outline" size="sm">
              Voir Page Publique <ExternalLink className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        )}
      </div>
      
      <SongForm 
        initialSongData={songData as Song | null}
        onFormSubmit={handleFormSubmit} 
        isSubmittingGlobal={isSubmitting}
        albumsData={userAlbums}
        currentUserId={currentUserId!}
        supabaseClient={supabase}
        mode="edit"
      />

      {previewSlug && (
        <div className="mt-4 p-4 border rounded bg-gray-50 dark:bg-gray-800">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Prévisualisation du slug: <span className="font-mono bg-gray-200 dark:bg-gray-700 p-1 rounded">{previewSlug}</span>
          </p>
        </div>
      )}
    </div>
  );
}

