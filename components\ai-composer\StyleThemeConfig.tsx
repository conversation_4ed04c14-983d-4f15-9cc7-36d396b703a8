'use client';

import React, { useState, useCallback, useMemo, memo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MultiSelect } from '@/components/ui/multi-select';
import { 
  Palette, Music, Settings, Sliders, Clock, Hash,
  Guitar, Piano, Mic, Volume2, X, Wand2, Target
} from 'lucide-react';
import { 
  GENRES_OPTIONS, 
  MOODS_OPTIONS, 
  INSTRUMENTS_OPTIONS, 
  MUSICAL_KEY_OPTIONS,
  TIME_SIGNATURE_OPTIONS 
} from '@/components/songs/song-options';

interface StyleThemeConfigProps {
  onConfigChange?: (config: StyleThemeConfig) => void;
  initialConfig?: Partial<StyleThemeConfig>;
}

export interface StyleThemeConfig {
  // Multi-selects
  genres: string[];
  moods: string[];
  instrumentation: string[];
  
  // Configuration musicale
  capo: number;
  tuningFrequency: number; // Hz (ex: 440)
  bpm: number;
  timeSignature: string;
  
  // Tonalité
  key: string;
  mode: 'major' | 'minor';
}

// Utilise les options du song editor pour la cohérence

// Utilisation des options du song editor
const MUSICAL_KEYS = MUSICAL_KEY_OPTIONS.map(option => option.value);

const DEFAULT_CONFIG: StyleThemeConfig = {
  genres: [],
  moods: [],
  instrumentation: [],
  capo: 0,
  tuningFrequency: 440,
  bpm: 120,
  timeSignature: '4/4',
  key: 'C',
  mode: 'major'
};

const StyleThemeConfigComponent = ({ onConfigChange, initialConfig }: StyleThemeConfigProps) => {
  const [config, setConfig] = useState<StyleThemeConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig
  });

  const updateConfig = useCallback((updates: Partial<StyleThemeConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  }, [config, onConfigChange]);

  const removeGenre = useCallback((genre: string) => {
    updateConfig({ genres: config.genres.filter(g => g !== genre) });
  }, [config.genres, updateConfig]);

  const removeMood = useCallback((mood: string) => {
    updateConfig({ moods: config.moods.filter(m => m !== mood) });
  }, [config.moods, updateConfig]);

  const removeInstrument = useCallback((instrument: string) => {
    updateConfig({ instrumentation: config.instrumentation.filter(i => i !== instrument) });
  }, [config.instrumentation, updateConfig]);

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Configuration Style & Thème
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <ScrollArea className="h-[600px] pr-4">
          {/* Section Genre */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Music className="h-4 w-4" />
              Genres musicaux
            </Label>
            <MultiSelect
              options={GENRES_OPTIONS}
              selected={config.genres}
              onChange={(selected) => updateConfig({ genres: selected })}
              placeholder="Sélectionnez des genres"
            />
            {config.genres.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {config.genres.map((genre) => {
                  const option = GENRES_OPTIONS.find(opt => opt.value === genre);
                  return (
                    <Badge key={genre} variant="secondary" className="text-xs">
                      {option?.label || genre}
                      <X 
                        className="h-3 w-3 ml-1 cursor-pointer hover:text-destructive" 
                        onClick={() => removeGenre(genre)}
                      />
                    </Badge>
                  );
                })}
              </div>
            )}
          </div>

          <Separator className="my-4" />

          {/* Section Mood */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              Ambiances
            </Label>
            <MultiSelect
              options={MOODS_OPTIONS}
              selected={config.moods}
              onChange={(selected) => updateConfig({ moods: selected })}
              placeholder="Sélectionnez des ambiances"
            />
            {config.moods.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {config.moods.map((mood) => {
                  const option = MOODS_OPTIONS.find(opt => opt.value === mood);
                  return (
                    <Badge key={mood} variant="outline" className="text-xs">
                      {option?.label || mood}
                      <X 
                        className="h-3 w-3 ml-1 cursor-pointer hover:text-destructive" 
                        onClick={() => removeMood(mood)}
                      />
                    </Badge>
                  );
                })}
              </div>
            )}
          </div>

          <Separator className="my-4" />

          {/* Section Instrumentation */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Guitar className="h-4 w-4" />
              Instrumentation
            </Label>
            <MultiSelect
              options={INSTRUMENTS_OPTIONS}
              selected={config.instrumentation}
              onChange={(selected) => updateConfig({ instrumentation: selected })}
              placeholder="Sélectionnez des instruments"
            />
            {config.instrumentation.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {config.instrumentation.map((instrument) => {
                  const option = INSTRUMENTS_OPTIONS.find(opt => opt.value === instrument);
                  return (
                    <Badge key={instrument} variant="default" className="text-xs">
                      {option?.label || instrument}
                      <X 
                        className="h-3 w-3 ml-1 cursor-pointer hover:text-destructive" 
                        onClick={() => removeInstrument(instrument)}
                      />
                    </Badge>
                  );
                })}
              </div>
            )}
          </div>

          <Separator className="my-4" />

          {/* Section Configuration musicale */}
          <div className="space-y-4">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configuration musicale
            </Label>
            
            <div className="grid grid-cols-2 gap-4">
              {/* Tonalité */}
              <div className="space-y-2">
                <Label htmlFor="key" className="text-xs">Tonalité</Label>
                <div className="flex gap-2">
                  <Select value={config.key} onValueChange={(value) => updateConfig({ key: value })}>
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {MUSICAL_KEYS.map(key => (
                        <SelectItem key={key} value={key}>{key}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={config.mode} onValueChange={(value: 'major' | 'minor') => updateConfig({ mode: value })}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="major">Maj</SelectItem>
                      <SelectItem value="minor">Min</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* BPM */}
              <div className="space-y-2">
                <Label htmlFor="bpm" className="text-xs flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  BPM
                </Label>
                <Input
                  id="bpm"
                  type="number"
                  min="40"
                  max="200"
                  value={config.bpm}
                  onChange={(e) => updateConfig({ bpm: parseInt(e.target.value) || 120 })}
                  className="text-sm"
                />
              </div>

              {/* Signature rythmique */}
              <div className="space-y-2">
                <Label htmlFor="timeSignature" className="text-xs flex items-center gap-1">
                  <Hash className="h-3 w-3" />
                  Signature
                </Label>
                <Select value={config.timeSignature} onValueChange={(value) => updateConfig({ timeSignature: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_SIGNATURE_OPTIONS.map(sig => (
                      <SelectItem key={sig.value} value={sig.value}>{sig.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Capo */}
              <div className="space-y-2">
                <Label htmlFor="capo" className="text-xs flex items-center gap-1">
                  <Guitar className="h-3 w-3" />
                  Capo
                </Label>
                <Input
                  id="capo"
                  type="number"
                  min="0"
                  max="12"
                  value={config.capo}
                  onChange={(e) => updateConfig({ capo: parseInt(e.target.value) || 0 })}
                  className="text-sm"
                />
              </div>
            </div>

            {/* Fréquence d'accordage */}
            <div className="space-y-2">
              <Label htmlFor="tuningFrequency" className="text-xs flex items-center gap-1">
                <Sliders className="h-3 w-3" />
                Fréquence d'accordage (Hz)
              </Label>
              <Input
                id="tuningFrequency"
                type="number"
                min="415"
                max="466"
                step="0.1"
                value={config.tuningFrequency}
                onChange={(e) => updateConfig({ tuningFrequency: parseFloat(e.target.value) || 440 })}
                className="text-sm"
                placeholder="440"
              />
              <p className="text-xs text-muted-foreground">
                Standard: 440 Hz • Baroque: 415 Hz • Moderne: 442-444 Hz
              </p>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Résumé de la configuration */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Résumé</Label>
            <div className="p-3 bg-muted/50 rounded-lg space-y-2 text-xs">
              <div><strong>Tonalité:</strong> {config.key} {config.mode === 'major' ? 'majeur' : 'mineur'}</div>
              <div><strong>Tempo:</strong> {config.bpm} BPM</div>
              <div><strong>Signature:</strong> {config.timeSignature}</div>
              <div><strong>Capo:</strong> {config.capo === 0 ? 'Aucun' : `Case ${config.capo}`}</div>
              <div><strong>Accordage:</strong> {config.tuningFrequency} Hz</div>
              <div><strong>Genres:</strong> {config.genres.length || 'Aucun'}</div>
              <div><strong>Ambiances:</strong> {config.moods.length || 'Aucune'}</div>
              <div><strong>Instruments:</strong> {config.instrumentation.length || 'Aucun'}</div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setConfig(DEFAULT_CONFIG);
                onConfigChange?.(DEFAULT_CONFIG);
              }}
              className="flex-1"
            >
              Réinitialiser
            </Button>
            <Button 
              size="sm" 
              onClick={() => onConfigChange?.(config)}
              className="flex-1"
            >
              Appliquer
            </Button>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

// Mémoisation du composant pour éviter les re-rendus inutiles
export const StyleThemeConfig = memo(StyleThemeConfigComponent);