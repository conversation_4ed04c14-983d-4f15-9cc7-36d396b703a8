-- Script de rollback pour la migration de tracks vers songs
-- À utiliser uniquement en cas de problème avec la migration
-- <PERSON><PERSON><PERSON> le: 2024-05-16
-- Auteur: <PERSON><PERSON><PERSON>

-- Début de la transaction
BEGIN;

-- 1. Vérifier si la migration a été effectuée
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'songs') THEN
        RAISE EXCEPTION 'La table songs n''existe pas. Aucun rollback nécessaire.';
    END IF;
    
    -- Vérifier si la vue de compatibilité existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.views WHERE table_schema = 'public' AND table_name = 'tracks') THEN
        RAISE WARNING 'La vue de compatibilité n''existe pas. La table tracks a peut-être été supprimée.';
    END IF;
END
$$;

-- 2. Récupérer les données originales depuis la colonne metadata si nécessaire
-- Cette étape est optionnelle et dépend de la structure de vos données

-- 3. Supprimer la vue de compatibilité si elle existe
DROP VIEW IF EXISTS public.tracks;

-- 4. Supprimer les index créés
DROP INDEX IF EXISTS idx_songs_project_id;
DROP INDEX IF EXISTS idx_songs_created_at;

-- 5. Supprimer la table songs
-- ATTENTION: Ne pas exécuter si vous souhaitez conserver les données
-- DROP TABLE IF EXISTS public.songs;

-- 6. Recréer les vues et fonctions originales si nécessaire
-- (À adapter selon votre schéma)

-- Fin de la transaction
-- COMMIT; -- Ne pas décommenter avant de vérifier le script

-- Instructions:
-- 1. Vérifier soigneusement le script avant exécution
-- 2. Sauvegarder la base de données avant toute opération
-- 3. Tester en environnement de développement avant production

-- Pour annuler le rollback en cas d'erreur:
-- ROLLBACK;
