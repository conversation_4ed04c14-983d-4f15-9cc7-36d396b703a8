'use client';

import React, { useState, useCallback, useRef, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { 
  Sparkles, Music, Play, Pause, Download, Edit3, ArrowRight, 
  FileText, Guitar, Palette, BarChart3, Save, AlertCircle, 
  CheckCircle2, Wand2, Brain, Settings, RotateCcw, Volume2, 
  Share2, Lightbulb, ChevronDown, ChevronRight, PanelRightClose, 
  PanelRightOpen, Layout, Maximize2, Minimize2, Eye, EyeOff,
  Clock, Target, Layers, Mic, Headphones, SkipBack, SkipForward, 
  Repeat, Shuffle, Upload, PanelLeft, PanelRight, PanelBottom, 
  Hash, Tag, User, Calendar, Globe
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

// Import des composants optimisés
import { AILyricsAssistant } from '../AILyricsAssistant';
import { SongStructurePanel } from '../SongStructurePanel';
import ChordLibraryManager from '../ChordLibraryManager';
import { SongInfoHeaderComplete } from './SongInfoHeaderComplete';
import { ResponsiveHelpSystem } from './ResponsiveHelpSystem';
import { UnifiedAIWorkspace } from './UnifiedAIWorkspace';
import { ToolsPanel } from './ToolsPanel';
import { useResponsiveState } from './hooks/useResponsiveState';
import { useAIComposerSupabase } from './hooks/useAIComposerSupabase';

// Types
interface StyleConfigState {
  bpm: number;
  key: string;
  timeSignature: string;
  genres: string[];
  moods: string[];
  instrumentation: string[];
  capo?: number; // Added capo as it's in SongInfoHeaderComplete
}

export interface AIHistoryEntry {
  id: string;
  prompt: string;
  type: string; // e.g., 'lyrics', 'chords', 'structure'
  result: string;
  timestamp: Date;
  applied: boolean;
}

interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: Array<{
    position: number;
    chord: string;
    instrument: string;
  }>;
  aiSuggestions?: string[];
  duration?: number;
  startTime?: number;
}

interface AIComposerMegaProLayoutProps {
  songId?: string;
  initialContent?: string;
  onContentChange?: (content: string) => void;
  onSave?: () => Promise<void>;
}

type ViewMode = 'compose' | 'arrange' | 'analyze' | 'structure';
type LayoutMode = 'focus' | 'split' | 'full' | 'studio';
type PanelState = 'collapsed' | 'normal' | 'expanded';

interface LayoutState {
  viewMode: ViewMode;
  layoutMode: LayoutMode;
  leftPanel: PanelState;
  rightPanel: PanelState;
  bottomPanel: PanelState;
  showAI: boolean;
  showInsights: boolean;
  showStructure: boolean;
  showChords: boolean;
}

// Exposed Handles for parent component interaction
export interface AIComposerMegaProLayoutHandles {
  insertTextAtCursor: (text: string, position?: number) => void;
  insertChordAtCursor: (chord: string, position?: number) => void;
  getCurrentContent: () => string;
}

const DEFAULT_LAYOUT_STATE: LayoutState = {
  viewMode: 'compose',
  layoutMode: 'split',
  leftPanel: 'normal',
  rightPanel: 'normal',
  bottomPanel: 'collapsed',
  showAI: true,
  showInsights: true,
  showStructure: true,
  showChords: true
};

export const AIComposerMegaProLayout = forwardRef<AIComposerMegaProLayoutHandles, AIComposerMegaProLayoutProps>(({
  songId,
  initialContent = '',
  onContentChange,
  onSave
}, ref) => {
  // États principaux
  const [layoutState, setLayoutState] = useState<LayoutState>(DEFAULT_LAYOUT_STATE);
  const [lyricsContent, setLyricsContent] = useState(initialContent);
  const [selectedSection, setSelectedSection] = useState('verse-1');
  const [songSections, setSongSections] = useState<LyricsSection[]>([
    {
      id: 'verse-1',
      type: 'verse',
      title: 'Couplet 1',
      content: '',
      chords: []
    },
    {
      id: 'chorus-1',
      type: 'chorus',
      title: 'Refrain',
      content: '',
      chords: []
    }
  ]);
  const [generalPrompt, setGeneralPrompt] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(240);

  // Hooks
  const responsiveState = useResponsiveState();
  const {
    currentSong,
    isLoading,
    isSaving,
    error,
    loadSong,
    saveSong,
    createNewSong,
    uploadAudioFile,
    uploadCoverArt,
    updateSongField,
    resetSong
  } = useAIComposerSupabase();

  // États pour l'AI et la configuration
  const [aiHistory, setAiHistory] = useState<AIHistoryEntry[]>([]);
  const [lastAiResult, setLastAiResult] = useState<string | null>(null);
  const [aiError, setAiError] = useState<string | null>(null);
  const [styleConfig, setStyleConfig] = useState<StyleConfigState>({
    bpm: 120,
    key: 'C',
    timeSignature: '4/4',
    genres: [] as string[],
    moods: [] as string[],
    instrumentation: [] as string[],
    capo: 0
  });
  // const [aiHistory, setAiHistory] = useState([]); // Moved and typed above
  // const [lastAiResult, setLastAiResult] = useState(null); // Moved and typed above
  const [aiLoading, setAiLoading] = useState(false);
  // const [aiError, setAiError] = useState(null); // Moved and typed above
  const [selectedText, setSelectedText] = useState<string>('');
  const [isAIGenerating, setIsAIGenerating] = useState<boolean>(false);

  // Refs
  const lyricsEditorRef = useRef<any>(null);

  // Effect to synchronize styleConfig with loaded currentSong
  useEffect(() => {
    if (currentSong) {
      setStyleConfig(prevConfig => ({
        ...prevConfig,
        bpm: currentSong.bpm ?? prevConfig.bpm ?? 120,
        key: currentSong.musical_key ?? prevConfig.key ?? 'C',
        timeSignature: currentSong.time_signature ?? prevConfig.timeSignature ?? '4/4',
        genres: currentSong.genres ?? prevConfig.genres ?? [],
        moods: currentSong.moods ?? prevConfig.moods ?? [],
        instrumentation: currentSong.instrumentation ?? prevConfig.instrumentation ?? [],
        capo: currentSong.capo ?? prevConfig.capo ?? 0
      }));
    }
  }, [currentSong]);

  // Gestionnaires d'événements
  const handleLyricsChange = useCallback((content: string) => {
    setLyricsContent(content);
    onContentChange?.(content);
  }, [onContentChange]);

  // Simplified setCurrentSong to directly use updateSongField
  // This function will be passed to SongInfoHeaderComplete
  const handleUpdateSongInfo = useCallback((field: string, value: any) => {
    updateSongField(field, value);
  }, [updateSongField]);

  // Gestionnaire pour l'insertion de texte
  const handleInsertText = useCallback((text: string, position?: number) => {
    if (lyricsEditorRef.current) {
      lyricsEditorRef.current.insertText(text, position);
    }
  }, []);

  // Gestionnaire pour l'insertion d'accords
  const handleChordInsert = useCallback((chord: string, position?: number) => {
    if (lyricsEditorRef.current) {
      lyricsEditorRef.current.insertText(chord, position);
    }
  }, []);

  // Gestionnaire pour l'application de suggestions
  const handleSuggestionApply = useCallback((suggestion: string) => {
    handleInsertText(suggestion);
  }, [handleInsertText]);

  const handleAIGenerate = useCallback(async (prompt: string, type: string): Promise<string> => {
    setIsAIGenerating(true);
    setAiError(null);
    
    try {
      // Simulation d'appel IA
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockResult = {
        id: Date.now().toString(),
        prompt,
        type,
        result: `Résultat IA pour: ${prompt}`,
        timestamp: new Date(),
        applied: false
      } as AIHistoryEntry; // Cast to AIHistoryEntry
        
      setAiHistory(prev => [mockResult, ...prev]);
      setLastAiResult(mockResult.result);
      
      return mockResult.result;
      
    } catch (error) {
      setAiError((error as Error).message || 'Erreur lors de la génération AI');
      throw error;
    } finally {
      setIsAIGenerating(false);
    }
  }, []);

  // Wrapper for AILyricsAssistant's onAIGenerate prop
  const handleAIGenerateForLyricsAssistant = useCallback(async (prompt: string, type: 'lyrics' | 'chords' | 'structure'): Promise<void> => {
    await handleAIGenerate(prompt, type);
    // No return value, so it's Promise<void>
  }, [handleAIGenerate]);

  const handleSaveClick = useCallback(async () => {
    try {
      const songData = {
        title: currentSong?.title || 'Nouveau morceau',
        artist: currentSong?.artist || 'Artiste',
        description: currentSong?.description || '',
        bpm: styleConfig?.bpm ?? 120,
        musical_key: styleConfig?.key ?? 'C',
        time_signature: styleConfig?.timeSignature ?? '4/4',
        genre: styleConfig.genres && styleConfig.genres.length > 0 ? styleConfig.genres[0] : null,
        mood: styleConfig.moods && styleConfig.moods.length > 0 ? styleConfig.moods[0] : null,
        instrumentation: styleConfig.instrumentation || [],
        lyrics: lyricsContent,
        editor_data: {
          sections: songSections,
          selectedSection,
          generalPrompt,
          layoutState
        },
      };
      
      await saveSong(songData);
      await onSave?.();
      toast({
        title: "Sauvegarde réussie",
        description: "Votre composition a été sauvegardée."
      });
    } catch (error) {
      toast({
        title: "Erreur de sauvegarde",
        description: "Impossible de sauvegarder votre composition.",
        variant: "destructive"
      });
    }
  }, [saveSong, currentSong, styleConfig, lyricsContent, songSections, selectedSection, generalPrompt, layoutState, aiHistory, lastAiResult, onSave]);

  const updateLayoutState = useCallback((updates: Partial<LayoutState>) => {
    setLayoutState(prev => ({ ...prev, ...updates }));
  }, []);

  const togglePanel = useCallback((panel: keyof Pick<LayoutState, 'leftPanel' | 'rightPanel' | 'bottomPanel'>) => {
    setLayoutState(prev => ({
      ...prev,
      [panel]: prev[panel] === 'collapsed' ? 'normal' : 'collapsed'
    }));
  }, []);

  // Imperative handles exposed to parent component
  useImperativeHandle(ref, () => ({
    insertTextAtCursor: (text, position) => {
      // Call the existing handler or directly interact with the ref
      if (lyricsEditorRef.current && typeof lyricsEditorRef.current.insertText === 'function') {
        lyricsEditorRef.current.insertText(text, position);
      } else {
        // Fallback or error if the ref or method isn't available
        // This could involve updating lyricsContent directly as a less ideal fallback
        console.warn('lyricsEditorRef.current.insertText is not available. Text insertion might not work as expected.');
        // Example fallback: append to lyricsContent (if that's the desired behavior)
        // const currentText = lyricsContent;
        // const newText = position !== undefined 
        //   ? currentText.slice(0, position) + text + currentText.slice(position)
        //   : currentText + text;
        // setLyricsContent(newText);
        // if (onContentChange) onContentChange(newText);
        toast({ title: 'Insertion Fallback', description: 'Texte inséré via fallback, éditeur direct non disponible.', variant: 'default' });
      }
    },
    insertChordAtCursor: (chord, position) => {
      // Similar to insertTextAtCursor, using the existing handleChordInsert or direct ref call
      if (lyricsEditorRef.current && typeof lyricsEditorRef.current.insertText === 'function') {
        // Assuming chords are also inserted as text into the same editor for simplicity here
        // Adjust if ChordEditor has its own ref and insertion mechanism
        lyricsEditorRef.current.insertText(`[${chord}]`, position); 
      } else {
        console.warn('lyricsEditorRef.current.insertText for chords is not available.');
        toast({ title: 'Insertion Accord Fallback', description: 'Accord inséré via fallback.', variant: 'default' });
      }
    },
    getCurrentContent: () => lyricsContent,
  }));

  // Rendu des modes de vue
  const renderViewModeSelector = () => (
    <div className="flex items-center gap-2">
      <Badge variant="outline" className="text-xs">
        Mode: {layoutState.viewMode}
      </Badge>
      <div className="flex rounded-lg border p-1">
        {[
          { id: 'compose', label: 'Composer', icon: Edit3 },
          { id: 'arrange', label: 'Arranger', icon: Layers },
          { id: 'analyze', label: 'Analyser', icon: BarChart3 },
          { id: 'structure', label: 'Structure', icon: Target }
        ].map(({ id, label, icon: Icon }) => (
          <Button
            key={id}
            variant={layoutState.viewMode === id ? 'default' : 'ghost'}
            size="sm"
            onClick={() => updateLayoutState({ viewMode: id as ViewMode })}
            className="h-8 px-3"
          >
            <Icon className="w-4 h-4 mr-1" />
            {label}
          </Button>
        ))}
      </div>
    </div>
  );

  // Rendu du sélecteur de layout
  const renderLayoutSelector = () => (
    <div className="flex items-center gap-2">
      <div className="flex rounded-lg border p-1">
        {[
          { id: 'focus', label: 'Focus', icon: Eye },
          { id: 'split', label: 'Split', icon: Layout },
          { id: 'full', label: 'Full', icon: Maximize2 },
          { id: 'studio', label: 'Studio', icon: Mic }
        ].map(({ id, label, icon: Icon }) => (
          <Button
            key={id}
            variant={layoutState.layoutMode === id ? 'default' : 'ghost'}
            size="sm"
            onClick={() => updateLayoutState({ layoutMode: id as LayoutMode })}
            className="h-8 px-2"
          >
            <Icon className="w-4 h-4" />
            <span className="sr-only">{label}</span>
          </Button>
        ))}
      </div>
    </div>
  );

  // Rendu de la barre d'outils principale
  const renderMainToolbar = () => (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-2 sm:p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 gap-2">
      <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
        {renderViewModeSelector()}
        <Separator orientation="vertical" className="h-6 hidden sm:block" />
        {renderLayoutSelector()}
      </div>
      
      <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
        {/* Contrôles de lecture */}
        <div className="flex items-center gap-1 mr-2 sm:mr-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className="h-8 px-2"
          >
            {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </Button>
          <Button variant="outline" size="sm" className="h-8 px-2">
            <Volume2 className="w-4 h-4" />
          </Button>
        </div>

        {/* Actions principales */}
        <Button variant="outline" size="sm" onClick={handleSaveClick} className="h-8 px-2">
          <Save className="w-4 h-4 mr-1" />
          <span className="hidden sm:inline">Sauvegarder</span>
        </Button>
        
        <Button variant="outline" size="sm" className="h-8 px-2">
          <Share2 className="w-4 h-4 mr-1" />
          <span className="hidden sm:inline">Partager</span>
        </Button>

        {/* Contrôles de panneaux */}
        <div className="flex items-center gap-1 ml-1 sm:ml-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => togglePanel('leftPanel')}
            className={`h-8 px-2 ${layoutState.leftPanel === 'collapsed' ? 'opacity-50' : ''}`}
          >
            <PanelRightOpen className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => togglePanel('rightPanel')}
            className={`h-8 px-2 ${layoutState.rightPanel === 'collapsed' ? 'opacity-50' : ''}`}
          >
            <PanelRightClose className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  // Rendu du panneau principal (éditeur de paroles)
  const renderMainPanel = () => (
    <Card className="h-full border-0 shadow-none flex flex-col">
      <CardHeader className="pb-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl flex items-center gap-2 font-bold">
            <FileText className="w-6 h-6" />
            Éditeur de Paroles
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-sm px-3 py-1">{selectedSection}</Badge>
            <Button variant="ghost" size="sm">
              <Wand2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 min-h-0 p-4">
        <div className="h-full">
          <AILyricsAssistant
            ref={lyricsEditorRef}
            content={lyricsContent}
            onContentChange={handleLyricsChange}
            selectedSection={selectedSection}
            sections={songSections}
            onAIGenerate={handleAIGenerateForLyricsAssistant} // Use the new wrapper function
            generalPrompt={generalPrompt}
            onEditGeneralPrompt={setGeneralPrompt}
          />
        </div>
      </CardContent>
    </Card>
  );

  
  // Rendu du panneau gauche
  const renderLeftPanel = () => {
    if (layoutState.leftPanel === 'collapsed' && !responsiveState.isMobile) return null;
    // Basic mobile handling: hide if not in studio/full mode. More sophisticated logic might be needed.
    if (responsiveState.isMobile && layoutState.layoutMode !== 'studio' && layoutState.layoutMode !== 'full') return null; 

    const panelContent = (
      <ScrollArea className="h-full">
        <Accordion type="multiple" defaultValue={['structure', 'chords']} className="w-full p-1">
          {layoutState.showStructure && (
            <AccordionItem value="structure" className="border-b-0 mb-1">
              <AccordionTrigger className="px-3 py-2 text-sm font-medium hover:no-underline bg-slate-800 hover:bg-slate-700/80 data-[state=open]:bg-slate-700 rounded-md text-slate-200">
                <div className="flex items-center gap-2 w-full">
                  <Target className="h-4 w-4 text-blue-400" />
                  <span>Structure du Morceau</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-1 pb-0 px-0 bg-transparent border-t border-slate-700 mt-1 rounded-b-md">
                <SongStructurePanel
                  sections={songSections}
                  onSectionSelect={setSelectedSection}
                  onSectionsUpdate={setSongSections}
                  currentSongId={currentSong?.id}
                />
              </AccordionContent>
            </AccordionItem>
          )}
          {layoutState.showChords && (
            <AccordionItem value="chords" className="border-b-0 mb-1">
              <AccordionTrigger className="px-3 py-2 text-sm font-medium hover:no-underline bg-slate-800 hover:bg-slate-700/80 data-[state=open]:bg-slate-700 rounded-md text-slate-200">
                <div className="flex items-center gap-2 w-full">
                  <Guitar className="h-4 w-4 text-green-400" />
                  <span>Bibliothèque d'Accords</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-1 pb-0 px-0 bg-transparent border-t border-slate-700 mt-1 rounded-b-md">
                <ChordLibraryManager
                  onChordSelect={(chord) => handleChordInsert(`[${chord}]`)}
                  currentKey={styleConfig.key}
                />
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>
      </ScrollArea>
    );

    if (responsiveState.isMobile) {
      // On mobile, if the panel is supposed to be open, render it directly (e.g., in 'studio' or 'full' mode)
      // Or, it could be part of a sheet triggered by a button elsewhere.
      // This example assumes direct rendering if conditions met.
      return (
        <div className="bg-slate-900 border-r border-slate-700 h-full flex flex-col w-full sm:w-80 md:w-96">
          {panelContent}
        </div>
      );
    }

    return (
      <Card className="h-full overflow-hidden border-0 shadow-none bg-slate-900 rounded-none">
        <CardHeader className="p-2 border-b border-slate-700 flex flex-row items-center justify-between space-y-0 h-[41px]">
          <h3 className="text-sm font-semibold text-slate-200">Outils & Structure</h3>
          <Button variant="ghost" size="icon" onClick={() => togglePanel('leftPanel')} className="h-7 w-7 text-slate-400 hover:text-slate-100">
            {layoutState.leftPanel === 'collapsed' ? <PanelRightOpen className="h-4 w-4" /> : <PanelLeft className="h-4 w-4" />}
          </Button>
        </CardHeader>
        <CardContent className="p-0 h-[calc(100%-41px)]">
          {panelContent}
        </CardContent>
      </Card>
    );
  };

  // Rendu du panneau droit - Hub IA Unifié
  const renderRightPanel = () => {
    if (layoutState.rightPanel === 'collapsed' && !responsiveState.isMobile) return null;
    if (responsiveState.isMobile && layoutState.layoutMode !== 'studio' && layoutState.layoutMode !== 'full') return null;

    const panelContent = (
      <ScrollArea className="h-full">
        <Accordion type="multiple" defaultValue={['ai-workspace']} className="w-full p-1">
          {layoutState.showAI && (
            <AccordionItem value="ai-workspace" className="border-b-0 mb-1">
              <AccordionTrigger className="px-3 py-2 text-sm font-medium hover:no-underline bg-slate-800 hover:bg-slate-700/80 data-[state=open]:bg-slate-700 rounded-md text-slate-200">
                <div className="flex items-center gap-2 w-full">
                  <Sparkles className="h-4 w-4 text-purple-400" />
                  <span>Assistant IA & Historique</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-1 pb-0 px-0 bg-transparent border-t border-slate-700 mt-1 rounded-b-md">
                <UnifiedAIWorkspace
                  onAIGenerate={handleAIGenerate}
                  onInsertChord={(chord) => {
                    if (lyricsEditorRef.current) {
                      lyricsEditorRef.current.insertText(`[${chord}]`);
                    }
                  }}
                  onInsertText={(text) => {
                    if (lyricsEditorRef.current) {
                      lyricsEditorRef.current.insertText(text);
                    }
                  }}
                  currentSection={selectedSection}
                  selectedText={lyricsContent}
                  aiHistory={aiHistory}
                  isLoading={isAIGenerating}
                  onHistoryItemApply={handleSuggestionApply}
                  onHistoryItemDelete={(id: string) => setAiHistory(prev => prev.filter(item => item.id !== id))}
                />
              </AccordionContent>
            </AccordionItem>
          )}
          {/* Future AI tools or panels can be added here as AccordionItems */}
        </Accordion>
      </ScrollArea>
    );

    if (responsiveState.isMobile) {
      return (
        <div className="bg-slate-900 border-l border-slate-700 h-full flex flex-col w-full sm:w-80 md:w-96">
          {panelContent}
        </div>
      );
    }

    return (
      <Card className="h-full overflow-hidden border-0 shadow-none bg-slate-900 rounded-none">
        <CardHeader className="p-2 border-b border-slate-700 flex flex-row items-center justify-between space-y-0 h-[41px]">
          <h3 className="text-sm font-semibold text-slate-200">Espace IA</h3>
          <Button variant="ghost" size="icon" onClick={() => togglePanel('rightPanel')} className="h-7 w-7 text-slate-400 hover:text-slate-100">
            {layoutState.rightPanel === 'collapsed' ? <PanelLeft className="h-4 w-4" /> : <PanelRightClose className="h-4 w-4" />}
          </Button>
        </CardHeader>
        <CardContent className="p-0 h-[calc(100%-41px)]">
          {panelContent}
        </CardContent>
      </Card>
    );
  };

  // Rendu du panneau inférieur
  const renderBottomPanel = () => {
    if (layoutState.bottomPanel === 'collapsed') return null;
    
    return (
      <Card className="border-0 shadow-none">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Timeline & Contrôles
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => togglePanel('bottomPanel')}
            >
              <Minimize2 className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Timeline de progression */}
            <div className="flex items-center gap-4">
              <div className="flex-1 bg-muted rounded-full h-2 relative">
                <div 
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ width: `${(currentTime / duration) * 100}%` }}
                />
              </div>
              <div className="text-sm text-muted-foreground min-w-[80px]">
                {Math.floor(currentTime / 60)}:{(currentTime % 60).toString().padStart(2, '0')} / 
                {Math.floor(duration / 60)}:{(duration % 60).toString().padStart(2, '0')}
              </div>
            </div>
            
            {/* Contrôles avancés */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="outline">BPM: 120</Badge>
                <Badge variant="outline">Clé: C</Badge>
                <Badge variant="outline">4/4</Badge>
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Mic className="w-4 h-4 mr-1" />
                  Enregistrer
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-1" />
                  Exporter
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Rendu principal selon le mode de layout
  const renderLayout = () => {
    switch (layoutState.layoutMode) {
      case 'focus':
        return (
          <div className="h-full p-4">
            {renderMainPanel()}
          </div>
        );
        
      case 'full':
        return (
          <ResizablePanelGroup direction="horizontal" className="h-full">
            <ResizablePanel defaultSize={18} minSize={15}>
              {renderLeftPanel()}
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={60} minSize={40}>
              {renderMainPanel()}
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={22} minSize={18}>
              {renderRightPanel()}
            </ResizablePanel>
          </ResizablePanelGroup>
        );
        
      case 'studio':
        return (
          <ResizablePanelGroup direction="vertical" className="h-full">
            <ResizablePanel defaultSize={75}>
              <ResizablePanelGroup direction="horizontal">
                <ResizablePanel defaultSize={25} minSize={15}>
                  {renderLeftPanel()}
                </ResizablePanel>
                <ResizableHandle withHandle />
                <ResizablePanel defaultSize={50} minSize={30}>
                  {renderMainPanel()}
                </ResizablePanel>
                <ResizableHandle withHandle />
                <ResizablePanel defaultSize={25} minSize={15}>
                  {renderRightPanel()}
                </ResizablePanel>
              </ResizablePanelGroup>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={25} minSize={15}>
              {renderBottomPanel()}
            </ResizablePanel>
          </ResizablePanelGroup>
        );
        
      default: // split
        return (
          <ResizablePanelGroup direction="horizontal" className="h-full">
            {layoutState.leftPanel !== 'collapsed' && (
              <>
                <ResizablePanel defaultSize={20} minSize={15}>
                  {renderLeftPanel()}
                </ResizablePanel>
                <ResizableHandle withHandle />
              </>
            )}
            <ResizablePanel defaultSize={layoutState.leftPanel === 'collapsed' ? 75 : 60} minSize={40}>
              {renderMainPanel()}
            </ResizablePanel>
            {layoutState.rightPanel !== 'collapsed' && (
              <>
                <ResizableHandle withHandle />
                <ResizablePanel defaultSize={20} minSize={15}>
                  {renderRightPanel()}
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>
        );
    } // End of switch statement in renderLayout
  }; // End of renderLayout function

  // Main return statement of AIComposerMegaProLayout
  return (
    <div className="min-h-screen h-screen flex flex-col bg-background overflow-hidden">
      {/* Accordion for main sections */}
      <Accordion type="multiple" defaultValue={['song-info']} className="w-full p-4 flex-shrink-0">
        <AccordionItem value="song-info">
          <AccordionTrigger>Informations du Morceau</AccordionTrigger>
          <AccordionContent>
            <SongInfoHeaderComplete
              currentSong={currentSong}
              setCurrentSong={handleUpdateSongInfo}
              onSave={handleSaveClick}
              isSaving={isSaving}
              isLoading={isLoading} // isLoading from useAIComposerSupabase
              error={error} // error from useAIComposerSupabase
              resetSong={resetSong}
              responsiveState={responsiveState} // from useResponsiveState
              styleConfig={styleConfig}
              setStyleConfig={setStyleConfig}
            />
          </AccordionContent>
        </AccordionItem>
        {/* TODO: Add other accordion items for Structure, Chords, etc. here */}
      </Accordion>

      {/* Barre d'outils principale (reste visible pour l'instant) */}
      <div className="flex-shrink-0">
        {renderMainToolbar()}
      </div>

      {/* Zone de contenu principal avec hauteur optimisée */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {renderLayout()} {/* This function renders the main editor, left/right panels based on layoutState */}
      </div>

      {/* Panneau inférieur (timeline) - Rendu conditionnel s'il n'est pas géré dans renderLayout */}
      {layoutState.bottomPanel !== 'collapsed' && layoutState.layoutMode !== 'studio' && (
        <div className="flex-shrink-0 border-t" style={{ height: '120px', minHeight: '120px' }}>
          <div className="h-full overflow-y-auto">
            {renderBottomPanel()}
          </div>
        </div>
      )}
      
      {/* Système d'aide responsive */}
      <ResponsiveHelpSystem
        screenSize={responsiveState.screenSize}
        currentContext={`${layoutState.viewMode}-${layoutState.layoutMode}`}
        isFirstVisit={false}
      />
      
      {/* Alertes et notifications */}
      {aiError && (
        <Alert className="fixed bottom-4 right-4 w-80 max-w-[calc(100vw-2rem)] border-destructive z-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{aiError}</AlertDescription>
        </Alert>
      )}
    </div> // Closing main div
  ); // Closing return statement
});

AIComposerMegaProLayout.displayName = 'AIComposerMegaProLayout';

export default AIComposerMegaProLayout;