'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { 
  HelpCircle, X, Lightbulb, Zap, Target, Music, 
  Smartphone, Tablet, Monitor, Keyboard, Mouse,
  Eye, Settings, BookOpen, Video, ExternalLink
} from 'lucide-react';

interface ResponsiveHelpSystemProps {
  screenSize: 'mobile' | 'tablet' | 'desktop';
  currentContext: string;
  isFirstVisit?: boolean;
}

interface HelpTip {
  id: string;
  title: string;
  description: string;
  context: string[];
  screenSizes: ('mobile' | 'tablet' | 'desktop')[];
  category: 'navigation' | 'editing' | 'ai' | 'shortcuts' | 'workflow';
  priority: 'high' | 'medium' | 'low';
  icon: React.ReactNode;
}

const HELP_TIPS: HelpTip[] = [
  // Navigation
  {
    id: 'mobile-menu',
    title: 'Menu Mobile',
    description: 'Utilisez le bouton menu (☰) en haut à droite pour accéder aux panneaux et options sur mobile.',
    context: ['header', 'navigation'],
    screenSizes: ['mobile'],
    category: 'navigation',
    priority: 'high',
    icon: <Smartphone className="h-4 w-4" />
  },
  {
    id: 'view-modes',
    title: 'Modes d\'Affichage',
    description: 'Basculez entre Focus (édition), Complet (vue d\'ensemble) et Analyse (métriques) selon vos besoins.',
    context: ['header', 'workspace'],
    screenSizes: ['tablet', 'desktop'],
    category: 'navigation',
    priority: 'high',
    icon: <Eye className="h-4 w-4" />
  },
  {
    id: 'panel-management',
    title: 'Gestion des Panneaux',
    description: 'Les panneaux s\'adaptent automatiquement à votre écran. Vous pouvez les réduire ou masquer selon l\'espace disponible.',
    context: ['workspace', 'panels'],
    screenSizes: ['tablet', 'desktop'],
    category: 'navigation',
    priority: 'medium',
    icon: <Monitor className="h-4 w-4" />
  },

  // Édition
  {
    id: 'touch-editing',
    title: 'Édition Tactile',
    description: 'Les zones de texte sont optimisées pour le tactile. Utilisez les gestes de pincement pour zoomer si nécessaire.',
    context: ['editor', 'lyrics'],
    screenSizes: ['mobile', 'tablet'],
    category: 'editing',
    priority: 'high',
    icon: <Target className="h-4 w-4" />
  },
  {
    id: 'section-management',
    title: 'Gestion des Sections',
    description: 'Créez et organisez vos sections (couplet, refrain, pont) via la timeline. Chaque section peut avoir ses propres accords.',
    context: ['timeline', 'sections'],
    screenSizes: ['mobile', 'tablet', 'desktop'],
    category: 'editing',
    priority: 'high',
    icon: <Music className="h-4 w-4" />
  },
  {
    id: 'auto-save',
    title: 'Sauvegarde Automatique',
    description: 'Vos modifications sont sauvegardées automatiquement toutes les 30 secondes. L\'indicateur en haut vous informe du statut.',
    context: ['header', 'saving'],
    screenSizes: ['mobile', 'tablet', 'desktop'],
    category: 'workflow',
    priority: 'medium',
    icon: <Zap className="h-4 w-4" />
  },

  // IA
  {
    id: 'ai-context',
    title: 'IA Contextuelle',
    description: 'L\'IA utilise votre vision générale du projet et le contexte actuel pour des suggestions plus pertinentes.',
    context: ['ai', 'assistant'],
    screenSizes: ['mobile', 'tablet', 'desktop'],
    category: 'ai',
    priority: 'high',
    icon: <Lightbulb className="h-4 w-4" />
  },
  {
    id: 'general-prompt',
    title: 'Vision Générale',
    description: 'Définissez la vision de votre projet en haut. Cette information guide toutes les suggestions IA.',
    context: ['prompt', 'ai'],
    screenSizes: ['tablet', 'desktop'],
    category: 'ai',
    priority: 'high',
    icon: <Target className="h-4 w-4" />
  },

  // Raccourcis
  {
    id: 'keyboard-shortcuts',
    title: 'Raccourcis Clavier',
    description: 'Ctrl+S pour sauvegarder, Ctrl+Z pour annuler, Tab pour naviguer entre les champs.',
    context: ['shortcuts', 'productivity'],
    screenSizes: ['desktop'],
    category: 'shortcuts',
    priority: 'medium',
    icon: <Keyboard className="h-4 w-4" />
  },

  // Workflow
  {
    id: 'responsive-workflow',
    title: 'Workflow Adaptatif',
    description: 'L\'interface s\'adapte à votre appareil : focus sur mobile, vue complète sur desktop.',
    context: ['workflow', 'responsive'],
    screenSizes: ['mobile', 'tablet', 'desktop'],
    category: 'workflow',
    priority: 'medium',
    icon: <Settings className="h-4 w-4" />
  }
];

const QUICK_TUTORIALS = [
  {
    id: 'getting-started',
    title: 'Premiers Pas',
    description: 'Découvrez les bases de l\'AI Composer Pro',
    duration: '3 min',
    steps: [
      'Définir la vision de votre projet',
      'Créer vos premières sections',
      'Utiliser l\'assistant IA',
      'Sauvegarder votre travail'
    ]
  },
  {
    id: 'mobile-workflow',
    title: 'Workflow Mobile',
    description: 'Optimiser votre créativité sur mobile',
    duration: '2 min',
    steps: [
      'Navigation avec le menu mobile',
      'Mode Focus pour l\'édition',
      'Gestes tactiles optimisés',
      'Synchronisation automatique'
    ]
  },
  {
    id: 'ai-mastery',
    title: 'Maîtriser l\'IA',
    description: 'Tirer le meilleur parti de l\'assistant IA',
    duration: '5 min',
    steps: [
      'Configurer votre vision générale',
      'Utiliser le contexte des sections',
      'Affiner les suggestions',
      'Historique et itérations'
    ]
  }
];

export const ResponsiveHelpSystem: React.FC<ResponsiveHelpSystemProps> = ({
  screenSize,
  currentContext,
  isFirstVisit = false
}) => {
  const [isOpen, setIsOpen] = useState(isFirstVisit);
  const [activeTab, setActiveTab] = useState('tips');
  const [dismissedTips, setDismissedTips] = useState<string[]>([]);
  const [showContextualTip, setShowContextualTip] = useState(false);

  // Filtrer les conseils selon le contexte et la taille d'écran
  const relevantTips = HELP_TIPS.filter(tip => 
    tip.screenSizes.includes(screenSize) &&
    (tip.context.includes(currentContext) || tip.context.includes('general')) &&
    !dismissedTips.includes(tip.id)
  ).sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });

  // Conseil contextuel prioritaire
  const contextualTip = relevantTips.find(tip => 
    tip.priority === 'high' && tip.context.includes(currentContext)
  );

  // Afficher automatiquement un conseil contextuel
  useEffect(() => {
    if (contextualTip && !dismissedTips.includes(contextualTip.id)) {
      const timer = setTimeout(() => {
        setShowContextualTip(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [contextualTip, dismissedTips]);

  const dismissTip = (tipId: string) => {
    setDismissedTips(prev => [...prev, tipId]);
    if (contextualTip?.id === tipId) {
      setShowContextualTip(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'navigation': return <Monitor className="h-4 w-4" />;
      case 'editing': return <Target className="h-4 w-4" />;
      case 'ai': return <Lightbulb className="h-4 w-4" />;
      case 'shortcuts': return <Keyboard className="h-4 w-4" />;
      case 'workflow': return <Settings className="h-4 w-4" />;
      default: return <HelpCircle className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'navigation': return 'bg-blue-500';
      case 'editing': return 'bg-green-500';
      case 'ai': return 'bg-purple-500';
      case 'shortcuts': return 'bg-orange-500';
      case 'workflow': return 'bg-cyan-500';
      default: return 'bg-gray-500';
    }
  };

  // Conseil contextuel flottant
  const ContextualTip = () => {
    if (!showContextualTip || !contextualTip) return null;

    return (
      <div className="fixed bottom-4 right-4 z-50 max-w-sm">
        <Card className="bg-slate-800 border-slate-600 shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className={`p-1 rounded ${getCategoryColor(contextualTip.category)}`}>
                  {contextualTip.icon}
                </div>
                <CardTitle className="text-sm text-white">{contextualTip.title}</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => dismissTip(contextualTip.id)}
                className="h-6 w-6 p-0 text-slate-400 hover:text-white"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-xs text-slate-300 mb-3">{contextualTip.description}</p>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => {
                  setIsOpen(true);
                  setShowContextualTip(false);
                }}
                className="text-xs h-7"
              >
                En savoir plus
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => dismissTip(contextualTip.id)}
                className="text-xs h-7"
              >
                Compris
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Interface principale
  const HelpContent = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b border-slate-700">
        <div className="flex items-center gap-2">
          <HelpCircle className="h-5 w-5 text-blue-400" />
          <h2 className="text-lg font-semibold text-white">Aide & Conseils</h2>
          <Badge variant="outline" className="text-xs">
            {screenSize === 'mobile' ? '📱' : screenSize === 'tablet' ? '📱' : '🖥️'} {screenSize}
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(false)}
          className="text-slate-400 hover:text-white"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800 m-4 mb-0">
          <TabsTrigger value="tips" className="text-xs">Conseils</TabsTrigger>
          <TabsTrigger value="tutorials" className="text-xs">Tutoriels</TabsTrigger>
          <TabsTrigger value="shortcuts" className="text-xs">Raccourcis</TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="tips" className="h-full m-0">
            <ScrollArea className="h-full px-4">
              <div className="space-y-3 py-4">
                {relevantTips.length === 0 ? (
                  <div className="text-center py-8">
                    <HelpCircle className="h-12 w-12 text-slate-600 mx-auto mb-3" />
                    <p className="text-slate-400 text-sm">Aucun conseil spécifique pour ce contexte.</p>
                  </div>
                ) : (
                  relevantTips.map(tip => (
                    <Card key={tip.id} className="bg-slate-800/50 border-slate-600">
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className={`p-1.5 rounded ${getCategoryColor(tip.category)} flex-shrink-0`}>
                            {tip.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h3 className="text-sm font-medium text-white">{tip.title}</h3>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => dismissTip(tip.id)}
                                className="h-6 w-6 p-0 text-slate-400 hover:text-white flex-shrink-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                            <p className="text-xs text-slate-300 leading-relaxed">{tip.description}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline" className="text-xs">
                                {getCategoryIcon(tip.category)}
                                <span className="ml-1 capitalize">{tip.category}</span>
                              </Badge>
                              <Badge 
                                variant={tip.priority === 'high' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {tip.priority === 'high' ? '🔥' : tip.priority === 'medium' ? '⭐' : '💡'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="tutorials" className="h-full m-0">
            <ScrollArea className="h-full px-4">
              <div className="space-y-3 py-4">
                {QUICK_TUTORIALS.map(tutorial => (
                  <Card key={tutorial.id} className="bg-slate-800/50 border-slate-600">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm text-white">{tutorial.title}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {tutorial.duration}
                        </Badge>
                      </div>
                      <p className="text-xs text-slate-400">{tutorial.description}</p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2 mb-3">
                        {tutorial.steps.map((step, index) => (
                          <div key={index} className="flex items-center gap-2 text-xs text-slate-300">
                            <div className="w-4 h-4 rounded-full bg-blue-500 text-white flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            {step}
                          </div>
                        ))}
                      </div>
                      <Button size="sm" className="w-full text-xs">
                        <Video className="h-3 w-3 mr-1" />
                        Commencer le tutoriel
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="shortcuts" className="h-full m-0">
            <ScrollArea className="h-full px-4">
              <div className="space-y-3 py-4">
                <Card className="bg-slate-800/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-sm text-white flex items-center gap-2">
                      <Keyboard className="h-4 w-4" />
                      Raccourcis Clavier
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {[
                      { keys: 'Ctrl + S', action: 'Sauvegarder' },
                      { keys: 'Ctrl + Z', action: 'Annuler' },
                      { keys: 'Ctrl + Y', action: 'Refaire' },
                      { keys: 'Tab', action: 'Champ suivant' },
                      { keys: 'Shift + Tab', action: 'Champ précédent' },
                      { keys: 'Esc', action: 'Fermer les modales' },
                      { keys: 'F11', action: 'Plein écran' }
                    ].map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span className="text-slate-300">{shortcut.action}</span>
                        <Badge variant="outline" className="font-mono text-xs">
                          {shortcut.keys}
                        </Badge>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-sm text-white flex items-center gap-2">
                      <Mouse className="h-4 w-4" />
                      Gestes Tactiles
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {[
                      { gesture: 'Pincer', action: 'Zoomer/Dézoomer' },
                      { gesture: 'Glisser 2 doigts', action: 'Défiler' },
                      { gesture: 'Appui long', action: 'Menu contextuel' },
                      { gesture: 'Double tap', action: 'Sélection rapide' }
                    ].map((gesture, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span className="text-slate-300">{gesture.action}</span>
                        <Badge variant="outline" className="text-xs">
                          {gesture.gesture}
                        </Badge>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </TabsContent>
        </div>
      </Tabs>

      <div className="border-t border-slate-700 p-4">
        <div className="flex items-center justify-between text-xs text-slate-400">
          <span>Conseils adaptatifs • {relevantTips.length} disponibles</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDismissedTips([])}
            className="text-xs h-6"
          >
            Réinitialiser
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Bouton d'aide flottant */}
      <div className="fixed bottom-4 left-4 z-40">
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg"
        >
          <HelpCircle className="h-5 w-5" />
        </Button>
        {relevantTips.length > 0 && (
          <Badge 
            className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center"
          >
            {relevantTips.length}
          </Badge>
        )}
      </div>

      {/* Conseil contextuel */}
      <ContextualTip />

      {/* Interface d'aide */}
      {screenSize === 'mobile' ? (
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetContent side="bottom" className="h-[80vh] bg-slate-900 border-slate-700">
            <HelpContent />
          </SheetContent>
        </Sheet>
      ) : (
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetContent side="right" className="w-96 bg-slate-900 border-slate-700">
            <HelpContent />
          </SheetContent>
        </Sheet>
      )}
    </>
  );
};

export default ResponsiveHelpSystem;