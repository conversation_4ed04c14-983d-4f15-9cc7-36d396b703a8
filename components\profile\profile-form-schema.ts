import { z } from 'zod';

// Sous-schémas
const socialLinkSchema = z.object({
  platform: z.string().min(1, "La plateforme est requise"),
  url: z.string().url("URL invalide"),
});

const instrumentPlayedSchema = z.object({
  name: z.string().min(1, "Le nom de l'instrument est requis"),
  experience_years: z.number().min(0, "L'expérience doit être un nombre positif").optional(),
});

// Schéma principal du profil
export const profileFormSchema = z.object({
  // --- Informations de base ---
  username: z.string()
    .min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères")
    .max(50, "Le nom d'utilisateur ne peut pas dépasser 50 caractères")
    .regex(/^[a-zA-Z0-9_]+$/, "Le nom d'utilisateur ne peut contenir que des lettres, des chiffres et des underscores"),
  display_name: z.string().max(100, "Le nom d'affichage ne peut pas dépasser 100 caractères").nullable().optional(),
  bio: z.string().max(1000, "La biographie ne peut pas dépasser 1000 caractères").nullable().optional(),
  
  // --- Localisation & Liens ---
  location_city: z.string().max(100, "La ville ne peut pas dépasser 100 caractères").nullable().optional(),
  location_country: z.string().max(100, "Le pays ne peut pas dépasser 100 caractères").nullable().optional(),
  website: z.string().url("URL du site web invalide").or(z.literal('')).nullable().optional(),
  social_links: z.array(socialLinkSchema).nullable().optional(),

  // --- Profil Artiste/Musique ---
  role_primary: z.string().nullable().optional(),
  roles_secondary: z.array(z.string()).nullable().optional(),
  genres: z.array(z.string()).nullable().optional(),
  influences: z.array(z.string()).nullable().optional(),
  tags: z.array(z.string()).nullable().optional(),
  main_instruments: z.array(z.string()).nullable().optional(),
  instruments_played: z.array(instrumentPlayedSchema).nullable().optional(),
  primary_daw: z.string().nullable().optional(),
  other_daws: z.array(z.string()).nullable().optional(),
  operating_systems: z.array(z.string()).nullable().optional(),
  equipment: z.string().max(2000, "La description de l'équipement ne peut pas dépasser 2000 caractères").nullable().optional(),
  record_label: z.string().max(100, "Le nom du label ne peut pas dépasser 100 caractères").nullable().optional(),
  
  // --- Utilisation de l'IA ---
  ai_usage_level: z.enum(['none', 'light', 'moderate', 'heavy']).default('none'),
  ai_usage_percent: z.number().min(0).max(100).nullable().optional(),
  ai_tools: z.array(z.string()).nullable().optional(),

  // --- Collaboration & Langues ---
  open_to_collab: z.boolean().default(false),
  spoken_languages: z.array(z.string()).nullable().optional(),

  // --- Paramètres (non publics) ---
  is_profile_public: z.boolean().default(true),
});

export type ProfileFormValues = z.infer<typeof profileFormSchema>;
