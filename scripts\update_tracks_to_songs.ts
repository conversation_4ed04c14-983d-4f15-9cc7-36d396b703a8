// Script pour mettre à jour les références de 'tracks' à 'songs' dans le code TypeScript/JSX
// Ce script doit être exécuté avec Node.js

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Configuration des chemins
const ROOT_DIR = join(__dirname, '..');
const APP_DIR = join(ROOT_DIR, 'app');
const COMPONENTS_DIR = join(ROOT_DIR, 'components');

// Règles de remplacement
const REPLACEMENTS = [
  // Remplacements généraux
  { pattern: /\btracks\b/g, replacement: 'songs' },
  { pattern: /\bTracks\b/g, replacement: 'Songs' },
  { pattern: /\bTRACKS\b/g, replacement: 'SONGS' },
  { pattern: /\btrack\b/g, replacement: 'song' },
  { pattern: /\bTrack\b/g, replacement: 'Song' },
  
  // Remplacements spécifiques aux noms de fichiers et variables
  { pattern: /trendingTracks/g, replacement: 'trendingSongs' },
  { pattern: /validTracks/g, replacement: 'validSongs' },
  { pattern: /TrendingTracks/g, replacement: 'TrendingSongs' },
  { pattern: /trackId/g, replacement: 'songId' },
  { pattern: /track_id/g, replacement: 'song_id' },
  { pattern: /track_order/g, replacement: 'song_order' },
];

// Fonction pour traiter un fichier
function processFile(filePath: string) {
  try {
    let content = readFileSync(filePath, 'utf-8');
    let updated = false;
    
    // Appliquer les remplacements
    for (const { pattern, replacement } of REPLACEMENTS) {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        updated = true;
      }
    }
    
    // Écrire le fichier uniquement si des modifications ont été apportées
    if (updated) {
      writeFileSync(filePath, content, 'utf-8');
      console.log(`✅ Mise à jour: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Erreur lors du traitement de ${filePath}:`, error);
  }
}

// Fonction pour parcourir récursivement un répertoire
function processDirectory(directory: string) {
  const files = require('fs').readdirSync(directory, { withFileTypes: true });
  
  for (const file of files) {
    const fullPath = join(directory, file.name);
    
    if (file.isDirectory()) {
      // Ignorer les dossiers node_modules, .next, etc.
      if (!['node_modules', '.next', '.git', '.vercel', '.vscode'].includes(file.name)) {
        processDirectory(fullPath);
      }
    } else if (file.isFile() && ['.ts', '.tsx', '.js', '.jsx'].some(ext => file.name.endsWith(ext))) {
      processFile(fullPath);
    }
  }
}

// Exécution du script
console.log('🚀 Début de la mise à jour des références de tracks vers songs...');

// Traiter les répertoires principaux
[APP_DIR, COMPONENTS_DIR].forEach(dir => {
  if (require('fs').existsSync(dir)) {
    console.log(`\n📂 Traitement du répertoire: ${dir}`);
    processDirectory(dir);
  }
});

console.log('\n✅ Mise à jour terminée !');
console.log('\n📋 Prochaines étapes:');
console.log('1. Vérifier les changements avec git diff');
console.log('2. Tester soigneusement toutes les fonctionnalités liées aux chansons');
console.log('3. Mettre à jour la documentation si nécessaire');
console.log('4. Créer un commit avec les modifications');
