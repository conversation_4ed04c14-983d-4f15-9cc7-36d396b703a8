#!/usr/bin/env node

/**
 * Script utilitaire pour gérer les migrations de base de données
 * 
 * Commandes disponibles:
 *   node scripts/db-migrate.js up      - Exécute les migrations en attente
 *   node scripts/db-migrate.js down    - Annule la dernière migration
 *   node scripts/db-migrate.js status  - Affiche l'état des migrations
 *   node scripts/db-migrate.js test    - Exécute les tests de migration
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const { program } = require('commander');
const chalk = require('chalk');

// Charger la configuration
let config;
try {
  config = require('../db/migrations/config');
} catch (error) {
  console.error(chalk.red('Erreur de chargement de la configuration:'));
  console.error(chalk.red('Assurez-vous que le fichier de configuration existe et est valide.'));
  process.exit(1);
}

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '../db/migrations');
const LOG_FILE = path.join(MIGRATIONS_DIR, 'migration.log');

// Initialiser Commander
program
  .name('db-migrate')
  .description('Utilitaire de gestion des migrations de base de données')
  .version('1.0.0');

// Commande: up
program
  .command('up')
  .description('Exécute les migrations en attente')
  .option('-f, --file <file>', 'Fichier de migration spécifique à exécuter')
  .action(async (options) => {
    try {
      logToFile('=== Début de la migration ===');
      
      if (options.file) {
        await runMigration(options.file);
      } else {
        await runAllMigrations();
      }
      
      logToFile('=== Migration terminée avec succès ===');
      console.log(chalk.green('✓ Migration terminée avec succès'));
    } catch (error) {
      logToFile(`ERREUR: ${error.message}`);
      console.error(chalk.red(`✗ Erreur lors de la migration: ${error.message}`));
      process.exit(1);
    }
  });

// Commande: down
program
  .command('down')
  .description('Annule la dernière migration')
  .action(async () => {
    try {
      logToFile('=== Début du rollback ===');
      await rollbackLastMigration();
      logToFile('=== Rollback terminé avec succès ===');
      console.log(chalk.green('✓ Rollback terminé avec succès'));
    } catch (error) {
      logToFile(`ERREUR: ${error.message}`);
      console.error(chalk.red(`✗ Erreur lors du rollback: ${error.message}`));
      process.exit(1);
    }
  });

// Commande: status
program
  .command('status')
  .description('Affiche l\'état des migrations')
  .action(() => {
    try {
      displayMigrationStatus();
    } catch (error) {
      console.error(chalk.red(`✗ Erreur: ${error.message}`));
      process.exit(1);
    }
  });

// Commande: test
program
  .command('test')
  .description('Exécute les tests de migration')
  .action(async () => {
    try {
      console.log(chalk.blue('=== Démarrage des tests de migration ==='));
      await runMigrationTests();
      console.log(chalk.green('✓ Tous les tests ont réussi'));
    } catch (error) {
      console.error(chalk.red(`✗ Échec des tests: ${error.message}`));
      process.exit(1);
    }
  });

// Fonctions utilitaires
function logToFile(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  try {
    fs.appendFileSync(LOG_FILE, logMessage, 'utf8');
  } catch (error) {
    console.warn(chalk.yellow(`⚠ Impossible d'écrire dans le fichier de log: ${error.message}`));
  }
}

async function runMigration(migrationFile) {
  const filePath = path.isAbsolute(migrationFile) 
    ? migrationFile 
    : path.join(MIGRATIONS_DIR, migrationFile);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Fichier de migration introuvable: ${filePath}`);
  }
  
  console.log(chalk.blue(`Exécution de la migration: ${path.basename(filePath)}`));
  logToFile(`Exécution de la migration: ${filePath}`);
  
  // Utiliser psql ou sqlcmd selon le système d'exploitation
  const isWindows = process.platform === 'win32';
  const { database } = config;
  
  let command;
  
  if (isWindows) {
    // Utiliser sqlcmd sous Windows
    command = `sqlcmd -S ${database.host},${database.port} -U ${database.user} -d ${database.name} -i "${filePath}"`;
  } else {
    // Utiliser psql sous Linux/Mac
    command = `PGPASSWORD=${database.password} psql -h ${database.host} -p ${database.port} -U ${database.user} -d ${database.name} -f "${filePath}"`;
  }
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      env: { 
        ...process.env, 
        PGPASSWORD: database.password,
        PATH: process.env.PATH // Conserver le PATH existant
      }
    });
  } catch (error) {
    throw new Error(`Échec de l'exécution de la migration: ${error.message}`);
  }
}

async function runAllMigrations() {
  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql') && file.match(/^\d+_/)) // Fichiers commençant par un timestamp
    .sort();
  
  for (const file of files) {
    await runMigration(path.join(MIGRATIONS_DIR, file));
  }
}

async function rollbackLastMigration() {
  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.startsWith('rollback_') && file.endsWith('.sql'))
    .sort()
    .reverse();
  
  if (files.length === 0) {
    throw new Error('Aucun fichier de rollback trouvé');
  }
  
  const lastRollback = files[0];
  await runMigration(path.join(MIGRATIONS_DIR, lastRollback));
}

function displayMigrationStatus() {
  console.log(chalk.blue('\n=== État des Migrations ===\n'));
  
  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql') && file.match(/^\d+_/))
    .sort();
  
  if (files.length === 0) {
    console.log('Aucune migration trouvée');
    return;
  }
  
  // Afficher les migrations disponibles
  console.log('Migrations disponibles:');
  files.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file}`);
  });
  
  // Afficher les commandes utiles
  console.log('\nCommandes utiles:');
  console.log('  node scripts/db-migrate.js up    - Exécuter les migrations');
  console.log('  node scripts/db-migrate.js down  - Annuler la dernière migration');
  console.log('  node scripts/db-migrate.js test - Exécuter les tests de migration');
}

async function runMigrationTests() {
  const testFile = path.join(MIGRATIONS_DIR, 'test_migration.js');
  
  if (!fs.existsSync(testFile)) {
    throw new Error('Fichier de test de migration introuvable');
  }
  
  try {
    // Vérifier si les dépendances sont installées
    try {
      require.resolve('@supabase/supabase-js');
    } catch (e) {
      console.log(chalk.yellow('Installation des dépendances requises...'));
      execSync('npm install @supabase/supabase-js', { stdio: 'inherit' });
    }
    
    // Exécuter le test avec Node.js
    console.log(chalk.blue('\nExécution des tests de migration...'));
    execSync(`node ${testFile}`, { stdio: 'inherit' });
  } catch (error) {
    throw new Error(`Les tests de migration ont échoué: ${error.message}`);
  }
}

// Gestion des erreurs de commande inconnue
program.on('command:*', () => {
  console.error(chalk.red(`\n  Erreur: Commande invalide: ${program.args.join(' ')}`));
  console.log('  Utilisez --help pour voir la liste des commandes disponibles\n');
  process.exit(1);
});

// Afficher l'aide si aucune commande n'est fournie
if (process.argv.length <= 2) {
  program.help();
}

// Parser les arguments de la ligne de commande
program.parse(process.argv);
