import Link from "next/link";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { AlbumCard } from '@/components/albums/album-card';
import { PlaylistCard } from '@/components/playlists/playlist-card'; 
import { FeaturedArtistCard } from "@/components/discover/featured-artist-card";
import { BandCard } from '@/components/bands/band-card';
import type { Song, Album, UserProfile, Playlist, Band } from "@/types"; // Added Playlist & Band types
import { SongCard } from '@/components/songs/song-card'; 
import { Music2, Disc3, Users, ListFilter, Tag, ListMusic, Guitar } from 'lucide-react'; // Added Guitar for Bands 

interface GenrePageParams {
  params: { genre: string };
}

// Define a type for playlists on this page, including potential profile data
interface PlaylistForGenrePage extends Playlist {
  profiles?: UserProfile | null; // Expect a full UserProfile or null for compatibility
}

// Helper function to create skeleton loaders for cards
const CardListSkeleton = ({ count, className }: { count: number, className?: string }) => (
  <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 ${className}`}>
    {[...Array(count)].map((_, i) => (
      <div key={i} className="space-y-2">
        <Skeleton className="h-40 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
    ))}
  </div>
);

const SongCardSkeleton = ({ count, className }: { count: number, className?: string }) => (
  <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
    {[...Array(count)].map((_, i) => (
       <Skeleton key={i} className="h-32 w-full" />
    ))}
  </div>
);

export default async function GenrePage({ params }: GenrePageParams) {
  const supabase = createSupabaseServerClient();
  const decodedGenre = decodeURIComponent(params.genre);

  // Fetch songs for the genre
  const { data: songsData, error: songsError } = await supabase
    .from("songs")
    .select("*, profiles(id, username, display_name), albums(id, title, cover_url, slug)")
    .ilike("genre", `%${decodedGenre}%`)
    .order("plays", { ascending: false })
    .limit(12);

  console.log("GenrePage - Raw songsData from Supabase:", JSON.stringify(songsData, null, 2));

  const songs: Song[] = songsError ? [] : (songsData || []);

  // Fetch albums related to the genre (direct fetch by genre)
  let albums: Album[] = [];
  let albumsFetchError: any = null;

  const { data: albumsData, error: supabaseAlbumsError } = await supabase
    .from("albums")
    .select("*, profiles(id, display_name, username, avatar_url)") // Added avatar_url for consistency with AlbumCard
    .overlaps("genre", [decodedGenre]) // Query directly on album's genre array
    .eq('is_public', true)
    .order("release_date", { ascending: false, nullsFirst: false })
    .limit(12); // Increased limit for albums

  if (supabaseAlbumsError) {
    console.error('Error fetching albums for genre page:', supabaseAlbumsError);
    albumsFetchError = supabaseAlbumsError;
  } else {
    albums = albumsData || [];
    console.log("GenrePage - Raw albumsData from Supabase:", JSON.stringify(albumsData, null, 2));
  }


  // Fetch playlists related to the genre
  let playlists: PlaylistForGenrePage[] = [];
  let playlistsFetchError: any = null;

  // Step 1: Fetch playlists related to the genre
  const { data: rawPlaylistsData, error: supabasePlaylistsError } = await supabase
    .from("playlists")
    .select("*") // Select all playlist fields, including user_id
    .overlaps('genres', [decodedGenre])
    .eq('is_public', true)
    .order("created_at", { ascending: false })
    .limit(4);

  if (supabasePlaylistsError) {
    playlistsFetchError = supabasePlaylistsError;
    console.error('Error fetching raw playlists for genre page:', supabasePlaylistsError);
  } else if (rawPlaylistsData && rawPlaylistsData.length > 0) {
    // Step 2: Extract user_ids from playlists
    const userIds = [...new Set(rawPlaylistsData.map(p => p.user_id).filter(id => id))];

    if (userIds.length > 0) {
      // Step 3: Fetch profiles for these user_ids
      const { data: profilesData, error: profilesError } = await supabase
        .from("profiles") // Assuming your public profiles table is named 'profiles'
        .select("id, username, display_name, avatar_url")
        .in("id", userIds);

      if (profilesError) {
        console.error('Error fetching profiles for playlists:', profilesError);
        // Assign playlists without profile data if profiles fetch fails
        playlists = rawPlaylistsData.map(p => ({ ...p, profiles: null }));
      } else {
        // Step 4: Manually map profiles back to playlists
        playlists = rawPlaylistsData.map(p => ({
          ...p,
          profiles: profilesData?.find(profile => profile.id === p.user_id) || null,
        }));
      }
    } else {
      // No user_ids to fetch profiles for, assign playlists without profile data
      playlists = rawPlaylistsData.map(p => ({ ...p, profiles: null }));
    }
  } else {
    playlists = []; // No playlists found or an error occurred previously
  }

  // Fetch artists (individual creators) related to the genre
  let artists: (UserProfile & { songs_count?: number })[] = [];
  let artistsFetchError: any = null;
  const artistIdsFromSongs = [...new Set(songs.map(song => song.creator_user_id).filter(id => id !== null))] as string[];
  let rawArtistsDataFromSupabase: any[] | null = null; // Declared with wider scope

  if (artistIdsFromSongs.length > 0) {
    const { data, error: supabaseArtistsError } = await supabase // Renamed to 'data' to avoid conflict
      .from("profiles")
      .select("*, songs(count)")
      .in("id", artistIdsFromSongs)
      .order("created_at", { ascending: false })
      .limit(5);
    
    rawArtistsDataFromSupabase = data; // Assign to the wider-scoped variable

    if (supabaseArtistsError) {
      artistsFetchError = supabaseArtistsError;
    } else {
      artists = rawArtistsDataFromSupabase?.map(p => ({...p, songs_count: p.songs[0]?.count || 0})) || [];
    }
  }

  // Fetch Bands related to the genre
  let bands: Band[] = [];
  let bandsFetchError: any = null;

  const { data: bandsData, error: supabaseBandsError } = await supabase
    .from("bands")
    .select("*") // Select all band fields for now, can be optimized later
    .overlaps("genres", [decodedGenre])
    .eq('is_public', true)
    .order("follower_count", { ascending: false, nullsFirst: true })
    .limit(5);

  if (supabaseBandsError) {
    console.error('Error fetching bands for genre page:', supabaseBandsError);
    bandsFetchError = supabaseBandsError;
  } else {
    bands = bandsData || [];
    console.log("GenrePage - Raw bandsData from Supabase:", JSON.stringify(bandsData, null, 2));
  }

  return (
    <div className="container mx-auto max-w-7xl py-8 px-4">
      <h1 className="text-4xl font-bold mb-2 capitalize flex items-center">
        Genre :&nbsp;
        <Badge variant="secondary" className="text-3xl px-3 py-1">
          {decodedGenre}
        </Badge>
      </h1>
      <p className="mb-8 text-muted-foreground text-lg">
        Découvrez les morceaux, albums et artistes populaires associés au genre {decodedGenre}.
      </p>

      {/* Songs Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <Music2 className="mr-2 h-6 w-6" /> Morceaux Populaires
        </h2>
        {songsError ? (
          <p className="text-red-500">Erreur lors de la récupération des morceaux : {songsError.message}</p>
        ) : songs.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {songs.map((song) => (
              <SongCard key={song.id} song={song} />
            ))}
          </div>
        ) : (
          <p>Aucun morceau trouvé pour le genre "{decodedGenre}".</p>
        )}
        {!songsData && !songsError && (
          <SongCardSkeleton count={8} className="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4" />
        )}
      </section>

      {/* Albums Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <Disc3 className="mr-2 h-6 w-6" /> Albums Populaires
        </h2>
        {albumsFetchError ? (
          <p className="text-red-500">Erreur lors de la récupération des albums : {albumsFetchError.message}</p>
        ) : albums.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {albums.map((album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </div>
        ) : (
          <p>Aucun album trouvé pour le genre "{decodedGenre}".</p>
        )}
        {!albumsData && !albumsFetchError && (
           <CardListSkeleton count={6} className="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6" />
        )}
      </section>

      {/* Artists Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <Users className="mr-2 h-6 w-6" /> Artistes Populaires
        </h2>
        {artistsFetchError ? (
          <p className="text-red-500">Erreur lors de la récupération des artistes : {artistsFetchError.message}</p>
        ) : artists.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {artists.map((artist) => (
              <FeaturedArtistCard key={artist.id} artist={artist} />
            ))}
          </div>
        ) : (
          <p>Aucun artiste trouvé pour le genre "{decodedGenre}".</p>
        )}
        {artistIdsFromSongs.length > 0 && !rawArtistsDataFromSupabase && !artistsFetchError && (
           <CardListSkeleton count={5} className="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5" />
        )}
      </section>

      {/* Bands Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <Guitar className="mr-2 h-6 w-6" /> Groupes Populaires
        </h2>
        {bandsFetchError ? (
          <p className="text-red-500">Erreur lors de la récupération des groupes : {bandsFetchError.message}</p>
        ) : bands.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {bands.map((band) => (
              <BandCard key={band.id} band={band} />
            ))}
          </div>
        ) : (
          <p>Aucun groupe trouvé pour le genre "{decodedGenre}".</p>
        )}
        {!bandsData && !bandsFetchError && (
           <CardListSkeleton count={5} className="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5" />
        )}
      </section>

      {/* Playlists Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <ListMusic className="mr-2 h-6 w-6" /> Playlists Publiques
        </h2>
        {playlistsFetchError ? (
          <p className="text-red-500">Erreur lors de la récupération des playlists : {playlistsFetchError.message}</p>
        ) : playlists.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {playlists.map((playlist) => (
              <PlaylistCard key={playlist.id} playlist={playlist} />
            ))}
          </div>
        ) : (
          <p>Aucune playlist publique trouvée pour le genre "{decodedGenre}".</p>
        )}
        {!rawPlaylistsData && !playlistsFetchError && (
           <CardListSkeleton count={4} className="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4" />
        )}
      </section>
    </div>
  );
}

