"use server";

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

const toggleAlbumVisibilitySchema = z.object({
  albumId: z.string().uuid(),
  currentIsPublic: z.boolean(),
});

export async function toggleAlbumVisibility(albumId: string, currentIsPublic: boolean) {
  const supabase = createSupabaseServerClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'Authentification requise.' };
  }

  const validation = toggleAlbumVisibilitySchema.safeParse({ albumId, currentIsPublic });

  if (!validation.success) {
    return { error: 'Données invalides.' };
  }

  const newIsPublic = !currentIsPublic;

  const { error } = await supabase
    .from('albums')
    .update({ is_public: newIsPublic })
    .eq('id', albumId)
    .eq('user_id', user.id); // Security check

  if (error) {
    console.error('Error toggling album visibility:', error);
    return { error: 'Impossible de mettre à jour la visibilité de l\'album.' };
  }

  revalidatePath('/albums');
  revalidatePath(`/albums/${albumId}`);

  return { success: true, newIsPublic };
}

const deleteAlbumSchema = z.object({
  albumId: z.string().uuid(),
});

export async function deleteAlbum(albumId: string) {
  const supabase = createSupabaseServerClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'Authentification requise.' };
  }

  const validation = deleteAlbumSchema.safeParse({ albumId });

  if (!validation.success) {
    return { error: 'ID d\'album invalide.' };
  }

  // Optional: Check if album is empty before deleting, or handle cascades in DB

  const { error } = await supabase
    .from('albums')
    .delete()
    .eq('id', albumId)
    .eq('user_id', user.id); // Security check

  if (error) {
    console.error('Error deleting album:', error);
    return { error: 'Impossible de supprimer l\'album.' };
  }

  revalidatePath('/albums');

  return { success: true };
}
