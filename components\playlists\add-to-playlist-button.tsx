"use client";

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { PlusCircle } from "lucide-react";
import { AddToPlaylistModal } from "./add-to-playlist-modal"; // To be created

interface AddToPlaylistButtonProps {
  songId: string;
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | null;
  buttonSize?: "default" | "sm" | "lg" | "icon" | null;
  buttonClassName?: string;
  // Potentially other props like currentPlaylists to avoid re-fetch in modal, or trigger text/icon
}

export function AddToPlaylistButton({ 
  songId, 
  buttonVariant = "ghost", 
  buttonSize = "icon", 
  buttonClassName 
}: AddToPlaylistButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        variant={buttonVariant}
        size={buttonSize}
        onClick={() => setIsModalOpen(true)}
        title="Ajouter à une playlist"
        className={cn("h-8 w-8", buttonClassName)} // Default h-8 w-8, can be overridden by buttonClassName
      >
        <PlusCircle className="h-4 w-4" />
        <span className="sr-only">Ajouter à une playlist</span>
      </Button>
      
      {isModalOpen && (
        <AddToPlaylistModal
          songId={songId}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </>
  );
}
