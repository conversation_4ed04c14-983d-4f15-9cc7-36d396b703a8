// components/ui/chord-diagram.tsx
'use client';

import React from 'react';
import { ChordPosition } from '@/lib/chords/types';

interface ChordDiagramProps {
  position: ChordPosition;
  instrument: {
    strings: number;
    tuning: string[];
  };
  lite?: boolean; // Optional lite version for smaller displays
}

const ChordDiagram: React.FC<ChordDiagramProps> = ({ position, instrument, lite = false }) => {
  const { frets, fingers, baseFret, barres } = position;
  const strings = instrument.strings;
  const numFrets = 5; // Standard number of frets to display

  const width = lite ? 80 : 100;
  const height = lite ? 100 : 120;
  const fretHeight = height / (numFrets + 1);
  const stringWidth = width / (strings - 1);

  // Basic validation
  if (!frets || frets.length !== strings) {
    return <div className="text-red-500">Invalid chord data</div>;
  }

  return (
    <div className="p-2 bg-white rounded-md shadow-sm border border-gray-200">
      <svg viewBox={`0 0 ${width} ${height}`} width={width} height={height} xmlns="http://www.w3.org/2000/svg">
        {/* Base Fret Number */}
        {baseFret > 1 && (
          <text x={-15} y={fretHeight * 1.5} fontSize={lite ? '10' : '12'} fill="#4a4a4a">{baseFret}fr</text>
        )}

        {/* Strings */}
        {Array.from({ length: strings }).map((_, i) => (
          <line key={`string-${i}`} x1={i * stringWidth} y1={fretHeight / 2} x2={i * stringWidth} y2={height} stroke="#e0e0e0" strokeWidth="0.5" />
        ))}

        {/* Frets */}
        {Array.from({ length: numFrets + 1 }).map((_, i) => (
          <line key={`fret-${i}`} x1={0} y1={fretHeight / 2 + i * fretHeight} x2={width} y2={fretHeight / 2 + i * fretHeight} stroke="#a0a0a0" strokeWidth={i === 0 ? 2.5 : 1} />
        ))}

        {/* Muted/Open Strings Indicators */}
        {frets.map((fret, i) => {
          if (fret === 'x' || fret === -1) {
            return <text key={`mute-${i}`} x={i * stringWidth} y={fretHeight * 0.4} fontSize={lite ? '10' : '12'} textAnchor="middle" fill="#a0a0a0">x</text>;
          }
          if (fret === 0) {
            return <circle key={`open-${i}`} cx={i * stringWidth} cy={fretHeight * 0.25} r={lite ? 3 : 4} stroke="#4a4a4a" strokeWidth="1" fill="none" />;
          }
          return null;
        })}

        {/* Barre Chords */}
        {barres.map((barre, i) => {
          const y = (barre.fret - baseFret + 0.5) * fretHeight + fretHeight / 2;
          // Assuming fromString=6 is the thinnest string (index 5) and fromString=1 is the thickest (index 0)
          const startStringIndex = barre.fromString - 1;
          const endStringIndex = barre.toString - 1;
          const x = startStringIndex * stringWidth;
          const barreWidth = (endStringIndex - startStringIndex) * stringWidth;

          return (
            <rect
              key={`barre-${i}`}
              x={x}
              y={y - (lite ? 3 : 4)}
              width={barreWidth}
              height={lite ? 6 : 8}
              rx={lite ? 2 : 3}
              fill="#4a4a4a"
            />
          );
        })}

        {/* Finger Positions */}
        {frets.map((fret, stringIndex) => {
          if (typeof fret === 'number' && fret > 0) {
            const y = (fret - baseFret + 0.5) * fretHeight + fretHeight / 2;
            return <circle key={`dot-${stringIndex}`} cx={stringIndex * stringWidth} cy={y} r={lite ? 5 : 6} fill="#4a4a4a" />;
          }
          return null;
        })}

      </svg>
    </div>
  );
};

export default ChordDiagram;
