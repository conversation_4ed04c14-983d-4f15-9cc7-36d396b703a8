'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  Music, Edit3, Wand2, Play, Pause, Save, Settings,
  Guitar, Hash, SkipForward, RotateCcw, Plus, Trash2,
  ChevronDown, ChevronUp, Sparkles, Brain, Target,
  Volume2, Clock, Layers, Eye, EyeOff, FileText, Info,
  User, Calendar, Mic, GripVertical
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { ChordVisualizerAdvanced } from './ChordVisualizerAdvanced';

// Types
interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro';
  title: string;
  content: string;
  chords: string[];
}

interface SongInfo {
  id: string;
  title: string;
  artist: string;
  audioFile?: string;
  duration?: string;
  key: string;
  tempo: number;
  timeSignature: string;
  genre?: string;
  createdAt: string;
  lastModified: string;
}

interface AIComposerCompactLayoutProps {
  songId?: string;
  initialContent?: string;
  songInfo?: SongInfo;
  onContentChange?: (content: string) => void;
  onSave?: () => Promise<void>;
  onSongInfoChange?: (info: Partial<SongInfo>) => void;
}

// Constantes
const QUICK_AI_ACTIONS = [
  { id: 'continue', label: 'Continuer', icon: SkipForward, color: 'bg-blue-500' },
  { id: 'improve', label: 'Améliorer', icon: Wand2, color: 'bg-green-500' },
  { id: 'rhyme', label: 'Rimes', icon: Hash, color: 'bg-purple-500' },
  { id: 'rewrite', label: 'Réécrire', icon: RotateCcw, color: 'bg-orange-500' }
];

const CHORD_SUGGESTIONS = [
  { name: 'C', type: 'major' },
  { name: 'Am', type: 'minor' },
  { name: 'F', type: 'major' },
  { name: 'G', type: 'major' },
  { name: 'Dm', type: 'minor' },
  { name: 'Em', type: 'minor' }
];

// Le composant ChordVisualizerAdvanced est maintenant importé depuis un fichier séparé

// Composant principal
export const AIComposerCompactLayout: React.FC<AIComposerCompactLayoutProps> = ({
  songId,
  initialContent = '',
  songInfo,
  onContentChange,
  onSave,
  onSongInfoChange
}) => {
  // États principaux
  const [content, setContent] = useState(initialContent);
  const [selectedSection, setSelectedSection] = useState('verse-1');
  const [sections, setSections] = useState<LyricsSection[]>([
    { id: 'verse-1', type: 'verse', title: 'Couplet 1', content: '', chords: [] },
    { id: 'chorus-1', type: 'chorus', title: 'Refrain', content: '', chords: [] }
  ]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const [showChords, setShowChords] = useState(true);
  const [activeTab, setActiveTab] = useState('ai');
  const [selectedChordForVisualizer, setSelectedChordForVisualizer] = useState<string | null>(null);
  const [showSongInfo, setShowSongInfo] = useState(true);
  
  // États pour les colonnes redimensionnables
  const [leftWidth, setLeftWidth] = useState(224); // 14rem = 224px
  const [rightWidth, setRightWidth] = useState(256); // 16rem = 256px
  const [isResizing, setIsResizing] = useState<'left' | 'right' | null>(null);
  
  // Informations du morceau par défaut
  const [currentSongInfo, setCurrentSongInfo] = useState<SongInfo>(songInfo || {
    id: songId || 'new-song',
    title: 'Nouveau Morceau',
    artist: 'Artiste Inconnu',
    key: 'C Major',
    tempo: 120,
    timeSignature: '4/4',
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString()
  });
  
  // État pour l'affichage de l'heure et de la date (client-side seulement)
  const [displayTime, setDisplayTime] = useState<string>('--:--:--');
  const [displayDate, setDisplayDate] = useState<string>('--/--/----');
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Effet pour mettre à jour l'heure d'affichage côté client uniquement
  useEffect(() => {
    const updateDisplayTime = () => {
      setDisplayTime(new Date(currentSongInfo.lastModified).toLocaleTimeString());
    };
    
    updateDisplayTime();
  }, [currentSongInfo.lastModified]);

  // Gestionnaires
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    onContentChange?.(newContent);
  }, [onContentChange]);

  const handleSave = useCallback(async () => {
    try {
      await onSave?.();
      toast({ title: "Sauvegardé", description: "Votre composition a été sauvegardée." });
    } catch (error) {
      toast({ title: "Erreur", description: "Impossible de sauvegarder.", variant: "destructive" });
    }
  }, [onSave]);

  const handleAIAction = useCallback(async (actionId: string) => {
    setAiLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast({ title: "IA", description: `Action ${actionId} exécutée avec succès.` });
    } catch (error) {
      toast({ title: "Erreur IA", description: "Impossible d'exécuter l'action.", variant: "destructive" });
    } finally {
      setAiLoading(false);
    }
  }, []);

  const addChord = useCallback((chord: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + `[${chord}]` + content.substring(end);
      handleContentChange(newContent);
      
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + chord.length + 2, start + chord.length + 2);
      }, 0);
    }
  }, [content, handleContentChange]);

  // Gestionnaires de redimensionnement
  const handleMouseDown = useCallback((side: 'left' | 'right') => {
    setIsResizing(side);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !containerRef.current) return;
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    
    if (isResizing === 'left') {
      const newWidth = Math.max(200, Math.min(400, e.clientX - containerRect.left));
      setLeftWidth(newWidth);
    } else if (isResizing === 'right') {
      const newWidth = Math.max(200, Math.min(400, containerRect.right - e.clientX));
      setRightWidth(newWidth);
    }
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(null);
  }, []);

  // Gestionnaire de mise à jour des informations du morceau
  const updateSongInfo = useCallback((updates: Partial<SongInfo>) => {
    const updatedInfo = { ...currentSongInfo, ...updates, lastModified: new Date().toISOString() };
    setCurrentSongInfo(updatedInfo);
    onSongInfoChange?.(updatedInfo);
  }, [currentSongInfo, onSongInfoChange]);

  // Effets pour les événements de souris
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  return (
    <div className="h-screen w-screen flex flex-col bg-background overflow-hidden">
      {/* Header compact */}
      <div className="border-b bg-card/30 px-4 py-2">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Music className="w-5 h-5 text-primary" />
              <h1 className="font-semibold text-lg">AI Composer</h1>
            </div>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center gap-3">
              <div className="flex flex-col">
                <Input
                  value={currentSongInfo.title}
                  onChange={(e) => updateSongInfo({ title: e.target.value })}
                  className="h-6 text-sm font-medium border-0 bg-transparent p-0 focus-visible:ring-0"
                  placeholder="Titre du morceau"
                />
                <Input
                  value={currentSongInfo.artist}
                  onChange={(e) => updateSongInfo({ artist: e.target.value })}
                  className="h-4 text-xs text-muted-foreground border-0 bg-transparent p-0 focus-visible:ring-0"
                  placeholder="Artiste"
                />
              </div>
              <Badge variant="outline" className="text-xs">
                Brouillon
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowSongInfo(!showSongInfo)}
              className={showSongInfo ? 'bg-accent' : ''}
            >
              <Info className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsPlaying(!isPlaying)}>
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={handleSave}>
              <Save className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Panneau d'informations du morceau */}
      {showSongInfo && (
        <div className="border-b bg-card/20 px-4 py-3">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">Créé</div>
                  <div className="text-xs">{displayDate}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">Durée</div>
                  <div className="text-xs">{currentSongInfo.duration || '0:00'}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Hash className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">Tonalité</div>
                  <Input
                    value={currentSongInfo.key}
                    onChange={(e) => updateSongInfo({ key: e.target.value })}
                    className="h-5 text-xs border-0 bg-transparent p-0 focus-visible:ring-0"
                  />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Volume2 className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">Tempo</div>
                  <Input
                    type="number"
                    value={currentSongInfo.tempo}
                    onChange={(e) => updateSongInfo({ tempo: parseInt(e.target.value) || 120 })}
                    className="h-5 text-xs border-0 bg-transparent p-0 focus-visible:ring-0 w-12"
                  />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Music className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">Mesure</div>
                  <Input
                    value={currentSongInfo.timeSignature}
                    onChange={(e) => updateSongInfo({ timeSignature: e.target.value })}
                    className="h-5 text-xs border-0 bg-transparent p-0 focus-visible:ring-0 w-8"
                  />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Mic className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">Fichier audio</div>
                  <div className="text-xs">{currentSongInfo.audioFile || 'Aucun'}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Corps principal */}
      <div ref={containerRef} className="flex-1 flex flex-row relative w-full"> {/* MODIFIED: removed overflow-hidden */}
        {/* Sidebar gauche - Structure */}
        <div 
          className="border-r bg-card/30 flex flex-col relative h-full"
          style={{ width: `${leftWidth}px`, minWidth: '200px', maxWidth: '400px' }}
        >
          <div className="p-3 border-b">
            <h2 className="text-sm font-medium flex items-center gap-2">
              <Layers className="w-4 h-4" />
              Structure
            </h2>
          </div>
          
          <ScrollArea className="flex-1 p-3">
            <div className="space-y-2">
              {sections.map((section) => (
                <Button
                  key={section.id}
                  variant={selectedSection === section.id ? "default" : "ghost"}
                  size="sm"
                  className="w-full justify-start h-8 text-xs"
                  onClick={() => setSelectedSection(section.id)}
                >
                  <FileText className="w-3 h-3 mr-2" />
                  {section.title}
                </Button>
              ))}
              
              <Button
                variant="outline"
                size="sm"
                className="w-full h-8 text-xs border-dashed"
                onClick={() => {
                  const newSection = {
                    id: `section-${Date.now()}`,
                    type: 'verse' as const,
                    title: `Section ${sections.length + 1}`,
                    content: '',
                    chords: []
                  };
                  setSections([...sections, newSection]);
                }}
              >
                <Plus className="w-3 h-3 mr-1" />
                Ajouter
              </Button>
            </div>
            
            <Separator className="my-4" />
            
            {/* Timeline mini */}
            <div className="space-y-2">
              <h3 className="text-xs font-medium text-muted-foreground">Timeline</h3>
              <div className="bg-muted rounded p-2">
                <div className="flex gap-1 mb-1">
                  <div className="w-6 h-1.5 bg-green-400 rounded" />
                  <div className="w-8 h-1.5 bg-red-400 rounded" />
                  <div className="w-6 h-1.5 bg-green-400 rounded" />
                  <div className="w-8 h-1.5 bg-red-400 rounded" />
                </div>
                <div className="text-xs text-muted-foreground">0:00 - 3:20</div>
              </div>
            </div>
          </ScrollArea>
          
          {/* Poignée de redimensionnement gauche */}
          <div 
            className="absolute top-0 right-0 w-1 h-full bg-border hover:bg-primary/50 cursor-col-resize transition-colors group"
            onMouseDown={() => handleMouseDown('left')}
          >
            <div className="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
              <GripVertical className="w-3 h-3 text-muted-foreground" />
            </div>
          </div>
        </div>

        {/* Zone centrale - Éditeur */}
        <div 
          className="flex-1 flex flex-col min-w-0 h-full"
          style={{ 
            // width: `calc(100% - ${leftWidth}px - ${rightWidth}px)`, // Laisser flex-1 gérer la largeur
            minWidth: '300px'
          }}
        >
          {/* Toolbar éditeur */}
          {/* Toolbar éditeur */}
          <div className="border-b bg-card/20 px-4 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className="text-xs">
                  {sections.find(s => s.id === selectedSection)?.title || 'Section'}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChords(!showChords)}
                  className="h-7 px-2"
                >
                  <Guitar className="w-3 h-3 mr-1" />
                  <span className="text-xs">
                    {showChords ? 'Masquer' : 'Afficher'} accords
                  </span>
                </Button>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {content.length} caractères
              </div>
            </div>
          </div>

          {/* Éditeur principal */}
          <div className="flex-1 p-4 flex flex-col"> {/* MODIFIED: removed overflow-hidden, added flex flex-col */}
            <div className="flex-1 relative"> {/* MODIFIED: h-full to flex-1 */}
              <Textarea
                ref={textareaRef}
                value={content}
                onChange={(e) => handleContentChange(e.target.value)}
                placeholder="Écrivez vos paroles ici...\n\nUtilisez [C] pour ajouter des accords\nEx: [C]Hello [Am]world [F]this is my [G]song"
                // MODIFIED: Use absolute positioning to fill its relative parent
                className="absolute inset-0 w-full h-full resize-none text-base leading-relaxed font-mono border-0 focus-visible:ring-0 bg-transparent overflow-y-auto" // MODIFIED: added overflow-y-auto
              />
              
              {showChords && (
                <div className="absolute top-2 right-2 opacity-60 z-10"> {/* Added z-10 */}
                  <Badge variant="outline" className="text-xs">
                    Mode accords
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar droite - Outils */}
        <div 
          className="border-l bg-card/30 flex flex-col relative h-full"
          style={{ width: `${rightWidth}px`, minWidth: '200px', maxWidth: '400px' }}
        >
          {/* Poignée de redimensionnement droite */}
          <div 
            className="absolute top-0 left-0 w-1 h-full bg-border hover:bg-primary/50 cursor-col-resize transition-colors group"
            onMouseDown={() => handleMouseDown('right')}
          >
            <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
              <GripVertical className="w-3 h-3 text-muted-foreground" />
            </div>
          </div>
          <div className="p-3 border-b">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 h-8">
                <TabsTrigger value="ai" className="text-xs h-6">
                  <Brain className="w-3 h-3 mr-1" />
                  IA
                </TabsTrigger>
                <TabsTrigger value="chords" className="text-xs h-6">
                  <Guitar className="w-3 h-3 mr-1" />
                  Accords
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          
          <ScrollArea className="flex-1 p-3">
            {activeTab === 'ai' && (
              <div className="space-y-4">
                {/* Actions IA rapides */}
                <div>
                  <h3 className="text-sm font-medium mb-3">Actions rapides</h3>
                  <div className="grid grid-cols-2 gap-2"> {/* Reduced gap */}
                    {QUICK_AI_ACTIONS.map((action) => (
                      <Button
                        key={action.id}
                        variant="outline"
                        size="sm"
                        className={`h-10 text-xs flex-col items-center justify-center ${action.color} text-white hover:opacity-90`}
                        onClick={() => handleAIAction(action.id)}
                        disabled={aiLoading}
                      >
                        <action.icon className="w-4 h-4 mb-1" />
                        {action.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Suggestions IA avancées (placeholder) */}
                <div>
                  <h3 className="text-sm font-medium mb-2 mt-4">Suggestions avancées</h3>
                  <div className="p-3 bg-muted rounded text-xs text-muted-foreground">
                    Bientôt disponible : suggestions de structure, progression d'accords, etc.
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'chords' && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-3">Ajouter un accord</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {CHORD_SUGGESTIONS.map((chord) => (
                      <Button
                        key={chord.name}
                        variant="outline"
                        size="sm"
                        className="h-9 text-xs"
                        onClick={() => {
                          addChord(chord.name);
                          setSelectedChordForVisualizer(chord.name);
                        }}
                      >
                        {chord.name}
                      </Button>
                    ))}
                  </div>
                </div>
                
                <Separator />

                {/* Chord Visualizer */}
                <ChordVisualizerAdvanced 
                  chord={selectedChordForVisualizer || 'C'}
                  instrument="guitar"
                  onChordChange={(newChord) => {
                    setSelectedChordForVisualizer(newChord);
                    addChord(newChord);
                  }}
                  showTheory={true}
                  showAlternatives={true}
                />

                <div>
                  <h3 className="text-sm font-medium mb-2 mt-4">Bibliothèque d'accords</h3>
                  <div className="p-3 bg-muted rounded text-xs text-muted-foreground">
                    Recherche et gestion des accords (à venir).
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'chords' && (
              <div className="space-y-4">
                {/* Accords suggérés */}
                <div>
                  <h3 className="text-sm font-medium mb-3">Accords suggérés</h3>
                  <div className="grid grid-cols-3 gap-1">
                    {CHORD_SUGGESTIONS.map((chord) => (
                      <Button
                        key={chord.name}
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => {
                          addChord(chord.name);
                          setSelectedChordForVisualizer(chord.name);
                        }}
                      >
                        {chord.name}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Visualiseur d'accords */}
                {selectedChordForVisualizer && (
                  <div>
                    <h3 className="text-sm font-medium mb-3">Visualiseur</h3>
                    <ChordVisualizerAdvanced 
                      chord={selectedChordForVisualizer}
                      instrument="guitar"
                      onChordChange={(newChord) => {
                        setSelectedChordForVisualizer(newChord);
                        addChord(newChord);
                      }}
                      showTheory={true}
                      showAlternatives={true}
                    />
                  </div>
                )}
                
                <Separator />
                
                {/* Progressions */}
                <div>
                  <h3 className="text-sm font-medium mb-3">Progressions</h3>
                  <div className="space-y-1">
                    {[
                      'C - Am - F - G',
                      'Am - F - C - G',
                      'F - G - Am - Am'
                    ].map((progression, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start h-8 text-xs"
                        onClick={() => {
                          const chords = progression.split(' - ');
                          chords.forEach(chord => addChord(chord));
                        }}
                      >
                        {progression}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </div>
      </div>
      
      {/* Footer avec statut */}
      <div className="border-t bg-card/20 px-4 py-2">
        <div className="flex items-center justify-between max-w-7xl mx-auto text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Tonalité: {currentSongInfo.key}</span>
            <span>Tempo: {currentSongInfo.tempo} BPM</span>
            <span>Mesure: {currentSongInfo.timeSignature}</span>
            <span>{content.length} caractères</span>
            {currentSongInfo.genre && <span>Genre: {currentSongInfo.genre}</span>}
          </div>
          <div className="flex items-center gap-4">
            <span>Modifié: {displayTime}</span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>Sauvegarde automatique</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};