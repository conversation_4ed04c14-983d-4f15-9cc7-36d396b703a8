<#
.SYNOPSIS
    Script de migration de la base de données Mouvik
.DESCRIPTION
    Ce script permet d'exécuter la migration de la table 'tracks' vers 'songs'.
    Il gère automatiquement les dépendances et les configurations nécessaires.
.NOTES
    Version: 1.0
    Auteur: Équipe <PERSON>
#>

# Configuration
$ErrorActionPreference = "Stop"
$env:Path += ";C:\Program Files\PostgreSQL\15\bin"

# Variables
$rootDir = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$migrationsDir = Join-Path $rootDir "db\migrations"
$logFile = Join-Path $migrationsDir "migration_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# Fonction pour écrire dans le journal
function Write-Log {
    param (
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Add-Content -Path $logFile -Value $logMessage
    
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "INFO"  { Write-Host $logMessage -ForegroundColor Cyan }
        default { Write-Host $logMessage }
    }
}

# Vérifier les prérequis
try {
    Write-Log "Vérification des prérequis..."
    
    # Vérifier si sqlcmd est disponible
    try {
        $null = Get-Command sqlcmd -ErrorAction Stop
        $sqlCmdAvailable = $true
    } catch {
        $sqlCmdAvailable = $false
    }
    
    # Vérifier si psql est disponible
    try {
        $null = Get-Command psql -ErrorAction Stop
        $psqlAvailable = $true
    } catch {
        $psqlAvailable = $false
    }
    
    if (-not ($sqlCmdAvailable -or $psqlAvailable)) {
        throw "Aucun outil de ligne de commande SQL trouvé. Installez soit SQL Server CLI (sqlcmd) soit PostgreSQL CLI (psql)."
    }
    
    # Vérifier si le fichier de migration existe
    $migrationFile = Join-Path $migrationsDir "20240516_rename_tracks_to_songs.sql"
    if (-not (Test-Path $migrationFile)) {
        throw "Fichier de migration introuvable: $migrationFile"
    }
    
    Write-Log "Tous les prérequis sont satisfaits"
    
} catch {
    Write-Log "Erreur lors de la vérification des prérequis: $_" -Level ERROR
    exit 1
}

# Fonction pour exécuter une requête SQL
function Invoke-SqlQuery {
    param (
        [string]$Query,
        [string]$Database,
        [string]$Server = "localhost",
        [string]$User = "postgres",
        [System.Security.SecureString]$SecurePassword = $null
    )
    
    # Convertir SecureString en texte clair si fourni
    $Password = if ($SecurePassword) {
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword)
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
    } else {
        "postgres"  # Valeur par défaut
    }
    
    if ($psqlAvailable) {
        if ($Password) { $env:PGPASSWORD = $Password }
        $arguments = @(
            "-h", $Server,
            "-U", $User,
            "-d", $Database,
            "-c", $Query
        )
        
        $output = & psql $arguments 2>&1
        $output
    } 
    elseif ($sqlCmdAvailable) {
        $arguments = @(
            "-S", "$Server,1433",
            "-U", $User,
            "-d", $Database,
            "-Q", $Query
        )
        
        if ($Password) {
            $arguments += "-P"
            $arguments += $Password
        }
        
        $output = & sqlcmd $arguments 2>&1
        $output
    }
    else {
        throw "Aucun outil SQL disponible pour exécuter la requête"
    }
}

# Fonction pour effectuer la sauvegarde
function Backup-Database {
    param (
        [string]$BackupPath = (Join-Path $rootDir "backup"),
        [System.Security.SecureString]$SecurePassword = $null
    )
    
    # Convertir SecureString en texte clair si fourni
    $Password = if ($SecurePassword) {
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword)
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
    } else {
        "postgres"  # Valeur par défaut
    }
    
    try {
        if (-not (Test-Path $BackupPath)) {
            New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
        }
        
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFile = Join-Path $BackupPath "mouvik_backup_${timestamp}.sql"
        
        Write-Log "Création d'une sauvegarde dans $backupFile..."
        
        if ($psqlAvailable) {
            if (-not $env:PGPASSWORD) { $env:PGPASSWORD = $Password }
            & pg_dump -h localhost -U postgres -d mouvik -F c -f $backupFile
        }
        else {
            Write-Log "La sauvegarde automatique n'est pas disponible avec sqlcmd. Veuillez effectuer une sauvegarde manuelle." -Level WARN
            $backupFile = $null
        }
        
        return $backupFile
    }
    catch {
        Write-Log "Erreur lors de la sauvegarde: $_" -Level ERROR
        return $null
    }
}

# Fonction principale de migration
function Invoke-Migration {
    param (
        [switch]$Rollback,
        [System.Security.SecureString]$SecurePassword = $null
    )
    
    try {
        # Convertir SecureString en texte clair si fourni et le stocker dans une variable de script
        if ($SecurePassword) {
            $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword)
            $script:DatabasePassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
            [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)  # Nettoyer la mémoire
        } else {
            $script:DatabasePassword = "postgres"  # Valeur par défaut
        }
        
        # Vérifier que le mot de passe est défini
        if (-not $script:DatabasePassword) {
            throw "Mot de passe de base de données non défini"
        }
        # Sauvegarder la base de données
        $backupFile = Backup-Database
        if ($backupFile) {
            Write-Log "Sauvegarde créée avec succès: $backupFile"
        }
        
        # Lire le fichier de migration ou de rollback
        $migrationFile = if ($Rollback) {
            Join-Path $migrationsDir "rollback_20240516_rename_tracks_to_songs.sql"
        } else {
            Join-Path $migrationsDir "20240516_rename_tracks_to_songs.sql"
        }
        
        if (-not (Test-Path $migrationFile)) {
            throw "Fichier de migration non trouvé: $migrationFile"
        }
        
        Write-Log "Exécution de la migration: $(Split-Path $migrationFile -Leaf)"
        
        # Lire et exécuter le script SQL ligne par ligne
        $sqlContent = Get-Content $migrationFile -Raw
        $batches = $sqlContent -split "\n\s*GO\s*(?:\n|$)" | Where-Object { $_.Trim() -ne "" }
        
        foreach ($batch in $batches) {
            if ($batch.Trim() -eq "") { continue }
            
            Write-Log "Exécution du lot SQL..."
            $result = Invoke-SqlQuery -Query $batch -Database "mouvik"
            
            if ($LASTEXITCODE -ne 0) {
                throw "Erreur lors de l'exécution du lot SQL: $result"
            }
            
            Write-Log "Lot SQL exécuté avec succès"
        }
        
        $action = if ($Rollback) { "rollback" } else { "migration" }
        Write-Log "$action terminée avec succès" -Level "SUCCESS"
        return 0
        
    } catch {
        $errorMessage = "Erreur lors de la migration: $_"
        Write-Log $errorMessage -Level ERROR
        
        if ($backupFile -and (Test-Path $backupFile)) {
            $backupMsg = "Une sauvegarde est disponible ici: $backupFile"
            Write-Log $backupMsg -Level WARN
        }
        
        Write-Error $errorMessage
        return 1
    }
}

# Fonction principale
function Main {
    try {
        # Charger la configuration
        $configPath = Join-Path $PSScriptRoot "config.ps1"
        if (-not (Test-Path $configPath)) {
            throw "Fichier de configuration introuvable: $configPath"
        }

        # Importer la configuration
        $config = . $configPath

        # Vérifier la configuration de la base de données
        if (-not $config.Database) {
            throw "Configuration de la base de données manquante"
        }

        # Convertir le mot de passe en SecureString
        $securePassword = if ($config.Database.Password) {
            ConvertTo-SecureString $config.Database.Password -AsPlainText -Force
        } else {
            Write-Warning "Aucun mot de passe défini, utilisation de la valeur par défaut"
            ConvertTo-SecureString "postgres" -AsPlainText -Force
        }

        # Vérifier l'action demandée
        $action = if ($args.Count -gt 0) { $args[0].ToLower() } else { "" }
        
        if (@("up", "down") -notcontains $action) {
            throw "Action non valide. Utilisez 'up' pour migrer ou 'down' pour annuler."
        }

        # Exécuter l'action demandée
        switch ($action) {
            "up" {
                Write-Log "Démarrage de la migration..."
                Invoke-Migration -SecurePassword $securePassword
            }
            "down" {
                Write-Log "Démarrage du rollback..."
                Invoke-Migration -Rollback -SecurePassword $securePassword
            }
        }
        
        Write-Log "Opération terminée avec succès" -Level "SUCCESS"
        return 0
    } catch {
        Write-Log "ERREUR: $_" -Level "ERROR"
        Write-Error $_
        return 1
    }
}

# Point d'entrée principal
$exitCode = Main
Exit $exitCode
