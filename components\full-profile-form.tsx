"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

let fetchProfileInstanceCounter = 0;
import { type User } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';

import { profileFormSchema, type ProfileFormValues } from './profile/profile-form-schema';
import { createBrowserClient } from '@/lib/supabase/client';
import { useToast } from '@/hooks/use-toast';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Loader2, Save } from 'lucide-react';

import { ProfileGeneralTab } from './profile/profile-general-tab';
import { ProfileArtistTab } from './profile/profile-artist-tab';
import { ProfileSocialTab } from './profile/profile-social-tab';
import { ProfileSettingsTab } from './profile/profile-settings-tab';

interface FullProfileFormProps {
  user: User;
}

export function FullProfileForm({ user }: FullProfileFormProps) {
  const supabase = createBrowserClient();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {},
    mode: 'onChange',
  });

  const fetchProfile = useCallback(async () => {
    fetchProfileInstanceCounter++;
    const currentFetchId = fetchProfileInstanceCounter;
    console.log(`[FullProfileForm #${currentFetchId}] fetchProfile started.`);
    if (!user || !supabase) {
      console.log(`[FullProfileForm #${currentFetchId}] User or Supabase client not available yet.`);
      setLoading(false); // Ensure loading is false if we exit early
      return;
    }
    console.log(`[FullProfileForm #${currentFetchId}] Fetching profile for user ID: ${user.id}`);
    try {
      console.log(`[FullProfileForm #${currentFetchId}] PRE-QUERY: About to call Supabase client for profiles.`);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      console.log(`[FullProfileForm #${currentFetchId}] Supabase response - data:`, data);
      console.log(`[FullProfileForm #${currentFetchId}] Supabase response - error:`, error);

      if (error) {
        console.error(`[FullProfileForm #${currentFetchId}] Error fetching profile:`, error);
        toast({
          title: 'Erreur',
          description: `Erreur lors de la récupération du profil: ${error.message}`,
          variant: 'destructive',
        });
      } else if (data) {
        console.log(`[FullProfileForm #${currentFetchId}] Data found, attempting form.reset with:`, data);
        form.reset(data);
        console.log(`[FullProfileForm #${currentFetchId}] form.reset completed.`);
      } else {
        console.log(`[FullProfileForm #${currentFetchId}] No data and no error, profile might not exist.`);
      }
    } catch (error) {
      console.error(`[FullProfileForm #${currentFetchId}] EXCEPTION during fetch/process profile:`, error);
      toast({
        title: 'Erreur Critique',
        description: `Une erreur inattendue est survenue: ${(error as Error).message}`,
        variant: 'destructive',
      });
    } finally {
      console.log(`[FullProfileForm #${currentFetchId}] fetchProfile finally block, setting loading to false.`);
      setLoading(false);
    }
  }, [user, supabase, form, toast, setLoading]);

  useEffect(() => {
    setLoading(true);
    console.log(`[FullProfileForm] useEffect triggered. Scheduling fetchProfile with a delay.`);
    const timerId = setTimeout(() => {
      console.log(`[FullProfileForm] setTimeout expired. Calling fetchProfile.`);
      fetchProfile();
    }, 500); // 500ms delay
    return () => clearTimeout(timerId); // Cleanup timer if component unmounts
  }, [fetchProfile, setLoading]);


  const onSubmit = async (values: ProfileFormValues) => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .upsert({ ...values, id: user.id, updated_at: new Date().toISOString() });

      if (error) throw error;

      toast({
        title: 'Profil mis à jour',
        description: 'Vos informations ont été sauvegardées avec succès.',
      });
      router.refresh();
    } catch (error: any) {
      toast({
        title: 'Erreur lors de la sauvegarde',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center p-8"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <FormProvider {...form}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-6">
              <TabsTrigger value="general">Général</TabsTrigger>
              <TabsTrigger value="artist">Artiste</TabsTrigger>
              <TabsTrigger value="social">Réseaux & Liens</TabsTrigger>
              <TabsTrigger value="settings">Paramètres</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general">
              <ProfileGeneralTab />
            </TabsContent>
            <TabsContent value="artist">
              <ProfileArtistTab />
            </TabsContent>
            <TabsContent value="social">
              <ProfileSocialTab />
            </TabsContent>
            <TabsContent value="settings">
              <ProfileSettingsTab />
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-4">
            <Button type="submit" disabled={isSaving || !form.formState.isDirty}>
              {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Enregistrer les modifications
            </Button>
          </div>
        </form>
      </Form>
    </FormProvider>
  );
}
