"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Play } from "lucide-react";
import { getSupabaseClient } from "@/lib/supabase/client";
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore";
import { useToast } from "@/hooks/use-toast";
import type { Song } from "@/types"; // Assuming Song type is comprehensive
import { useState } from "react";

interface PlayPlaylistButtonProps {
  playlistId: string;
  playlistName?: string; // Optional, for toast messages
  songs: Song[]; // Array of full song objects for the player
  initialPlayCount?: number; // To display and optimistically update
  onPlayCountIncremented?: (newPlayCount: number) => void; // Callback for parent to update display
  buttonText?: string;
  buttonSize?: "sm" | "lg" | "default" | "icon";
  className?: string;
}

export function PlayPlaylistButton({
  playlistId,
  playlistName = "Playlist",
  songs,
  initialPlayCount,
  onPlayCountIncremented,
  buttonText = "Écouter Tout",
  buttonSize = "lg",
  className,
}: PlayPlaylistButtonProps) {
  const supabase = getSupabaseClient();
  const playSong = useAudioPlayerStore(state => state.playSong);
  const setQueue = useAudioPlayerStore(state => state.setQueue);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [currentPlayCount, setCurrentPlayCount] = useState(initialPlayCount);

  const handlePlayPlaylist = async () => {
    if (songs.length === 0) {
      toast({ title: "Playlist Vide", description: "Cette playlist ne contient aucun morceau.", variant: "default" });
      return;
    }
    setIsLoading(true);
    try {
      // Increment play count
      const { data, error } = await supabase.rpc('increment_playlist_plays', { p_playlist_id: playlistId });
      if (error) {
        console.error("Error incrementing playlist play count:", error);
        // Don't block playback for this error, but log it
      } else if (data !== null) {
        setCurrentPlayCount(data); // Update local count from RPC response
        if (onPlayCountIncremented) {
          onPlayCountIncremented(data);
        }
      }

      // Set queue and play first song
      setQueue(songs, true, 0); 

    } catch (rpcError) {
      console.error("Unexpected error during play playlist:", rpcError);
      toast({ title: "Erreur", description: "Impossible de lancer la lecture de la playlist.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      size={buttonSize}
      className={className ? className : "gap-2"}
      onClick={handlePlayPlaylist}
      disabled={isLoading || songs.length === 0}
    >
      <Play className="h-5 w-5" /> {buttonText}
      {/* Optionally display play count if needed, though parent usually does */}
      {/* {currentPlayCount !== undefined && <span className="ml-2 text-xs">({currentPlayCount})</span>} */}
    </Button>
  );
}
