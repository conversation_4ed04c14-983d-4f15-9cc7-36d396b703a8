import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, Lightbulb, History, Settings, Wand2, RefreshCcw,
  Zap, Target, BarChart3, Music, Guitar, Piano, Mic,
  Save, Trash2, Copy, Download, Upload, Play, Pause,
  ChevronDown, ChevronUp, Search, Plus, Edit3, X,
  GripVertical
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

// Types
interface AIAction {
  id: string;
  type: 'suggestion' | 'correction' | 'translation' | 'rhyme' | 'structure';
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  prompt: string;
  category: 'lyrics' | 'chords' | 'structure' | 'melody';
}

interface CustomPrompt {
  id: string;
  name: string;
  prompt: string;
  category: string;
  variables: string[];
  createdAt: Date;
  usageCount: number;
}

interface AIHistoryItem {
  id: string;
  prompt: string;
  result: string;
  type: string;
  timestamp: Date;
  applied: boolean;
}

interface ChordData {
  id: string;
  name: string;
  instrument: string;
  fingering: number[];
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  variations: string[];
}

interface UnifiedAIWorkspaceProps {
  onAIGenerate: (prompt: string, type: string) => Promise<string>;
  onInsertChord: (chord: string, position?: number) => void;
  onInsertText: (text: string, position?: number) => void;
  currentSection?: string;
  selectedText?: string;
  aiHistory?: AIHistoryItem[];
  isLoading?: boolean;
  currentSong?: any; // Assuming Song type is defined elsewhere or using 'any' as placeholder
}

// Actions rapides prédéfinies
const QUICK_ACTIONS: AIAction[] = [
  {
    id: 'suggest-lyrics',
    type: 'suggestion',
    title: 'Suggérer des paroles',
    description: 'Générer des paroles pour cette section',
    icon: Lightbulb,
    prompt: 'Écris des paroles pour un {sectionType} dans le style {genre} avec le thème {theme}',
    category: 'lyrics'
  },
  {
    id: 'improve-lyrics',
    type: 'correction',
    title: 'Améliorer les paroles',
    description: 'Améliorer le style et la fluidité',
    icon: RefreshCcw,
    prompt: 'Améliore ces paroles en gardant le sens mais en améliorant le style et la fluidité: {selectedText}',
    category: 'lyrics'
  },
  {
    id: 'find-rhymes',
    type: 'rhyme',
    title: 'Trouver des rimes',
    description: 'Suggérer des mots qui riment',
    icon: Target,
    prompt: 'Trouve des mots qui riment avec: {selectedText}',
    category: 'lyrics'
  },
  {
    id: 'suggest-chords',
    type: 'suggestion',
    title: 'Suggérer des accords',
    description: 'Proposer une progression d\'accords',
    icon: Music,
    prompt: 'Suggère une progression d\'accords pour {sectionType} en {key} dans le style {genre}',
    category: 'chords'
  },
  {
    id: 'analyze-structure',
    type: 'structure',
    title: 'Analyser la structure',
    description: 'Analyser et suggérer des améliorations',
    icon: BarChart3,
    prompt: 'Analyse la structure de cette chanson et suggère des améliorations',
    category: 'structure'
  }
];

// Accords prédéfinis avec support drag & drop
const CHORD_LIBRARY: ChordData[] = [
  {
    id: 'c-major',
    name: 'C',
    instrument: 'guitar',
    fingering: [0, 1, 0, 2, 3, 0],
    difficulty: 'easy',
    category: 'major',
    variations: ['Cmaj7', 'Cadd9', 'C6']
  },
  {
    id: 'g-major',
    name: 'G',
    instrument: 'guitar',
    fingering: [3, 2, 0, 0, 3, 3],
    difficulty: 'easy',
    category: 'major',
    variations: ['G7', 'Gmaj7', 'Gsus4']
  },
  {
    id: 'am-minor',
    name: 'Am',
    instrument: 'guitar',
    fingering: [0, 0, 2, 2, 1, 0],
    difficulty: 'easy',
    category: 'minor',
    variations: ['Am7', 'Am6', 'Asus2']
  },
  {
    id: 'f-major',
    name: 'F',
    instrument: 'guitar',
    fingering: [1, 1, 3, 3, 2, 1],
    difficulty: 'medium',
    category: 'major',
    variations: ['Fmaj7', 'F6', 'Fsus2']
  }
];

export const UnifiedAIWorkspace: React.FC<UnifiedAIWorkspaceProps> = ({
  onAIGenerate,
  onInsertChord,
  onInsertText,
  currentSection = 'verse-1',
  selectedText = '',
  aiHistory = [],
  isLoading = false
}) => {
  // États
  const [activeTab, setActiveTab] = useState('quick-actions');
  const [customPrompts, setCustomPrompts] = useState<CustomPrompt[]>([]);
  const [newPrompt, setNewPrompt] = useState({ name: '', prompt: '', category: 'lyrics' });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedInstrument, setSelectedInstrument] = useState('guitar');
  const [chordFilter, setChordFilter] = useState('all');
  const [draggedChord, setDraggedChord] = useState<ChordData | null>(null);
  
  // Refs pour le drag & drop
  const dragRef = useRef<HTMLDivElement>(null);

  // Gestionnaires d'événements
  const handleQuickAction = useCallback(async (action: AIAction) => {
    try {
      let processedPrompt = action.prompt;
      
      // Remplacer les variables dans le prompt
      processedPrompt = processedPrompt.replace('{selectedText}', selectedText);
      processedPrompt = processedPrompt.replace('{sectionType}', currentSection);
      
      const result = await onAIGenerate(processedPrompt, action.type);
      
      if (action.category === 'chords') {
        // Insérer les accords suggérés
        onInsertText(result);
      } else {
        // Insérer le texte généré
        onInsertText(result);
      }
      
      toast({
        title: "Action IA réussie",
        description: `${action.title} appliquée avec succès.`
      });
    } catch (error) {
      toast({
        title: "Erreur IA",
        description: "Impossible d'exécuter l'action IA.",
        variant: "destructive"
      });
    }
  }, [onAIGenerate, onInsertText, selectedText, currentSection]);

  const handleSavePrompt = useCallback(() => {
    if (!newPrompt.name || !newPrompt.prompt) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs.",
        variant: "destructive"
      });
      return;
    }

    const prompt: CustomPrompt = {
      id: Date.now().toString(),
      ...newPrompt,
      variables: newPrompt.prompt.match(/{\w+}/g) || [],
      createdAt: new Date(),
      usageCount: 0
    };

    setCustomPrompts(prev => [...prev, prompt]);
    setNewPrompt({ name: '', prompt: '', category: 'lyrics' });
    
    toast({
      title: "Prompt sauvegardé",
      description: "Votre prompt personnalisé a été ajouté."
    });
  }, [newPrompt]);

  const handleDeletePrompt = useCallback((id: string) => {
    setCustomPrompts(prev => prev.filter(p => p.id !== id));
  }, []);

  const handleUsePrompt = useCallback(async (prompt: CustomPrompt) => {
    try {
      const result = await onAIGenerate(prompt.prompt, 'custom');
      onInsertText(result);
      
      // Incrémenter le compteur d'usage
      setCustomPrompts(prev => 
        prev.map(p => p.id === prompt.id ? { ...p, usageCount: p.usageCount + 1 } : p)
      );
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible d'exécuter le prompt.",
        variant: "destructive"
      });
    }
  }, [onAIGenerate, onInsertText]);

  // Drag & Drop pour les accords
  const handleChordDragStart = useCallback((e: React.DragEvent, chord: ChordData) => {
    setDraggedChord(chord);
    e.dataTransfer.setData('text/plain', `[${chord.name}]`);
    e.dataTransfer.effectAllowed = 'copy';
  }, []);

  const handleChordDragEnd = useCallback(() => {
    setDraggedChord(null);
  }, []);

  const handleChordClick = useCallback((chord: ChordData) => {
    onInsertChord(`[${chord.name}]`);
    toast({
      title: "Accord inséré",
      description: `L'accord ${chord.name} a été ajouté à votre composition.`
    });
  }, [onInsertChord]);

  // Filtrage des accords
  const filteredChords = CHORD_LIBRARY.filter(chord => {
    const matchesInstrument = chord.instrument === selectedInstrument;
    const matchesFilter = chordFilter === 'all' || chord.category === chordFilter;
    const matchesSearch = chord.name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesInstrument && matchesFilter && matchesSearch;
  });

  // Rendu des onglets
  const renderQuickActions = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-2">
        {QUICK_ACTIONS.map((action) => {
          const Icon = action.icon;
          return (
            <Button
              key={action.id}
              variant="outline"
              className="h-auto p-3 justify-start text-left"
              onClick={() => handleQuickAction(action)}
              disabled={isLoading}
            >
              <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
              <div className="flex-1">
                <div className="font-medium text-sm">{action.title}</div>
                <div className="text-xs text-muted-foreground">{action.description}</div>
              </div>
            </Button>
          );
        })}
      </div>
      
      {selectedText && (
        <div className="p-3 bg-muted rounded-lg">
          <Label className="text-xs font-medium">Texte sélectionné :</Label>
          <p className="text-sm mt-1 italic">"{selectedText}"</p>
        </div>
      )}
    </div>
  );

  const renderCustomPrompts = () => (
    <div className="space-y-4">
      {/* Création de nouveau prompt */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Créer un prompt personnalisé</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Label htmlFor="prompt-name" className="text-xs">Nom du prompt</Label>
            <Input
              id="prompt-name"
              placeholder="Ex: Paroles romantiques"
              value={newPrompt.name}
              onChange={(e) => setNewPrompt(prev => ({ ...prev, name: e.target.value }))}
              className="h-8"
            />
          </div>
          
          <div>
            <Label htmlFor="prompt-category" className="text-xs">Catégorie</Label>
            <Select
              value={newPrompt.category}
              onValueChange={(value) => setNewPrompt(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="lyrics">Paroles</SelectItem>
                <SelectItem value="chords">Accords</SelectItem>
                <SelectItem value="structure">Structure</SelectItem>
                <SelectItem value="melody">Mélodie</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="prompt-text" className="text-xs">Prompt (utilisez {"{"}variable{"}"} pour les variables)</Label>
            <Textarea
              id="prompt-text"
              placeholder="Ex: Écris des paroles {genre} sur le thème {theme} pour un {sectionType}"
              value={newPrompt.prompt}
              onChange={(e) => setNewPrompt(prev => ({ ...prev, prompt: e.target.value }))}
              className="min-h-[60px] text-sm"
            />
          </div>
          
          <Button onClick={handleSavePrompt} size="sm" className="w-full">
            <Save className="w-4 h-4 mr-1" />
            Sauvegarder
          </Button>
        </CardContent>
      </Card>
      
      {/* Liste des prompts sauvegardés */}
      <div className="space-y-2">
        {customPrompts.map((prompt) => (
          <Card key={prompt.id} className="p-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm">{prompt.name}</h4>
                  <Badge variant="secondary" className="text-xs">{prompt.category}</Badge>
                  <Badge variant="outline" className="text-xs">{prompt.usageCount} utilisations</Badge>
                </div>
                <p className="text-xs text-muted-foreground mb-2">{prompt.prompt}</p>
                {prompt.variables.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {prompt.variables.map((variable, index) => (
                      <Badge key={index} variant="outline" className="text-xs">{variable}</Badge>
                    ))}
                  </div>
                )}
              </div>
              <div className="flex gap-1 ml-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleUsePrompt(prompt)}
                  className="h-6 w-6 p-0"
                >
                  <Play className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDeletePrompt(prompt.id)}
                  className="h-6 w-6 p-0 text-destructive"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
        
        {customPrompts.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Wand2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Aucun prompt personnalisé</p>
            <p className="text-xs">Créez votre premier prompt ci-dessus</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderChordLibrary = () => (
    <div className="space-y-4">
      {/* Contrôles de filtrage */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              placeholder="Rechercher un accord..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8"
            />
          </div>
          <Button variant="outline" size="sm" className="h-8 px-2">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Select value={selectedInstrument} onValueChange={setSelectedInstrument}>
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="guitar">Guitare</SelectItem>
              <SelectItem value="piano">Piano</SelectItem>
              <SelectItem value="ukulele">Ukulélé</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={chordFilter} onValueChange={setChordFilter}>
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous</SelectItem>
              <SelectItem value="major">Majeurs</SelectItem>
              <SelectItem value="minor">Mineurs</SelectItem>
              <SelectItem value="seventh">7ème</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Grille d'accords avec drag & drop */}
      <ScrollArea className="h-[300px]">
        <div className="grid grid-cols-2 gap-2 p-1">
          {filteredChords.map((chord) => (
            <div
              key={chord.id}
              draggable
              onDragStart={(e) => handleChordDragStart(e, chord)}
              onDragEnd={handleChordDragEnd}
              onClick={() => handleChordClick(chord)}
              className={`
                p-3 border rounded-lg cursor-pointer transition-all
                hover:bg-accent hover:border-accent-foreground/20
                ${draggedChord?.id === chord.id ? 'opacity-50' : ''}
                ${chord.difficulty === 'easy' ? 'border-green-200' : 
                  chord.difficulty === 'medium' ? 'border-yellow-200' : 'border-red-200'}
              `}
            >
              <div className="flex items-center justify-between mb-1">
                <span className="font-bold text-lg">{chord.name}</span>
                <GripVertical className="w-3 h-3 text-muted-foreground" />
              </div>
              
              <div className="text-xs text-muted-foreground mb-2">
                {chord.instrument} • {chord.difficulty}
              </div>
              
              {/* Diagramme simplifié */}
              <div className="flex gap-1 mb-2">
                {chord.fingering.map((fret, index) => (
                  <div
                    key={index}
                    className="w-4 h-4 border rounded text-xs flex items-center justify-center bg-muted"
                  >
                    {fret === 0 ? 'o' : fret}
                  </div>
                ))}
              </div>
              
              {/* Variations */}
              {chord.variations.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {chord.variations.slice(0, 2).map((variation, index) => (
                    <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                      {variation}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
      
      <div className="text-xs text-muted-foreground text-center">
        💡 Glissez-déposez les accords dans l'éditeur ou cliquez pour les insérer
      </div>
    </div>
  );

  const renderHistory = () => (
    <ScrollArea className="h-[400px]">
      <div className="space-y-2">
        {aiHistory.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <History className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Aucun historique IA</p>
            <p className="text-xs">Vos générations apparaîtront ici</p>
          </div>
        ) : (
          aiHistory.map((item) => (
            <Card key={item.id} className="p-3">
              <div className="flex items-start justify-between mb-2">
                <Badge variant="outline" className="text-xs">{item.type}</Badge>
                <span className="text-xs text-muted-foreground">
                  {item.timestamp.toLocaleTimeString()}
                </span>
              </div>
              
              <div className="space-y-2">
                <div>
                  <Label className="text-xs font-medium">Prompt :</Label>
                  <p className="text-xs text-muted-foreground">{item.prompt}</p>
                </div>
                
                <div>
                  <Label className="text-xs font-medium">Résultat :</Label>
                  <p className="text-xs bg-muted p-2 rounded">{item.result}</p>
                </div>
                
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onInsertText(item.result)}
                    className="h-6 text-xs"
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    Réutiliser
                  </Button>
                  
                  {item.applied && (
                    <Badge variant="secondary" className="text-xs">Appliqué</Badge>
                  )}
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </ScrollArea>
  );

  return (
    <Card className="h-full border-0 shadow-none">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Brain className="w-5 h-5" />
          Hub IA Unifié
        </CardTitle>
      </CardHeader>
      
      <CardContent className="h-[calc(100%-80px)]">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-4 mb-4">
            <TabsTrigger value="quick-actions" className="text-xs">
              <Zap className="w-3 h-3 mr-1" />
              Actions
            </TabsTrigger>
            <TabsTrigger value="prompts" className="text-xs">
              <Wand2 className="w-3 h-3 mr-1" />
              Prompts
            </TabsTrigger>
            <TabsTrigger value="chords" className="text-xs">
              <Music className="w-3 h-3 mr-1" />
              Accords
            </TabsTrigger>
            <TabsTrigger value="history" className="text-xs">
              <History className="w-3 h-3 mr-1" />
              Historique
            </TabsTrigger>
          </TabsList>
          
          <div className="h-[calc(100%-60px)]">
            <TabsContent value="quick-actions" className="h-full mt-0">
              {renderQuickActions()}
            </TabsContent>
            
            <TabsContent value="prompts" className="h-full mt-0">
              <ScrollArea className="h-full">
                {renderCustomPrompts()}
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="chords" className="h-full mt-0">
              {renderChordLibrary()}
            </TabsContent>
            
            <TabsContent value="history" className="h-full mt-0">
              {renderHistory()}
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
      
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <RefreshCcw className="w-4 h-4 animate-spin" />
            <span className="text-sm">Génération IA en cours...</span>
          </div>
        </div>
      )}
    </Card>
  );
};

export default UnifiedAIWorkspace;