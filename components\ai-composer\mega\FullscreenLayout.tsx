import React from 'react';
import { EnhancedLyricsEditor } from '@/components/enhanced-lyrics-editor/EnhancedLyricsEditor';
import { ChordPlacement, ChordPlacementDisplay } from '@/types/composer';
import { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';

interface FullscreenLayoutProps {
  quillRef?: React.RefObject<any>;
  value?: string;
  chords?: ChordPlacement[];
  onLyricsChange?: (lyrics: string) => void;
  onChordsChange?: (chords: ChordPlacement[]) => void;
}

export const FullscreenLayout: React.FC<FullscreenLayoutProps> = ({
  quillRef,
  value,
  chords,
  onLyricsChange,
  onChordsChange
}) => {
  return (
    <div className="flex h-full w-full">
      <EnhancedLyricsEditor
        quillRef={quillRef}
        value={value || ''}
        chords={chords || []}
        onChange={onLyricsChange || (() => {})}
        onChordsChange={onChordsChange || (() => {})}
        className="h-full w-full"
      />
    </div>
  );
};
