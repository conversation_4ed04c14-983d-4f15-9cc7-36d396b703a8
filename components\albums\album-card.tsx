"use client";

import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit3, Trash2, PlayCircle, Disc, UserCircle, Copy } from 'lucide-react';
import type { Album, UserProfile } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { ActionableVisibilityToggle } from '@/components/ui/actionable-visibility-toggle';

interface AlbumForCard extends Album {
  songs_count?: number;
}

interface AlbumCardProps {
  album: AlbumForCard;
  onDelete?: (albumId: string) => void;
  onToggleVisibility: (albumId: string, currentIsPublic: boolean) => void;
  isVisibilityUpdating: boolean;
}

export function AlbumCard({ album, onDelete, onToggleVisibility, isVisibilityUpdating }: AlbumCardProps) {
  const { toast } = useToast();
  const router = useRouter();

  const viewPageUrl = album.is_public && album.slug 
    ? `/album/${album.slug}`
    : `/albums/${album.id}`;

  const handleDelete = () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'album "${album.title}" ?`)) {
      if (onDelete) {
        onDelete(album.id);
      } else {
        toast({ title: "Suppression non configurée" });
      }
    }
  };

  const handlePlay = () => {
    toast({ title: "Lecture (Placeholder)", description: `Lancer la lecture de l'album "${album.title}".` });
  };

  const handleDuplicate = () => {
    toast({ title: "Duplication (Placeholder)", description: `L'album "${album.title}" serait dupliqué.` });
  };

  const artistProfile = album.profiles as UserProfile | null;

  return (
    <Card className="overflow-hidden flex flex-col h-full group/card">
      <CardHeader className="p-0 relative">
        <div className="absolute top-2 left-2 z-10 bg-card/50 backdrop-blur-sm rounded-full">
          <ActionableVisibilityToggle
            isPublic={album.is_public ?? false}
            isUpdating={isVisibilityUpdating}
            onToggle={() => onToggleVisibility(album.id, album.is_public ?? false)}
          />
        </div>
        <Link href={viewPageUrl} className="block aspect-square relative group">
          {album.cover_url ? (
            <Image
              src={album.cover_url}
              alt={album.title}
              width={300}
              height={300}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Disc className="w-16 h-16 text-muted-foreground" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <PlayCircle className="w-12 h-12 text-white" />
          </div>
        </Link>
      </CardHeader>
      <CardContent className="p-4 flex-grow">
        <Link href={viewPageUrl}>
          <CardTitle className="text-lg font-semibold hover:underline mb-1 truncate" title={album.title}>
            {album.title}
          </CardTitle>
        </Link>
        <div className="text-xs text-muted-foreground mb-2">
          {artistProfile ? (
            <Link href={`/artists/${artistProfile.username}`} className="hover:underline flex items-center gap-1">
              {artistProfile.avatar_url ? (
                <Image src={artistProfile.avatar_url} alt={artistProfile.display_name || artistProfile.username || 'avatar'} width={16} height={16} className="rounded-full" />
              ) : (
                <UserCircle className="w-4 h-4" />
              )}
              {artistProfile.display_name || artistProfile.username}
            </Link>
          ) : (
            <span>{album.artist_name || "Artiste inconnu"}</span>
          )}
          {album.songs_count !== undefined && (
            <>
              <span className="mx-1">•</span>
              <span>{album.songs_count} morceau{album.songs_count === 1 ? '' : 'x'}</span>
            </>
          )}
        </div>
        {album.release_date && (
            <p className="text-xs text-muted-foreground">Sorti le {format(new Date(album.release_date), 'd MMM yyyy', { locale: fr })}</p>
        )}

        {/* Placeholder for stats - TODO: Add these to AlbumForCard and fetch them */}
        {/* <div className="mt-2 flex items-center gap-x-3 gap-y-1 flex-wrap text-xs text-muted-foreground">
          {album.view_count !== undefined && album.view_count > 0 && (
            <span className="flex items-center gap-1" title="Vues"><Eye className="w-3 h-3" /> {album.view_count}</span>
          )}
          {album.like_count !== undefined && album.like_count > 0 && (
            <span className="flex items-center gap-1" title="Likes"><ThumbsUp className="w-3 h-3" /> {album.like_count}</span>
          )}
        </div> */}
      </CardContent>
      <CardFooter className="p-4 pt-2 flex justify-between items-center">
        <Button onClick={handlePlay} size="sm" variant="outline" className="flex-grow mr-2">
          <PlayCircle className="mr-2 h-4 w-4" /> Écouter
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/albums/${album.id}/edit`)}>
              <Edit3 className="mr-2 h-4 w-4" /> Modifier
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" /> Dupliquer
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-4 w-4" /> Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}
