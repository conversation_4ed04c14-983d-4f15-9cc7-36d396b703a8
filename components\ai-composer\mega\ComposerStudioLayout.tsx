'use client';

import React, { useMemo, useRef, useCallback, useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Maximize2, Columns, LayoutDashboard, Save, Play, Share2 } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { useComposerStore } from '@/stores/composer';
import { LayoutMode, PanelState, ViewMode } from '@/types/layout-state'; // Corrected path
import { AiGenerationType } from '@/types/ai-types'; // Corrected path
import { useLayoutStore } from '@/stores/layout';
import { FullscreenLayout } from './FullscreenLayout';
import { NormalLayout } from './NormalLayout';
import { StudioLayout } from './StudioLayout';
import { ResizablePanelGroup, ResizablePanel } from '@/components/ui/resizable'; // Corrected path
import { UserProfile } from '@/types/auth';
import { ArpeggioPattern, UnifiedChordPosition, ChordSystemState, ChordSystemActions } from '@/components/chord-system/types/chord-system';
import { ChordPlacement, ChordPlacementDisplay, ChordPlacementStorage } from '@/types/composer';
import { chordPlacementToString, chordPlacementToUnified } from '@/utils/chord-converter';

import { UseFormReturn } from 'react-hook-form';
import { type Song } from '@/components/songs/song-schema'; // Corrected path
import { songSchema, SongFormData } from '@/components/songs/song-schema';
// TODO: ai-config seems to be missing or misplaced.
// import { aiConfigSchema } from '@/components/ai-composer/mega/ai-config';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import type { EnhancedLyricsEditorProps } from '@/components/enhanced-lyrics-editor/EnhancedLyricsEditor';
import { EnhancedLyricsEditor } from '@/components/enhanced-lyrics-editor/EnhancedLyricsEditor';
import { AIProvider } from '@/components/ai-provider';
// TODO: generate-ai utility seems to be missing or misplaced.
// import { generateAI } from '@/components/ai-utils/generate-ai';
import { Toast } from '@/components/ui/toast';
// import { SongInfoHeader } from '@/components/ui/song-info-header'; // Corrected path, but component is not used anymore

type SongSchema = z.infer<typeof songSchema>;

// TODO: aiConfigSchema is missing, using z.any() as a placeholder
const formSchema = songSchema.extend({
  ai_config: z.any().optional()
});

type FormValues = z.infer<typeof formSchema>;

// Type pour le composant EnhancedLyricsEditor
type EnhancedLyricsEditorType = typeof EnhancedLyricsEditor;



// Fonction utilitaire pour convertir les accords
// Dummy implementation for chordToString since the utility is missing
const chordToString = (chord: UnifiedChordPosition): string => {
  return chord.chord;
};

// Type guard to differentiate between storage and display types
const isChordPlacementDisplay = (item: ChordPlacement): item is ChordPlacementDisplay => {
  return typeof item.chord === 'object' && item.chord !== null;
};

// Type for chord placement with metadata
export type ChordPlacementWithMetadata = ChordPlacement & {
  metadata?: {
    beat?: number;
    measure?: number;
    emphasis?: 'strong' | 'medium' | 'weak';
  };
  textPosition: number;
  lineNumber: number;
  wordIndex: number;
};

// Helper function to convert UnifiedChordPosition to ChordPlacementDisplay
// Helper function to convert string or number frets to number array
const convertFrets = (frets: (string | number)[]): number[] => {
  return frets.map(fret => typeof fret === 'string' ? parseInt(fret) : fret);
};

// Helper function to convert UnifiedChordPosition to ChordPlacementDisplay

// Helper function to convert ChordPlacement to ChordPlacementDisplay

// Type for chord system state with actions
export type ChordSystemStateWithActions = ChordSystemState & {
  actions: ChordSystemActions;
};

// Exported interfaces

export interface ComposerStudioLayoutProps {
  song: Song | null;
  userProfile: UserProfile | null;
  isLoading: boolean;
  handleFieldChange: (fieldName: keyof SongFormData, value: any) => void;
  saveSongVersion: (data: Partial<Song>) => Promise<void>;
  unsavedChanges: boolean;
}

const ComposerStudioLayout = ({ song, userProfile, isLoading, handleFieldChange, saveSongVersion, unsavedChanges }: ComposerStudioLayoutProps) => {
  const { toast } = useToast();
  const { profile } = useAuth();
  const { state: layout, setLayoutState: setLayout } = useLayoutStore();
  

  const quillRef = useRef<any>(null);
  
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: song || {},
  });

  useEffect(() => {
    if (song) {
      form.reset(song as any);
    }
  }, [song, form]);

  const handleLyricsChange = useCallback((lyrics: string) => {
    handleFieldChange('lyrics', lyrics);
  }, [handleFieldChange]);

    const handleSingleChordChange = useCallback((chord: ChordPlacementDisplay) => {
    // This function can be used for components that emit a single chord change.
    // For now, we'll just log it. A full implementation would update the main chord array.
    console.log('Single chord changed:', chord);
  }, []);

    const handleGenericChordChange = useCallback((change: ChordPlacement | ChordPlacement[]) => {
    const currentChords = JSON.parse(form.getValues('chords') || '[]') as ChordPlacement[];
    let newChords: ChordPlacement[];

    if (Array.isArray(change)) {
      newChords = change;
    } else {
      const existingIndex = currentChords.findIndex(c => c.id === change.id);
      if (existingIndex !== -1) {
        newChords = [...currentChords];
        newChords[existingIndex] = change;
      } else {
        newChords = [...currentChords, change];
      }
    }

    const storageChords = newChords.map(c => 
      isChordPlacementDisplay(c) ? chordPlacementToString(c) : c
    );
    handleFieldChange('chords', JSON.stringify(storageChords));
  }, [form, handleFieldChange]);

  const handleChordsChange = useCallback((chords: ChordPlacement[]) => {
    const storageChords = chords.map(c => 
      isChordPlacementDisplay(c) ? chordPlacementToString(c) : c
    );
    handleFieldChange('chords', JSON.stringify(storageChords));
  }, [form, handleFieldChange]);

  const handleFormSubmit = async () => {
    try {
      const formData = form.getValues();
      await saveSongVersion(formData);
      toast({
        title: 'Sauvegarde réussie',
        description: 'Les modifications ont été sauvegardées avec succès.'
      });
    } catch (error) {
      console.error('Error saving song:', error);
      toast({
        title: 'Erreur de sauvegarde',
        description: 'Une erreur est survenue lors de la sauvegarde.',
        variant: 'destructive'
      });
    }
  };

  const handleAIGenerate = useCallback(async (prompt: string, type: AiGenerationType) => {
    if (!song || !userProfile?.id) {
      toast({ title: 'Erreur', description: 'Chanson ou profil manquant.', variant: 'destructive' });
      return null;
    }
    setIsGeneratingAI(true);
    try {
      // TODO: generateAI function is missing. This is a placeholder.
      console.log(`Generating AI for ${type} with prompt: ${prompt}`);
      await new Promise(res => setTimeout(res, 1000)); // Simulate async call
      const response = { lyrics: 'AI generated lyrics...', chords: [] }; // Dummy response
      if (response) {
        if (response.lyrics) handleFieldChange('lyrics', response.lyrics);
        if (response.chords) handleFieldChange('chords', JSON.stringify(response.chords));
      }
      return response;
    } catch (error) {
      console.error('Error generating AI:', error);
      toast({ title: 'Erreur de génération IA', variant: 'destructive' });
      return null;
    } finally {
      setIsGeneratingAI(false);
    }
  }, [song, userProfile, form, handleFieldChange]);

  const renderLayout = () => {
    // TODO: parseChordSuggestions is missing
    const parseChordSuggestions = (raw: any) => raw?.chords;

    switch (layout.mode) {
      case 'fullscreen':
        return (
          <div className="h-full flex flex-col">
            <EnhancedLyricsEditor
              value={form.watch('lyrics') || ''}
              onChange={handleLyricsChange}
              chords={JSON.parse(form.watch('chords') || '[]')}
              onChordsChange={handleChordsChange}
              quillRef={quillRef}
              onRequestChordSuggestions={async (context: string, position: number): Promise<UnifiedChordPosition[]> => {
                try {
                  const aiConf = form.getValues('ai_config');
                  const prompt = `Suggest chords for lyrics (around char ${position}): "${context.substring(Math.max(0, position - 50), Math.min(context.length, position + 50))}". Format: "[Am] at 12, [C] at 25". Only chord suggestions.`;
                  const rawSuggestions = await handleAIGenerate(prompt, 'chord_suggestion' as AiGenerationType);
                  if (rawSuggestions) {
                    const parsed = parseChordSuggestions(rawSuggestions);
                    return parsed ? parsed.map((cp: any) => cp.chord) : [];
                  }
                  return [];
                } catch (error) {
                  console.error("Error getting chord suggestions:", error);
                  toast({
                    title: 'Erreur de suggestion IA',
                    description: 'Impossible d\'obtenir les suggestions d\'accords.',
                    variant: 'destructive'
                  });
                  return [];
                }
              }}
              className="h-full"
            />
          </div>
        );
      case 'split':
        return (
          <div className="h-full flex">
            <div className="w-1/2 h-full">
              <EnhancedLyricsEditor
                value={form.watch('lyrics') || ''}
                onChange={handleLyricsChange}
                chords={JSON.parse(form.watch('chords') || '[]')}
                onChordsChange={handleChordsChange}
                quillRef={quillRef}
                onRequestChordSuggestions={async (context: string, position: number): Promise<UnifiedChordPosition[]> => {
                  try {
                    const aiConf = form.getValues('ai_config');
                    const prompt = `Suggest chords for lyrics (around char ${position}): "${context.substring(Math.max(0, position - 50), Math.min(context.length, position + 50))}". Format: "[Am] at 12, [C] at 25". Only chord suggestions.`;
                    const rawSuggestions = await handleAIGenerate(prompt, 'chord_suggestion' as AiGenerationType);
                    if (rawSuggestions) {
                      const parsed = parseChordSuggestions(rawSuggestions);
                      return parsed ? parsed.map((cp: any) => cp.chord) : [];
                    }
                    return [];
                  } catch (error) {
                    console.error("Error getting chord suggestions:", error);
                    toast({
                      title: 'Erreur de suggestion IA',
                      description: 'Impossible d\'obtenir les suggestions d\'accords.',
                      variant: 'destructive'
                    });
                    return [];
                  }
                }}
                className="h-full"
              />
            </div>
            <div className="w-1/2 h-full">
              {/* Right panel content */}
            </div>
          </div>
        );
      case 'studio':
        return (
          <AIProvider>
            <div className="h-screen flex flex-col bg-background text-foreground overflow-hidden">
              <header className="flex items-center justify-between px-4 py-2 border-b sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex-1">
                  <h2 className="text-lg font-semibold">{song?.title || 'New Song'}</h2>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={handleFormSubmit} disabled={isLoading || !unsavedChanges || !song}>
                    <Save className="mr-2 h-4 w-4" /> Save
                  </Button>
                  <Button variant="default" size="sm" disabled>
                    <Play className="mr-2 h-4 w-4" /> Play
                  </Button>
                  <Button variant="outline" size="sm" disabled>
                    <Share2 className="mr-2 h-4 w-4" /> Share
                  </Button>
                </div>
              </header>
              <main className="flex-1 overflow-auto">
                  <StudioLayout
                    value={form.watch('lyrics') || ''}
                    chords={JSON.parse(form.watch('chords') || '[]')}
                    onLyricsChange={handleLyricsChange}
                    onChordsChange={handleGenericChordChange}
                  />
              </main>
              <footer className="border-t p-4">
                <Button
                  onClick={handleFormSubmit}
                  disabled={isLoading || isGeneratingAI}
                >
                  Sauvegarder
                </Button>
              </footer>
            </div>
          </AIProvider>
        );
      default:
        return (
          <div className="flex items-center justify-center h-screen">Invalid layout mode</div>
        );
    }
  };

  // Loading state handled before main return if currentSong is not yet populated
  if (isLoading && !song) { 
    // If initialSongData was also not provided, and fetch hasn't completed (or failed silently)
    // This check ensures we show loading until currentSong is set.
    return <div className="flex items-center justify-center h-screen">Loading song data...</div>;
  }

  return (
    <AIProvider>
      <div className="flex h-full w-full">
        {renderLayout()}
      </div>
    </AIProvider>
  );
};

export default ComposerStudioLayout;
