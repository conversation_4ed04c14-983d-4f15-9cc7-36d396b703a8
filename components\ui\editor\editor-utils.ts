import { ChordPlacement, UnifiedChordPosition, ChordSuggestion } from '@/components/chord-system/types/chord-system';

export const handleInsertText = (quill: any, text: string, index: number, source: string) => {
  quill.insertText(index, text, source);
};

export const handleInsertChord = (quill: any, chord: UnifiedChordPosition, index: number, source: string) => {
  const chordText = chord.chord;
  quill.insertText(index, chordText, source);
};

export const handleUpdateChord = (quill: any, chord: UnifiedChordPosition, index: number, source: string) => {
  const chordText = chord.chord;
  quill.deleteText(index, chordText.length, source);
  quill.insertText(index, chordText, source);
};
