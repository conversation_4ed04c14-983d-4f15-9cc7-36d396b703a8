import type { AiGenerationType } from './ai-types';

export type LayoutMode = 'normal' | 'split' | 'fullscreen' | 'studio';
export type PanelState = 'hidden' | 'collapsed' | 'expanded';
export type ViewMode = 'editor' | 'preview' | 'split';
export type { AiGenerationType };

export interface LayoutState {
  mode: LayoutMode;
  panels: {
    left: PanelState;
    right: PanelState;
    bottom: PanelState;
  };
  view: ViewMode;
  generationType: AiGenerationType;
  isMaximized: boolean;
  lastFocusedPanel: keyof LayoutState['panels'] | null;
}
