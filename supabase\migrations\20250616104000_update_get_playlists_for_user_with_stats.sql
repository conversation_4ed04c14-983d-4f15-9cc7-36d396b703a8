-- Migration to update get_playlists_for_user with song count and total duration

CREATE OR REPLACE FUNCTION public.get_playlists_for_user(p_user_id uuid)
RETURNS json
LANGUAGE sql
STABLE
AS $$
  SELECT
    COALESCE(
      json_agg(
        json_build_object(
          'id', p.id,
          'name', p.name,
          'description', p.description,
          'is_public', p.is_public,
          'cover_art_url', p.cover_art_url,
          'user_id', p.user_id,
          'created_at', p.created_at,
          'updated_at', p.updated_at,
          'slug', p.slug,
          'profiles', json_build_object( -- Creator's profile
            'id', pr.id,
            'username', pr.username,
            'display_name', pr.display_name,
            'avatar_url', pr.avatar_url
          ),
          'song_count', ( -- Total number of songs in the playlist
            SELECT COUNT(*)
            FROM public.playlist_songs ps_count
            WHERE ps_count.playlist_id = p.id
          ),
          'total_duration_ms', ( -- Total duration of all songs in the playlist in milliseconds
            SELECT COALESCE(SUM(s_duration.duration_ms), 0)
            FROM public.playlist_songs ps_duration
            JOIN public.songs s_duration ON ps_duration.song_id = s_duration.id
            WHERE ps_duration.playlist_id = p.id
          ),
          'songs', ( -- Full list of songs with their details
            SELECT COALESCE(json_agg(s_detail.* ORDER BY ps_detail.position ASC NULLS LAST), '[]'::json)
            FROM public.playlist_songs ps_detail
            JOIN public.songs s_detail ON ps_detail.song_id = s_detail.id
            WHERE ps_detail.playlist_id = p.id
          )
        )
      ORDER BY p.created_at DESC
      ),
      '[]'::json
    )
  FROM
    public.playlists p
  JOIN
    public.profiles pr ON p.user_id = pr.id
  WHERE
    p.user_id = p_user_id;
$$;

-- Note:
-- This function now includes 'song_count' and 'total_duration_ms' for each playlist.
-- The 'songs' array now orders songs by 'ps_detail.position ASC NULLS LAST'.
-- Ensure the frontend type (e.g., PlaylistWithSongsAndCreator) is updated to include these new fields.
