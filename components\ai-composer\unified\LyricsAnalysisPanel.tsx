'use client';

import React, { useMemo } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BarChart3, Target, Clock, Music, Hash, Heart, 
  TrendingUp, AlertCircle, CheckCircle2
} from 'lucide-react';
import type { ChordPlacement } from '@/components/enhanced-lyrics-editor';

interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: Array<{
    position: number;
    chord: string;
    instrument: string;
  }>;
}

interface LyricsAnalysisPanelProps {
  content: string;
  chordPlacements: ChordPlacement[];
  currentSection?: LyricsSection;
}

interface AnalysisMetrics {
  wordCount: number;
  lineCount: number;
  syllableCount: number;
  rhymeScore: number;
  emotionScore: number;
  complexityScore: number;
  chordCount: number;
  uniqueChords: string[];
  estimatedDuration: number;
}

export const LyricsAnalysisPanel: React.FC<LyricsAnalysisPanelProps> = ({
  content,
  chordPlacements,
  currentSection
}) => {

  // Calcul des métriques d'analyse
  const metrics = useMemo((): AnalysisMetrics => {
    const lines = content.split('\n').filter(line => line.trim());
    const words = content.trim().split(/\s+/).filter(Boolean);
    
    // Estimation du nombre de syllabes (approximation)
    const syllableCount = words.reduce((total, word) => {
      const cleanWord = word.replace(/[^a-zA-ZàâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ]/g, '');
      const vowels = cleanWord.match(/[aeiouyàâäéèêëïîôöùûüÿAEIOUYÀÂÄÉÈÊËÏÎÔÖÙÛÜŸ]/g);
      return total + Math.max(1, vowels ? vowels.length : 1);
    }, 0);

    // Analyse des rimes (basique)
    const rhymeScore = calculateRhymeScore(lines);
    
    // Score d'émotion (basé sur des mots-clés)
    const emotionScore = calculateEmotionScore(content);
    
    // Score de complexité (basé sur la longueur des mots et la structure)
    const complexityScore = calculateComplexityScore(words, lines);
    
    // Analyse des accords
    const uniqueChords = [...new Set(chordPlacements.map(cp => cp.chord))];
    
    // Estimation de la durée (4 secondes par ligne en moyenne)
    const estimatedDuration = lines.length * 4;

    return {
      wordCount: words.length,
      lineCount: lines.length,
      syllableCount,
      rhymeScore,
      emotionScore,
      complexityScore,
      chordCount: chordPlacements.length,
      uniqueChords,
      estimatedDuration
    };
  }, [content, chordPlacements]);

  // Suggestions basées sur l'analyse
  const suggestions = useMemo(() => {
    const suggestions: Array<{
      type: 'info' | 'warning' | 'success';
      icon: React.ComponentType<any>;
      title: string;
      description: string;
    }> = [];

    if (metrics.wordCount < 20) {
      suggestions.push({
        type: 'warning',
        icon: AlertCircle,
        title: 'Section courte',
        description: 'Cette section pourrait bénéficier de plus de contenu'
      });
    }

    if (metrics.rhymeScore > 70) {
      suggestions.push({
        type: 'success',
        icon: CheckCircle2,
        title: 'Bonnes rimes',
        description: 'La structure des rimes est bien équilibrée'
      });
    }

    if (metrics.chordCount === 0) {
      suggestions.push({
        type: 'info',
        icon: Music,
        title: 'Pas d\'accords',
        description: 'Ajoutez des accords pour enrichir cette section'
      });
    }

    if (metrics.complexityScore > 80) {
      suggestions.push({
        type: 'warning',
        icon: TrendingUp,
        title: 'Complexité élevée',
        description: 'Le texte pourrait être simplifié pour plus d\'impact'
      });
    }

    return suggestions;
  }, [metrics]);

  return (
    <div className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <BarChart3 className="h-4 w-4" />
          Analyse des Paroles
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="space-y-4">
            
            {/* Métriques de base */}
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold">{metrics.wordCount}</div>
                <div className="text-xs text-muted-foreground">Mots</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold">{metrics.lineCount}</div>
                <div className="text-xs text-muted-foreground">Lignes</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold">{metrics.syllableCount}</div>
                <div className="text-xs text-muted-foreground">Syllabes</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold">{Math.round(metrics.estimatedDuration)}s</div>
                <div className="text-xs text-muted-foreground">Durée est.</div>
              </div>
            </div>

            {/* Scores d'analyse */}
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-medium flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    Rimes
                  </span>
                  <span className="text-xs text-muted-foreground">{metrics.rhymeScore}%</span>
                </div>
                <Progress value={metrics.rhymeScore} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-medium flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    Émotion
                  </span>
                  <span className="text-xs text-muted-foreground">{metrics.emotionScore}%</span>
                </div>
                <Progress value={metrics.emotionScore} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-medium flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    Complexité
                  </span>
                  <span className="text-xs text-muted-foreground">{metrics.complexityScore}%</span>
                </div>
                <Progress value={metrics.complexityScore} className="h-2" />
              </div>
            </div>

            {/* Informations sur les accords */}
            {metrics.chordCount > 0 && (
              <div>
                <h4 className="text-xs font-medium mb-2 flex items-center gap-1">
                  <Music className="h-3 w-3" />
                  Accords ({metrics.chordCount})
                </h4>
                <div className="flex flex-wrap gap-1">
                  {metrics.uniqueChords.map(chord => (
                    <Badge key={chord} variant="outline" className="text-xs">
                      {chord}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Suggestions */}
            {suggestions.length > 0 && (
              <div>
                <h4 className="text-xs font-medium mb-2">Suggestions</h4>
                <div className="space-y-2">
                  {suggestions.map((suggestion, index) => {
                    const Icon = suggestion.icon;
                    return (
                      <div
                        key={index}
                        className={`p-2 rounded-lg border text-xs ${
                          suggestion.type === 'success' 
                            ? 'bg-green-50 border-green-200 text-green-800'
                            : suggestion.type === 'warning'
                            ? 'bg-yellow-50 border-yellow-200 text-yellow-800'
                            : 'bg-blue-50 border-blue-200 text-blue-800'
                        }`}
                      >
                        <div className="flex items-start gap-2">
                          <Icon className="h-3 w-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <div className="font-medium">{suggestion.title}</div>
                            <div className="text-xs opacity-80">{suggestion.description}</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Informations sur la section */}
            {currentSection && (
              <div>
                <h4 className="text-xs font-medium mb-2">Section actuelle</h4>
                <div className="space-y-1 text-xs text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Type:</span>
                    <Badge variant="outline" className="text-xs capitalize">
                      {currentSection.type}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Titre:</span>
                    <span>{currentSection.title}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </div>
  );
};

// Fonctions utilitaires pour les calculs d'analyse
function calculateRhymeScore(lines: string[]): number {
  if (lines.length < 2) return 0;
  
  let rhymeCount = 0;
  const totalPairs = Math.floor(lines.length / 2);
  
  for (let i = 0; i < lines.length - 1; i += 2) {
    const line1 = lines[i]?.trim().toLowerCase();
    const line2 = lines[i + 1]?.trim().toLowerCase();
    
    if (line1 && line2) {
      const ending1 = line1.slice(-3);
      const ending2 = line2.slice(-3);
      
      if (ending1 === ending2 || 
          ending1.slice(-2) === ending2.slice(-2)) {
        rhymeCount++;
      }
    }
  }
  
  return totalPairs > 0 ? Math.round((rhymeCount / totalPairs) * 100) : 0;
}

function calculateEmotionScore(content: string): number {
  const emotionalWords = [
    'amour', 'cœur', 'âme', 'rêve', 'espoir', 'joie', 'bonheur',
    'tristesse', 'douleur', 'larme', 'sourire', 'passion', 'désir'
  ];
  
  const words = content.toLowerCase().split(/\s+/);
  const emotionalCount = words.filter(word => 
    emotionalWords.some(emo => word.includes(emo))
  ).length;
  
  return Math.min(100, Math.round((emotionalCount / words.length) * 500));
}

function calculateComplexityScore(words: string[], lines: string[]): number {
  const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
  const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length;
  
  const lengthScore = Math.min(100, (avgWordLength - 3) * 20);
  const structureScore = Math.min(100, (avgLineLength - 20) * 2);
  
  return Math.round((lengthScore + structureScore) / 2);
}
