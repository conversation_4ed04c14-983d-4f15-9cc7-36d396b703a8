export interface AIHistoryEntry {
  id: string;
  type: 'chords' | 'lyrics' | 'structure' | 'arrangement';
  timestamp: string;
  prompt: string;
  response: string;
  applied: boolean;
  metadata?: {
    [key: string]: any;
  };
}

export interface AISongConfig {
  model: string;
  temperature: number;
  max_tokens: number;
  prompt_template: string;
  system_prompt: string;
}

export interface AISongState {
  history: AIHistoryEntry[];
  config: AISongConfig;
  isGenerating: boolean;
  error?: string;
}
