/* AI Composer Responsive Styles */

/* Variables CSS pour la cohérence */
:root {
  --ai-composer-primary: #3b82f6;
  --ai-composer-secondary: #8b5cf6;
  --ai-composer-accent: #06b6d4;
  --ai-composer-success: #10b981;
  --ai-composer-warning: #f59e0b;
  --ai-composer-error: #ef4444;
  
  --ai-composer-bg-primary: #0f172a;
  --ai-composer-bg-secondary: #1e293b;
  --ai-composer-bg-tertiary: #334155;
  
  --ai-composer-text-primary: #f8fafc;
  --ai-composer-text-secondary: #cbd5e1;
  --ai-composer-text-muted: #64748b;
  
  --ai-composer-border: #475569;
  --ai-composer-border-light: #64748b;
  
  --ai-composer-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ai-composer-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Base responsive utilities */
.ai-composer-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
  .ai-composer-container {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .ai-composer-container {
    padding: 0 var(--spacing-xl);
  }
}

/* Responsive grid system */
.ai-composer-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .ai-composer-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .ai-composer-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Responsive text sizes */
.ai-composer-text-responsive {
  font-size: var(--font-size-sm);
}

@media (min-width: 768px) {
  .ai-composer-text-responsive {
    font-size: var(--font-size-base);
  }
}

@media (min-width: 1024px) {
  .ai-composer-text-responsive {
    font-size: var(--font-size-lg);
  }
}

/* Responsive spacing */
.ai-composer-spacing-responsive {
  padding: var(--spacing-sm);
}

@media (min-width: 768px) {
  .ai-composer-spacing-responsive {
    padding: var(--spacing-md);
  }
}

@media (min-width: 1024px) {
  .ai-composer-spacing-responsive {
    padding: var(--spacing-lg);
  }
}

/* Panel animations */
.ai-composer-panel {
  transition: all var(--transition-normal);
  transform: translateX(0);
  opacity: 1;
}

.ai-composer-panel.collapsed {
  transform: translateX(100%);
  opacity: 0;
}

.ai-composer-panel.hidden {
  display: none;
}

/* Mobile-first responsive design */
.ai-composer-mobile-hidden {
  display: none;
}

@media (min-width: 768px) {
  .ai-composer-mobile-hidden {
    display: block;
  }
}

.ai-composer-desktop-hidden {
  display: block;
}

@media (min-width: 768px) {
  .ai-composer-desktop-hidden {
    display: none;
  }
}

/* Touch-friendly buttons */
.ai-composer-touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Improved focus states */
.ai-composer-focus:focus {
  outline: 2px solid var(--ai-composer-primary);
  outline-offset: 2px;
}

/* Loading states */
.ai-composer-loading {
  position: relative;
  overflow: hidden;
}

.ai-composer-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: ai-composer-shimmer 1.5s infinite;
}

@keyframes ai-composer-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Smooth scrolling */
.ai-composer-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--ai-composer-border) transparent;
}

.ai-composer-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ai-composer-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.ai-composer-scroll::-webkit-scrollbar-thumb {
  background: var(--ai-composer-border);
  border-radius: 3px;
}

.ai-composer-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--ai-composer-border-light);
}

/* Responsive typography scale */
.ai-composer-heading-1 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .ai-composer-heading-1 {
    font-size: var(--font-size-2xl);
  }
}

@media (min-width: 1024px) {
  .ai-composer-heading-1 {
    font-size: var(--font-size-3xl);
  }
}

.ai-composer-heading-2 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  line-height: 1.3;
}

@media (min-width: 768px) {
  .ai-composer-heading-2 {
    font-size: var(--font-size-xl);
  }
}

@media (min-width: 1024px) {
  .ai-composer-heading-2 {
    font-size: var(--font-size-2xl);
  }
}

/* Status indicators */
.ai-composer-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 0.375rem;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.ai-composer-status.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--ai-composer-success);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.ai-composer-status.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--ai-composer-warning);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.ai-composer-status.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--ai-composer-error);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.ai-composer-status.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--ai-composer-primary);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Responsive cards */
.ai-composer-card {
  background: var(--ai-composer-bg-secondary);
  border: 1px solid var(--ai-composer-border);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  box-shadow: var(--ai-composer-shadow);
  transition: all var(--transition-normal);
}

.ai-composer-card:hover {
  box-shadow: var(--ai-composer-shadow-lg);
  border-color: var(--ai-composer-border-light);
}

@media (min-width: 768px) {
  .ai-composer-card {
    padding: var(--spacing-lg);
  }
}

/* Responsive buttons */
.ai-composer-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0.375rem;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  cursor: pointer;
  min-height: 36px;
}

@media (min-width: 768px) {
  .ai-composer-button {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    min-height: 40px;
  }
}

.ai-composer-button.primary {
  background: var(--ai-composer-primary);
  color: white;
}

.ai-composer-button.primary:hover {
  background: #2563eb;
}

.ai-composer-button.secondary {
  background: var(--ai-composer-bg-tertiary);
  color: var(--ai-composer-text-primary);
  border-color: var(--ai-composer-border);
}

.ai-composer-button.secondary:hover {
  background: var(--ai-composer-border);
}

/* Responsive inputs */
.ai-composer-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--ai-composer-bg-tertiary);
  border: 1px solid var(--ai-composer-border);
  border-radius: 0.375rem;
  color: var(--ai-composer-text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.ai-composer-input:focus {
  outline: none;
  border-color: var(--ai-composer-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ai-composer-input::placeholder {
  color: var(--ai-composer-text-muted);
}

@media (min-width: 768px) {
  .ai-composer-input {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* Responsive layout utilities */
.ai-composer-flex-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

@media (min-width: 768px) {
  .ai-composer-flex-responsive {
    flex-direction: row;
    align-items: center;
  }
}

/* Print styles */
@media print {
  .ai-composer-no-print {
    display: none !important;
  }
  
  .ai-composer-print-only {
    display: block !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --ai-composer-border: #ffffff;
    --ai-composer-text-secondary: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .ai-composer-panel,
  .ai-composer-button,
  .ai-composer-input,
  .ai-composer-card {
    transition: none;
  }
  
  .ai-composer-loading::after {
    animation: none;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --ai-composer-bg-primary: #020617;
    --ai-composer-bg-secondary: #0f172a;
    --ai-composer-bg-tertiary: #1e293b;
  }
}

/* Accessibility improvements */
.ai-composer-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus trap for modals */
.ai-composer-focus-trap {
  position: relative;
}

.ai-composer-focus-trap:focus {
  outline: none;
}

/* Responsive video/media */
.ai-composer-media-responsive {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* Utility classes for spacing */
.ai-composer-mt-responsive {
  margin-top: var(--spacing-sm);
}

@media (min-width: 768px) {
  .ai-composer-mt-responsive {
    margin-top: var(--spacing-md);
  }
}

@media (min-width: 1024px) {
  .ai-composer-mt-responsive {
    margin-top: var(--spacing-lg);
  }
}

.ai-composer-mb-responsive {
  margin-bottom: var(--spacing-sm);
}

@media (min-width: 768px) {
  .ai-composer-mb-responsive {
    margin-bottom: var(--spacing-md);
  }
}

@media (min-width: 1024px) {
  .ai-composer-mb-responsive {
    margin-bottom: var(--spacing-lg);
  }
}

/* Performance optimizations */
.ai-composer-gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.ai-composer-contain-layout {
  contain: layout;
}

.ai-composer-contain-paint {
  contain: paint;
}

/* Custom scrollbar for webkit browsers */
.ai-composer-custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ai-composer-custom-scrollbar::-webkit-scrollbar-track {
  background: var(--ai-composer-bg-secondary);
  border-radius: 4px;
}

.ai-composer-custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--ai-composer-border);
  border-radius: 4px;
}

.ai-composer-custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--ai-composer-border-light);
}

.ai-composer-custom-scrollbar::-webkit-scrollbar-corner {
  background: var(--ai-composer-bg-secondary);
}