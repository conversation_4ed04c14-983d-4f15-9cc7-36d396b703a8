'use client';

import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Music, Guitar, Piano, Volume2, VolumeX, Play, Pause, 
  Plus, Trash2, Edit, Eye, Settings, Layers, Target,
  RotateCcw, Save, Download, Upload, Copy, Zap, Search, BarChart3
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { MidiChordPlayer } from '@/lib/chords/midi-chord-player';

// Import des données d'accords
import guitarChords from '@/lib/chords/guitar.json';
import pianoChords from '@/lib/chords/piano.json';
import ukuleleChords from '@/lib/chords/ukulele_gcea_complete.json';

// Types pour les données d'accords
interface ChordData {
  instrument?: string;
  tuning?: string[];
  keys?: string[];
  suffixes?: string[];
  chords?: Record<string, any[]>;
  [key: string]: any;
}

interface InstrumentData {
  name: string;
  data: ChordData;
  tunings: Array<{
    id: string;
    name: string;
    notes: string[];
  }>;
}

interface ChordPosition {
  id: string;
  position: number;
  chord: string;
  instrument: string;
  tuning?: string;
  fret?: number;
  fingering?: number[];
  preview?: {
    svg?: string;
    audio?: string;
  };
  chordData?: any;
}

interface AIChordIntegrationProProps {
  structure: any;
  styleConfig: any;
  chords: ChordPosition[];
  onChordsChange: (chords: ChordPosition[]) => void;
  selectedSectionId: string;
}

// Données d'instruments avec leurs accordages
const INSTRUMENTS_DATA: Record<string, InstrumentData> = {
  guitar: {
    name: 'Guitare',
    data: guitarChords as ChordData,
    tunings: [
      { id: 'standard', name: 'Standard (E-A-D-G-B-E)', notes: ['E', 'A', 'D', 'G', 'B', 'E'] },
      { id: 'drop_d', name: 'Drop D (D-A-D-G-B-E)', notes: ['D', 'A', 'D', 'G', 'B', 'E'] },
      { id: 'open_g', name: 'Open G (D-G-D-G-B-D)', notes: ['D', 'G', 'D', 'G', 'B', 'D'] }
    ]
  },
  piano: {
    name: 'Piano',
    data: pianoChords as ChordData,
    tunings: [
      { id: 'standard', name: 'Standard', notes: [] }
    ]
  },
  ukulele: {
    name: 'Ukulélé',
    data: ukuleleChords as ChordData,
    tunings: [
      { id: 'gcea', name: 'Standard (G-C-E-A)', notes: ['G', 'C', 'E', 'A'] }
    ]
  }
};

// Progressions d'accords étendues
const CHORD_PROGRESSIONS = {
  pop: [
    { name: 'I-V-vi-IV', chords: ['C', 'G', 'Am', 'F'], description: 'Progression pop classique' },
    { name: 'vi-IV-I-V', chords: ['Am', 'F', 'C', 'G'], description: 'Progression alternative' },
    { name: 'I-vi-IV-V', chords: ['C', 'Am', 'F', 'G'], description: 'Progression doo-wop' }
  ],
  rock: [
    { name: 'I-♭VII-♭VI-♭VII', chords: ['C', 'Bb', 'Ab', 'Bb'], description: 'Progression rock' },
    { name: 'i-♭VI-♭VII-i', chords: ['Am', 'F', 'G', 'Am'], description: 'Rock mineur' },
    { name: 'I-IV-V-I', chords: ['C', 'F', 'G', 'C'], description: 'Blues rock' }
  ],
  jazz: [
    { name: 'ii-V-I', chords: ['Dm7', 'G7', 'Cmaj7'], description: 'Progression jazz classique' },
    { name: 'I-vi-ii-V', chords: ['Cmaj7', 'Am7', 'Dm7', 'G7'], description: 'Cycle des quintes' },
    { name: 'iii-vi-ii-V', chords: ['Em7', 'Am7', 'Dm7', 'G7'], description: 'Jazz moderne' }
  ]
};

const AIChordIntegrationProComponent: React.FC<AIChordIntegrationProProps> = ({
  structure,
  styleConfig,
  chords,
  onChordsChange,
  selectedSectionId
}) => {
  // États locaux
  const [selectedInstrument, setSelectedInstrument] = useState('guitar');
  const [selectedTuning, setSelectedTuning] = useState('standard');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentChord, setCurrentChord] = useState<string | null>(null);
  const [showChordLibrary, setShowChordLibrary] = useState(false);
  const [chordFilter, setChordFilter] = useState('');
  const [selectedProgression, setSelectedProgression] = useState<string>('pop');
  const [isAIGenerating, setIsAIGenerating] = useState(false);
  const [selectedChordType, setSelectedChordType] = useState('major');
  const [selectedKey, setSelectedKey] = useState('C');
  const [midiPlayer, setMidiPlayer] = useState<MidiChordPlayer | null>(null);

  // Initialisation du lecteur MIDI
  useEffect(() => {
    const player = new MidiChordPlayer();
    setMidiPlayer(player);
    return () => {
      player.stopAll();
    };
  }, []);

  // Mémoisation des données calculées
  const currentSection = useMemo(() => 
    structure?.sections?.find((s: any) => s.id === selectedSectionId), 
    [structure, selectedSectionId]
  );
  
  const currentInstrumentData = useMemo(() => 
    INSTRUMENTS_DATA[selectedInstrument as keyof typeof INSTRUMENTS_DATA], 
    [selectedInstrument]
  );
  
  const currentTuningData = useMemo(() => 
    currentInstrumentData?.tunings.find(t => t.id === selectedTuning), 
    [currentInstrumentData, selectedTuning]
  );

  // Obtenir les accords disponibles pour l'instrument sélectionné
  const availableChords = useMemo(() => {
    if (!currentInstrumentData?.data) return [];
    
    const data = currentInstrumentData.data;
    const chordsArray: Array<{
      name: string;
      key: string;
      suffix?: string;
      positions?: any[];
      chordData?: any;
    }> = [];
    
    // Vérifier si data existe et est un objet
    if (!data || typeof data !== 'object') return [];
    
    // Pour la guitare et l'ukulélé
    if ('chords' in data && data.chords && typeof data.chords === 'object') {
      Object.keys(data.chords).forEach(key => {
        const keyChords = data.chords![key];
        if (Array.isArray(keyChords)) {
          keyChords.forEach((chordGroup: any) => {
            chordsArray.push({
              name: `${key}${chordGroup.suffix === 'major' ? '' : chordGroup.suffix}`,
              key,
              suffix: chordGroup.suffix,
              positions: chordGroup.positions || [],
              chordData: chordGroup
            });
          });
        }
      });
    }
    
    // Pour le piano
    if ('keys' in data && 'suffixes' in data && Array.isArray(data.keys) && Array.isArray(data.suffixes)) {
      data.keys.forEach((key: string) => {
        data.suffixes!.forEach((suffix: string) => {
          chordsArray.push({
            name: `${key}${suffix === 'major' ? '' : suffix}`,
            key,
            suffix,
            positions: [],
            chordData: data.chords?.[key as keyof typeof data.chords]?.[suffix as keyof (typeof data.chords)[keyof typeof data.chords]]
          });
        });
      });
    }
    
    return chordsArray;
  }, [currentInstrumentData]);

  // Filtrer les accords selon les critères
  const filteredChords = useMemo(() => {
    return availableChords.filter(chord => {
      const matchesFilter = chord.name.toLowerCase().includes(chordFilter.toLowerCase());
      const matchesKey = selectedKey === 'all' || chord.key === selectedKey;
      const matchesType = selectedChordType === 'all' || chord.suffix === selectedChordType;
      return matchesFilter && matchesKey && matchesType;
    });
  }, [availableChords, chordFilter, selectedKey, selectedChordType]);

  // Gestionnaires d'événements
  const handlePlayChord = useCallback(async (chordName: string, chordData?: any) => {
    if (!midiPlayer) return;
    
    if (isPlaying && currentChord === chordName) {
      midiPlayer.stopAll();
      setIsPlaying(false);
      setCurrentChord(null);
      return;
    }
    
    try {
      setIsPlaying(true);
      setCurrentChord(chordName);
      
      if (chordData && chordData.positions && chordData.positions.length > 0) {
        await midiPlayer.playChord(chordData.positions[0], 2000);
      } else {
        // Fallback pour les accords sans données de position
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      setIsPlaying(false);
      setCurrentChord(null);
    } catch (error) {
      console.error('Erreur lors de la lecture de l\'accord:', error);
      setIsPlaying(false);
      setCurrentChord(null);
    }
  }, [midiPlayer, isPlaying, currentChord]);
  
  const handleAddChord = useCallback((chordName: string, chordData?: any) => {
    const newChord: ChordPosition = {
      id: `chord-${Date.now()}`,
      position: chords?.length || 0,
      chord: chordName,
      instrument: selectedInstrument,
      tuning: selectedTuning,
      chordData
    };
    
    const updatedChords = [...(chords || []), newChord];
    onChordsChange(updatedChords);
    
    toast({
      title: "Accord ajouté",
      description: `L'accord ${chordName} a été ajouté`
    });
  }, [chords, selectedInstrument, selectedTuning, onChordsChange]);
  
  const handleRemoveChord = useCallback((chordId: string) => {
    const updatedChords = (chords || []).filter((c: any) => c.id !== chordId);
    onChordsChange(updatedChords);
    
    toast({
      title: "Accord supprimé",
      description: "L'accord a été supprimé"
    });
  }, [chords, onChordsChange]);
  
  const handleProgressionSelect = useCallback((progression: any) => {
    const newChords = progression.chords.map((chordName: string, index: number) => {
      const chordData = availableChords.find(c => c.name === chordName);
      return {
        id: `chord-${Date.now()}-${index}`,
        position: (chords?.length || 0) + index,
        chord: chordName,
        instrument: selectedInstrument,
        tuning: selectedTuning,
        chordData: chordData?.chordData
      };
    });
    
    const updatedChords = [...(chords || []), ...newChords];
    onChordsChange(updatedChords);
    
    toast({
      title: "Progression ajoutée",
      description: `La progression ${progression.name} a été ajoutée`
    });
  }, [chords, selectedInstrument, selectedTuning, onChordsChange, availableChords]);

  // Fonction pour gérer la génération IA
  const handleAIGenerate = useCallback(async (prompt: string) => {
    setIsAIGenerating(true);
    try {
      // Simulation de génération IA - à remplacer par l'API réelle
      const generatedChords = [
        { id: `ai-chord-${Date.now()}-1`, position: chords?.length || 0, chord: 'C', instrument: selectedInstrument, tuning: selectedTuning },
        { id: `ai-chord-${Date.now()}-2`, position: (chords?.length || 0) + 1, chord: 'Am', instrument: selectedInstrument, tuning: selectedTuning },
        { id: `ai-chord-${Date.now()}-3`, position: (chords?.length || 0) + 2, chord: 'F', instrument: selectedInstrument, tuning: selectedTuning },
        { id: `ai-chord-${Date.now()}-4`, position: (chords?.length || 0) + 3, chord: 'G', instrument: selectedInstrument, tuning: selectedTuning }
      ];
      
      const updatedChords = [...(chords || []), ...generatedChords];
      onChordsChange(updatedChords);
      
      toast({
        title: "Accords générés",
        description: "L'IA a généré de nouveaux accords"
      });
    } catch (error) {
      console.error('Erreur lors de la génération IA:', error);
      toast({
        title: "Erreur",
        description: "Impossible de générer les accords",
        variant: "destructive"
      });
    } finally {
      setIsAIGenerating(false);
    }
  }, [chords, selectedInstrument, selectedTuning, onChordsChange]);

  // Rendu du diagramme d'accord
  const renderChordDiagram = useCallback((chord: ChordPosition) => {
    if (!chord.chordData || !chord.chordData.positions || chord.chordData.positions.length === 0) {
      return (
        <div className="w-16 h-20 border rounded flex items-center justify-center bg-muted">
          <span className="text-xs text-muted-foreground">N/A</span>
        </div>
      );
    }

    const position = chord.chordData.positions[0];
    
    if (selectedInstrument === 'guitar' || selectedInstrument === 'ukulele') {
      return (
        <div className="w-16 h-20 border rounded p-1 bg-background">
          <div className="text-xs font-medium text-center mb-1">{chord.chord}</div>
          <div className="grid grid-cols-6 gap-px h-12">
            {position.frets?.map((fret: number, index: number) => (
              <div key={index} className="flex flex-col">
                <div className="h-2 border-b border-gray-300"></div>
                {fret > 0 && (
                  <div 
                    className="w-2 h-2 bg-blue-500 rounded-full mx-auto"
                    style={{ marginTop: `${(fret - 1) * 8}px` }}
                  ></div>
                )}
              </div>
            ))}
          </div>
          <div className="text-xs text-center mt-1">
            {position.baseFret > 1 && `${position.baseFret}fr`}
          </div>
        </div>
      );
    }
    
    return (
      <div className="w-16 h-20 border rounded flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="text-sm font-medium">{chord.chord}</div>
          <Piano className="h-6 w-6 mx-auto mt-1 text-muted-foreground" />
        </div>
      </div>
    );
  }, [selectedInstrument]);

  const renderChordPreview = useCallback((chord: ChordPosition) => {
    return (
      <div key={chord.id} className="flex items-center gap-3 p-3 border rounded-lg bg-card hover:bg-accent/50 transition-colors">
        {renderChordDiagram(chord)}
        
        <div className="flex-1">
          <div className="font-medium">{chord.chord}</div>
          <div className="text-sm text-muted-foreground">
            {currentInstrumentData?.name} - {currentTuningData?.name}
          </div>
          {chord.chordData?.positions?.[0]?.difficulty && (
            <Badge variant="outline" className="text-xs mt-1">
              {chord.chordData.positions[0].difficulty}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePlayChord(chord.chord, chord.chordData)}
            disabled={isPlaying && currentChord === chord.chord}
          >
            {isPlaying && currentChord === chord.chord ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRemoveChord(chord.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }, [currentInstrumentData?.name, currentTuningData?.name, isPlaying, currentChord, handlePlayChord, handleRemoveChord, renderChordDiagram]);

  return (
    <div className="h-full flex flex-col">
      {/* En-tête amélioré */}
      <div className="border-b bg-card p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Music className="h-5 w-5 text-primary" />
            <h2 className="font-medium">Intégration Accords Pro</h2>
            {currentSection && <Badge variant="outline">{currentSection.title}</Badge>}
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label className="text-sm">Instrument:</Label>
            <Select value={selectedInstrument} onValueChange={setSelectedInstrument}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(INSTRUMENTS_DATA).map(([key, instrument]) => (
                  <SelectItem key={key} value={key}>
                    {instrument.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <Label className="text-sm">Accordage:</Label>
            <Select value={selectedTuning} onValueChange={setSelectedTuning}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {currentInstrumentData?.tunings.map(tuning => (
                  <SelectItem key={tuning.id} value={tuning.id}>
                    {tuning.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <Label className="text-sm">Tonalité:</Label>
            <Select value={selectedKey} onValueChange={setSelectedKey}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes</SelectItem>
                {['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'].map(key => (
                  <SelectItem key={key} value={key}>{key}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <Label className="text-sm">Type:</Label>
            <Select value={selectedChordType} onValueChange={setSelectedChordType}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous</SelectItem>
                <SelectItem value="major">Majeur</SelectItem>
                <SelectItem value="minor">Mineur</SelectItem>
                <SelectItem value="7">7ème</SelectItem>
                <SelectItem value="maj7">Maj7</SelectItem>
                <SelectItem value="m7">m7</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      <div className="flex-1 flex">
        {/* Panneau principal */}
        <div className="flex-1 p-4">
          <Tabs defaultValue="chords" className="h-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="chords">Accords</TabsTrigger>
              <TabsTrigger value="progressions">Progressions</TabsTrigger>
              <TabsTrigger value="library">Bibliothèque</TabsTrigger>
              <TabsTrigger value="ai">IA</TabsTrigger>
            </TabsList>
            
            <TabsContent value="chords" className="mt-4 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Accords actuels</h3>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="gap-1"
                  onClick={() => setShowChordLibrary(true)}
                >
                  <Plus className="h-4 w-4" />
                  Ajouter
                </Button>
              </div>
              
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {(!chords || chords.length === 0) ? (
                    <div className="text-center py-12 text-muted-foreground">
                      <Guitar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">Aucun accord</p>
                      <p className="text-sm">Utilisez la bibliothèque, les progressions ou l'IA pour commencer</p>
                    </div>
                  ) : (
                    chords.map(renderChordPreview)
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="progressions" className="mt-4 space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Label>Style:</Label>
                <Select value={selectedProgression} onValueChange={setSelectedProgression}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(CHORD_PROGRESSIONS).map(style => (
                      <SelectItem key={style} value={style}>
                        {style.charAt(0).toUpperCase() + style.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <ScrollArea className="h-80">
                <div className="space-y-3">
                  {CHORD_PROGRESSIONS[selectedProgression as keyof typeof CHORD_PROGRESSIONS]?.map((progression, index) => (
                    <Card key={index} className="p-4 hover:bg-accent/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-medium mb-1">{progression.name}</div>
                          <div className="text-sm text-muted-foreground mb-3">
                            {progression.description}
                          </div>
                          <div className="flex gap-2">
                            {progression.chords.map((chord, idx) => (
                              <Badge key={idx} variant="secondary" className="text-sm px-3 py-1">
                                {chord}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleProgressionSelect(progression)}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Ajouter
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="library" className="mt-4 space-y-4">
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher un accord..."
                    value={chordFilter}
                    onChange={(e) => setChordFilter(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <ScrollArea className="h-80">
                <div className="grid grid-cols-4 gap-3">
                  {filteredChords.slice(0, 50).map((chord, index) => (
                    <Card 
                      key={index} 
                      className="p-3 hover:bg-accent/50 transition-colors cursor-pointer"
                      onClick={() => handleAddChord(chord.name, chord.chordData)}
                    >
                      <div className="text-center">
                        <div className="font-medium mb-1">{chord.name}</div>
                        <div className="text-xs text-muted-foreground mb-2">
                          {chord.suffix === 'major' ? 'Majeur' : chord.suffix}
                        </div>
                        <Button variant="ghost" size="sm" className="w-full">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
                {filteredChords.length > 50 && (
                  <div className="text-center py-4 text-muted-foreground">
                    ... et {filteredChords.length - 50} autres accords
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="ai" className="mt-4 space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Prompt pour l'IA</Label>
                  <Input
                    id="ai-prompt"
                    placeholder="Ex: Génère des accords pour un refrain énergique en Do majeur"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAIGenerate(e.currentTarget.value);
                      }
                    }}
                  />
                </div>
                
                <Button
                  onClick={() => {
                    const input = document.getElementById('ai-prompt') as HTMLInputElement;
                    if (input?.value) {
                      handleAIGenerate(input.value);
                    }
                  }}
                  disabled={isAIGenerating}
                  className="w-full gap-2"
                >
                  <Zap className="h-4 w-4" />
                  {isAIGenerating ? 'Génération...' : 'Générer avec l\'IA'}
                </Button>
                
                <Separator />
                
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Suggestions contextuelles</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { label: 'Style Pop', prompt: 'Génère des accords pour un style pop moderne' },
                      { label: 'Style Rock', prompt: 'Génère des accords pour un style rock énergique' },
                      { label: 'Style Jazz', prompt: 'Génère des accords pour un style jazz sophistiqué' },
                      { label: 'Style Folk', prompt: 'Génère des accords pour un style folk acoustique' }
                    ].map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleAIGenerate(suggestion.prompt)}
                        disabled={isAIGenerating}
                        className="text-left justify-start"
                      >
                        {suggestion.label}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Panneau latéral amélioré */}
        <div className="w-80 border-l bg-card/50 p-4">
          <div className="space-y-6">
            <div className="space-y-3">
              <h3 className="font-medium flex items-center gap-2">
                <Target className="h-4 w-4" />
                Aperçu
              </h3>
              <Card className="p-3">
                <div className="text-sm font-medium mb-2">
                  {currentSection?.title || 'Aucune section sélectionnée'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {currentSection?.type || 'N/A'}
                </div>
              </Card>
            </div>
            
            <Separator />
            
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Statistiques
              </h4>
              <div className="grid grid-cols-2 gap-2">
                <Card className="p-3 text-center">
                  <div className="text-lg font-bold text-primary">{chords?.length || 0}</div>
                  <div className="text-xs text-muted-foreground">Accords</div>
                </Card>
                <Card className="p-3 text-center">
                  <div className="text-lg font-bold text-primary">{currentInstrumentData?.name}</div>
                  <div className="text-xs text-muted-foreground">Instrument</div>
                </Card>
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Actions rapides</h4>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full gap-2">
                  <Copy className="h-4 w-4" />
                  Copier accords
                </Button>
                
                <Button variant="outline" size="sm" className="w-full gap-2">
                  <Download className="h-4 w-4" />
                  Exporter MIDI
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full gap-2"
                  onClick={() => onChordsChange([])}
                >
                  <RotateCcw className="h-4 w-4" />
                  Réinitialiser
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Dialog pour la bibliothèque d'accords */}
      <Dialog open={showChordLibrary} onOpenChange={setShowChordLibrary}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Bibliothèque d'accords - {currentInstrumentData?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher un accord..."
                  value={chordFilter}
                  onChange={(e) => setChordFilter(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedKey} onValueChange={setSelectedKey}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes</SelectItem>
                  {['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'].map(key => (
                    <SelectItem key={key} value={key}>{key}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <ScrollArea className="h-96">
              <div className="grid grid-cols-6 gap-3">
                {filteredChords.slice(0, 100).map((chord, index) => (
                  <Card 
                    key={index} 
                    className="p-3 hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => {
                      handleAddChord(chord.name, chord.chordData);
                      setShowChordLibrary(false);
                    }}
                  >
                    <div className="text-center">
                      <div className="font-medium text-sm mb-1">{chord.name}</div>
                      <div className="text-xs text-muted-foreground mb-2">
                        {chord.suffix === 'major' ? 'Maj' : chord.suffix}
                      </div>
                      <Button variant="ghost" size="sm" className="w-full h-8">
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Mémoisation du composant pour éviter les re-rendus inutiles
export const AIChordIntegrationPro = memo(AIChordIntegrationProComponent);
export default AIChordIntegrationPro;