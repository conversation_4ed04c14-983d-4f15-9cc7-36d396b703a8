'use client';

import { useState, useCallback, useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { toast } from 'sonner';

interface SongData {
  id?: string;
  title: string;
  artist: string;
  description?: string;
  bpm?: number;
  musical_key?: string;
  time_signature?: string;
  genres?: string[];
  moods?: string[];
  instrumentation?: string[];
  audio_url?: string;
  cover_art_url?: string;
  duration_ms?: number;
  lyrics?: string;
  chords?: string;
  editor_data?: any;
  ai_composer_data?: any;
  created_at?: string;
  updated_at?: string;
}

interface UseAIComposerSupabaseReturn {
  currentSong: SongData | null;
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  loadSong: (songId: string) => Promise<void>;
  saveSong: (songData: Partial<SongData>) => Promise<string | null>;
  createNewSong: (initialData?: Partial<SongData>) => Promise<string | null>;
  uploadAudioFile: (file: File) => Promise<string | null>;
  uploadCoverArt: (file: File) => Promise<string | null>;
  updateSongField: (field: string, value: any) => void;
  resetSong: () => void;
}

export const useAIComposerSupabase = (): UseAIComposerSupabaseReturn => {
  const [currentSong, setCurrentSong] = useState<SongData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useUser();
  const supabase = getSupabaseClient();

  // Charger une chanson existante
  const loadSong = useCallback(async (songId: string) => {
    if (!user) {
      setError('Utilisateur non connecté');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error: fetchError } = await supabase
        .from('songs')
        .select(`
          id, title, artist, description, bpm, musical_key, time_signature,
          genres, moods, audio_url, cover_art_url, duration_ms, lyrics, chords,
          editor_data, ai_composer_data, created_at, updated_at
        `)
        .eq('id', songId)
        .eq('creator_user_id', user.id)
        .single();

      if (fetchError) throw fetchError;

      setCurrentSong(data);
    } catch (err: any) {
      console.error('Erreur lors du chargement de la chanson:', err);
      setError(err.message);
      toast.error('Erreur', {
        description: `Impossible de charger la chanson: ${err.message}`
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, supabase]);

  // Sauvegarder une chanson
  const saveSong = useCallback(async (songData: Partial<SongData>): Promise<string | null> => {
    if (!user) {
      setError('Utilisateur non connecté');
      return null;
    }

    setIsSaving(true);
    setError(null);

    try {
      const now = new Date().toISOString();
      const dataToSave = {
        ...songData,
        creator_user_id: user.id,
        updated_at: now,
        // Assurer que les champs requis sont présents
        title: songData.title || currentSong?.title || 'Nouveau morceau',
        artist: songData.artist || currentSong?.artist || 'Artiste inconnu',
      };

      let result;

      if (currentSong?.id) {
        // Mise à jour d'une chanson existante
        const { data, error: updateError } = await supabase
          .from('songs')
          .update(dataToSave)
          .eq('id', currentSong.id)
          .eq('creator_user_id', user.id)
          .select()
          .single();

        if (updateError) throw updateError;
        result = data;
      } else {
        // Création d'une nouvelle chanson
        const { data, error: insertError } = await supabase
          .from('songs')
          .insert({
            ...dataToSave,
            created_at: now,
          })
          .select()
          .single();

        if (insertError) throw insertError;
        result = data;
      }

      setCurrentSong(result);
      toast.success('Sauvegarde réussie', {
        description: 'La chanson a été sauvegardée avec succès'
      });

      return result.id;
    } catch (err: any) {
      console.error('Erreur lors de la sauvegarde:', err);
      setError(err.message);
      toast.error('Erreur de sauvegarde', {
        description: `Impossible de sauvegarder: ${err.message}`
      });
      return null;
    } finally {
      setIsSaving(false);
    }
  }, [user, supabase, currentSong]);

  // Créer une nouvelle chanson
  // Créer une nouvelle chanson
  const createNewSong = useCallback(async (initialData?: Partial<SongData>): Promise<string | null> => {
    const newSongData = {
      title: 'Nouveau morceau',
      artist: user?.display_name || user?.name || 'Artiste',
      description: '',
      bpm: 120,
      musical_key: 'C',
      time_signature: '4/4',
      genres: [],
      moods: [],
      lyrics: '',
      chords: '',
      editor_data: {},
      ai_composer_data: {
        sections: [],
        styleConfig: {
          key: 'C',
          bpm: 120,
          timeSignature: '4/4',
          genres: [],
          mood: ''
        }
      },
      ...initialData
    };

    const songId = await saveSong(newSongData);
    return songId;
  }, [user, saveSong]);

  // Upload d'un fichier audio
  const uploadAudioFile = useCallback(async (file: File): Promise<string | null> => {
    if (!user) {
      setError('Utilisateur non connecté');
      return null;
    }

    try {
      const timestamp = Date.now();
      const sanitizedFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9._-]/g, '');
      const filePath = `${user.id}/${timestamp}_${sanitizedFileName}`;

      const { error: uploadError } = await supabase.storage
        .from('audio')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('audio')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (err: any) {
      console.error('Erreur upload audio:', err);
      setError(err.message);
      toast.error('Erreur upload', {
        description: `Impossible d'uploader le fichier audio: ${err.message}`
      });
      return null;
    }
  }, [user, supabase]);

  // Upload d'une image de couverture
  const uploadCoverArt = useCallback(async (file: File): Promise<string | null> => {
    if (!user) {
      setError('Utilisateur non connecté');
      return null;
    }

    try {
      const timestamp = Date.now();
      const sanitizedFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9._-]/g, '');
      const filePath = `${user.id}/${timestamp}_${sanitizedFileName}`;

      const { error: uploadError } = await supabase.storage
        .from('covers')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('covers')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (err: any) {
      console.error('Erreur upload cover:', err);
      setError(err.message);
      toast.error('Erreur upload', {
        description: `Impossible d'uploader l'image: ${err.message}`
      });
      return null;
    }
  }, [user, supabase]);

  // Mettre à jour un champ de la chanson
  const updateSongField = useCallback((field: string, value: any) => {
    setCurrentSong(prev => prev ? { ...prev, [field]: value } : null);
  }, []);

  // Réinitialiser la chanson
  const resetSong = useCallback(() => {
    setCurrentSong(null);
    setError(null);
  }, []);

  return {
    currentSong,
    isLoading,
    isSaving,
    error,
    loadSong,
    saveSong,
    createNewSong,
    uploadAudioFile,
    uploadCoverArt,
    updateSongField,
    resetSong,
  };
};
