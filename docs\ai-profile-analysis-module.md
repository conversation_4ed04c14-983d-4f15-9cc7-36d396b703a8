# Module d'Analyse de Profil IA - Documentation Technique

## Vue d'ensemble

Le module d'analyse de profil IA est un composant réutilisable qui permet d'analyser différents types de données musicales (profil utilisateur, morceaux, albums, statistiques) en utilisant l'intelligence artificielle. Il offre une personnalisation avancée via des mots-clés et des mini-prompts, ainsi qu'une persistance locale des analyses générées.

## Architecture

### Composants principaux

```
components/preferences/
└── advanced-settings-tab.tsx  # Implémentation actuelle du module d'analyse

app/api/
└── chat/
    └── route.ts              # Route API pour la communication avec les fournisseurs IA
```

### Diagramme de flux

```
[Interface Utilisateur] → [Collecte de données] → [Construction du prompt] → [Appel API IA] → [Streaming de réponse] → [Formatage] → [Affichage] → [Persistance]
```

## Fonctionnalités détaillées

### 1. Génération d'analyse personnalisée

#### Fonction principale : `generateProfileAnalysis()`

```typescript
const generateProfileAnalysis = async () => {
  // Récupération des données utilisateur
  // Construction du prompt avec mots-clés et mini-prompt
  // Appel à fetchStreamingText pour l'analyse
};
```

#### Paramètres de personnalisation

- **Mots-clés** (`analysisKeywords`) : Thèmes ou concepts à mettre en avant dans l'analyse
- **Mini-prompt** (`analysisPrompt`) : Question ou instruction spécifique pour orienter l'analyse
- **Profondeur d'analyse** (`analysisDepth`) : Niveau de détail de l'analyse (basique, standard, approfondie)
- **Limite de mots** (`wordLimit`) : Nombre maximum de mots pour l'analyse générée

### 2. Streaming de texte en temps réel

#### Fonction : `fetchStreamingText()`

```typescript
const fetchStreamingText = async (messages: any[]) => {
  // Configuration de la requête
  // Gestion du streaming et décodage progressif
  // Formatage HTML et mise à jour de l'interface
  // Sauvegarde dans le localStorage
};
```

#### Processus détaillé

1. Envoi d'une requête à l'API avec le prompt construit
2. Lecture du stream de réponse chunk par chunk
3. Décodage des chunks avec TextDecoder
4. Détection et conversion des codes ASCII si nécessaire
5. Formatage HTML (gras, italique, sauts de ligne)
6. Mise à jour progressive de l'interface utilisateur
7. Sauvegarde des données complètes dans le localStorage

### 3. Persistance des analyses

#### Structure de données

```json
{
  "content": "<HTML formatté de l'analyse>",
  "provider": "OpenAI ou Ollama",
  "timestamp": "2025-06-14T19:45:28.000Z",
  "keywords": "jazz, improvisation",
  "prompt": "Comment améliorer mes compositions ?"
}
```

#### Fonctions de sauvegarde et chargement

```typescript
// Sauvegarde
localStorage.setItem('mouvik_profile_analysis', JSON.stringify(analysisData));

// Chargement
const savedAnalysis = localStorage.getItem('mouvik_profile_analysis');
if (savedAnalysis) {
  const parsedData = JSON.parse(savedAnalysis);
  // Restauration de l'analyse et des paramètres
}
```

## Guide d'implémentation pour la réutilisation

### Extraction en composant réutilisable

Pour réutiliser ce module dans d'autres contextes (morceaux, albums, statistiques), il est recommandé d'extraire les fonctionnalités principales dans un composant ou un hook réutilisable :

```typescript
// hooks/useAiAnalysis.ts
import { useState, useEffect } from 'react';

export const useAiAnalysis = (contextType, contextId) => {
  const [analysisKeywords, setAnalysisKeywords] = useState('');
  const [analysisPrompt, setAnalysisPrompt] = useState('');
  const [analysis, setAnalysis] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);

  // Clé de stockage dynamique basée sur le contexte
  const storageKey = `mouvik_${contextType}_analysis_${contextId || 'profile'}`;

  // Chargement de l'analyse sauvegardée
  useEffect(() => {
    const savedAnalysis = localStorage.getItem(storageKey);
    if (savedAnalysis) {
      try {
        const parsedData = JSON.parse(savedAnalysis);
        setAnalysisKeywords(parsedData.keywords || '');
        setAnalysisPrompt(parsedData.prompt || '');
        setAnalysis(parsedData);
      } catch (error) {
        console.error(`[useAiAnalysis] Error loading saved ${contextType} analysis:`, error);
      }
    }
  }, [contextType, contextId, storageKey]);

  // Fonction de génération d'analyse
  const generateAnalysis = async (contextData) => {
    setIsGenerating(true);
    setError(null);
    
    try {
      // Construction du prompt selon le contexte
      let prompt = `Tu es un expert en analyse musicale. `;
      
      switch (contextType) {
        case 'profile':
          prompt += `Analyse le profil musical de cet utilisateur:\n${JSON.stringify(contextData)}\n`;
          break;
        case 'song':
          prompt += `Analyse ce morceau musical:\n${JSON.stringify(contextData)}\n`;
          break;
        case 'album':
          prompt += `Analyse cet album musical:\n${JSON.stringify(contextData)}\n`;
          break;
        case 'stats':
          prompt += `Analyse ces statistiques musicales:\n${JSON.stringify(contextData)}\n`;
          break;
      }
      
      // Ajout des mots-clés et mini-prompt
      if (analysisKeywords) {
        prompt += `\nMots-clés/thèmes à mettre en avant: ${analysisKeywords}\n`;
      }
      
      if (analysisPrompt) {
        prompt += `\nQuestion/instruction spécifique: ${analysisPrompt}\n`;
        prompt += `Assure-toi de répondre directement à cette question/instruction dans ton analyse.\n`;
      }
      
      // Appel à l'API et streaming
      const result = await fetchStreamingText([{ role: "user", content: prompt }]);
      
      // Sauvegarde avec le contexte approprié
      const analysisData = {
        content: result.formattedText,
        provider: result.provider,
        timestamp: new Date().toISOString(),
        keywords: analysisKeywords,
        prompt: analysisPrompt,
        contextType,
        contextId
      };
      
      localStorage.setItem(storageKey, JSON.stringify(analysisData));
      setAnalysis(analysisData);
      
    } catch (error) {
      setError(error.message);
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    analysis,
    isGenerating,
    error,
    analysisKeywords,
    setAnalysisKeywords,
    analysisPrompt,
    setAnalysisPrompt,
    generateAnalysis,
    clearAnalysis: () => {
      localStorage.removeItem(storageKey);
      setAnalysis(null);
    }
  };
};
```

### Exemples d'utilisation

#### Analyse de profil (existant)

```tsx
// Dans advanced-settings-tab.tsx
const {
  analysis,
  isGenerating,
  error,
  analysisKeywords,
  setAnalysisKeywords,
  analysisPrompt,
  setAnalysisPrompt,
  generateAnalysis
} = useAiAnalysis('profile', user?.id);

// Utilisation
<Button onClick={() => generateAnalysis(userProfileData)}>
  Générer une analyse de profil
</Button>
```

#### Analyse de morceau (à implémenter)

```tsx
// Dans song-detail.tsx
const {
  analysis,
  isGenerating,
  error,
  analysisKeywords,
  setAnalysisKeywords,
  analysisPrompt,
  setAnalysisPrompt,
  generateAnalysis
} = useAiAnalysis('song', song.id);

// Utilisation
<Button onClick={() => generateAnalysis(songData)}>
  Analyser ce morceau
</Button>
```

#### Analyse d'album (à implémenter)

```tsx
// Dans album-detail.tsx
const {
  analysis,
  isGenerating,
  error,
  analysisKeywords,
  setAnalysisKeywords,
  analysisPrompt,
  setAnalysisPrompt,
  generateAnalysis
} = useAiAnalysis('album', album.id);

// Utilisation
<Button onClick={() => generateAnalysis(albumData)}>
  Analyser cet album
</Button>
```

#### Analyse de statistiques (à implémenter)

```tsx
// Dans stats-dashboard.tsx
const {
  analysis,
  isGenerating,
  error,
  analysisKeywords,
  setAnalysisKeywords,
  analysisPrompt,
  setAnalysisPrompt,
  generateAnalysis
} = useAiAnalysis('stats', user?.id);

// Utilisation
<Button onClick={() => generateAnalysis(userStatsData)}>
  Analyser mes statistiques
</Button>
```

### Composant d'affichage réutilisable

```tsx
// components/ai/AnalysisDisplay.tsx
import { Sparkles } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface AnalysisDisplayProps {
  analysis: {
    content: string;
    provider: string;
    timestamp: string;
    keywords?: string;
    prompt?: string;
  } | null;
  title?: string;
  icon?: React.ReactNode;
}

export const AnalysisDisplay = ({ 
  analysis, 
  title = "Analyse IA", 
  icon = <Sparkles size={20} className="mr-2 text-yellow-500" />
}: AnalysisDisplayProps) => {
  if (!analysis) return null;
  
  return (
    <div className="prose prose-sm dark:prose-invert max-w-none p-4 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-950 rounded-md border shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-primary flex items-center m-0">
          {icon}
          {title}
        </h4>
        <Badge variant="outline" className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-800">
          {analysis.provider}
        </Badge>
      </div>
      
      {(analysis.keywords || analysis.prompt) && (
        <div className="mb-3 text-xs bg-slate-100 dark:bg-slate-800 p-2 rounded-md">
          {analysis.keywords && (
            <p className="mb-1"><span className="font-medium">Mots-clés :</span> {analysis.keywords}</p>
          )}
          {analysis.prompt && (
            <p className="mb-0"><span className="font-medium">Question/prompt :</span> {analysis.prompt}</p>
          )}
        </div>
      )}
      
      <div dangerouslySetInnerHTML={{ __html: analysis.content }} />
      
      <div className="text-xs text-gray-500 mt-4">
        Généré le {new Date(analysis.timestamp).toLocaleString()}
      </div>
    </div>
  );
};
```

## Bonnes pratiques pour la réutilisation

1. **Séparation des préoccupations**
   - Séparer la logique de génération d'analyse (hook) de l'affichage (composant)
   - Adapter le prompt selon le contexte d'utilisation

2. **Gestion des états cohérente**
   - Utiliser les mêmes états pour le chargement, les erreurs et l'affichage
   - Maintenir une expérience utilisateur cohérente entre les différents contextes

3. **Persistance contextuelle**
   - Utiliser des clés de stockage dynamiques basées sur le type de contexte et l'ID
   - Conserver la même structure de données pour la persistance

4. **Personnalisation**
   - Toujours inclure les champs de mots-clés et mini-prompt
   - Adapter les placeholders selon le contexte d'utilisation

5. **Performance**
   - Éviter de régénérer des analyses existantes
   - Proposer une option de rafraîchissement manuel

## Intégration avec d'autres modules

### AI Composer

Le module d'analyse peut être intégré avec l'AI Composer pour fournir des insights sur les compositions en cours :

```tsx
// Dans AI Composer
const { analysis, generateAnalysis } = useAiAnalysis('composition', compositionId);

// Utilisation pour analyser une composition en cours
<Button onClick={() => generateAnalysis(compositionData)}>
  Analyser cette composition
</Button>
```

### Dashboard Analytics

L'analyse peut être utilisée pour enrichir les tableaux de bord analytiques :

```tsx
// Dans le dashboard
const { analysis, generateAnalysis } = useAiAnalysis('stats', 'monthly');

// Génération d'insights sur les statistiques mensuelles
<Button onClick={() => generateAnalysis(monthlyStatsData)}>
  Obtenir des insights sur ce mois
</Button>
```

## Prochaines étapes

1. Extraire la logique actuelle dans un hook réutilisable `useAiAnalysis`
2. Créer un composant d'affichage réutilisable `AnalysisDisplay`
3. Implémenter l'analyse de morceaux dans la page de détail des morceaux
4. Implémenter l'analyse d'albums dans la page de détail des albums
5. Intégrer l'analyse aux tableaux de bord de statistiques
6. Ajouter des options de partage des analyses générées
