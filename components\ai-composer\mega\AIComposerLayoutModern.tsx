'use client';

import React, { useState, useCallback } from 'react';
import { 
  Music, Play, Pause, Save, Setting<PERSON>, Brain, 
  FileText, Guitar, Mic2, Volume2, BarChart3,
  ChevronLeft, ChevronRight, Maximize2, Minimize2
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

interface AIComposerLayoutModernProps {
  songId?: string;
  currentSong: any;
  setCurrentSong: (song: any) => void;
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (sectionId: string) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
  onSave: () => void;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
}

export const AIComposerLayoutModern: React.FC<AIComposerLayoutModernProps> = ({
  currentSong,
  setCurrentSong,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  isPlaying,
  setIsPlaying,
  onSave,
  onAIGenerate
}) => {
  // États du layout
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(true);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState('lyrics');
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Section courante
  const currentSection = songSections.find(s => s.id === selectedSection);

  // Gestionnaire de changement de paroles
  const handleLyricsChange = useCallback((content: string) => {
    setLyricsContent(content);
    // Mettre à jour la section courante
    if (currentSection) {
      const updatedSections = songSections.map(section => 
        section.id === selectedSection 
          ? { ...section, lyrics: content }
          : section
      );
      setSongSections(updatedSections);
    }
  }, [currentSection, selectedSection, songSections, setSongSections, setLyricsContent]);

  // Gestionnaire d'ajout d'accord
  const handleAddChord = useCallback((chordName: string) => {
    if (!currentSection) return;
    
    const newChord = {
      id: `chord-${Date.now()}`,
      name: chordName,
      position: 0,
      instrument: 'guitar'
    };

    const updatedSections = songSections.map(section => 
      section.id === selectedSection 
        ? { 
            ...section, 
            chords: [...(section.chords || []), newChord]
          }
        : section
    );
    setSongSections(updatedSections);
  }, [currentSection, selectedSection, songSections, setSongSections]);

  return (
    <div className="h-screen flex flex-col bg-slate-900 text-white overflow-hidden">
      {/* Header compact */}
      <div className="h-14 bg-slate-800 border-b border-slate-700 flex items-center justify-between px-4 flex-shrink-0">
        {/* Logo et titre */}
        <div className="flex items-center gap-3">
          <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Music className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-white">
              {currentSong?.title || 'Nouveau Morceau'}
            </h1>
            <p className="text-xs text-slate-400">
              {currentSong?.artist || 'Artiste'} • {styleConfig?.key || 'C'} • {styleConfig?.bpm || 120} BPM
            </p>
          </div>
        </div>

        {/* Contrôles centraux */}
        <div className="flex items-center gap-2">
          <Button
            variant={isPlaying ? "default" : "outline"}
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className="gap-1"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            {isPlaying ? 'Pause' : 'Play'}
          </Button>
          
          <Button variant="outline" size="sm" onClick={onSave} className="gap-1">
            <Save className="h-4 w-4" />
            Sauver
          </Button>

          <Separator orientation="vertical" className="h-6" />

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>

        {/* Actions droite */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            Section: {currentSection?.title || 'Aucune'}
          </Badge>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLeftSidebarOpen(!leftSidebarOpen)}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setRightSidebarOpen(!rightSidebarOpen)}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Layout principal */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* Sidebar gauche - Structure et outils */}
        {leftSidebarOpen && (
          <div className="w-80 bg-slate-800 border-r border-slate-700 flex flex-col">
            <div className="p-3 border-b border-slate-700">
              <h3 className="font-medium text-sm text-white">Structure & Outils</h3>
            </div>
            
            <div className="flex-1 overflow-y-auto p-3 space-y-4">
              {/* Sections */}
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-white">Sections</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {songSections.map((section) => (
                    <Button
                      key={section.id}
                      variant={selectedSection === section.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedSection(section.id)}
                      className="w-full justify-start gap-2"
                    >
                      <div className={`w-2 h-2 rounded-full ${
                        section.type === 'verse' ? 'bg-blue-400' :
                        section.type === 'chorus' ? 'bg-green-400' :
                        section.type === 'bridge' ? 'bg-purple-400' :
                        'bg-gray-400'
                      }`} />
                      {section.title}
                      <span className="ml-auto text-xs text-slate-400">
                        {section.duration || 16}s
                      </span>
                    </Button>
                  ))}
                </CardContent>
              </Card>

              {/* Accords rapides */}
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-white">Accords Rapides</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-2">
                    {['C', 'G', 'Am', 'F', 'Dm', 'Em'].map((chord) => (
                      <Button
                        key={chord}
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddChord(chord)}
                        className="text-xs"
                      >
                        {chord}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Configuration */}
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-white">Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-xs text-slate-300">Tonalité</Label>
                    <Input
                      value={styleConfig?.key || 'C'}
                      onChange={(e) => setStyleConfig({...styleConfig, key: e.target.value})}
                      className="bg-slate-600 border-slate-500 text-white h-8"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-slate-300">BPM</Label>
                    <Input
                      type="number"
                      value={styleConfig?.bpm || 120}
                      onChange={(e) => setStyleConfig({...styleConfig, bpm: parseInt(e.target.value)})}
                      className="bg-slate-600 border-slate-500 text-white h-8"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Zone centrale - Éditeur principal */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Onglets */}
          <div className="bg-slate-800 border-b border-slate-700 px-4 py-2">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="bg-slate-700">
                <TabsTrigger value="lyrics" className="gap-2">
                  <FileText className="h-4 w-4" />
                  Paroles
                </TabsTrigger>
                <TabsTrigger value="chords" className="gap-2">
                  <Guitar className="h-4 w-4" />
                  Accords
                </TabsTrigger>
                <TabsTrigger value="audio" className="gap-2">
                  <Volume2 className="h-4 w-4" />
                  Audio
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Contenu principal */}
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} className="h-full">
              <TabsContent value="lyrics" className="h-full m-0">
                <div className="h-full flex flex-col">
                  <div className="flex-1 p-4">
                    <Textarea
                      value={lyricsContent}
                      onChange={(e) => handleLyricsChange(e.target.value)}
                      placeholder={`Écrivez les paroles pour ${currentSection?.title || 'cette section'}...`}
                      className="w-full h-full resize-none bg-slate-800 border-slate-600 text-white placeholder-slate-400 text-base leading-relaxed"
                    />
                  </div>
                  
                  {/* Actions IA */}
                  <div className="border-t border-slate-700 bg-slate-800 p-3">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onAIGenerate('Améliore ces paroles', 'lyrics')}
                        className="gap-1"
                      >
                        <Brain className="h-4 w-4" />
                        Améliorer
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onAIGenerate('Continue ces paroles', 'lyrics')}
                        className="gap-1"
                      >
                        <FileText className="h-4 w-4" />
                        Continuer
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="chords" className="h-full m-0">
                <div className="h-full p-4">
                  <div className="text-center text-slate-400 mt-8">
                    <Guitar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Système d'accords en cours de développement</p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="audio" className="h-full m-0">
                <div className="h-full p-4">
                  <div className="text-center text-slate-400 mt-8">
                    <Mic2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Outils audio en cours de développement</p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Sidebar droite - IA Assistant */}
        {rightSidebarOpen && (
          <div className="w-80 bg-slate-800 border-l border-slate-700 flex flex-col">
            <div className="p-3 border-b border-slate-700">
              <h3 className="font-medium text-sm text-white flex items-center gap-2">
                <Brain className="h-4 w-4" />
                Assistant IA
              </h3>
            </div>
            
            <div className="flex-1 overflow-y-auto p-3">
              <div className="text-center text-slate-400 mt-8">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Assistant IA en cours de développement</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer - Timeline */}
      <div className="h-16 bg-slate-800 border-t border-slate-700 flex items-center px-4 flex-shrink-0">
        <div className="flex items-center gap-4 text-sm text-slate-400">
          <span>Timeline:</span>
          {songSections.map((section, index) => (
            <div
              key={section.id}
              className={`px-2 py-1 rounded text-xs cursor-pointer ${
                selectedSection === section.id 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-slate-700 hover:bg-slate-600'
              }`}
              onClick={() => setSelectedSection(section.id)}
            >
              {section.title}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
