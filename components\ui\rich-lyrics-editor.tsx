"use client";

import React, { forwardRef, useRef, useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import ChordBlot from './quill-chord-blot';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import ChordDiagram from '@/components/ui/chord-diagram'; // Corrected to default import
import { loadChordLibrary, findChord } from '@/lib/chords/utils';
import { ChordLibrary, ChordPosition, ChordVariation } from '@/lib/chords/types';

const ReactQuill = dynamic(
  async () => {
    const Quill = (await import('quill')).default;
    Quill.register(ChordBlot);
    return import('react-quill');
  },
  { ssr: false }
);

interface RichLyricsEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  quillRef?: React.RefObject<any>;
  onSelectionChange?: (range: any, source: any, editor: any) => void;
  className?: string;
}

const parseChordName = (chordName: string): { root: string; suffix: string } => {
  let root = '';
  let suffix = '';

  if (chordName.length > 1 && (chordName[1] === '#' || chordName[1] === 'b')) {
    root = chordName.substring(0, 2);
    suffix = chordName.substring(2);
  } else {
    root = chordName.substring(0, 1);
    suffix = chordName.substring(1);
  }

  if (suffix === '') {
    suffix = 'major'; // Default to major if no suffix is provided
  }
  if (suffix === 'm') {
      suffix = 'minor'; // common abbreviation
  }

  return { root, suffix };
};

export const RichLyricsEditor = forwardRef<any, RichLyricsEditorProps>(({ value, onChange, placeholder, label, quillRef, onSelectionChange, className }: RichLyricsEditorProps, ref) => {
  const customQuillRef = useRef<any>(null);
  const [chordLibrary, setChordLibrary] = useState<ChordLibrary | null>(null);
  const [activeChord, setActiveChord] = useState<{ name: string; variation: ChordVariation | null; position: ChordPosition | null }>({ name: '', variation: null, position: null });
  const [popoverTarget, setPopoverTarget] = useState<HTMLElement | null>(null);
  const [popoverPosition, setPopoverPosition] = useState({ top: 0, left: 0 });
  const popoverRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load the default guitar chord library on component mount
    const loadLibrary = async () => {
      const library = await loadChordLibrary('guitar', 'standard');
      setChordLibrary(library);
    };
    loadLibrary();
  }, []);

  useEffect(() => {
    const quill = customQuillRef.current?.getEditor();
    if (!quill?.root) return;

    const handleMouseOver = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('chord-blot')) {
        const chordName = target.dataset.chordName;
        if (chordName && chordLibrary) {
          const { root, suffix } = parseChordName(chordName);
          const foundChord = findChord(chordLibrary, root, suffix);
          const rect = target.getBoundingClientRect();
          setPopoverPosition({ top: rect.top - 8, left: rect.left + rect.width / 2 });

          if (foundChord && foundChord.positions[0]) {
            setActiveChord({ name: chordName, variation: foundChord, position: foundChord.positions[0] });
          } else {
            setActiveChord({ name: chordName, variation: null, position: null });
          }
          setPopoverTarget(target);
        }
      }
    };

    const handleMouseOut = (e: MouseEvent) => {
      const target = e.relatedTarget as HTMLElement;
      if (popoverRef.current?.contains(target)) {
        return; // Mouse is over the popover, do nothing
      }
      setPopoverTarget(null);
    };

    const editorNode = quill.root;
    editorNode.addEventListener('mouseover', handleMouseOver);
    editorNode.addEventListener('mouseout', handleMouseOut);

    return () => {
      editorNode.removeEventListener('mouseover', handleMouseOver);
      editorNode.removeEventListener('mouseout', handleMouseOut);
    };
  }, [chordLibrary, popoverTarget]);

  const handleInsertChord = () => {
    const quill = customQuillRef.current?.getEditor();
    if (!quill) return;

    const chordName = prompt('Enter chord name (e.g., Am, G/B, C#m7):');
    if (chordName) {
      const range = quill.getSelection(true);
      quill.insertEmbed(range.index, 'chord', chordName, 'user');
      quill.setSelection(range.index + 1, 0, 'silent');
    }
  };

    return (
    <div className={`space-y-2 w-full ${className || ''}`}>
      <Popover open={!!popoverTarget} onOpenChange={(open) => !open && setPopoverTarget(null)}>
        <PopoverTrigger asChild>
            {/* This is a phantom trigger used to position the popover */}
            <div style={{ position: 'fixed', top: popoverPosition.top, left: popoverPosition.left, width: 0, height: 0 }} />
        </PopoverTrigger>
        <PopoverContent ref={popoverRef} onMouseLeave={() => setPopoverTarget(null)} side="top" align="center" className="w-auto p-2">
          {activeChord.position && chordLibrary ? (
            <div className="flex flex-col items-center">
                <h4 className="font-bold text-lg mb-2">{activeChord.variation?.name || activeChord.name}</h4>
                <ChordDiagram 
                    position={activeChord.position} 
                    instrument={chordLibrary.instrument} 
                />
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">Accord "{activeChord.name}" non trouvé.</p>
          )}
        </PopoverContent>
      </Popover>

      {label && <label className="block font-semibold mb-1">{label}</label>}
      <style>
        {`
          .chord-blot {
            background-color: #e0e7ff;
            color: #3730a3;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
          }
        `}
      </style>
      {/* @ts-ignore */}
      <ReactQuill
        ref={ref} // Pass ref to ReactQuill
        theme="snow"
        value={value}
        onChange={onChange}
        onChangeSelection={onSelectionChange}
        placeholder={placeholder || "Écris ou colle tes paroles ici..."}
        modules={{
          toolbar: {
            container: [
              [{ header: [1, 2, 3, false] }],
              [{ 'size': ['small', false, 'large', 'huge'] }],
              ['bold', 'italic', 'underline'],
              [{ 'color': [] }, { 'background': [] }],
              [{ 'align': [] }],
              [{ list: 'ordered' }, { list: 'bullet' }],
              ['blockquote', 'link'],
              ['clean'],
              ['chord'], // Add custom button
            ],
            handlers: {
              chord: handleInsertChord,
            },
          },
        }}
        className="bg-white dark:bg-zinc-900 rounded shadow min-h-[300px]"
      />
    </div>
  );
}
