/**
 * 🎼 ENHANCED LYRICS EDITOR - Éditeur Texte + Accords Intégré
 * 
 * Extension de RichLyricsEditor avec support complet des accords
 * Interface unifiée pour texte, accords et suggestions IA
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { 
  Music, Eye, EyeOff, Type, Grid3X3, Sparkles, 
  Volume2, Edit3, Trash2, Plus, RotateCcw, Save
} from 'lucide-react';
import { RichLyricsEditor } from '@/components/ui/rich-lyrics-editor';
import { ChordDiagramViewer } from '@/components/chord-system/components/ChordDiagramViewer';
import { ChordPickerModal } from '@/components/chord-system/components/ChordPickerModal';
import { useChordSystem } from '@/components/chord-system/providers/ChordSystemProvider';
import { type AIHistoryEntry } from '@/components/providers/AIProviderContext';
import { 
  type ChordPlacement, 
  type DisplayMode, 
  type UnifiedChordPosition 
} from '@/types/composer';

export interface EnhancedLyricsEditorProps {
  // Props héritées de RichLyricsEditor
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  quillRef?: React.RefObject<any>;
  onSelectionChange?: (range: any, source: any, editor: any) => void;
  
  // Props pour les accords
  chords: ChordPlacement[];
  onChordsChange: (chords: ChordPlacement[]) => void;
  displayMode?: DisplayMode;
  onDisplayModeChange?: (mode: DisplayMode) => void;
  
  // Props pour l'intégration IA
  onRequestChordSuggestions?: (context: string, position: number) => Promise<UnifiedChordPosition[]>;
  
  // Props pour le drag & drop
  onChordDrop?: (chord: UnifiedChordPosition, position: number) => void;
  
  // Props pour la sauvegarde
  onSave?: (lyrics: string, chords: ChordPlacement[]) => void;
  autoSave?: boolean;

  // Props pour l'historique IA
  aiHistory?: AIHistoryEntry[]; // Added aiHistory prop
}

export interface ChordOverlayProps {
  chords: ChordPlacement[];
  editorRef: React.RefObject<any>;
  displayMode: DisplayMode;
  onChordClick: (chord: ChordPlacement) => void;
  onChordRemove: (chordId: string) => void;
  onChordEdit: (chord: ChordPlacement) => void;
  // aiHistory?: AIHistoryEntry[]; // This was already in ChordOverlayProps, ensure it uses the imported AIHistoryEntry
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Overlay pour afficher les accords au-dessus du texte
 */
const ChordOverlay: React.FC<ChordOverlayProps> = ({
  chords,
  editorRef,
  displayMode,
  onChordClick,
  onChordRemove,
  onChordEdit
}) => {
  const [overlayPositions, setOverlayPositions] = useState<{ [chordId: string]: { x: number; y: number } }>({});

  // Calculer les positions des accords
  useEffect(() => {
    if (!editorRef.current || displayMode === 'text-only') return;

    const editor = editorRef.current.getEditor();
    if (!editor) return;

    const newPositions: { [chordId: string]: { x: number; y: number } } = {};

    chords.forEach(chordPlacement => {
      try {
        const bounds = editor.getBounds(chordPlacement.textPosition, 0);
        if (bounds) {
          newPositions[chordPlacement.id] = {
            x: bounds.left,
            y: bounds.top - 30 // 30px au-dessus du texte
          };
        }
      } catch (error) {
        console.warn('Erreur calcul position accord:', error);
      }
    });

    setOverlayPositions(newPositions);
  }, [chords, editorRef, displayMode]);

  if (displayMode === 'text-only') return null;

  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      {chords.map(chordPlacement => {
        const position = overlayPositions[chordPlacement.id];
        if (!position) return null;

        return (
          <div
            key={chordPlacement.id}
            className="absolute pointer-events-auto"
            style={{
              left: position.x,
              top: position.y,
              transform: 'translateX(-50%)'
            }}
          >
            <div className="group relative">
              {/* Badge d'accord */}
              <div 
                className={`
                  px-2 py-1 rounded-md text-xs font-bold cursor-pointer transition-all
                  ${chordPlacement.metadata?.emphasis === 'strong' 
                    ? 'bg-blue-600 text-white' 
                    : chordPlacement.metadata?.emphasis === 'medium'
                    ? 'bg-blue-100 text-blue-800 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-300'
                  }
                  hover:scale-110 hover:shadow-md
                `}
                onClick={() => onChordClick(chordPlacement)}
              >
                {chordPlacement.chord.chord}
              </div>

              {/* Contrôles au hover */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex items-center space-x-1 bg-white border border-gray-300 rounded-md shadow-lg p-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Jouer l'accord
                    }}
                    className="p-1 text-gray-600 hover:text-blue-600 rounded"
                    title="Jouer l'accord"
                  >
                    <Volume2 className="w-3 h-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onChordEdit(chordPlacement);
                    }}
                    className="p-1 text-gray-600 hover:text-green-600 rounded"
                    title="Éditer l'accord"
                  >
                    <Edit3 className="w-3 h-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onChordRemove(chordPlacement.id);
                    }}
                    className="p-1 text-gray-600 hover:text-red-600 rounded"
                    title="Supprimer l'accord"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>

              {/* Preview du diagramme au hover */}
              <div className="absolute top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity z-20">
                <div className="bg-white border border-gray-300 rounded-lg shadow-xl p-2">
                  <ChordDiagramViewer
                    chord={chordPlacement.chord}
                    size="small"
                    interactive={false}
                    showLabels={false}
                    showDetails={false}
                  />
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

/**
 * Barre d'outils pour l'éditeur amélioré
 */
const EnhancedToolbar: React.FC<{
  displayMode: DisplayMode;
  onDisplayModeChange: (mode: DisplayMode) => void;
  onAddChord: () => void;
  onRequestSuggestions: () => void;
  onSave: () => void;
  chordsCount: number;
  hasUnsavedChanges: boolean;
}> = ({ 
  displayMode, 
  onDisplayModeChange, 
  onAddChord, 
  onRequestSuggestions, 
  onSave,
  chordsCount,
  hasUnsavedChanges
}) => {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
      {/* Modes d'affichage */}
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium text-gray-700">Affichage:</span>
        <div className="flex items-center border border-gray-300 rounded-lg">
          <button
            onClick={() => onDisplayModeChange('text-only')}
            className={`px-3 py-1 text-sm flex items-center ${
              displayMode === 'text-only' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Texte seul"
          >
            <Type className="w-4 h-4 mr-1" />
            Texte
          </button>
          <button
            onClick={() => onDisplayModeChange('hybrid')}
            className={`px-3 py-1 text-sm flex items-center ${
              displayMode === 'hybrid' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Texte + Accords"
          >
            <Grid3X3 className="w-4 h-4 mr-1" />
            Hybride
          </button>
          <button
            onClick={() => onDisplayModeChange('chords-only')}
            className={`px-3 py-1 text-sm flex items-center ${
              displayMode === 'chords-only' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Accords seuls"
          >
            <Music className="w-4 h-4 mr-1" />
            Accords
          </button>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        {/* Compteur d'accords */}
        <span className="text-sm text-gray-600">
          {chordsCount} accord{chordsCount !== 1 ? 's' : ''}
        </span>

        {/* Boutons d'action */}
        <button
          onClick={onAddChord}
          className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus className="w-4 h-4 mr-1" />
          Ajouter
        </button>

        <button
          onClick={onRequestSuggestions}
          className="px-3 py-1 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 flex items-center"
        >
          <Sparkles className="w-4 h-4 mr-1" />
          Suggérer IA
        </button>

        <button
          onClick={onSave}
          disabled={!hasUnsavedChanges}
          className={`px-3 py-1 text-sm rounded-lg flex items-center ${
            hasUnsavedChanges
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <Save className="w-4 h-4 mr-1" />
          Sauver
        </button>
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const EnhancedLyricsEditor: React.FC<EnhancedLyricsEditorProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  quillRef,
  onSelectionChange,
  chords = [],
  onChordsChange,
  displayMode = 'hybrid',
  onDisplayModeChange,
  onRequestChordSuggestions,
  onChordDrop,
  onSave,
  autoSave = false
}) => {
  const { actions } = useChordSystem();
  
  // État local
  const [currentDisplayMode, setCurrentDisplayMode] = useState<DisplayMode>(displayMode);
  const [showChordPicker, setShowChordPicker] = useState(false);
  const [selectedChordPlacement, setSelectedChordPlacement] = useState<ChordPlacement | null>(null);
  const [currentSelection, setCurrentSelection] = useState<any>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isRequestingSuggestions, setIsRequestingSuggestions] = useState(false);

  const internalQuillRef = useRef<any>(null);
  const editorRef = quillRef || internalQuillRef;

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleDisplayModeChange = useCallback((mode: DisplayMode) => {
    setCurrentDisplayMode(mode);
    onDisplayModeChange?.(mode);
  }, [onDisplayModeChange]);

  const handleTextChange = useCallback((newValue: string) => {
    onChange(newValue);
    setHasUnsavedChanges(true);
  }, [onChange]);

  const handleSelectionChange = useCallback((range: any, source: any, editor: any) => {
    setCurrentSelection(range);
    onSelectionChange?.(range, source, editor);
  }, [onSelectionChange]);

  const handleAddChord = useCallback(() => {
    setShowChordPicker(true);
  }, []);

  const handleChordSelect = useCallback((chord: UnifiedChordPosition) => {
    const position = currentSelection?.index || 0;
    
    const newChordPlacement: ChordPlacement = {
      id: crypto.randomUUID(),
      chord,
      textPosition: position,
      lineNumber: 0, // TODO: Calculer la ligne
      wordIndex: 0, // TODO: Calculer l'index du mot
      timestamp: new Date().toISOString(),
      metadata: {
        emphasis: 'medium'
      }
    };

    const updatedChords = [...chords, newChordPlacement];
    onChordsChange(updatedChords);
    setHasUnsavedChanges(true);
    setShowChordPicker(false);
    
    onChordDrop?.(chord, position);
  }, [currentSelection, chords, onChordsChange, onChordDrop]);

  const handleChordClick = useCallback((chordPlacement: ChordPlacement) => {
    setSelectedChordPlacement(chordPlacement);
    // TODO: Positionner le curseur à la position de l'accord
  }, []);

  const handleChordRemove = useCallback((chordId: string) => {
    const updatedChords = chords.filter(c => c.id !== chordId);
    onChordsChange(updatedChords);
    setHasUnsavedChanges(true);
  }, [chords, onChordsChange]);

  const handleChordEdit = useCallback((chordPlacement: ChordPlacement) => {
    setSelectedChordPlacement(chordPlacement);
    setShowChordPicker(true);
  }, []);

  const handleRequestSuggestions = useCallback(async () => {
    if (!onRequestChordSuggestions) return;

    setIsRequestingSuggestions(true);
    try {
      const context = value; // Contenu textuel actuel
      const position = currentSelection?.index || 0;
      
      const suggestions = await onRequestChordSuggestions(context, position);
      
      // TODO: Afficher les suggestions dans une interface dédiée
      console.log('Suggestions IA reçues:', suggestions);
      
    } catch (error) {
      console.error('Erreur suggestions IA:', error);
    } finally {
      setIsRequestingSuggestions(false);
    }
  }, [onRequestChordSuggestions, value, currentSelection]);

  const handleSave = useCallback(() => {
    onSave?.(value, chords);
    setHasUnsavedChanges(false);
  }, [onSave, value, chords]);

  // Auto-save
  useEffect(() => {
    if (autoSave && hasUnsavedChanges) {
      const timer = setTimeout(() => {
        handleSave();
      }, 2000); // Auto-save après 2 secondes

      return () => clearTimeout(timer);
    }
  }, [autoSave, hasUnsavedChanges, handleSave]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className={`enhanced-lyrics-editor ${className}`}>
      {/* Barre d'outils */}
      <EnhancedToolbar
        displayMode={currentDisplayMode}
        onDisplayModeChange={handleDisplayModeChange}
        onAddChord={handleAddChord}
        onRequestSuggestions={handleRequestSuggestions}
        onSave={handleSave}
        chordsCount={chords.length}
        hasUnsavedChanges={hasUnsavedChanges}
      />

      {/* Zone d'édition */}
      <div className="relative">
        {/* Éditeur de texte */}
        {currentDisplayMode !== 'chords-only' && (
          <div className="relative">
            <RichLyricsEditor
              value={value}
              onChange={handleTextChange}
              placeholder={placeholder}
              quillRef={editorRef}
              onSelectionChange={handleSelectionChange}
              className="min-h-[400px]"
            />
            
            {/* Overlay des accords */}
            <ChordOverlay
              chords={chords}
              editorRef={editorRef}
              displayMode={currentDisplayMode}
              onChordClick={handleChordClick}
              onChordRemove={handleChordRemove}
              onChordEdit={handleChordEdit}
            />
          </div>
        )}

        {/* Vue accords seuls */}
        {currentDisplayMode === 'chords-only' && (
          <div className="p-6 min-h-[400px] bg-gray-50 border border-gray-300 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Progression d'Accords ({chords.length})
            </h3>
            
            {chords.length === 0 ? (
              <div className="text-center py-12">
                <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Aucun accord ajouté</p>
                <button
                  onClick={handleAddChord}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Ajouter le premier accord
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {chords.map((chordPlacement, index) => (
                  <div
                    key={chordPlacement.id}
                    className="relative p-3 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer"
                    onClick={() => handleChordClick(chordPlacement)}
                  >
                    {/* Numéro de position */}
                    <div className="absolute -top-2 -left-2 w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                      {index + 1}
                    </div>

                    {/* Diagramme */}
                    <div className="w-full h-16 mb-2">
                      <ChordDiagramViewer
                        chord={chordPlacement.chord}
                        size="small"
                        interactive={false}
                        showLabels={false}
                        showDetails={false}
                      />
                    </div>

                    {/* Nom */}
                    <h4 className="text-sm font-semibold text-center">
                      {chordPlacement.chord.chord}
                    </h4>

                    {/* Contrôles */}
                    <div className="flex justify-center mt-2 space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleChordEdit(chordPlacement);
                        }}
                        className="p-1 text-gray-600 hover:text-blue-600 rounded"
                      >
                        <Edit3 className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleChordRemove(chordPlacement.id);
                        }}
                        className="p-1 text-gray-600 hover:text-red-600 rounded"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modal de sélection d'accords */}
      <ChordPickerModal
        isOpen={showChordPicker}
        onClose={() => {
          setShowChordPicker(false);
          setSelectedChordPlacement(null);
        }}
        onChordSelect={handleChordSelect}
        title={selectedChordPlacement ? "Remplacer l'accord" : "Ajouter un accord"}
      />
    </div>
  );
};
