# API Documentation

## Overview

MOUVIK provides a comprehensive API for interacting with the platform's features. This document outlines the available endpoints, request/response formats, and authentication requirements.

## Authentication

All API requests require authentication using a JWT token. The token should be included in the `Authorization` header using the Bearer scheme.

\`\`\`
Authorization: Bearer <your_jwt_token>
\`\`\`

### Obtaining a Token

Tokens are obtained through the authentication flow:

\`\`\`typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

const token = data.session.access_token
\`\`\`

## API Endpoints

### Songs

#### List Songs

\`\`\`
GET /api/songs
\`\`\`

Query parameters:
- `limit`: Maximum number of songs to return (default: 10)
- `offset`: Number of songs to skip (default: 0)
- `status`: Filter by status ('published', 'draft', 'archived')
- `user_id`: Filter by user ID
- `genre`: Filter by genre

Response:
\`\`\`json
{
  "songs": [
    {
      "id": "uuid",
      "title": "Song Title",
      "description": "Song Description",
      "genre": ["pop", "electronic"],
      "moods": ["upbeat", "energetic"],
      "instrumentation": ["synth", "drum machine"],
      "tags": ["80s", "dance"],
      "key": "C",
      "bpm": 120,
      "cover_url": "https://example.com/cover.jpg",
      "audio_url": "https://example.com/audio.mp3",
      "waveform_url": "https://example.com/waveform.json",
      "status": "published",
      "is_explicit": false,
      "duration": 180,
      "user_id": "user-uuid",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "total": 100
}
\`\`\`

#### Get Song

\`\`\`
GET /api/songs/:id
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "title": "Song Title",
  "description": "Song Description",
  "genre": ["pop", "electronic"],
  "moods": ["upbeat", "energetic"],
  "instrumentation": ["synth", "drum machine"],
  "tags": ["80s", "dance"],
  "key": "C",
  "bpm": 120,
  "cover_url": "https://example.com/cover.jpg",
  "audio_url": "https://example.com/audio.mp3",
  "waveform_url": "https://example.com/waveform.json",
  "status": "published",
  "is_explicit": false,
  "duration": 180,
  "user_id": "user-uuid",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}
\`\`\`

#### Create Song

\`\`\`
POST /api/songs
\`\`\`

Request body:
\`\`\`json
{
  "title": "Song Title",
  "description": "Song Description",
  "genre": ["pop", "electronic"],
  "moods": ["upbeat"],
  "instrumentation": ["synth"],
  "tags": ["vocal"],
  "key": "C",
  "bpm": 120,
  "cover_url": "https://example.com/cover.jpg",
  "audio_url": "https://example.com/audio.mp3",
  "waveform_url": "https://example.com/waveform.json",
  "status": "published",
  "is_explicit": false
}
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "title": "Song Title",
  "description": "Song Description",
  "genre": ["pop", "electronic"],
  "moods": ["upbeat"],
  "instrumentation": ["synth"],
  "tags": ["vocal"],
  "key": "C",
  "bpm": 120,
  "cover_url": "https://example.com/cover.jpg",
  "audio_url": "https://example.com/audio.mp3",
  "waveform_url": "https://example.com/waveform.json",
  "status": "published",
  "is_explicit": false,
  "duration": 180,
  "user_id": "user-uuid",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}
\`\`\`

#### Update Song

\`\`\`
PUT /api/songs/:id
\`\`\`

Request body:
\`\`\`json
{
  "title": "Updated Song Title",
  "description": "Updated Song Description",
  "genre": ["rock", "alternative"],
  "moods": ["introspective"],
  "instrumentation": ["guitar", "drums"],
  "tags": ["indie"],
  "key": "D",
  "bpm": 130,
  "cover_url": "https://example.com/new-cover.jpg",
  "audio_url": "https://example.com/new-audio.mp3",
  "waveform_url": "https://example.com/new-waveform.json",
  "status": "published",
  "is_explicit": true
}
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "title": "Updated Song Title",
  "description": "Updated Song Description",
  "genre": ["rock", "alternative"],
  "moods": ["introspective"],
  "instrumentation": ["guitar", "drums"],
  "tags": ["indie"],
  "key": "D",
  "bpm": 130,
  "cover_url": "https://example.com/new-cover.jpg",
  "audio_url": "https://example.com/new-audio.mp3",
  "waveform_url": "https://example.com/new-waveform.json",
  "status": "published",
  "is_explicit": true,
  "duration": 180,
  "user_id": "user-uuid",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-02T00:00:00Z"
}
\`\`\`

#### Delete Song

\`\`\`
DELETE /api/songs/:id
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "Song deleted successfully"
}
\`\`\`

### Albums

#### List Albums

\`\`\`
GET /api/albums
\`\`\`

Query parameters:
- `limit`: Maximum number of albums to return (default: 10)
- `offset`: Number of albums to skip (default: 0)
- `status`: Filter by status ('published', 'draft', 'archived')
- `user_id`: Filter by user ID
- `genre`: Filter by genre

Response:
\`\`\`json
{
  "albums": [
    {
      "id": "uuid",
      "title": "Album Title",
      "description": "Album Description",
      "genre": ["rock", "alternative"],
      "moods": ["epic", "moody"],
      "instrumentation": ["orchestra", "choir"],
      "release_date": "2023-01-01",
      "cover_url": "https://example.com/album-cover.jpg",
      "status": "published",
      "is_explicit": false,
      "user_id": "user-uuid",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "song_count": 10
    }
  ],
  "total": 50
}
\`\`\`

#### Get Album

\`\`\`
GET /api/albums/:id
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "title": "Album Title",
  "description": "Album Description",
  "genre": ["rock", "alternative"],
  "moods": ["epic", "moody"],
  "instrumentation": ["orchestra", "choir"],
  "release_date": "2023-01-01",
  "cover_url": "https://example.com/album-cover.jpg",
  "status": "published",
  "is_explicit": false,
  "user_id": "user-uuid",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "songs": [
    {
      "id": "song-uuid-1",
      "title": "Song 1",
      "duration": 180,
      "track_number": 1
    },
    {
      "id": "song-uuid-2",
      "title": "Song 2",
      "duration": 210,
      "track_number": 2
    }
  ]
}
\`\`\`

#### Create Album

\`\`\`
POST /api/albums
\`\`\`

Request body:
\`\`\`json
{
  "title": "Album Title",
  "description": "Album Description",
  "genre": ["rock", "alternative"],
  "moods": ["epic"],
  "instrumentation": ["orchestra"],
  "release_date": "2023-01-01",
  "cover_url": "https://example.com/album-cover.jpg",
  "status": "published",
  "is_explicit": false,
  "songs": [
    {
      "id": "song-uuid-1",
      "track_number": 1
    },
    {
      "id": "song-uuid-2",
      "track_number": 2
    }
  ]
}
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "title": "Album Title",
  "description": "Album Description",
  "genre": ["rock", "alternative"],
  "moods": ["epic"],
  "instrumentation": ["orchestra"],
  "release_date": "2023-01-01",
  "cover_url": "https://example.com/album-cover.jpg",
  "status": "published",
  "is_explicit": false,
  "user_id": "user-uuid",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}
\`\`\`

#### Update Album

\`\`\`
PUT /api/albums/:id
\`\`\`

Request body:
\`\`\`json
{
  "title": "Updated Album Title",
  "description": "Updated Album Description",
  "genre": ["pop", "electronic"],
  "moods": ["happy", "danceable"],
  "instrumentation": ["synths", "vocals"],
  "release_date": "2023-02-01",
  "cover_url": "https://example.com/new-album-cover.jpg",
  "status": "published",
  "is_explicit": true,
  "songs": [
    {
      "id": "song-uuid-1",
      "track_number": 2
    },
    {
      "id": "song-uuid-2",
      "track_number": 1
    },
    {
      "id": "song-uuid-3",
      "track_number": 3
    }
  ]
}
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "title": "Updated Album Title",
  "description": "Updated Album Description",
  "genre": ["pop", "electronic"],
  "moods": ["happy", "danceable"],
  "instrumentation": ["synths", "vocals"],
  "release_date": "2023-02-01",
  "cover_url": "https://example.com/new-album-cover.jpg",
  "status": "published",
  "is_explicit": true,
  "user_id": "user-uuid",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-02T00:00:00Z"
}
\`\`\`

#### Delete Album

\`\`\`
DELETE /api/albums/:id
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "Album deleted successfully"
}
\`\`\`

### Users

#### Get User Profile

\`\`\`
GET /api/users/:id/profile
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "username": "username",
  "display_name": "Display Name",
  "full_name": "Full Name",
  "avatar_url": "https://example.com/avatar.jpg",
  "header_url": "https://example.com/header.jpg",
  "bio": "User bio",
  "website": "https://example.com",
  "location_city": "City",
  "location_country": "Country",
  "social_links": [{ "platform": "spotify", "url": "https://spotify.com/artist" }],
  "genres": ["electronic", "ambient"],
  "influences": ["aphex twin"],
  "tags": ["livecoding", "generative"],
  "is_artist": true,
  "role_primary": "producer",
  "roles_secondary": ["dj", "sound designer"],
  "instruments_played": [{ "name": "Synthesizer", "experience_years": 5 }],
  "equipment": "Moog One, Ableton Push",
  "operating_systems": ["macos", "linux"],
  "is_profile_public": true,
  "followers_count": 100,
  "following_count": 50,
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
  // Other fields like primary_daw, ai_usage_level etc. can be added
}
\`\`\`

#### Update User Profile

\`\`\`
PUT /api/users/profile
\`\`\`

Request body:
\`\`\`json
{
  "display_name": "New Display Name",
  "full_name": "New Full Name",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "header_url": "https://example.com/new-header.jpg",
  "bio": "New user bio",
  "website": "https://example.com/new",
  "location_city": "New City",
  "location_country": "New Country",
  "social_links": [{ "platform": "youtube", "url": "https://youtube.com/channel" }],
  "genres": ["techno"],
  "influences": ["kraftwerk"],
  "tags": ["modular"],
  "is_artist": true,
  "role_primary": "musician",
  "equipment": "Fender Strat, Logic Pro",
  "operating_systems": ["windows"]
  // Note: username is typically not updatable or handled separately due to uniqueness.
}
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "username": "username", // Assuming username is not changed by this endpoint
  "display_name": "New Display Name",
  "full_name": "New Full Name",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "header_url": "https://example.com/new-header.jpg",
  "bio": "New user bio",
  "website": "https://example.com/new",
  "location_city": "New City",
  "location_country": "New Country",
  "equipment": "Fender Strat, Logic Pro",
  "is_artist": true,
  // Include other updated fields
  "updated_at": "2023-01-02T00:00:00Z"
}
\`\`\`

### Social

#### Follow User

\`\`\`
POST /api/social/follow
\`\`\`

Request body:
\`\`\`json
{
  "following_id": "user-uuid"
}
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "User followed successfully"
}
\`\`\`

#### Unfollow User

\`\`\`
DELETE /api/social/follow/:user_id
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "User unfollowed successfully"
}
\`\`\`

#### Like Content

\`\`\`
POST /api/social/like
\`\`\`

Request body:
\`\`\`json
{
  "resource_id": "content-uuid",
  "resource_type": "song"
}
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "Content liked successfully"
}
\`\`\`

#### Unlike Content

\`\`\`
DELETE /api/social/like/:resource_type/:resource_id
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "Content unliked successfully"
}
\`\`\`

#### Add Comment

\`\`\`
POST /api/social/comment
\`\`\`

Request body:
\`\`\`json
{
  "resource_id": "content-uuid",
  "resource_type": "song",
  "content": "This is a comment",
  "parent_id": null
}
\`\`\`

Response:
\`\`\`json
{
  "id": "uuid",
  "user_id": "user-uuid",
  "resource_id": "content-uuid",
  "resource_type": "song",
  "content": "This is a comment",
  "parent_id": null,
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "user": {
    "username": "username",
    "avatar_url": "https://example.com/avatar.jpg"
  }
}
\`\`\`

#### Get Comments

\`\`\`
GET /api/social/comments/:resource_type/:resource_id
\`\`\`

Query parameters:
- `limit`: Maximum number of comments to return (default: 10)
- `offset`: Number of comments to skip (default: 0)

Response:
\`\`\`json
{
  "comments": [
    {
      "id": "uuid",
      "user_id": "user-uuid",
      "resource_id": "content-uuid",
      "resource_type": "song",
      "content": "This is a comment",
      "parent_id": null,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "user": {
        "username": "username",
        "avatar_url": "https://example.com/avatar.jpg"
      },
      "replies_count": 2
    }
  ],
  "total": 15
}
\`\`\`

### Analytics

#### Record Play

\`\`\`
POST /api/analytics/play
\`\`\`

Request body:
\`\`\`json
{
  "song_id": "song-uuid"
}
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "Play recorded successfully"
}
\`\`\`

#### Record View

\`\`\`
POST /api/analytics/view
\`\`\`

Request body:
\`\`\`json
{
  "resource_id": "content-uuid",
  "resource_type": "album"
}
\`\`\`

Response:
\`\`\`json
{
  "success": true,
  "message": "View recorded successfully"
}
\`\`\`

#### Get Play Count

\`\`\`
GET /api/analytics/plays/:song_id
\`\`\`

Response:
\`\`\`json
{
  "song_id": "song-uuid",
  "plays": 1000
}
\`\`\`

#### Get View Count

\`\`\`
GET /api/analytics/views/:resource_type/:resource_id
\`\`\`

Response:
\`\`\`json
{
  "resource_id": "content-uuid",
  "resource_type": "album",
  "view_count": 500
}
\`\`\`

## Error Handling

All API endpoints return standard error responses in the following format:

\`\`\`json
{
  "error": {
    "code": "error_code",
    "message": "Error message",
    "details": {}
  }
}
\`\`\`

Common error codes:
- `authentication_error`: Authentication failed
- `authorization_error`: User is not authorized to perform the action
- `validation_error`: Request validation failed
- `not_found`: Resource not found
- `server_error`: Internal server error

## Rate Limiting

API requests are rate-limited to prevent abuse. The current limits are:

- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: The maximum number of requests allowed per minute
- `X-RateLimit-Remaining`: The number of requests remaining in the current minute
- `X-RateLimit-Reset`: The time at which the rate limit will reset (Unix timestamp)

When a rate limit is exceeded, a 429 Too Many Requests response is returned.
