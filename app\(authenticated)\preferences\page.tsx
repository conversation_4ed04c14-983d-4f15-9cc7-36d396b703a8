import { createSupabaseServerClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FullProfileForm } from '@/components/full-profile-form';
import { SubscriptionTab } from '@/components/preferences/subscription-tab';
import { LanguageTab } from '@/components/preferences/language-tab';
import { AdvancedSettingsTab } from '@/components/preferences/advanced-settings-tab';
import { ProfileThemeTab } from "@/components/profile/profile-theme-tab";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Construction, Palette, UserCircle, Languages, Settings, Gem } from 'lucide-react';
import type { Profile } from "@/types/index";

// Placeholder for future tabs
const PlaceholderTab = ({ title, icon: Icon }: { title: string, icon: React.ElementType }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-3">
        <Icon className="h-6 w-6 text-muted-foreground" />
        <span>{title}</span>
      </CardTitle>
      <CardDescription>
        Cette section est en cours de construction et sera bientôt disponible.
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="flex items-center justify-center h-48 border-2 border-dashed rounded-lg">
        <p className="text-muted-foreground">Revenez bientôt pour découvrir les nouvelles fonctionnalités !</p>
      </div>
    </CardContent>
  </Card>
);

export default async function PreferencesPage() {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login");
  }

  const { data: profile, error } = await supabase
    .from("profiles")
    .select("*, background_url")
    .eq("id", user.id)
    .single<Profile>();

  if (error || !profile) {
    console.error("Error fetching profile for preferences:", error?.message);
    return (
      <div className="container mx-auto py-10 text-center">
        <h1 className="text-2xl font-bold text-destructive">Erreur</h1>
        <p className="text-muted-foreground">Impossible de charger votre profil. Veuillez réessayer plus tard.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-2">Préférences</h1>
      <p className="text-muted-foreground mb-8">
        Gérez les informations de votre profil, votre abonnement et les paramètres de l'application.
      </p>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5">
          <TabsTrigger value="profile"><UserCircle className="w-4 h-4 mr-2"/>Profil</TabsTrigger>
          <TabsTrigger value="theme"><Palette className="w-4 h-4 mr-2"/>Apparence</TabsTrigger>
          <TabsTrigger value="subscription"><Gem className="w-4 h-4 mr-2"/>Abonnement</TabsTrigger>
          <TabsTrigger value="language"><Languages className="w-4 h-4 mr-2"/>Langue</TabsTrigger>
          <TabsTrigger value="settings"><Settings className="w-4 h-4 mr-2"/>Avancé</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="mt-6">
          <FullProfileForm user={user} />
        </TabsContent>
        
        <TabsContent value="theme" className="mt-6">
          <ProfileThemeTab user={user} profile={profile} />
        </TabsContent>

        <TabsContent value="subscription" className="mt-6">
          <SubscriptionTab />
        </TabsContent>

        <TabsContent value="language" className="mt-6">
          <LanguageTab />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <AdvancedSettingsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}
