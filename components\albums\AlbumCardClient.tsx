"use client";

import Link from "next/link";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Music, Edit3, Copy, Trash2 } from "lucide-react";
import type { Album } from "@/types"; // Ensure this path is correct

interface AlbumCardClientProps {
  album: Album;
}

export function AlbumCardClient({ album }: AlbumCardClientProps) {
  const handleDuplicate = () => {
    // Placeholder for actual duplicate logic
    alert(`Duplication de l'album : ${album.title} (Bientôt disponible)`);
  };

  const handleDelete = () => {
    // Placeholder for actual delete logic
    alert(`Suppression de l'album : ${album.title} (Bientôt disponible)`);
  };

  return (
    <Card key={album.id} className="group relative overflow-hidden hover:shadow-lg transition-shadow">
      {/* Action buttons - appear on hover */}
      <div className="absolute top-2 right-2 z-10 hidden group-hover:flex items-center space-x-1 bg-background/70 backdrop-blur-sm p-1 rounded-lg">
        <Link href={`/albums/${album.id}/edit`}>
          <Button variant="ghost" size="icon" title="Modifier l'album">
            <Edit3 className="h-4 w-4" />
          </Button>
        </Link>
        <Button variant="ghost" size="icon" title="Dupliquer l'album" onClick={handleDuplicate}>
          <Copy className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" title="Supprimer l'album" onClick={handleDelete}>
          <Trash2 className="h-4 w-4 text-destructive" />
        </Button>
      </div>

      <Link href={album.slug ? `/album/${album.slug}` : '#'} onClick={(e) => !album.slug && e.preventDefault()} aria-disabled={!album.slug} title={!album.slug ? "Cet album n'a pas encore de page publique" : album.title}>
        <div className="h-40 bg-muted flex items-center justify-center">
          {album.cover_url ? (
            <img src={album.cover_url} alt={album.title} className="w-full h-full object-cover" />
          ) : (
            <Music className="w-16 h-16 text-muted-foreground" />
          )}
        </div>
        <CardHeader>
          <CardTitle>{album.title}</CardTitle>
          <CardDescription className="line-clamp-2">
            {album.description || 'Aucune description'}
          </CardDescription>
        </CardHeader>
      </Link>
    </Card>
  );
}
