"use client"; 

import { useState, useEffect, useMemo } from 'react'; 
import { createBrowserClient } from '@/lib/supabase/client'; 
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ListMusic, UserCircle, LayoutGrid, List, Clock, Disc, Share2, Edit3, ZoomIn, ZoomOut } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { PlayButton } from '@/components/audio/play-button'; 
import { Label } from "@/components/ui/label"; 
import { Input } from "@/components/ui/input"; 
import { formatDuration } from '@/lib/utils'; 

import { useUser } from '@/contexts/user-context'; 
import { LikeButton } from '@/components/social/like-button';
import { DislikeButton } from '@/components/social/dislike-button';
import { ResourceViewTracker } from '@/components/stats/resource-view-tracker'; 
import { PlayPlaylistButton } from '@/components/playlists/play-playlist-button'; 
import type { Song } from '@/components/songs/song-schema';
import type { Album as AlbumType } from '@/types'; 
import { SimilarAlbums } from '@/components/albums/similar-albums'; 
import { ArtistCardDisplay } from '@/components/artists/artist-card-display'; 
import { CommentSection } from '@/components/comments/comment-section'; 
import { SharePopover } from '@/components/shared/share-popover';
import { ResourceHeaderBanner } from '@/components/shared/resource-header-banner';
import { ResourceStatsDisplay } from '@/components/shared/ResourceStatsDisplay'; // Added ResourceStatsDisplay
import { AudioSliderPlayer } from '@/components/audio-slider-player'; // Added AudioSliderPlayer
import { useAudio } from '@/contexts/audio-context'; // Added useAudio
import { usePlaySong } from '@/hooks/use-play-song'; // Added usePlaySong
import { SongCard } from '@/components/songs/song-card'; // Import SongCard
import type { SongForCard } from '@/components/songs/song-card';

import { AddAlbumToQueueButton } from '@/components/albums/add-album-to-queue-button';
import { toast } from 'sonner';

interface PublicAlbumData extends Omit<AlbumType, 'genre' | 'instrumentation'> {
  genre?: string[] | null; 
  instrumentation?: string[] | null; 
  profiles: {
    id: string; 
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
    bio?: string | null;
    website?: string | null;
    location_city?: string | null;
    location_country?: string | null;
  } | null;
  songs: Array<Song & { 
    track_number: number | null; 
    artist_name?: string; 
  }>;
  album_type?: string | null;
  slug?: string | null; 
  like_count?: number;
  dislike_count?: number;
  view_count?: number;
  are_comments_public?: boolean; 
  gallery_image_urls?: string[] | null; 
  is_gallery_public?: boolean; 
  plays?: number; // Added for consistency, though might not be fetched yet
  tags?: string[] | null;
  is_liked_by_user?: boolean; // Added for LikeButton
}

interface AlbumDetailsFromDB {
  id: string;
  title: string;
  description?: string | null;
  cover_url?: string | null;
  release_date?: string | null;
  user_id: string;
  genre?: string[] | null; 
  moods?: string[] | null;
  instrumentation?: string[] | null; 
  album_type?: string | null;
  like_count?: number;
  dislike_count?: number;
  view_count?: number;
  status?: string;
  is_public: boolean;
  slug: string | null;
  profiles: {
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  } | null;
  gallery_image_urls?: string[] | null; 
  is_gallery_public?: boolean;
  are_comments_public?: boolean;
}

type SongDetailFromDB = {
  id: string;
  title: string;
  duration_ms?: number | null;
  audio_url?: string | null; 
  cover_art_url?: string | null;
  creator_user_id?: string | null;
  genre?: string[] | null;
  subgenre?: string[] | null;
  moods?: string[] | null;
  instrumentation?: string[] | null;
  tags?: string[] | null;
  is_public?: boolean | null; // Added for song status
};

async function getAlbumBySlug(slug: string): Promise<PublicAlbumData | null> {
  const supabase = createBrowserClient(); 

  const { data: albumDetails, error: albumError } = await supabase
    .from('albums')
    .select(`
      id, title, description, cover_url, release_date, user_id, genre, moods, album_type,
      dislike_count, status, is_public, slug, are_comments_public, gallery_image_urls, is_gallery_public,
      profiles:user_id (id, username, display_name, avatar_url, bio, website, location_city, location_country)
    `) 
    .eq('slug', slug)
    .eq('is_public', true) 
    .single();

  if (albumError || !albumDetails) {
    console.error('Error fetching public album details by slug:', slug, albumError);
    return null;
  }

  // Transform album cover_url if it's relative and not null
  if (albumDetails.cover_url && typeof albumDetails.cover_url === 'string' && !albumDetails.cover_url.startsWith('http')) {
    const { data: publicUrlData } = supabase.storage.from('covers').getPublicUrl(albumDetails.cover_url); // Use 'covers' bucket
    albumDetails.cover_url = publicUrlData?.publicUrl || albumDetails.cover_url;
  }

  let likeCount = 0;
  let viewCount = 0;

  const { data: likeCountData } = await supabase
    .rpc('get_like_count', { resource_id_param: albumDetails.id, resource_type_param: 'album' });
  if (typeof likeCountData === 'number') likeCount = likeCountData;

  const { data: viewCountData } = await supabase
    .rpc('get_view_count', { resource_id_param: albumDetails.id, resource_type_param: 'album' });
  if (typeof viewCountData === 'number') viewCount = viewCountData;

  const { data: albumSongsEntries, error: albumSongsError } = await supabase
    .from('album_songs')
    .select('song_id, track_number')
    .eq('album_id', albumDetails.id)
    .order('track_number', { ascending: true });

  if (albumSongsError) {
    console.error('Error fetching album_songs for album:', albumDetails.id, albumSongsError);
    return { 
      ...(albumDetails as unknown as PublicAlbumData), 
      songs: [], 
      like_count: likeCount, 
      view_count: viewCount, 
      dislike_count: albumDetails.dislike_count || 0,
      slug: albumDetails.slug,
    };
  }

  let songsData: PublicAlbumData['songs'] = [];
  if (albumSongsEntries && albumSongsEntries.length > 0) {
    const songIds = albumSongsEntries.map((as: { song_id: string }) => as.song_id);
    
    const { data: songDetailsList, error: songsError } = await supabase
      .from('songs')
      .select('id, title, duration_ms, audio_url, cover_art_url, creator_user_id, genre, subgenre, moods, instrumentation, tags, is_public') // Added is_public for songs     .in('id', songIds);

    if (songsError) {
      console.error('Error fetching song details for album:', albumDetails.id, songsError);
    } else if (songDetailsList) {
      const typedSongDetailsList = songDetailsList as Array<SongDetailFromDB & { instrumentation?: string[]}>;
      
      // Transform song cover_urls
      typedSongDetailsList.forEach(songDetail => {
        if (songDetail.cover_art_url && typeof songDetail.cover_art_url === 'string' && !songDetail.cover_art_url.startsWith('http')) { // Use DB field name
          const { data: songPublicUrlData } = supabase.storage.from('covers').getPublicUrl(songDetail.cover_art_url); // Use 'covers' bucket
          songDetail.cover_art_url = songPublicUrlData?.publicUrl || songDetail.cover_art_url; // Update DB field after getting public URL
        }
      });

      const songDetailsMap = new Map(
        typedSongDetailsList.map((s) => [s.id, s])
      );
      songsData = albumSongsEntries.map(
        (as: { song_id: string; track_number: number | null }): PublicAlbumData['songs'][0] => {
          const detail = songDetailsMap.get(as.song_id);
          const albumArtistName = (albumDetails.profiles?.display_name || albumDetails.profiles?.username) || '';
          
          return {
            id: as.song_id,
            title: detail?.title || 'Titre inconnu',
            duration_ms: detail?.duration_ms || 0,
            audio_url: detail?.audio_url || null,
            track_number: as.track_number,
            cover_url: detail?.cover_art_url || albumDetails?.cover_url || null,
            creator_user_id: detail?.creator_user_id || albumDetails.user_id,
            artist_name: albumArtistName,
            is_public: detail?.is_public ?? true, // Map song's is_public, default to true if null/undefined for safety on public page
            slug: null, // Assuming 'slug' is not in 'songs' table or not needed here
            genres: [...(detail?.genre || []), ...(detail?.subgenre || [])], // Combine 'genre' and 'subgenre' from DB
            moods: detail?.moods || [],
            instrumentation: detail?.instrumentation || [], // Directly from DB 'instrumentation' column
            tags: detail?.tags || [], // From DB 'tags' column
            lyrics: null, 
            bpm: null,
            key: null,
            ai_content_origin: null,
            is_explicit: false,
            // 'status' field in Song type: if it means public/private, use is_public. If it's something else, adjust as needed.
            // For now, assuming Song type's 'status' might be different from simple 'is_public'
            status: detail?.is_public ? 'published' : 'private', // Example mapping to a status string
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            album_id: albumDetails.id,
            like_count: 0,
            view_count: 0,
            comment_count:0,
          } as Song & { track_number: number | null; artist_name?: string; album_id?: string };

        }
      );
    }
  }
  
  const total_duration_ms = songsData.reduce((sum, song) => sum + (song.duration_ms || 0), 0);

  const finalAlbumData: PublicAlbumData = {
    id: albumDetails.id,
    title: albumDetails.title,
    description: albumDetails.description || null,
    cover_url: albumDetails.cover_url || null,
    release_date: albumDetails.release_date || null,
    user_id: albumDetails.user_id,
    profiles: albumDetails.profiles || null,
    songs: songsData,
    genre: albumDetails.genre || [],
    moods: albumDetails.moods || [],
    instrumentation: albumDetails.instrumentation || [],
    album_type: albumDetails.album_type || null,
    like_count: likeCount,
    dislike_count: albumDetails.dislike_count || 0,
    view_count: viewCount,
    slug: albumDetails.slug,
    gallery_image_urls: albumDetails.gallery_image_urls || [],
    total_duration_ms,
  };

  return finalAlbumData;
}

export default function PublicAlbumPage({ params }: { params: { slug: string } }) {
  const [album, setAlbum] = useState<PublicAlbumData | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [searchQuery, setSearchQuery] = useState('');
  const [xlCols, setXlCols] = useState(4); // Default 4 columns for XL screens
  const { user } = useUser();

  const filteredSongs = useMemo(() => {
    if (!album) return [];
    if (!searchQuery) return album.songs;
    const lowerCaseQuery = searchQuery.toLowerCase();
    return album.songs.filter(song =>
      song.title.toLowerCase().includes(lowerCaseQuery) ||
      (song.artist_name && song.artist_name.toLowerCase().includes(lowerCaseQuery)) ||
      (album.profiles && album.profiles.display_name && album.profiles.display_name.toLowerCase().includes(lowerCaseQuery)) ||
      (album.profiles && album.profiles.username && album.profiles.username.toLowerCase().includes(lowerCaseQuery))
    );
  }, [album, searchQuery]);

  useEffect(() => {
    const fetchAlbum = async () => {
      try {
        setLoading(true);
        const albumData = await getAlbumBySlug(params.slug);
        if (!albumData) {
          notFound();
          return;
        }
        setAlbum(albumData);
      } catch (error) {
        console.error('Failed to fetch album:', error);
        notFound();
      } finally {
        setLoading(false);
      }
    };

    fetchAlbum();
  }, [params.slug, user]);

  const songsForPlayer = useMemo(() => {
    if (!album) return [];
    return album.songs.map(song => ({ ...song, artist: song.profiles?.display_name || 'Artiste inconnu' }));
  }, [album]);

  const totalDurationFormatted = useMemo(() => {
    if (!album || !album.total_duration_ms) return null;
    return formatDuration(album.total_duration_ms);
  }, [album]);
  
  const { playCollection } = usePlaySong();

  const handleSongDelete = (songId: string) => {
    toast.info("La suppression n'est pas disponible sur cette page.");
  };

  const handleSongUpdateStatus = () => {};
  const handleSongToggleVisibility = () => {};

  if (loading) {
    return <div className="flex items-center justify-center h-screen"><div className="w-16 h-16 border-4 border-dashed rounded-full animate-spin border-primary"></div></div>;
  }

  if (!album) {
    return notFound();
  }

  const { is_liked_by_user: isLiked, like_count: initialLikeCount } = album;

  return (
    <div className="container mx-auto px-4 py-8">
      <ResourceViewTracker resourceId={album.id} resourceType="album" />

      <ResourceHeaderBanner
        mainImageUrl={album.cover_url ?? undefined}
        defaultIcon={<Disc className="w-20 h-20 text-muted-foreground" />}
        resourceTypeLabel={album.album_type || 'Album'}
        title={album.title}
        description={album.profiles ? `Par ${album.profiles.display_name || album.profiles.username}` : 'Artiste inconnu'}
        details={
          <div className="flex items-center flex-wrap gap-x-2 text-sm text-muted-foreground">
            <span>{album.album_type || 'Album'}</span>
            {album.release_date && (
              <span>• Sorti le {format(new Date(album.release_date), 'd MMM yyyy', { locale: fr })}</span>
            )}
            <span>• {album.songs.length} morceau{album.songs.length === 1 ? '' : 'x'}</span>
            {totalDurationFormatted && (
              <span className="flex items-center gap-1">
                <Clock className="h-3.5 w-3.5" />
                {totalDurationFormatted}
              </span>
            )}
          </div>
        }
        genreBadges={album.genre && album.genre.length > 0 ? (
          <div className="flex flex-wrap gap-2 mt-2">
            {album.genre.map((g) => (
              <Badge key={g} variant="secondary" className="cursor-pointer hover:bg-primary/20" onClick={() => router.push(`/genres/${g.toLowerCase()}`)}>
                {g}
              </Badge>
            ))}
          </div>
        ) : null}
        actionButtons={
          <div className="flex items-center gap-2">
            <PlayPlaylistButton playlistId={album.id} songs={songsForPlayer} playlistName={album.title} />
            <AddAlbumToQueueButton songs={album.songs} albumTitle={album.title} />
            <LikeButton resourceId={album.id} resourceType="album" initialLikes={initialLikeCount} initialIsLiked={isLiked} />
          </div>
        }
        stats={
          <ResourceStatsDisplay
            resourceId={album.id}
            resourceType="album"
            likeCount={album.like_count}
            dislikeCount={album.dislike_count}
            viewCount={album.view_count}
            // playCount={album.plays}
          />
        }
      />


       
      <div className="lg:grid lg:grid-cols-3 lg:gap-x-8 px-4 sm:px-6 lg:px-8">
        <div className="lg:col-span-3">
          <section className="mb-12">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold flex items-center">
                <ListMusic className="mr-3 h-7 w-7 text-primary" />
                Morceaux ({filteredSongs.length})
              </h2>
              <div className="flex items-center gap-2">
                {viewMode === 'grid' && (
                  <>
                    <Button variant="ghost" size="icon" onClick={() => setXlCols(prev => Math.max(2, prev - 1))} title="Moins de colonnes" disabled={xlCols <= 2}>
                      <ZoomOut className="h-5 w-5" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => setXlCols(prev => Math.min(5, prev + 1))} title="Plus de colonnes" disabled={xlCols >= 5}>
                      <ZoomIn className="h-5 w-5" />
                    </Button>
                  </>
                )}
                <Button variant={viewMode === 'grid' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('grid')} title="Vue grille">
                  <LayoutGrid className="h-5 w-5" />
                </Button>
                <Button variant={viewMode === 'list' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('list')} title="Vue liste">
                  <List className="h-5 w-5" />
                </Button>
              </div>
            </div>
            {/* Search Input */}
            <div className="my-4">
              <Input
                type="text"
                placeholder={`Rechercher parmi ${album.songs.length} morceau${album.songs.length === 1 ? '' : 'x'}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-full sm:max-w-md"
              />
            </div>
            {filteredSongs.length > 0 ? (
              <div className={cn(
                viewMode === 'grid'
                  ? `grid grid-cols-1 sm:grid-cols-2 md:grid-cols-${Math.max(2, xlCols - 1)} lg:grid-cols-${xlCols} xl:grid-cols-${xlCols} 2xl:grid-cols-${xlCols} gap-4`
                  : 'flex flex-col gap-1'
              )}>
                {filteredSongs.map((song) => {
              // Adapt the song object to the SongForCard interface
              const songForCard: SongForCard = {
                ...song,
                profiles: album.profiles, // Pass album artist profile to each song card
                albums: { // Pass partial album info for context (e.g., fallback cover)
                  id: album.id,
                  title: album.title,
                  cover_url: album.cover_url,
                },
              };

              return (
                <SongCard
                  key={song.id}
                  song={songForCard}
                  viewMode={viewMode}
                  onDelete={handleSongDelete} // These are no-op on public page
                  onUpdateStatus={handleSongUpdateStatus}
                  onToggleVisibility={handleSongToggleVisibility}
                  isVisibilityUpdating={false} // No live updates on this page
                  size="md" // size is now mainly for grid view
                />
              );
            })}
              </div>
            ) : (
              <p className="text-muted-foreground">
                {searchQuery ? `Aucun morceau ne correspond à votre recherche "${searchQuery}".` : "Cet album ne contient aucun morceau pour le moment."}
              </p>
            )}
          </section>

          {album.profiles && <ArtistCardDisplay artist={{...album.profiles, id: album.user_id }} />}

          <SimilarAlbums 
            currentAlbumId={album.id} 
            currentAlbumGenres={album.genre} 
            currentAlbumArtistId={album.user_id} 
          />
        </div>
      </div>
    </div>
  );
}
