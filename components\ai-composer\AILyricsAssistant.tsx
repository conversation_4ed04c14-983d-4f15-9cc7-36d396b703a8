'use client';

import React, { useState, useRef, forwardRef, useImperativeHandle, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Wand2, Brain, Lightbulb, RefreshCcw, Languages, AlignLeft, 
  Type, Palette, Settings, History, ChevronDown, ChevronUp,
  Sparkles, Target, BarChart3, Zap, Music, FileText, Copy,
  Download, Upload, Eye, Edit, Save, Trash2, RotateCcw, Play, Pause, Volume2, Mic, BookOpen, PenTool, Clock, TrendingUp, Award, Star, Loader2, X
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Slider } from '@/components/ui/slider';

interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: Array<{
    position: number;
    chord: string;
    instrument: string;
  }>;
  aiSuggestions?: string[];
}

interface AILyricsAssistantProps {
  content: string;
  onContentChange: (content: string) => void;
  selectedSection: string;
  sections: LyricsSection[];
  onAIGenerate: (prompt: string, type: 'lyrics' | 'chords' | 'structure') => Promise<void>;
  generalPrompt?: string;
  onEditGeneralPrompt?: (newPrompt: string) => void;
}

interface AIAction {
  id: string;
  type: 'suggestion' | 'correction' | 'translation' | 'rhyme' | 'structure';
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  prompt: string;
}

const AI_ACTIONS: AIAction[] = [
  {
    id: 'suggest-lyrics',
    type: 'suggestion',
    title: 'Suggérer des paroles',
    description: 'Générer des paroles pour cette section',
    icon: Lightbulb,
    prompt: 'Écris des paroles pour un {sectionType} dans le style {genre} avec le thème {theme}'
  },
  {
    id: 'improve-lyrics',
    type: 'correction',
    title: 'Améliorer les paroles',
    description: 'Améliorer le style et la fluidité',
    icon: RefreshCcw,
    prompt: 'Améliore ces paroles en gardant le sens mais en améliorant le style et la fluidité'
  },
  {
    id: 'find-rhymes',
    type: 'rhyme',
    title: 'Trouver des rimes',
    description: 'Suggérer des mots qui riment',
    icon: Target,
    prompt: 'Trouve des mots qui riment avec "{word}" et qui s\'intègrent dans le contexte musical'
  },
  {
    id: 'translate-lyrics',
    type: 'translation',
    title: 'Traduire',
    description: 'Traduire vers une autre langue',
    icon: Languages,
    prompt: 'Traduis ces paroles en {language} en gardant le rythme et les rimes'
  },
  {
    id: 'analyze-structure',
    type: 'structure',
    title: 'Analyser la structure',
    description: 'Analyser la structure rythmique',
    icon: BarChart3,
    prompt: 'Analyse la structure rythmique et métrique de ces paroles'
  }
];

const WRITING_STYLES = [
  { value: 'pop', label: 'Pop' },
  { value: 'rock', label: 'Rock' },
  { value: 'folk', label: 'Folk' },
  { value: 'rap', label: 'Rap/Hip-Hop' },
  { value: 'country', label: 'Country' },
  { value: 'jazz', label: 'Jazz' },
  { value: 'blues', label: 'Blues' },
  { value: 'reggae', label: 'Reggae' },
  { value: 'electronic', label: 'Électronique' },
  { value: 'classical', label: 'Classique' }
];

const THEMES = [
  { value: 'love', label: 'Amour', icon: '💕' },
  { value: 'freedom', label: 'Liberté', icon: '🕊️' },
  { value: 'nostalgia', label: 'Nostalgie', icon: '🌅' },
  { value: 'hope', label: 'Espoir', icon: '🌟' },
  { value: 'adventure', label: 'Aventure', icon: '🗺️' },
  { value: 'melancholy', label: 'Mélancolie', icon: '🌧️' },
  { value: 'celebration', label: 'Célébration', icon: '🎉' },
  { value: 'rebellion', label: 'Rébellion', icon: '⚡' },
  { value: 'nature', label: 'Nature', icon: '🌿' },
  { value: 'urban', label: 'Urbain', icon: '🏙️' },
  { value: 'spiritual', label: 'Spirituel', icon: '🙏' },
  { value: 'social', label: 'Social', icon: '🤝' }
];

// Nouvelles constantes pour les fonctionnalités avancées
const ADVANCED_AI_TOOLS = [
  {
    id: 'rhyme-analyzer',
    title: 'Analyseur de Rimes',
    description: 'Analyse et améliore les schémas de rimes',
    icon: Target,
    category: 'analysis'
  },
  {
    id: 'syllable-counter',
    title: 'Compteur de Syllabes',
    description: 'Optimise le rythme et la métrique',
    icon: BarChart3,
    category: 'analysis'
  },
  {
    id: 'emotion-enhancer',
    title: 'Amplificateur d\'Émotion',
    description: 'Renforce l\'impact émotionnel',
    icon: Sparkles,
    category: 'enhancement'
  },
  {
    id: 'vocabulary-expander',
    title: 'Expanseur de Vocabulaire',
    description: 'Enrichit le vocabulaire utilisé',
    icon: BookOpen,
    category: 'enhancement'
  },
  {
    id: 'flow-optimizer',
    title: 'Optimiseur de Flow',
    description: 'Améliore la fluidité du texte',
    icon: TrendingUp,
    category: 'optimization'
  },
  {
    id: 'hook-generator',
    title: 'Générateur de Hooks',
    description: 'Crée des accroches mémorables',
    icon: Star,
    category: 'generation'
  }
];

const WRITING_MODES = [
  { value: 'creative', label: 'Créatif', description: 'Mode libre et artistique' },
  { value: 'commercial', label: 'Commercial', description: 'Orienté succès commercial' },
  { value: 'experimental', label: 'Expérimental', description: 'Approche innovante' },
  { value: 'classic', label: 'Classique', description: 'Style traditionnel' }
];

const LANGUAGE_STYLES = [
  { value: 'simple', label: 'Simple', description: 'Langage accessible' },
  { value: 'poetic', label: 'Poétique', description: 'Style littéraire' },
  { value: 'modern', label: 'Moderne', description: 'Expressions contemporaines' },
  { value: 'vintage', label: 'Vintage', description: 'Style rétro' }
];

interface AIAnalysisResult {
  score: number;
  suggestions: string[];
  metrics: {
    rhymeScheme: string;
    syllableCount: number;
    emotionalIntensity: number;
    vocabularyRichness: number;
  };
}

interface LyricsTemplate {
  name: string;
  description: string;
  structure: string[];
  example: string;
}

export const AILyricsAssistant = forwardRef<any, AILyricsAssistantProps>((
  { content, onContentChange, selectedSection, sections, onAIGenerate },
  ref
) => {
  // États locaux
  const [isAILoading, setIsAILoading] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('pop');
  const [selectedTheme, setSelectedTheme] = useState('love');
  const [selectedLanguage, setSelectedLanguage] = useState('fr');
  const [rhymeWord, setRhymeWord] = useState('');
  const [aiHistory, setAiHistory] = useState<Array<{ prompt: string; response: string; timestamp: Date }>>([]);
  const [showAIHistory, setShowAIHistory] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(true);
  
  // Nouveaux états pour les fonctionnalités avancées
  const [showAdvancedTools, setShowAdvancedTools] = useState(false);
  const [showAnalysisDialog, setShowAnalysisDialog] = useState(false);
  const [showTemplatesDialog, setShowTemplatesDialog] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(null);
  const [selectedWritingMode, setSelectedWritingMode] = useState('creative');
  const [selectedLanguageStyle, setSelectedLanguageStyle] = useState('modern');
  const [creativityLevel, setCreativityLevel] = useState([70]);
  const [emotionalIntensity, setEmotionalIntensity] = useState([60]);
  const [vocabularyComplexity, setVocabularyComplexity] = useState([50]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  // Références
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  useImperativeHandle(ref, () => ({
    focus: () => textareaRef.current?.focus(),
    insertText: (text: string) => {
      if (textareaRef.current) {
        const start = textareaRef.current.selectionStart;
        const end = textareaRef.current.selectionEnd;
        const newContent = content.substring(0, start) + text + content.substring(end);
        onContentChange(newContent);
        
        // Repositionner le curseur
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.selectionStart = start + text.length;
            textareaRef.current.selectionEnd = start + text.length;
            textareaRef.current.focus();
          }
        }, 0);
      }
    }
  }));
  
  // Gestionnaires d'événements
  const handleAIAction = async (action: AIAction) => {
    setIsAILoading(true);
    
    try {
      let prompt = action.prompt;
      const currentSection = sections.find(s => s.id === selectedSection);
      
      // Remplacer les variables dans le prompt
      prompt = prompt
        .replace('{sectionType}', currentSection?.type || 'section')
        .replace('{genre}', selectedStyle)
        .replace('{theme}', selectedTheme)
        .replace('{language}', selectedLanguage)
        .replace('{word}', rhymeWord);
      
      // Ajouter le contexte si nécessaire
      if (content.trim()) {
        prompt += `\n\nContexte actuel:\n${content}`;
      }
      
      await onAIGenerate(prompt, 'lyrics');
      
      // Ajouter à l'historique
      setAiHistory(prev => [{
        prompt: action.title,
        response: 'Réponse générée', // À remplacer par la vraie réponse
        timestamp: new Date()
      }, ...prev]);
      
      toast({
        title: "IA activée",
        description: `${action.title} en cours...`
      });
      
    } catch (error) {
      toast({
        title: "Erreur IA",
        description: "Impossible de générer le contenu",
        variant: "destructive"
      });
    } finally {
      setIsAILoading(false);
    }
  };
  
  const handleCustomPrompt = useCallback(async () => {
    if (!aiPrompt.trim()) return;
    
    setIsAILoading(true);
    try {
      await onAIGenerate?.(aiPrompt, 'lyrics');
      
      // Ajouter à l'historique
      setAiHistory(prev => [{
        prompt: aiPrompt,
        response: 'Contenu généré avec succès',
        timestamp: new Date()
      }, ...prev]);
        
        setAiPrompt('');
        
        toast({
          title: "Contenu généré",
          description: "Le texte a été inséré avec succès"
        });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de générer le contenu",
        variant: "destructive"
      });
    } finally {
      setIsAILoading(false);
    }
  }, [aiPrompt, selectedStyle, selectedTheme, selectedSection, selectedWritingMode, selectedLanguageStyle, creativityLevel, emotionalIntensity, vocabularyComplexity, content, onContentChange, onAIGenerate]);

  // Nouvelles fonctions pour les outils avancés
  const handleAdvancedTool = useCallback(async (toolId: string) => {
    if (!content.trim()) {
      toast({
        title: "Aucun contenu",
        description: "Veuillez d'abord saisir du texte à analyser",
        variant: "destructive"
      });
      return;
    }
    
    setIsAILoading(true);
    try {
      const tool = ADVANCED_AI_TOOLS.find(t => t.id === toolId);
      if (!tool) return;
      
      // Simuler l'appel à l'outil IA
      await onAIGenerate?.(`Utilise l'outil "${tool.title}" pour améliorer ce texte: ${content}`, 'lyrics');
      
      toast({
        title: `${tool.title} appliqué`,
        description: "Le texte a été amélioré avec succès"
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible d'appliquer l'outil",
        variant: "destructive"
      });
    } finally {
      setIsAILoading(false);
    }
  }, [content, selectedStyle, selectedTheme, onContentChange, onAIGenerate]);

  const analyzeContent = useCallback(async () => {
    if (!content.trim()) {
      toast({
        title: "Aucun contenu",
        description: "Veuillez d'abord saisir du texte à analyser",
        variant: "destructive"
      });
      return;
    }
    
    setIsAnalyzing(true);
    try {
      // Simuler une analyse IA
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockAnalysis: AIAnalysisResult = {
        score: Math.floor(Math.random() * 30) + 70, // Score entre 70-100
        suggestions: [
          "Améliorer la transition entre les couplets",
          "Renforcer l'émotion dans le refrain",
          "Varier le vocabulaire utilisé",
          "Optimiser le schéma de rimes"
        ],
        metrics: {
          rhymeScheme: "ABAB",
          syllableCount: content.split('\n').reduce((acc, line) => acc + line.split(' ').length, 0),
          emotionalIntensity: Math.floor(Math.random() * 40) + 60,
          vocabularyRichness: Math.floor(Math.random() * 30) + 70
        }
      };
      
      setAnalysisResult(mockAnalysis);
      setShowAnalysisDialog(true);
      
    } catch (error) {
      toast({
        title: "Erreur d'analyse",
        description: "Impossible d'analyser le contenu",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [content]);
  
  const currentSection = sections.find(s => s.id === selectedSection);
  
  return (
    <div className="h-full flex">
      {/* Éditeur principal */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* En-tête de section */}
        <div className="border-b bg-card p-3 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="capitalize text-sm">
                {currentSection?.type || 'Section'}
              </Badge>
              <h2 className="font-semibold text-lg">{currentSection?.title || 'Section'}</h2>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={analyzeContent}
                disabled={isAnalyzing || !content.trim()}
                className="flex items-center gap-1"
              >
                {isAnalyzing ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Brain className="h-4 w-4" />
                )}
                Analyser
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedTools(!showAdvancedTools)}
                className="flex items-center gap-1"
              >
                <Wand2 className="h-4 w-4" />
                Outils Pro
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTemplatesDialog(true)}
                className="flex items-center gap-1"
              >
                <FileText className="h-4 w-4" />
                Templates
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIPanel(!showAIPanel)}
                className="flex items-center gap-1"
              >
                <Sparkles className="h-4 w-4" />
                {showAIPanel ? 'Masquer IA' : 'Afficher IA'}
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Upload className="h-4 w-4" />
                Importer
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Download className="h-4 w-4" />
                Exporter
              </Button>
            </div>
          </div>
        </div>
        
        {/* Zone d'édition */}
        <div className="flex-1 p-4 min-h-0">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => onContentChange(e.target.value)}
            placeholder={`Écrivez les paroles pour ${currentSection?.title || 'cette section'}...\n\nUtilisez [Accord] pour insérer des accords\nEx: [C] Hello [G] world [Am] how are [F] you`}
            className="h-full resize-none font-mono text-base leading-relaxed ai-composer-textarea border-2 focus:border-primary focus:ring-2 focus:ring-primary/20"
            style={{ minHeight: '500px', height: '100%' }}
          />
        </div>
        
        {/* Barre d'outils */}
        <div className="border-t bg-card p-2 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{content.length} car.</span>
              <Separator orientation="vertical" className="h-3" />
              <span>{content.split('\n').length} lignes</span>
              <Separator orientation="vertical" className="h-3" />
              <span>{content.split(/\s+/).filter(w => w.length > 0).length} mots</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="gap-1">
                <Save className="h-4 w-4" />
                Sauvegarder
              </Button>
              
              <Button variant="outline" size="sm" className="gap-1">
                <Download className="h-4 w-4" />
                Exporter
              </Button>
            </div>
          </div>
        </div>
        
        {/* Outils avancés */}
        {showAdvancedTools && (
          <div className="border-t bg-muted/30 p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-3">
                <Wand2 className="h-4 w-4" />
                <h3 className="font-medium text-sm">Outils Professionnels</h3>
              </div>
              
              <Tabs defaultValue="analysis" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="analysis">Analyse</TabsTrigger>
                  <TabsTrigger value="enhancement">Amélioration</TabsTrigger>
                  <TabsTrigger value="optimization">Optimisation</TabsTrigger>
                  <TabsTrigger value="generation">Génération</TabsTrigger>
                </TabsList>
                
                {['analysis', 'enhancement', 'optimization', 'generation'].map(category => (
                  <TabsContent key={category} value={category} className="mt-3">
                    <div className="grid grid-cols-2 gap-2">
                      {ADVANCED_AI_TOOLS.filter(tool => tool.category === category).map(tool => {
                        const Icon = tool.icon;
                        return (
                          <Button
                            key={tool.id}
                            variant="outline"
                            size="sm"
                            onClick={() => handleAdvancedTool(tool.id)}
                            disabled={isAILoading || !content.trim()}
                            className="h-auto p-3 flex flex-col items-start gap-1"
                          >
                            <div className="flex items-center gap-2 w-full">
                              <Icon className="h-4 w-4" />
                              <span className="text-xs font-medium">{tool.title}</span>
                            </div>
                            <span className="text-xs text-muted-foreground text-left">
                              {tool.description}
                            </span>
                          </Button>
                        );
                      })}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
              
              {/* Configuration avancée */}
              <div className="grid grid-cols-3 gap-4 pt-3 border-t">
                <div className="space-y-2">
                  <Label className="text-xs">Mode d'écriture</Label>
                  <Select value={selectedWritingMode} onValueChange={setSelectedWritingMode}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {WRITING_MODES.map(mode => (
                        <SelectItem key={mode.value} value={mode.value}>
                          {mode.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Style de langage</Label>
                  <Select value={selectedLanguageStyle} onValueChange={setSelectedLanguageStyle}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {LANGUAGE_STYLES.map(style => (
                        <SelectItem key={style.value} value={style.value}>
                          {style.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs">Créativité: {creativityLevel[0]}%</Label>
                  <Slider
                    value={creativityLevel}
                    onValueChange={setCreativityLevel}
                    max={100}
                    step={10}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Panneau Assistant IA */}
      {showAIPanel && (
        <div className="w-72 border-l bg-card/50 max-h-full overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-3 space-y-3">
              {/* En-tête IA */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-primary" />
                  <h3 className="font-medium text-sm">Assistant IA</h3>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAIPanel(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Configuration */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-xs flex items-center gap-2">
                    <Settings className="h-3 w-3" />
                    Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="space-y-1">
                    <Label className="text-xs">Style musical</Label>
                    <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {WRITING_STYLES.map(style => (
                          <SelectItem key={style.value} value={style.value}>
                            {style.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-1">
                    <Label className="text-xs">Thème</Label>
                    <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {THEMES.map(theme => (
                          <SelectItem key={theme.value} value={theme.value}>
                            {theme.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
              
              {/* Actions IA rapides */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Actions rapides
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {AI_ACTIONS.map(action => {
                    const Icon = action.icon;
                    return (
                      <Button
                        key={action.id}
                        variant="outline"
                        size="sm"
                        className="w-full justify-start gap-2 h-auto p-2"
                        onClick={() => handleAIAction(action)}
                        disabled={isAILoading}
                      >
                        <Icon className="h-4 w-4" />
                        <div className="text-left">
                          <div className="text-xs font-medium">{action.title}</div>
                          <div className="text-xs text-muted-foreground">{action.description}</div>
                        </div>
                      </Button>
                    );
                  })}
                </CardContent>
              </Card>
              
              {/* Prompt personnalisé */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Wand2 className="h-4 w-4" />
                    Prompt personnalisé
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Textarea
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    placeholder="Décrivez ce que vous voulez que l'IA génère..."
                    className="h-20 text-sm"
                  />
                  <Button
                    onClick={handleCustomPrompt}
                    disabled={!aiPrompt.trim() || isAILoading}
                    className="w-full gap-2"
                    size="sm"
                  >
                    <Sparkles className="h-4 w-4" />
                    {isAILoading ? 'Génération...' : 'Générer'}
                  </Button>
                </CardContent>
              </Card>
              
              {/* Historique IA */}
              <Collapsible open={showAIHistory} onOpenChange={setShowAIHistory}>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" className="w-full justify-between" size="sm">
                    <span className="flex items-center gap-2">
                      <History className="h-4 w-4" />
                      Historique ({aiHistory.length})
                    </span>
                    {showAIHistory ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-2 mt-2">
                  {aiHistory.length === 0 ? (
                    <p className="text-xs text-muted-foreground text-center py-4">
                      Aucun historique IA
                    </p>
                  ) : (
                    aiHistory.slice(0, 5).map((item, index) => (
                      <Card key={index} className="p-2">
                        <div className="text-xs">
                          <div className="font-medium mb-1">{item.prompt}</div>
                          <div className="text-muted-foreground">
                            {item.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </Card>
                    ))
                  )}
                </CollapsibleContent>
              </Collapsible>
            </div>
          </ScrollArea>
        </div>
      )}
      
      {/* Dialogue d'analyse */}
      <Dialog open={showAnalysisDialog} onOpenChange={setShowAnalysisDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analyse des Paroles
            </DialogTitle>
          </DialogHeader>
          
          {isAnalyzing ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <RefreshCcw className="h-4 w-4 animate-spin" />
                <span>Analyse en cours...</span>
              </div>
              <Progress value={66} className="w-full" />
            </div>
          ) : analysisResult ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Score Global</Label>
                  <div className="bg-muted p-3 rounded-lg">
                    <div className="text-2xl font-bold text-center">{analysisResult.score}/100</div>
                    <Progress value={analysisResult.score} className="w-full mt-2" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Métriques</Label>
                  <div className="bg-muted p-3 rounded-lg space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Rimes:</span>
                      <span className="font-medium">{analysisResult.metrics.rhymeScheme}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Syllabes:</span>
                      <span className="font-medium">{analysisResult.metrics.syllableCount}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Émotion:</span>
                      <span className="font-medium">{analysisResult.metrics.emotionalIntensity}%</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Suggestions d'amélioration</Label>
                <div className="space-y-2">
                  {analysisResult.suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-start gap-2 p-2 bg-blue-50 rounded">
                      <Lightbulb className="h-4 w-4 text-blue-500 mt-0.5" />
                      <span className="text-sm">{suggestion}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : null}
        </DialogContent>
      </Dialog>
      
      {/* Dialogue de templates */}
      <Dialog open={showTemplatesDialog} onOpenChange={setShowTemplatesDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Templates de Paroles
            </DialogTitle>
          </DialogHeader>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
            {/* Templates simulés */}
            {[
              {
                id: 'pop-love',
                title: 'Chanson d\'amour Pop',
                description: 'Structure classique pour une chanson d\'amour moderne',
                content: `[Couplet 1]\nDans tes yeux je vois l'éternité\nUn amour qui ne peut s'effacer\nChaque jour à tes côtés\nEst un rêve réalisé\n\n[Refrain]\nTu es ma lumière dans la nuit\nMon étoile qui me guide\nAvec toi tout prend un sens\nNotre amour est immense\n\n[Couplet 2]\nTes mains dans les miennes\nFont battre mon cœur\nTu es ma reine\nMon plus grand bonheur`
              },
              {
                id: 'rap-motivation',
                title: 'Rap Motivationnel',
                description: 'Paroles inspirantes pour un morceau de rap',
                content: `[Couplet 1]\nJe me lève chaque matin avec la rage de vaincre\nLes obstacles sur ma route ne peuvent m'atteindre\nJ'ai grandi dans la rue, j'ai appris à me battre\nMaintenant c'est mon tour de faire que ça claque\n\n[Refrain]\nOn ne lâche rien, on continue\nMême quand tout s'écroule\nOn se relève, on évolue\nC'est notre histoire qu'on déroule`
              },
              {
                id: 'folk-nature',
                title: 'Folk Nature',
                description: 'Paroles contemplatives sur la nature',
                content: `[Couplet 1]\nSous les branches du vieux chêne\nJe trouve la paix sereine\nLe vent murmure des secrets\nQue seuls les arbres connaissent\n\n[Refrain]\nRetour aux sources\nLoin de la course\nLa nature nous apprend\nÀ vivre simplement`
              }
            ].map(template => (
              <div key={template.id} className="border rounded-lg p-4 hover:bg-muted/50 cursor-pointer"
                   onClick={() => {
                     onContentChange(content + '\n\n' + template.content);
                     setShowTemplatesDialog(false);
                   }}>
                <h3 className="font-medium mb-2">{template.title}</h3>
                <p className="text-sm text-muted-foreground mb-3">{template.description}</p>
                <div className="bg-muted p-2 rounded text-xs font-mono max-h-32 overflow-y-auto">
                  {template.content.split('\n').slice(0, 6).join('\n')}
                  {template.content.split('\n').length > 6 && '\n...'}
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});

export default AILyricsAssistant;