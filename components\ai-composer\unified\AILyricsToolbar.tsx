'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Wand2, Brain, Lightbulb, RefreshCcw, Languages, Target, 
  BarChart3, Sparkles, Zap, History, Settings, Send
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: Array<{
    position: number;
    chord: string;
    instrument: string;
  }>;
  aiSuggestions?: string[];
}

interface AILyricsToolbarProps {
  content: string;
  onContentChange: (content: string) => void;
  selectedSection: string;
  sections: LyricsSection[];
  onAIGenerate: (prompt: string, type: 'lyrics' | 'chords' | 'structure') => Promise<void>;
  viewMode: string;
}

interface AIAction {
  id: string;
  type: 'suggestion' | 'correction' | 'translation' | 'rhyme' | 'structure';
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  prompt: string;
}

const AI_ACTIONS: AIAction[] = [
  {
    id: 'suggest-lyrics',
    type: 'suggestion',
    title: 'Suggérer des paroles',
    description: 'Générer des paroles pour cette section',
    icon: Lightbulb,
    prompt: 'Écris des paroles pour un {sectionType} dans le style {genre} avec le thème {theme}'
  },
  {
    id: 'improve-lyrics',
    type: 'correction',
    title: 'Améliorer les paroles',
    description: 'Améliorer le style et la fluidité',
    icon: RefreshCcw,
    prompt: 'Améliore ces paroles en gardant le sens mais en améliorant le style et la fluidité'
  },
  {
    id: 'find-rhymes',
    type: 'rhyme',
    title: 'Trouver des rimes',
    description: 'Suggérer des mots qui riment',
    icon: Target,
    prompt: 'Trouve des mots qui riment avec "{word}" et qui s\'intègrent dans le contexte musical'
  },
  {
    id: 'translate-lyrics',
    type: 'translation',
    title: 'Traduire',
    description: 'Traduire vers une autre langue',
    icon: Languages,
    prompt: 'Traduis ces paroles en {language} en gardant le rythme et les rimes'
  },
  {
    id: 'analyze-structure',
    type: 'structure',
    title: 'Analyser la structure',
    description: 'Analyser la structure rythmique',
    icon: BarChart3,
    prompt: 'Analyse la structure rythmique et métrique de ces paroles'
  }
];

const WRITING_STYLES = [
  { value: 'pop', label: 'Pop' },
  { value: 'rock', label: 'Rock' },
  { value: 'folk', label: 'Folk' },
  { value: 'rap', label: 'Rap/Hip-Hop' },
  { value: 'country', label: 'Country' },
  { value: 'jazz', label: 'Jazz' },
  { value: 'blues', label: 'Blues' },
  { value: 'electronic', label: 'Électronique' }
];

const THEMES = [
  { value: 'love', label: 'Amour', icon: '💕' },
  { value: 'freedom', label: 'Liberté', icon: '🕊️' },
  { value: 'nostalgia', label: 'Nostalgie', icon: '🌅' },
  { value: 'hope', label: 'Espoir', icon: '🌟' },
  { value: 'adventure', label: 'Aventure', icon: '🗺️' },
  { value: 'melancholy', label: 'Mélancolie', icon: '🌧️' }
];

export const AILyricsToolbar: React.FC<AILyricsToolbarProps> = ({
  content,
  onContentChange,
  selectedSection,
  sections,
  onAIGenerate,
  viewMode
}) => {
  // États locaux
  const [isAILoading, setIsAILoading] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('pop');
  const [selectedTheme, setSelectedTheme] = useState('love');
  const [selectedLanguage, setSelectedLanguage] = useState('fr');
  const [rhymeWord, setRhymeWord] = useState('');
  const [activeTab, setActiveTab] = useState('quick');

  // Gestionnaire pour les actions IA prédéfinies
  const handleAIAction = useCallback(async (action: AIAction) => {
    setIsAILoading(true);
    
    try {
      let prompt = action.prompt;
      const currentSection = sections.find(s => s.id === selectedSection);
      
      // Remplacer les variables dans le prompt
      prompt = prompt
        .replace('{sectionType}', currentSection?.type || 'section')
        .replace('{genre}', selectedStyle)
        .replace('{theme}', selectedTheme)
        .replace('{language}', selectedLanguage)
        .replace('{word}', rhymeWord);
      
      // Ajouter le contexte si nécessaire
      if (content.trim()) {
        prompt += `\n\nContexte actuel:\n${content}`;
      }
      
      await onAIGenerate(prompt, 'lyrics');
      
      toast({
        title: "IA activée",
        description: `${action.title} en cours...`
      });
      
    } catch (error) {
      toast({
        title: "Erreur IA",
        description: "Impossible de générer le contenu",
        variant: "destructive"
      });
    } finally {
      setIsAILoading(false);
    }
  }, [selectedSection, sections, selectedStyle, selectedTheme, selectedLanguage, rhymeWord, content, onAIGenerate]);

  // Gestionnaire pour les prompts personnalisés
  const handleCustomPrompt = useCallback(async () => {
    if (!aiPrompt.trim()) return;
    
    setIsAILoading(true);
    try {
      await onAIGenerate(aiPrompt, 'lyrics');
      setAiPrompt('');
      
      toast({
        title: "Contenu généré",
        description: "Le texte a été inséré avec succès"
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de générer le contenu",
        variant: "destructive"
      });
    } finally {
      setIsAILoading(false);
    }
  }, [aiPrompt, onAIGenerate]);

  const currentSection = sections.find(s => s.id === selectedSection);

  return (
    <div className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Brain className="h-4 w-4" />
          Assistant IA - Paroles
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="quick" className="text-xs">Actions</TabsTrigger>
            <TabsTrigger value="custom" className="text-xs">Custom</TabsTrigger>
            <TabsTrigger value="config" className="text-xs">Config</TabsTrigger>
          </TabsList>
          
          <div className="flex-1 overflow-hidden mt-3">
            <TabsContent value="quick" className="h-full m-0">
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  {AI_ACTIONS.map(action => {
                    const Icon = action.icon;
                    return (
                      <Button
                        key={action.id}
                        variant="outline"
                        size="sm"
                        onClick={() => handleAIAction(action)}
                        disabled={isAILoading}
                        className="w-full h-auto p-3 flex flex-col items-start gap-1"
                      >
                        <div className="flex items-center gap-2 w-full">
                          <Icon className="h-4 w-4" />
                          <span className="text-xs font-medium">{action.title}</span>
                        </div>
                        <span className="text-xs text-muted-foreground text-left">
                          {action.description}
                        </span>
                      </Button>
                    );
                  })}
                </div>
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="custom" className="h-full m-0">
              <div className="space-y-3">
                <div>
                  <Label htmlFor="custom-prompt" className="text-xs">Prompt personnalisé</Label>
                  <Textarea
                    id="custom-prompt"
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    placeholder="Décrivez ce que vous voulez que l'IA génère..."
                    className="mt-1 text-xs"
                    rows={4}
                  />
                </div>
                
                <Button
                  onClick={handleCustomPrompt}
                  disabled={isAILoading || !aiPrompt.trim()}
                  className="w-full gap-2"
                  size="sm"
                >
                  <Send className="h-4 w-4" />
                  {isAILoading ? 'Génération...' : 'Générer'}
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="config" className="h-full m-0">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  <div>
                    <Label className="text-xs">Style musical</Label>
                    <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {WRITING_STYLES.map(style => (
                          <SelectItem key={style.value} value={style.value}>
                            {style.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label className="text-xs">Thème</Label>
                    <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {THEMES.map(theme => (
                          <SelectItem key={theme.value} value={theme.value}>
                            <span className="flex items-center gap-2">
                              <span>{theme.icon}</span>
                              {theme.label}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="rhyme-word" className="text-xs">Mot pour rimes</Label>
                    <Input
                      id="rhyme-word"
                      value={rhymeWord}
                      onChange={(e) => setRhymeWord(e.target.value)}
                      placeholder="ex: amour"
                      className="mt-1"
                    />
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
      
      {/* Informations contextuelles */}
      <div className="border-t p-3">
        <div className="flex flex-col gap-2 text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>Section:</span>
            <Badge variant="outline" className="text-xs capitalize">
              {currentSection?.type || 'Aucune'}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Mode:</span>
            <Badge variant="outline" className="text-xs">
              {viewMode}
            </Badge>
          </div>
          {content && (
            <div className="flex items-center justify-between">
              <span>Mots:</span>
              <span>{content.trim().split(/\s+/).filter(Boolean).length}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
