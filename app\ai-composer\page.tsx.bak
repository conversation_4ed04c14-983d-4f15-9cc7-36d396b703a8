'use client';

import { NewSongDraft } from '@/components/ai-composer/mega/NewSongDraft';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const AIComposerPage = () => {
  return (
    <div className="container mx-auto py-10">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>AI Composer Studio</CardTitle>
          <CardDescription>
            Créez une nouvelle chanson à partir de zéro. Rédigez vos paroles et vos accords, puis sauvegardez votre travail.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NewSongDraft />
        </CardContent>
      </Card>
    </div>
  );
};

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '@/components/LoadingSpinner';

export default function AIComposerRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirection immédiate vers ai-composer-workspace
    router.replace('/ai-composer-workspace');
  }, [router]);

  return (
    <div className="h-screen flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner />
        <p className="mt-4 text-muted-foreground">Redirection vers AI Composer...</p>
      </div>
    </div>
  );
}