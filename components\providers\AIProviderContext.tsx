"use client";

import React, { createContext, useContext, useMemo, useState, useCallback, useEffect } from "react"; // Added useEffect, useState, useCallback
import { getAIProvider, ProviderName } from "@/lib/aiClient";
import { useAIComposerConfig } from "@/hooks/useAIComposerConfig";

// Define a type for AI history entries
// This type should align with AiHistoryItem from song-schema.ts
export interface AIHistoryEntry {
  id: string;
  timestamp: string; // Changed from Date to string to match AiHistoryItem
  action: string; // Renamed from type, and aligns with AiHistoryItem
  input?: string; // Aligns with AiHistoryItem
  output?: string; // Renamed from result, and aligns with AiHistoryItem
  model?: string; // Renamed from modelUsed, and aligns with AiHistoryItem
  provider?: string; // Added to align with AiHistoryItem
  // 'applied' field is not in AiHistoryItem, consider if it's still needed or how to manage it.
  // For now, let's assume it's managed internally or can be omitted if not in AiHistoryItem.
  // If 'applied' is crucial, AiHistoryItem might need an update or a mapping strategy is required.
  applied: boolean; // Kept for now, but review based on usage and AiHistoryItem alignment. Made non-optional as per error.
}

// Temporary alias to bridge the gap if other parts of the code expect the old structure
// export type OldAIHistoryEntry = {
//   id: string;
//   prompt: string;
//   result: string;
//   timestamp: Date;
//   modelUsed?: string;
//   type: string;
//   applied: boolean;
// };

interface AIContextValue {
  generate: (prompt: string, options?: Partial<{ model: string; temperature: number; maxTokens: number }>) => Promise<string>;
  isLoading: boolean;
  error: Error | null;
  history: AIHistoryEntry[];
  addHistoryEntry: (entry: Omit<AIHistoryEntry, 'id' | 'timestamp' | 'action' | 'applied'> & { applied?: boolean }, action: string) => void; // Make applied optional in input, but ensure it's set in implementation
  markHistoryItemApplied: (itemId: string) => void;
  // clearHistory: () => void; // Optional: for managing history
  // Adding config related values if they are to be exposed by the context
  config: any; // Replace 'any' with your AiConfig type
  updateConfig?: (newConfig: Partial<any>) => void; // Optional updateConfig
}

const AIContext = createContext<AIContextValue | undefined>(undefined);

// Define props for AIProvider
export interface AIProviderProps {
  children: React.ReactNode;
  songId?: string; // Optional: if you need to scope history or config by song
  initialConfig?: any; // Replace 'any' with your AiConfig type
  initialHistory?: AIHistoryEntry[]; // Using AIHistoryEntry from this file
}

export const AIProvider: React.FC<AIProviderProps> = ({ children, songId, initialConfig, initialHistory }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  // Ensure initialHistory items are compatible with the new AIHistoryEntry structure
  const [history, setHistory] = useState<AIHistoryEntry[]>(
    initialHistory?.map(item => ({
      ...item,
      timestamp: typeof item.timestamp === 'string' ? item.timestamp : new Date(item.timestamp).toISOString(),
      // Ensure other fields match or are transformed as needed
      // This mapping might need to be more sophisticated depending on the exact structure of initialHistory items
      applied: item.applied !== undefined ? item.applied : false, // Ensure 'applied' is initialized
    })) || []
  );
  const { config, updateConfig: composerUpdateConfig } = useAIComposerConfig(); // Renamed to avoid conflict if context exposes its own updateConfig

  // Placeholder for context's own updateConfig if needed, otherwise remove or use composerUpdateConfig directly if appropriate
  const contextUpdateConfig = useCallback((newConfig: Partial<any>) => {
    // This is a placeholder. If the context needs to manage its own config state
    // separate from useAIComposerConfig, implement that logic here.
    // Otherwise, if useAIComposerConfig's updateConfig is sufficient, this can be removed
    // and composerUpdateConfig can be passed directly to the context value.
    if (composerUpdateConfig) {
      composerUpdateConfig(newConfig);
    }
    console.log("Context updateConfig called with:", newConfig);
  }, [composerUpdateConfig]);

  useEffect(() => {
    if (initialConfig && composerUpdateConfig) {
      // Assuming useAIComposerConfig has a way to set initial config
      // This might involve calling an updateConfig function from the hook
      // For example: updateConfig(initialConfig);
      // If direct update isn't possible, this logic might need adjustment
      // based on how useAIComposerConfig is implemented.
      // composerUpdateConfig(initialConfig); // Potentially call this if appropriate
      console.log("AIProvider: Initializing with config for songId:", songId, initialConfig);
    }
  }, [initialConfig, songId, composerUpdateConfig]);

  const provider = useMemo(() => {
    const activeConfig = initialConfig || config; // Prioritize initialConfig if provided
    return getAIProvider(activeConfig.provider as ProviderName, {
      apiKey: activeConfig.apiKey,
      endpoint: activeConfig.provider === "ollama" ? activeConfig.apiKey /* misuse field for URL */ : undefined,
      baseUrl: activeConfig.provider === "openai" && activeConfig.apiKey ? undefined : undefined,
    });
  }, [config, initialConfig]);

  const addHistoryEntry = useCallback((entry: Omit<AIHistoryEntry, 'id' | 'timestamp' | 'action' | 'applied'> & { applied?: boolean }, action: string = 'generation') => {
    setHistory(prevHistory => [
      { 
        ...entry, 
        id: Date.now().toString(), 
        timestamp: new Date().toISOString(), // Store as ISO string
        action, // Use 'action' to align with the updated interface
        applied: entry.applied !== undefined ? entry.applied : false // Default 'applied' if not provided
      } as AIHistoryEntry, // Ensure the created object matches AIHistoryEntry including non-optional 'applied'
      ...prevHistory
    ].slice(0, 50)); // Keep last 50 entries, for example
  }, []);


  const markHistoryItemApplied = useCallback((itemId: string) => {
    setHistory(prevHistory =>
      prevHistory.map(item =>
        item.id === itemId ? { ...item, applied: true } : item
      )
    );
  }, []);

  const generate = async (
    prompt: string,
    opts: Partial<{ model: string; temperature: number; maxTokens: number }> = {}
  ): Promise<string> => {
    setIsLoading(true);
    setError(null);
    try {
    const { text } = await provider.generate({ // 'text' here is the 'output' in AiHistoryItem/AIHistoryEntry

      prompt,
      model: opts.model || (initialConfig || config).model,
      temperature: opts.temperature ?? (initialConfig || config).temperature,
      maxTokens: opts.maxTokens ?? (initialConfig || config).maxTokens,
    });
    const finalModel = opts.model || (initialConfig || config).model;
    // Align with AIHistoryEntry: 'prompt' is 'input', 'result' is 'output', 'modelUsed' is 'model'
    addHistoryEntry({ input: prompt, output: text, model: finalModel, provider: (initialConfig || config).provider }, 'generation');
    return text;
  } catch (e) {
    const err = e instanceof Error ? e : new Error(String(e));
    setError(err);
    console.error("AI Generation Error:", err);
    throw err; // Re-throw to allow caller to handle if needed
  } finally {
    setIsLoading(false);
  }
  };

  const value = useMemo<AIContextValue>(() => ({
    generate,
    isLoading,
    error,
    history,
    addHistoryEntry,
    markHistoryItemApplied,
    // clearHistory: () => setHistory([]) // Example if clearHistory is added
    config: initialConfig || config, // Expose current config
    updateConfig: contextUpdateConfig, // Expose context's updateConfig or composerUpdateConfig
  }), [generate, isLoading, error, history, addHistoryEntry, markHistoryItemApplied, config, initialConfig, contextUpdateConfig]);

  return <AIContext.Provider value={value}>{children}</AIContext.Provider>;
};

export const useAI = () => {
  const ctx = useContext(AIContext);
  if (!ctx) throw new Error("useAI must be used within AIProvider");
  return ctx;
};
