import { NextResponse } from 'next/server';

// Pour l'instant, nous allons simuler une réponse de l'IA.
// TODO: Intégrer un véritable client API IA (par exemple, OpenAI, Anthropic, etc.)

export async function POST(request: Request) {
  try {
    const { prompt, provider, model, temperature } = await request.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Le prompt est manquant' }, { status: 400 });
    }

    // Simuler un appel à une API d'IA avec un délai
    await new Promise(resolve => setTimeout(resolve, 1000));

    const aiResponse = `Voici une continuation générée par l'IA pour : "${prompt.substring(0, 50)}..."`;

    return NextResponse.json({ text: aiResponse });
  } catch (error) {
    console.error('Erreur dans la route /api/ai/generate:', error);
    return NextResponse.json({ error: 'Une erreur interne est survenue' }, { status: 500 });
  }
}
