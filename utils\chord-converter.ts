import type { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';
import type { ChordPlacementStorage, ChordPlacementDisplay } from '@/types/composer';

/**
 * Converts a UnifiedChordPosition to a simple chord string for storage
 */
export const chordToString = (chord: UnifiedChordPosition): string => {
  return chord.chord;
};

/**
 * Converts a chord string to a UnifiedChordPosition for display
 */
export const stringToChord = (chordStr: string): UnifiedChordPosition => ({
  id: `chord-${Date.now()}-${Math.random()}`,
  chord: chordStr,
  instrument: 'guitar' as const,
  tuning: 'standard',
  frets: [0, 0, 0, 0, 0, 0],
  baseFret: 0,
  difficulty: 'beginner' as const,
  category: 'major',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

/**
 * Converts a ChordPlacement with UnifiedChordPosition to a simple chord string
 */
export const chordPlacementToString = (placement: ChordPlacementDisplay): ChordPlacementStorage => {
  const { beat, duration, strum, metadata, chord, ...rest } = placement;
  return {
    ...rest,
    chord: chordToString(chord),
    metadata: {
      ...metadata,
      beat,
      emphasis: placement.emphasis,
    },
  };
};

/**
 * Converts a ChordPlacement with string chord to UnifiedChordPosition
 */
export const chordPlacementToUnified = (placement: ChordPlacementStorage): ChordPlacementDisplay => {
  const { metadata, chord, ...rest } = placement;
  return {
    ...rest,
    chord: stringToChord(chord),
    beat: metadata?.beat ?? 0,
    duration: 0, // Default duration, can be adjusted
    emphasis: metadata?.emphasis,
  };
};
