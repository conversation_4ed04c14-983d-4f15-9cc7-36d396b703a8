"use client";

import Image from 'next/image';
import Link from 'next/link';
import { useTransition, useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, Pencil, Share2, Trash2, Copy, Music, Heart, Clock, Play, Pause, SkipForward, SkipBack } from 'lucide-react';

import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type { Song } from '@/components/songs/song-schema';
import type { Playlist, Profile } from '@/types';
import { ActionableVisibilityToggle } from '@/components/ui/actionable-visibility-toggle';
import { duplicatePlaylist } from '@/app/(authenticated)/playlists/actions';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore';

// Define the extended type locally to avoid import issues and ensure all required fields are present.
export type PlaylistWithSongsAndCreator = Playlist & {
  songs: Song[]; // Maintenu pour d'autres usages potentiels
  profiles: Profile | null;
  like_count?: number;
  song_count: number; // Ajouté : nombre de morceaux
  total_duration_ms: number; // Ajouté : durée totale en ms
};

interface PlaylistCardProps {
  playlist: PlaylistWithSongsAndCreator;
  onToggleVisibility: (playlistId: string, currentIsPublic: boolean) => void;
  isVisibilityUpdating: boolean;
  onDelete: (playlistId: string) => void;
  className?: string;
}

const PlaylistCard: React.FC<PlaylistCardProps> = ({
  playlist,
  onToggleVisibility,
  isVisibilityUpdating,
  onDelete,
  className,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Audio player state and actions
  const { playSong: globalPlaySong, pauseSong: globalPauseSong, setQueue: globalSetQueue, currentSong, isPlaying: isPlayingGlobal, nextSong: globalNextSong, previousSong: globalPreviousSong } = useAudioPlayerStore();
  const [currentPlayingSongIndexInCard, setCurrentPlayingSongIndexInCard] = useState(0);
  const [isThisCardPlaying, setIsThisCardPlaying] = useState(false);
  
  const coverUrl = playlist.cover_art_url || '/artworks/placeholder_playlist.png';
  const viewPageUrl = `/playlists/${playlist.id}`;

  const songsInPlaylist = playlist.songs || [];
  const hasSongs = songsInPlaylist.length > 0;

  const handlePlayPause = useCallback(() => {
    if (!hasSongs) return;

    const currentSongInCard = songsInPlaylist[currentPlayingSongIndexInCard];

    // Check if the currently playing song in the global store is from this card's playlist and at the current index
    const isCurrentGlobalSongFromThisCard = currentSong?.id === currentSongInCard?.id;

    if (isThisCardPlaying && isPlayingGlobal && isCurrentGlobalSongFromThisCard) {
      // This card is playing and active: Pause it
      globalPauseSong();
    } else if (isThisCardPlaying && !isPlayingGlobal && isCurrentGlobalSongFromThisCard) {
      // This card is the active one but paused: Resume it
      if (currentSongInCard) globalPlaySong(currentSongInCard);
    } else {
      // No song from this card is active, or another song is playing: Start this playlist
      // Reset index to 0 if this card wasn't the one playing or if it's a fresh play
      const startIndex = (isThisCardPlaying && isCurrentGlobalSongFromThisCard) ? currentPlayingSongIndexInCard : 0;
      setCurrentPlayingSongIndexInCard(startIndex);
      globalSetQueue(songsInPlaylist, true, startIndex);
      setIsThisCardPlaying(true);
    }
  }, [hasSongs, songsInPlaylist, currentPlayingSongIndexInCard, currentSong, isThisCardPlaying, isPlayingGlobal, globalPlaySong, globalPauseSong, globalSetQueue]);

  const handleNextSong = useCallback(() => {
    if (!hasSongs || !isThisCardPlaying) return; // Only allow next if this card is the one playing
    // Ensure the global queue is set from this playlist before calling nextSong
    if (currentSong && songsInPlaylist.some(s => s.id === currentSong.id)) {
       // Check if current global song is the last of this playlist card
      if (currentPlayingSongIndexInCard >= songsInPlaylist.length - 1) {
        // Optionally stop or loop, for now, just stop by doing nothing or pausing
        globalPauseSong();
        setIsThisCardPlaying(false); // Playlist finished
        setCurrentPlayingSongIndexInCard(0);
        return;
      }
      globalNextSong();
      setCurrentPlayingSongIndexInCard(prev => Math.min(prev + 1, songsInPlaylist.length - 1));
    } else {
      // If global song isn't from this card, play first song of this card
      handlePlayPause();
    }
  }, [hasSongs, isThisCardPlaying, songsInPlaylist, globalNextSong, currentSong, handlePlayPause, globalPauseSong, currentPlayingSongIndexInCard]);

  const handlePreviousSong = useCallback(() => {
    if (!hasSongs || !isThisCardPlaying) return;
    if (currentSong && songsInPlaylist.some(s => s.id === currentSong.id)) {
      // Check if current global song is the first of this playlist card
      if (currentPlayingSongIndexInCard <= 0) {
        // Optionally do nothing or restart song
        if (songsInPlaylist[0]) globalPlaySong(songsInPlaylist[0]); // Replay first song
        setCurrentPlayingSongIndexInCard(0);
        return;
      }
      globalPreviousSong();
      setCurrentPlayingSongIndexInCard(prev => Math.max(prev - 1, 0));
    } else {
      handlePlayPause();
    }
  }, [hasSongs, isThisCardPlaying, songsInPlaylist, globalPreviousSong, currentSong, handlePlayPause, globalPlaySong, currentPlayingSongIndexInCard]);

  // Effect to synchronize card's playing state with global audio player
  useEffect(() => {
    const songIdxInThisPlaylist = songsInPlaylist.findIndex(s => s.id === currentSong?.id);

    if (songIdxInThisPlaylist !== -1) {
      // The globally playing song is from this playlist
      setCurrentPlayingSongIndexInCard(songIdxInThisPlaylist);
      if (!isThisCardPlaying) {
        setIsThisCardPlaying(true); // Activate this card if it wasn't already
      }
    } else {
      // The globally playing song is NOT from this playlist
      if (isThisCardPlaying) {
        setIsThisCardPlaying(false); // Deactivate this card
        // setCurrentPlayingSongIndexInCard(0); // Optionally reset index, or keep for context if user returns
      }
    }
  }, [currentSong, songsInPlaylist, setIsThisCardPlaying, isThisCardPlaying, setCurrentPlayingSongIndexInCard]);


  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDelete(playlist.id);
  };
  
  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const url = `${window.location.origin}${viewPageUrl}`;
    navigator.clipboard.writeText(url);
    toast.success('Lien de la playlist copié dans le presse-papiers !');
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(`/playlists/${playlist.id}/edit`);
  };

  const handleDuplicate = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    startTransition(async () => {
      const result = await duplicatePlaylist(playlist.id);
      if (result.success) {
        toast.success(`Playlist "${playlist.name}" dupliquée.`);
        router.refresh(); // Refresh to show the new playlist
      } else {
        toast.error("Erreur lors de la duplication", {
          description: result.error,
        });
      }
    });
  };

  // Extract unique genres from songs in the playlist
  const genres: string[] = Array.from(
    new Set(playlist.songs?.flatMap((song) => song.genres || []) || [])
  ).slice(0, 3);

  const formatDuration = (ms: number | null | undefined): string => {
    if (ms === null || ms === undefined || ms <= 0) return '0:00';
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  return (
    <Card className={cn("group relative flex flex-col overflow-hidden", className)}>
      <CardHeader className="p-0 relative">
        <div className="absolute top-2 left-2 z-20 bg-card/50 backdrop-blur-sm rounded-full">
          <ActionableVisibilityToggle
            isPublic={playlist.is_public ?? false}
            isUpdating={isVisibilityUpdating}
            onToggle={() => onToggleVisibility(playlist.id, playlist.is_public ?? false)}
          />
        </div>
        <div className="absolute top-2 right-2 z-20">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Pencil className="mr-2 h-4 w-4" />
                <span>Modifier</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate} disabled={isPending}>
                <Copy className="mr-2 h-4 w-4" />
                <span>{isPending ? 'Duplication...' : 'Dupliquer'}</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleShare}>
                <Share2 className="mr-2 h-4 w-4" />
                <span>Partager</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDelete} className="text-red-500">
                <Trash2 className="mr-2 h-4 w-4" />
                <span>Supprimer</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Link href={viewPageUrl} className="block relative aspect-square w-full overflow-hidden rounded-t-md">
          <Image
            src={coverUrl}
            alt={`Cover for ${playlist.name}`}
            fill
            className="object-cover transition-transform group-hover:scale-105"
          />
        </Link>
      </CardHeader>
      <CardContent className="p-3 flex-grow flex flex-col">
        <div className="flex-grow">
          <div className="flex items-baseline gap-x-2 mb-1">
            <Link href={viewPageUrl} className="flex-shrink min-w-0">
              <CardTitle className="font-semibold hover:underline truncate" title={playlist.name}>
                {playlist.name}
              </CardTitle>
            </Link>
            {playlist.total_duration_ms > 0 && (
              <span className="text-xs text-muted-foreground flex-shrink-0">
                (<Clock className="h-3 w-3 inline-block mr-0.5" />{formatDuration(playlist.total_duration_ms)})
              </span>
            )}
          </div>
          <div className="text-xs text-muted-foreground truncate mb-2">
            par {playlist.profiles?.display_name || playlist.profiles?.username || 'Artiste inconnu'}
          </div>
          {genres.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {genres.map((genre) => (
                <Badge key={genre} variant="secondary" className="text-xs px-1.5 py-0.5">
                  {genre}
                </Badge>
              ))}
            </div>
          )}
        </div>
        <div className="flex items-center gap-x-3 text-xs text-muted-foreground mt-auto pt-2">
          <span className="flex items-center gap-1" title="Nombre de morceaux">
            <Music className="h-3 w-3" /> {playlist.song_count}
          </span>
          <span className="flex items-center gap-1" title="Likes">
            <Heart className="h-3 w-3" /> {playlist.like_count || 0}
          </span>
        </div>
        {/* Player Controls */}
        {hasSongs && (
          <div className="mt-3 flex items-center justify-between gap-2 border-t pt-3">
            <Button variant="ghost" size="icon" onClick={handlePreviousSong} disabled={!hasSongs || (isThisCardPlaying && currentPlayingSongIndexInCard === 0 && !isPlayingGlobal && songsInPlaylist.length <=1) }>
              <SkipBack className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handlePlayPause} disabled={!hasSongs}>
              {/* Conditional Play/Pause Icon */}
              {isThisCardPlaying && isPlayingGlobal && songsInPlaylist.some(s => s.id === currentSong?.id) ? (
                <Pause className="h-6 w-6" />
              ) : (
                <Play className="h-6 w-6" />
              )}
            </Button>
            <Button variant="ghost" size="icon" onClick={handleNextSong} disabled={!hasSongs || (isThisCardPlaying && currentPlayingSongIndexInCard >= songsInPlaylist.length - 1)}>
              <SkipForward className="h-5 w-5" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PlaylistCard;
