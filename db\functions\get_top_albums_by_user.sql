-- Fonction pour obtenir les albums les plus populaires d'un utilisateur pour une période donnée
CREATE OR REPLACE FUNCTION get_top_albums_by_user(p_user_id UUID, p_time_range TEXT, p_limit INT DEFAULT 5)
RETURNS TABLE (
  id UUID,
  title TEXT,
  cover_url TEXT,
  song_count BIGINT,
  total_plays BIGINT,
  total_likes BIGINT
)
LANGUAGE plpgsql
AS $$
DECLARE
  v_start_date TIMESTAMP;
  v_end_date TIMESTAMP;
BEGIN
  v_end_date := NOW();
  IF p_time_range = '7d' THEN
    v_start_date := v_end_date - INTERVAL '7 days';
  ELSIF p_time_range = '30d' THEN
    v_start_date := v_end_date - INTERVAL '30 days';
  ELSIF p_time_range = '90d' THEN
    v_start_date := v_end_date - INTERVAL '90 days';
  ELSIF p_time_range = '1y' THEN
    v_start_date := v_end_date - INTERVAL '1 year';
  ELSE
    v_start_date := '1970-01-01'::TIMESTAMP;
  END IF;

  RETURN QUERY
  WITH album_song_stats AS (
    SELECT
      als.album_id,
      s.id AS song_id,
      COALESCE(SUM(CASE WHEN p.created_at BETWEEN v_start_date AND v_end_date THEN 1 ELSE 0 END), 0) AS song_plays,
      COALESCE(SUM(CASE WHEN l.created_at BETWEEN v_start_date AND v_end_date THEN 1 ELSE 0 END), 0) AS song_likes
    FROM
      album_songs als
    JOIN
      songs s ON als.song_id = s.id
    LEFT JOIN
      plays p ON s.id = p.song_id
    LEFT JOIN
      likes l ON s.id = l.resource_id AND l.resource_type = 'song'
    WHERE
      s.creator_user_id = p_user_id -- S'assurer que les chansons appartiennent à l'utilisateur
      AND s.visibility = 'public'
    GROUP BY
      als.album_id, s.id
  )
  SELECT
    a.id,
    a.title::TEXT,
    a.cover_url::TEXT,
    COUNT(DISTINCT ass.song_id)::BIGINT AS song_count,
    COALESCE(SUM(ass.song_plays), 0)::BIGINT AS total_plays,
    COALESCE(SUM(ass.song_likes), 0)::BIGINT AS total_likes
  FROM
    albums a
  JOIN
    album_song_stats ass ON a.id = ass.album_id
  WHERE
    a.user_id = p_user_id -- S'assurer que l'album appartient à l'utilisateur
    AND a.visibility = 'public' -- Uniquement les albums publiés
  GROUP BY
    a.id
  ORDER BY
    total_plays DESC, total_likes DESC, a.created_at DESC
  LIMIT p_limit;

END;
$$;

COMMENT ON FUNCTION get_top_albums_by_user(UUID, TEXT, INT) IS 'Récupère les albums les plus populaires (basé sur les écoutes et likes des chansons) d''un utilisateur pour une période donnée.';

-- Exemple d'appel :
-- SELECT * FROM get_top_albums_by_user('VOTRE_USER_ID_ICI', '30d', 5);
