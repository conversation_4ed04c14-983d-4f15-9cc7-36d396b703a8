-- Migration to add banner_url to the get_playlist_details_for_view RPC function

CREATE OR REPLACE FUNCTION public.get_playlist_details_for_view(p_playlist_id uuid, p_requesting_user_id uuid DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    v_playlist_record RECORD;
BEGIN
    -- Fetch the playlist and its owner
    SELECT p.*, pr.username, pr.display_name, pr.avatar_url
    INTO v_playlist_record
    FROM public.playlists p
    JOIN public.profiles pr ON p.user_id = pr.id
    WHERE p.id = p_playlist_id;

    -- If not found, return null
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;

    -- Check for access rights
    IF v_playlist_record.is_public = FALSE AND v_playlist_record.user_id IS DISTINCT FROM p_requesting_user_id THEN
        RETURN NULL; -- Access denied
    END IF;

    -- Build and return the JSON object
    R<PERSON>URN json_build_object(
        'id', v_playlist_record.id,
        'name', v_playlist_record.name,
        'description', v_playlist_record.description,
        'is_public', v_playlist_record.is_public,
        'cover_art_url', v_playlist_record.cover_art_url, -- Already present
        'banner_url', v_playlist_record.banner_url,     -- Added banner_url
        'user_id', v_playlist_record.user_id,
        'created_at', v_playlist_record.created_at,
        'updated_at', v_playlist_record.updated_at,
        'slug', v_playlist_record.slug,
        'profiles', json_build_object(
            'id', v_playlist_record.user_id,
            'username', v_playlist_record.username,
            'display_name', v_playlist_record.display_name,
            'avatar_url', v_playlist_record.avatar_url
        ),
        'playlist_songs', (
             SELECT COALESCE(json_agg(
                json_build_object(
                    'id', s.id,
                    'title', s.title,
                    'artist_name', s.artist_name,
                    'audio_url', s.audio_url,
                    'cover_art_url', s.cover_art_url,
                    'duration_ms', s.duration_ms,
                    'position', ps.position,
                    'profiles', (
                        SELECT json_build_object(
                            'id', pr_song.id,
                            'username', pr_song.username,
                            'display_name', pr_song.display_name
                        )
                        FROM public.profiles pr_song
                        WHERE pr_song.id = s.creator_user_id
                    )
                ) ORDER BY ps.position ASC NULLS LAST
            ), '[]'::json)
            FROM public.playlist_songs ps
            JOIN public.songs s ON ps.song_id = s.id
            WHERE ps.playlist_id = v_playlist_record.id
        )
    );
END;
$$;

-- Note:
-- This function now includes 'banner_url' in the returned JSON object.
-- Ensure the frontend component consuming this RPC can handle and display the banner_url.
