import { create } from 'zustand';
import type { Song } from '@/types/types';

export type PlayerMode = 'normal' | 'mini' | 'micro' | 'large';

const DEFAULT_COVER_IMAGE_PATH = '/images/covers/mouvk.png';

export interface AudioPlayerState {
  queue: Song[];
  currentQueueIndex: number;
  currentSong: Song | null;
  isPlaying: boolean;
  isSeeking: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  coverUrl: string | null;
  audioElement: HTMLAudioElement | null;
  playerMode: PlayerMode;

  initAudioElement: () => void;
  setPlayerMode: (mode: PlayerMode) => void;
  cyclePlayerMode: () => void;
  playSong: (song: Song) => void;
  setCurrentSongDetails: (song: Song) => void;
  pauseSong: () => void;
  togglePlayPause: () => void;
  seek: (time: number) => void;
  setSeeking: (isSeeking: boolean) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  cleanup: () => void;

  setQueue: (songs: Song[], playImmediately?: boolean, startIndex?: number) => void;
  addToQueue: (song: Song) => void;
  nextSong: () => void;
  previousSong: () => void;
  clearQueue: () => void;
  playFromQueue: (index: number) => void;
}

export const useAudioPlayerStore = create<AudioPlayerState>((set, get) => {
  const setupAudioEvents = (audio: HTMLAudioElement) => {
    audio.onended = () => get().nextSong();
    audio.ontimeupdate = () => set({ currentTime: audio.currentTime });
    audio.onloadedmetadata = () => {
      if (audio && !isNaN(audio.duration) && isFinite(audio.duration)) {
        set({ duration: audio.duration });
      } else {
        set({ duration: 0 });
      }
    };
    audio.onerror = (e) => {
      console.error("Audio Element Error:", e);
      // When an error occurs, try to play the next song in the queue.
      // This prevents the player from getting stuck on a broken track.
      get().nextSong();
    };
  };

  return {
    queue: [],
    currentQueueIndex: -1,
    currentSong: null,
    isPlaying: false,
    isSeeking: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    coverUrl: null,
    audioElement: null,
    playerMode: 'normal',

    initAudioElement: () => {
      if (typeof window !== 'undefined' && !get().audioElement) {
        const audio = new Audio();
        setupAudioEvents(audio);
        set({ audioElement: audio });
      }
    },

    setPlayerMode: (mode: PlayerMode) => set({ playerMode: mode }),

    cyclePlayerMode: () => {
      const { playerMode } = get();
      const modeCycle: PlayerMode[] = ['normal', 'mini', 'micro', 'large'];
      const currentIndex = modeCycle.indexOf(playerMode);
      const nextIndex = (currentIndex + 1) % modeCycle.length;
      set({ playerMode: modeCycle[nextIndex] });
    },

    playSong: (song: Song) => {
      const { audioElement, setCurrentSongDetails } = get();
      if (audioElement) {
        setCurrentSongDetails(song);
        audioElement.src = song.audio_url ?? '';
        audioElement.play().catch(e => console.error("Error playing audio:", e));
        set({ isPlaying: true });
      }
    },

    setCurrentSongDetails: (song: Song) => {
      set({
        currentSong: song,
        coverUrl: song.cover_url ?? DEFAULT_COVER_IMAGE_PATH,
        duration: song.duration_ms ? song.duration_ms / 1000 : 0,
        currentTime: 0,
      });
    },

    pauseSong: () => {
      const { audioElement } = get();
      if (audioElement) {
        audioElement.pause();
        set({ isPlaying: false });
      }
    },

    togglePlayPause: () => {
      const { isPlaying, pauseSong, currentSong, audioElement } = get();
      if (isPlaying) {
        pauseSong();
      } else if (currentSong && audioElement) {
        audioElement.play().catch(e => console.error("Error resuming audio:", e));
        set({ isPlaying: true });
      } else if (currentSong) {
        get().playSong(currentSong);
      }
    },

    seek: (time: number) => {
      const { audioElement, duration } = get();
      if (audioElement && isFinite(time)) {
        audioElement.currentTime = Math.max(0, Math.min(time, duration));
      }
    },

    setSeeking: (isSeeking: boolean) => set({ isSeeking }),

    setVolume: (volume: number) => {
      const { audioElement, isMuted } = get();
      const newVolume = Math.max(0, Math.min(1, volume));
      if (audioElement) {
        audioElement.volume = isMuted ? 0 : newVolume;
      }
      set({ volume: newVolume, isMuted: newVolume === 0 ? true : isMuted && newVolume > 0 ? false : isMuted });
    },

    toggleMute: () => {
      const { audioElement, volume, isMuted } = get();
      const newMutedState = !isMuted;
      if (audioElement) {
        audioElement.volume = newMutedState ? 0 : volume;
      }
      set({ isMuted: newMutedState });
    },

    setQueue: (songs: Song[], playImmediately = false, startIndex = 0) => {
      set({ queue: songs, currentQueueIndex: -1 });
      if (playImmediately && songs.length > 0 && startIndex < songs.length) {
        get().playFromQueue(startIndex);
      }
    },

    addToQueue: (song: Song) => {
      set(state => ({ queue: [...state.queue, song] }));
    },

    nextSong: () => {
      const { queue, currentQueueIndex, playFromQueue } = get();
      const nextIndex = currentQueueIndex + 1;
      if (nextIndex < queue.length) {
        playFromQueue(nextIndex);
      } else {
        set({ isPlaying: false }); // End of queue
      }
    },

    previousSong: () => {
      const { queue, currentQueueIndex, playFromQueue } = get();
      const prevIndex = currentQueueIndex - 1;
      if (prevIndex >= 0) {
        playFromQueue(prevIndex);
      } else {
        const audio = get().audioElement;
        if (audio) audio.currentTime = 0;
      }
    },

    clearQueue: () => {
      set({ queue: [], currentQueueIndex: -1 });
    },

    playFromQueue: (index: number) => {
      const { queue, playSong } = get();
      if (index >= 0 && index < queue.length) {
        const song = queue[index];
        playSong(song);
        set({ currentQueueIndex: index });
      }
    },

    cleanup: () => {
      const { audioElement } = get();
      if (audioElement) {
        audioElement.pause();
        audioElement.src = '';
        set({ 
          currentSong: null, 
          isPlaying: false, 
          currentTime: 0, 
          duration: 0, 
          coverUrl: null,
          queue: [],
          currentQueueIndex: -1,
        });
      }
    },
  };
});

if (typeof window !== 'undefined') {
  useAudioPlayerStore.getState().initAudioElement();
}
