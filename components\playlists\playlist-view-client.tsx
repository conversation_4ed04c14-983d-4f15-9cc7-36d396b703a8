"use client";

import { useState, useMemo, useCallback } from 'react';
import { createBrowserClient } from '@/lib/supabase/client';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { LayoutGrid, List, ZoomIn, ZoomOut, ListMusic } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SongCard, SongForCard } from '@/components/songs/song-card';
// Types will be imported from the actual page file if possible, or defined if complex
// For now, using aliased imports for clarity assuming they can be resolved.
// If direct import from a server component page to a client component is problematic,
// these types might need to be in a shared types file.
import type { PublicPlaylistDetails as ActualPlaylistDetailsType, PlaylistSongForPage as ActualSongForPlaylistType } from '../../app/(public)/playlist/[slug]/page';
import { toast } from 'sonner';
import type { Song } from '@/components/songs/song-schema';

interface PlaylistViewClientProps {
  playlistData: ActualPlaylistDetailsType;
  initialUser?: any; // Or a more specific user type, e.g., User from @supabase/supabase-js
}

export function PlaylistViewClient({ playlistData, initialUser }: PlaylistViewClientProps) {
  const supabase = createBrowserClient();
  // Extract songs from playlistData.playlist_songs, which is Array<{ songs: ActualSongForPlaylistType | null }>
  const initialSongs = useMemo(() => 
    playlistData.playlist_songs
      .map(ps => ps.songs)
      .filter((song): song is ActualSongForPlaylistType => song !== null),
    [playlistData.playlist_songs]
  );
  const [songs, setSongs] = useState<ActualSongForPlaylistType[]>(initialSongs);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [searchQuery, setSearchQuery] = useState('');
  const [xlCols, setXlCols] = useState(4);
  const [updatingSongId, setUpdatingSongId] = useState<string | null>(null);

  const filteredSongs = useMemo(() => {
    if (!searchQuery) return songs;
    const lowerCaseQuery = searchQuery.toLowerCase();
    return songs.filter(song =>
      song.title.toLowerCase().includes(lowerCaseQuery) ||
      // artist_name might not be directly on ActualSongForPlaylistType, use profiles
      (song.profiles?.display_name && song.profiles.display_name.toLowerCase().includes(lowerCaseQuery)) ||
      (song.profiles?.username && song.profiles.username.toLowerCase().includes(lowerCaseQuery))
    );
  }, [songs, searchQuery]);

  const handleToggleVisibility = useCallback(async (songId: string, currentVisibility: boolean) => {
    setUpdatingSongId(songId);
    const newVisibility = !currentVisibility;
    try {
      const { error } = await supabase
        .from('songs')
        .update({ is_public: newVisibility })
        .eq('id', songId);

      if (error) {
        throw error;
      }

      setSongs(prevSongs => prevSongs.map(s => s.id === songId ? { ...s, is_public: newVisibility } : s));
      toast.success(`Visibilité du morceau mise à jour : ${newVisibility ? 'Public' : 'Privé'}`);
    } catch (error) {
      console.error('Error updating song visibility:', error);
      toast.error('Erreur lors de la mise à jour de la visibilité.');
    } finally {
      setUpdatingSongId(null);
    }
  }, [supabase]);

  if (!playlistData) return <p>Chargement des données de la playlist...</p>;

  const songsToDisplay = filteredSongs;

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <h2 className="text-2xl font-semibold flex items-center self-start sm:self-center">
          <ListMusic className="mr-3 h-7 w-7 text-primary" />
          Morceaux ({songsToDisplay.length})
        </h2>
        <div className="flex items-center gap-2 self-end sm:self-center">
          {viewMode === 'grid' && (
            <>
              <Button variant="ghost" size="icon" onClick={() => setXlCols(prev => Math.max(2, prev - 1))} title="Moins de colonnes" disabled={xlCols <= 2}>
                <ZoomOut className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" onClick={() => setXlCols(prev => Math.min(5, prev + 1))} title="Plus de colonnes" disabled={xlCols >= 5}>
                <ZoomIn className="h-5 w-5" />
              </Button>
            </>
          )}
          <Button variant={viewMode === 'grid' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('grid')} title="Vue grille">
            <LayoutGrid className="h-5 w-5" />
          </Button>
          <Button variant={viewMode === 'list' ? 'secondary' : 'ghost'} size="icon" onClick={() => setViewMode('list')} title="Vue liste">
            <List className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="my-4">
        <Input
          type="text"
          placeholder={`Rechercher parmi ${songs.length} morceau${songs.length === 1 ? '' : 'x'}...`}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-full sm:max-w-md"
        />
      </div>

      {songsToDisplay.length > 0 ? (
        <div className={cn(
          viewMode === 'grid'
            ? `grid grid-cols-1 sm:grid-cols-2 md:grid-cols-${Math.max(2, xlCols - 1)} lg:grid-cols-${xlCols} xl:grid-cols-${xlCols} 2xl:grid-cols-${xlCols} gap-4`
            : 'flex flex-col gap-1'
        )}>
          {songsToDisplay.map((song: ActualSongForPlaylistType) => {
            // Map ActualSongForPlaylistType to SongForCard
            const songForCard: SongForCard = {
              // Core Song fields from ActualSongForPlaylistType
              id: song.id,
              title: song.title,
              duration_ms: song.duration_ms,
              cover_art_url: song.cover_art_url,
              audio_url: song.audio_url ?? null,
              creator_user_id: song.creator_user_id ?? '',
              is_public: song.is_public ?? true, // Default to public if undefined
              slug: song.slug,
              genres: [], // ActualSongForPlaylistType doesn't have genres directly, SongCard might expect it
              moods: [],  // Add if available or default
              instrumentation: [], // Add if available or default
              tags: [], // Add if available or default
              lyrics: null, 
              bpm: null,
              key: null,
              status: (song.is_public ?? true) ? 'published' : 'private',
              created_at: new Date().toISOString(), // Placeholder or fetch if needed
              updated_at: new Date().toISOString(), // Placeholder or fetch if needed
              album_id: null, // Not directly relevant for a song in a playlist context for SongCard here
              like_count: 0, // Song specific likes, not from playlist context
              view_count: 0, // Song specific views
              comment_count: 0, // Song specific comments
              ai_content_origin: null,
              is_explicit: false,
              plays: 0, // Song specific plays
              user_rating: null,

              // ProfileForCard from song.profiles (song's original artist)
              profiles: song.profiles ? {
                id: song.creator_user_id || '', // ProfileForCard needs an id
                username: song.profiles.username,
                display_name: song.profiles.display_name,
                avatar_url: null, // ActualSongForPlaylistType.profiles doesn't have avatar_url
              } : null,
              
              // AlbumForCard - not directly applicable here, can be null
              albums: null, 

              // Visibility handlers (removed from songForCard)
            };

            return (
              <SongCard
                key={song.id}
                song={songForCard}
                viewMode={viewMode}
                onToggleVisibility={initialUser ? handleToggleVisibility : undefined}
                isVisibilityUpdating={updatingSongId === song.id}
                // onDelete and onUpdateStatus are not implemented here, so they will be undefined
                // Ensure they are optional in SongCardProps
              />
            );
          })}
        </div>
      ) : (
        <p className="text-muted-foreground text-center py-8">
          {searchQuery ? `Aucun morceau ne correspond à votre recherche "${searchQuery}".` : "Cette playlist est vide pour le moment."}
        </p>
      )}
    </div>
  );
}
