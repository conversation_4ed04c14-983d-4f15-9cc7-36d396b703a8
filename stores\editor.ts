import { create } from 'zustand';
import { Song } from '@/components/songs/song-schema';

interface EditorStore {
  currentSong: Song | null;
  setCurrentSong: (song: Song | null) => void;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  selectedLyrics: string;
  setSelectedLyrics: (lyrics: string) => void;
  selectedChords: string[];
  setSelectedChords: (chords: string[]) => void;
}

export const useEditorStore = create<EditorStore>((set) => ({
  currentSong: null,
  setCurrentSong: (song) => set({ currentSong: song }),
  isEditing: false,
  setIsEditing: (value) => set({ isEditing: value }),
  selectedLyrics: '',
  setSelectedLyrics: (lyrics) => set({ selectedLyrics: lyrics }),
  selectedChords: [],
  setSelectedChords: (chords) => set({ selectedChords: chords })
}));
