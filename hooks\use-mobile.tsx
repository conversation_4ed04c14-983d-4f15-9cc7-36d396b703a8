import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  // Initialize state based on current window width if available, otherwise default (e.g., false for SSR)
  const [isMobile, setIsMobile] = React.useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < MOBILE_BREAKPOINT;
    }
    return false; // Default for SSR or when window is not available during initial state setup
  });

  React.useEffect(() => {
    if (typeof window === 'undefined') {
      return; // Guard against running on server where window is not defined
    }

    // Handler to update isMobile state only if it has changed
    const updateMobileStatus = () => {
      const currentIsMobile = window.innerWidth < MOBILE_BREAKPOINT;
      setIsMobile(prevIsMobile => {
        // Only update if the value has actually changed
        if (prevIsMobile !== currentIsMobile) {
          return currentIsMobile;
        }
        return prevIsMobile; // Return previous state to prevent re-render if value is the same
      });
    };

    // Set initial status correctly on client mount, in case SSR default was different
    updateMobileStatus();

    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    // Use the same handler for media query changes
    mql.addEventListener("change", updateMobileStatus);

    return () => mql.removeEventListener("change", updateMobileStatus);
  }, []); // Empty dependency array ensures this runs once on mount and cleans up on unmount

  return isMobile; // isMobile is now always a boolean
}
