import { createSupabaseServerClient } from '@/lib/supabase/server';
import { notFound } from 'next/navigation';
import { WorkspaceClient } from './workspace-client';
import { Song } from '@/components/songs/song-schema';

interface WorkspacePageProps {
  params: {
    songId: string;
  };
}

export default async function AIComposerWorkspacePage({ params }: WorkspacePageProps) {
  const supabase = createSupabaseServerClient();

  const { data: song, error } = await supabase
    .from('songs')
    .select('*')
    .eq('id', params.songId)
    .single();

  if (error || !song) {
    notFound();
  }

  const typedSong = song as Song;

  return <WorkspaceClient song={typedSong} />;
}
