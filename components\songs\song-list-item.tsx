"use client";

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, Edit3, Trash2, PlayCircle, Music2, UserCircle, Disc, ListPlus, Heart, Copy, Loader2, Eye, Clock, Wand2
} from 'lucide-react';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore';
import { AddToPlaylistModal } from '@/components/playlists/add-to-playlist-modal';
import type { Song } from '@/components/songs/song-schema';
import type { UserProfile, Album } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

export interface SongForListItem extends Song {
  profiles?: UserProfile | null;
  albums?: Pick<Album, 'id' | 'title' | 'cover_url'> | null;
}

interface SongListItemProps {
  song: SongForListItem;
  onDelete: () => void;
  onUpdateStatus?: (songId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  viewMode: 'grid' | 'list';
}

export function SongListItem({ song, onDelete, onUpdateStatus, viewMode }: SongListItemProps) {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);
  const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] = useState(false);
  const { playSong, addToQueue, currentSong } = useAudioPlayerStore();
  const isCurrentSong = song.id === currentSong?.id;

  const workspaceUrl = `/ai-composer/workspace/${song.id}`;
  const artistDisplay = song.profiles?.display_name || song.profiles?.username || song.artist_name || "Artiste inconnu";

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le morceau \"${song.title || 'sans titre'}\" ?`)) {
      onDelete();
    }
  };

  const handlePlay = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    playSong({ ...song, artist_name: artistDisplay });
  };

  const renderDropdownMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full" onClick={(e) => {e.stopPropagation(); e.preventDefault();}}>
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Options</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" onClick={(e) => {e.stopPropagation(); e.preventDefault();}}>
        <DropdownMenuItem onClick={() => router.push(workspaceUrl)}><Wand2 className="mr-2 h-4 w-4" /> Ouvrir dans l'AI Composer</DropdownMenuItem>
        <DropdownMenuItem onClick={() => router.push(`/manage-songs/${song.id}/edit`)}><Edit3 className="mr-2 h-4 w-4" /> Gérer les métadonnées</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handlePlay}><PlayCircle className="mr-2 h-4 w-4" /> Écouter</DropdownMenuItem>
        <DropdownMenuItem onClick={() => addToQueue({ ...song, artist_name: artistDisplay })}><ListPlus className="mr-2 h-4 w-4" /> Ajouter à la file</DropdownMenuItem>
        <DropdownMenuItem onClick={() => setIsAddToPlaylistModalOpen(true)}><ListPlus className="mr-2 h-4 w-4" /> Ajouter à une playlist</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10"><Trash2 className="mr-2 h-4 w-4" /> Supprimer</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  if (viewMode === 'list') {
    return (
      <div className="flex items-center gap-4 p-2 rounded-lg hover:bg-muted/50 transition-colors w-full cursor-pointer" onClick={() => router.push(workspaceUrl)}>
        <div className="flex-shrink-0">
          <div className="relative w-12 h-12">
            {song.cover_art_url ? (
              <Image src={song.cover_art_url} alt={song.title ?? 'Pochette'} layout="fill" className="object-cover rounded-md"/>
            ) : (
              <div className="w-full h-full bg-muted rounded-md flex items-center justify-center">
                <Music2 className="w-6 h-6 text-muted-foreground" />
              </div>
            )}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <p className="font-semibold truncate" title={song.title}>{song.title}</p>
          <p className="text-sm text-muted-foreground truncate" title={artistDisplay}>{artistDisplay}</p>
        </div>
        <div className="hidden md:block text-sm text-muted-foreground w-48 truncate">
          {song.albums?.title}
        </div>
        <div className="hidden lg:block text-sm text-muted-foreground w-40">
          {song.created_at ? formatDistanceToNow(new Date(song.created_at), { addSuffix: true, locale: fr }) : ''}
        </div>
        <div className="flex items-center gap-1">
                    {!isCurrentSong ? (
            <Button onClick={handlePlay} size="icon" variant="ghost" className="h-8 w-8 rounded-full"><PlayCircle className="h-5 w-5" /></Button>
          ) : (
            <div className="h-8 w-8 flex items-center justify-center rounded-full bg-muted">
              <Music2 className="h-4 w-4 text-primary" />
            </div>
          )}
          {renderDropdownMenu()}
        </div>
      </div>
    );
  }

  // Grid View (default)
  return (
    <Link href={workspaceUrl} className="group relative flex flex-col h-full bg-card rounded-lg border shadow-sm overflow-hidden transition-all duration-200 ease-in-out hover:shadow-lg hover:border-primary/50 transform hover:-translate-y-1">
      <div className="relative w-full aspect-square">
        {song.cover_art_url || song.albums?.cover_url ? (
          <Image src={song.cover_art_url || song.albums.cover_url} alt={song.title ?? 'Pochette'} layout="fill" className="object-cover"/>
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center">
            <Music2 className="w-1/3 h-1/3 text-muted-foreground" />
          </div>
        )}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          {!isCurrentSong ? (
            <Button 
              onClick={handlePlay} 
              size="icon" 
              className="h-14 w-14 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full text-white"
            >
              <PlayCircle className="h-8 w-8" />
            </Button>
          ) : (
            <div className="h-14 w-14 flex items-center justify-center rounded-full bg-black/50 backdrop-blur-sm">
              <Music2 className="h-8 w-8 text-white animate-pulse" />
            </div>
          )}
        </div>
        <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {renderDropdownMenu()}
        </div>
      </div>
      <div className="p-3 flex-grow flex flex-col justify-between">
        <div>
          <p className="font-semibold truncate" title={song.title}>{song.title}</p>
          <p className="text-sm text-muted-foreground truncate" title={artistDisplay}>{artistDisplay}</p>
        </div>
      </div>
    </Link>
  );
}
