CREATE OR REPLACE FUNCTION public.get_dashboard_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_profile JSONB;
    v_tracks_summary JSONB;
    v_albums_summary JSONB;
    v_total_plays BIGINT;
    v_total_views BIGINT;
    v_total_likes BIGINT;
    v_total_followers BIGINT;
    v_daily_plays JSONB;
    v_weekly_plays JSONB;
    v_monthly_plays JSONB;
    v_recent_tracks JSONB;
    v_recent_albums JSONB;
    v_top_tracks JSONB;
    v_recent_comments JSONB;
    v_recent_followers JSONB;
    v_highlight_track JSONB;
    v_result JSONB;
BEGIN
    -- 1. Profil utilisateur
    SELECT jsonb_build_object(
        'id', id,
        'username', username,
        'display_name', display_name,
        'avatar_url', profile_picture_url,
        'bio', bio,
        'website', website_url,
        'location', location,
        'coins_balance', 0
    )
    INTO v_profile
    FROM public.profiles
    WHERE id = p_user_id;

    -- 2. R<PERSON>um<PERSON> des morceaux
    SELECT jsonb_build_object(
        'total_count', COUNT(*),
        'published_count', COUNT(*) FILTER (WHERE visibility = 'public')
    )
    INTO v_tracks_summary
    FROM public.tracks
    WHERE creator_user_id = p_user_id AND is_archived = false;

    -- 3. Résumé des albums
    SELECT jsonb_build_object(
        'total_count', COUNT(*),
        'published_count', COUNT(*) FILTER (WHERE visibility = 'public')
    )
    INTO v_albums_summary
    FROM public.albums
    WHERE creator_user_id = p_user_id AND is_archived = false;

    -- 4. Total des écoutes (approximation)
    SELECT 0
    INTO v_total_plays;

    -- 5. Total des vues (approximation)
    SELECT 0
    INTO v_total_views;

    -- 6. Total des likes
    SELECT COUNT(*)
    INTO v_total_likes
    FROM public.likes
    WHERE (track_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id))
       OR (album_id IN (SELECT id FROM public.albums WHERE creator_user_id = p_user_id));

    -- 7. Total des followers
    SELECT COUNT(*)
    INTO v_total_followers
    FROM public.follows
    WHERE following_id = p_user_id;

    -- 8. Morceaux récents
    SELECT jsonb_agg(jsonb_build_object(
        'id', t.id,
        'title', t.title,
        'cover_url', t.cover_art_url,
        'genres', t.genres,
        'plays', 0, -- Placeholder
        'created_at', t.created_at,
        'updated_at', t.updated_at,
        'status', CASE WHEN t.visibility = 'public' THEN 'published' ELSE 'draft' END
    ) ORDER BY t.created_at DESC NULLS LAST)
    INTO v_recent_tracks
    FROM public.tracks t
    WHERE t.creator_user_id = p_user_id AND t.is_archived = false
    LIMIT 5;

    -- 9. Albums récents
    SELECT jsonb_agg(jsonb_build_object(
        'id', a.id,
        'title', a.title,
        'cover_url', a.album_art_url,
        'genres', '[]'::jsonb, -- Albums don't have genres in this schema
        'created_at', a.created_at,
        'updated_at', a.updated_at,
        'status', CASE WHEN a.visibility = 'public' THEN 'published' ELSE 'draft' END
    ) ORDER BY a.created_at DESC NULLS LAST)
    INTO v_recent_albums
    FROM public.albums a
    WHERE a.creator_user_id = p_user_id AND a.is_archived = false
    LIMIT 5;

    -- 10. Morceaux les plus écoutés (approximation)
    SELECT jsonb_agg(jsonb_build_object(
        'id', t.id,
        'title', t.title,
        'cover_url', t.cover_art_url,
        'genres', t.genres,
        'plays', 0, -- Placeholder
        'created_at', t.created_at
    ) ORDER BY t.created_at DESC NULLS LAST)
    INTO v_top_tracks
    FROM public.tracks t
    WHERE t.creator_user_id = p_user_id AND t.is_archived = false
    LIMIT 5;

    -- 11. Métriques quotidiennes (30 derniers jours)
    WITH days_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '29 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day_date
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'date', TO_CHAR(ds.day_date, 'YYYY-MM-DD'),
            'plays', 0,
            'views', 0
        ) ORDER BY ds.day_date ASC
    )
    INTO v_daily_plays
    FROM days_series ds;

    -- 12. Métriques hebdomadaires (12 dernières semaines)
    WITH weeks_series AS (
        SELECT generate_series(
            date_trunc('week', NOW() - INTERVAL '11 weeks'),
            date_trunc('week', NOW()),
            INTERVAL '1 week'
        ) AS week_date
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'date', TO_CHAR(ws.week_date, 'YYYY-MM-DD'),
            'plays', 0,
            'views', 0
        ) ORDER BY ws.week_date ASC
    )
    INTO v_weekly_plays
    FROM weeks_series ws;

    -- 13. Métriques mensuelles (12 derniers mois)
    WITH months_series AS (
        SELECT generate_series(
            date_trunc('month', NOW() - INTERVAL '11 months'),
            date_trunc('month', NOW()),
            INTERVAL '1 month'
        ) AS month_date
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'date', TO_CHAR(ms.month_date, 'YYYY-MM-DD'),
            'plays', 0,
            'views', 0
        ) ORDER BY ms.month_date ASC
    )
    INTO v_monthly_plays
    FROM months_series ms;

    -- Construction du JSON final
    v_result := jsonb_build_object(
        'userProfile', COALESCE(v_profile, '{}'::jsonb),
        'tracks_summary', COALESCE(v_tracks_summary, '{}'::jsonb),
        'albums_summary', COALESCE(v_albums_summary, '{}'::jsonb),
        'totalPlays', COALESCE(v_total_plays, 0),
        'totalViews', COALESCE(v_total_views, 0),
        'totalLikes', COALESCE(v_total_likes, 0),
        'totalFollowers', COALESCE(v_total_followers, 0),
        'daily_plays_for_dashboard', COALESCE(v_daily_plays, '[]'::jsonb),
        'weekly_plays_for_dashboard', COALESCE(v_weekly_plays, '[]'::jsonb),
        'monthly_plays_for_dashboard', COALESCE(v_monthly_plays, '[]'::jsonb),
        'recentTracks', COALESCE(v_recent_tracks, '[]'::jsonb),
        'recentAlbums', COALESCE(v_recent_albums, '[]'::jsonb),
        'topTracks', COALESCE(v_top_tracks, '[]'::jsonb),
        'recentComments', COALESCE(v_recent_comments, '[]'::jsonb),
        'recentFollowers', COALESCE(v_recent_followers, '[]'::jsonb),
        'highlightTrack', COALESCE(v_highlight_track, '{}'::jsonb)
    );

    RETURN v_result;
END;
$$;
