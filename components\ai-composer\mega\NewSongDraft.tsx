'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { EnhancedLyricsEditor } from '@/components/enhanced-lyrics-editor/EnhancedLyricsEditor';
import { createSong } from '@/lib/actions/song.actions';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export function NewSongDraft() {
  const [lyrics, setLyrics] = useState('');
  const [chords, setChords] = useState('[]'); // Store chords as a JSON string
  const [title, setTitle] = useState('Nouveau titre');
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const result = await createSong({
        title,
        lyrics,
        chords,
        // On peut ajouter d'autres champs par défaut ici
      });

      if (result.success && result.data) {
        toast({
          title: 'Chanson créée !',
          description: `Votre chanson "${title}" a été sauvegardée.`,
        });
        // Rediriger vers la page de la nouvelle chanson
        router.push(`/ai-composer-workspace/${result.data.id}`);
      } else {
        if ('error' in result && typeof result.error === 'string') {
          throw new Error(result.error);
        } else {
          throw new Error('Une erreur inconnue est survenue lors de la création de la chanson.');
        }
      }
    } catch (error: any) {
      console.error('Failed to create song:', error);
      toast({
        title: 'Erreur lors de la création',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4 h-full flex flex-col gap-4">
        <h2 className="text-2xl font-bold">Créer une nouvelle chanson</h2>
        <input 
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Titre de la chanson"
            className="text-lg p-2 border rounded-md bg-transparent"
        />
      <div className="flex-grow h-full">
        <EnhancedLyricsEditor
          value={lyrics}
          onChange={setLyrics}
          chords={JSON.parse(chords)}
          onChordsChange={(newChords) => setChords(JSON.stringify(newChords))}
        />
      </div>
      <div className="flex-shrink-0">
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Enregistrer la nouvelle chanson
        </Button>
      </div>
    </div>
  );
}
