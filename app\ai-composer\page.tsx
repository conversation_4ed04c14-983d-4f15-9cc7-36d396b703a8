import { createSupabaseServerClient } from '@/lib/supabase/server';
import AIComposerClient from './ai-composer-client';
import { redirect } from 'next/navigation';
import type { Song } from '@/components/songs/song-schema';

// Use the full Song type for maximum compatibility
export type ComposerSong = Song;

export default async function AIComposerRootPage() {
  const supabase = createSupabaseServerClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login?message=You must be logged in to use the AI Composer.');
  }

  const { data: songs, error } = await supabase
    .from('songs')
    .select('*, profiles(display_name, username), albums(id, title, cover_url)') 
    .eq('creator_user_id', user.id)
    .order('updated_at', { ascending: false });

  if (error) {
    console.error('Error fetching songs for AI Composer:', error);
    // On peut passer l'erreur au composant client pour afficher un message
  }

  return <AIComposerClient songs={songs || []} user={user} />;
}
