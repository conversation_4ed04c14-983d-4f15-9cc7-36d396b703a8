"use client";

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from 'next-themes';
import { createBrowserClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User } from '@supabase/supabase-js';
import { Profile } from '@/types';
import { Image, X } from 'lucide-react';

const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

const themeFormSchema = z.object({
  theme: z.enum(['light', 'dark', 'system'], {
    required_error: 'Veuillez sélectionner un thème.',
  }),
  background_image: z
    .custom<FileList>()
    .refine((files) => !files || files.length <= 1, 'Vous ne pouvez télécharger qu_un seul fichier.')
    .refine((files) => !files || files.length === 0 || files[0].size <= MAX_FILE_SIZE, `La taille du fichier ne doit pas dépasser 2 Mo.`)
    .refine(
      (files) => !files || files.length === 0 || ACCEPTED_IMAGE_TYPES.includes(files[0].type),
      'Seuls les formats .jpg, .png et .webp sont acceptés.'
    )
    .optional(),
});

type ThemeFormValues = z.infer<typeof themeFormSchema>;

interface ProfileThemeTabProps {
  user: User;
  profile: Profile | null;
}

export function ProfileThemeTab({ user, profile }: ProfileThemeTabProps) {
  const supabase = createBrowserClient();
  const { setTheme } = useTheme();
  const [preview, setPreview] = useState<string | null>(null);
  const [currentBackground, setCurrentBackground] = useState<string | null>(profile?.background_url || null);
  const [isUploading, setIsUploading] = useState(false);

  const form = useForm<ThemeFormValues>({
    resolver: zodResolver(themeFormSchema),
    defaultValues: {
      theme: profile?.theme || 'system',
    },
  });

  const fileRef = form.register('background_image');

  useEffect(() => {
    if (currentBackground) {
      document.body.style.backgroundImage = `url(${currentBackground})`;
      document.body.style.backgroundSize = 'cover';
      document.body.style.backgroundPosition = 'center';
      document.body.style.backgroundAttachment = 'fixed';
    } else {
      document.body.style.backgroundImage = '';
    }
  }, [currentBackground]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
    }
  };

  const handleRemoveImage = async () => {
    setPreview(null);
    setCurrentBackground(null);
    form.resetField('background_image');

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ background_url: null })
        .eq('id', user.id);

      if (error) throw error;
      toast.success('Image de fond supprimée',
        { description: 'Votre image de fond a été réinitialisée.' }
      );
    } catch (error: any) {
      toast.error('Erreur',
        { description: `Une erreur est survenue : ${error.message}` }
      );
    }
  };

  async function onSubmit(data: ThemeFormValues) {
    setIsUploading(true);
    try {
      let backgroundUrl = currentBackground;

      if (data.background_image && data.background_image.length > 0) {
        const file = data.background_image[0];
        const filePath = `${user.id}/${Date.now()}-${file.name}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('theme-assets')
          .upload(filePath, file, { upsert: true });

        if (uploadError) throw uploadError;

        const { data: urlData } = supabase.storage.from('theme-assets').getPublicUrl(uploadData.path);
        backgroundUrl = urlData.publicUrl;
      }

      const { error: profileError } = await supabase
        .from('profiles')
        .update({ theme: data.theme, background_url: backgroundUrl })
        .eq('id', user.id);

      if (profileError) throw profileError;

      setTheme(data.theme);
      if (backgroundUrl) {
        setCurrentBackground(backgroundUrl);
      }
      setPreview(null);
      form.resetField('background_image');

      toast.success('Préférences mises à jour',
        { description: 'Votre thème et votre image de fond ont été mis à jour.' }
      );
    } catch (error: any) {
      toast.error('Erreur lors de la mise à jour',
        { description: error.message }
      );
    } finally {
      setIsUploading(false);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Thème & Apparence</CardTitle>
        <CardDescription>
          Personnalisez l_apparence de l_application. Choisissez un thème et une image de fond.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="theme"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Thème</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="light" />
                        </FormControl>
                        <FormLabel className="font-normal">Clair</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="dark" />
                        </FormControl>
                        <FormLabel className="font-normal">Sombre</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="system" />
                        </FormControl>
                        <FormLabel className="font-normal">Système</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="background_image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image de fond</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-20 w-20 rounded-md">
                        <AvatarImage src={preview || currentBackground || ''} asChild>
                          {preview || currentBackground ? <img src={preview || currentBackground || ''} className="object-cover"/> : <Image className="h-10 w-10 text-muted-foreground"/>}
                        </AvatarImage>
                        <AvatarFallback className="rounded-md flex items-center justify-center">
                          <Image className="h-10 w-10 text-muted-foreground"/>
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col gap-2">
                        <Input 
                          type="file" 
                          {...fileRef} 
                          onChange={handleFileChange} 
                          className="max-w-xs"
                          disabled={isUploading}
                        />
                        {currentBackground && (
                          <Button variant="outline" size="sm" onClick={handleRemoveImage} className="w-fit" type="button">
                            <X className="h-4 w-4 mr-2"/>
                            Réinitialiser le fond
                          </Button>
                        )}
                      </div>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Téléchargez une image pour personnaliser votre arrière-plan. Max 2Mo.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isUploading}>
              {isUploading ? 'Mise à jour...' : 'Mettre à jour les préférences'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}