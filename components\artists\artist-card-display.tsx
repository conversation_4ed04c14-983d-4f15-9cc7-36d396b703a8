"use client";

import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge"; // Added Badge import
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/hooks/use-toast';
import { UserPlus, UserCheck, Loader2, Globe, MapPin, Info as InfoIcon } from 'lucide-react'; // Added Globe, MapPin, InfoIcon

interface ArtistProfile {
  id: string; // User ID of the artist
  username: string | null;
  display_name: string | null;
  avatar_url: string | null;
  bio?: string | null;
  website?: string | null;
  location_city?: string | null;
  location_country?: string | null;
  genres?: string[] | null; // Added genres
  // social_links?: any; // JSONB, handle later if needed
}

interface ArtistCardDisplayProps {
  artist: ArtistProfile | null;
}

export function ArtistCardDisplay({ artist }: ArtistCardDisplayProps) {
  const supabase = getSupabaseClient();
  const { user: currentUser } = useUser();

  // Debugging logs
  // console.log("ArtistCardDisplay - currentUser:", currentUser);
  // console.log("ArtistCardDisplay - artist prop:", artist);

  const [followerCount, setFollowerCount] = useState<number | null>(null);
  const [isFollowing, setIsFollowing] = useState<boolean | null>(null);
  const [isLoadingFollow, setIsLoadingFollow] = useState(false);
  const [showFullBio, setShowFullBio] = useState(false);

  const fetchFollowData = useCallback(async () => {
    if (!artist?.id) return;
    // setIsLoadingFollow(true); // Only set loading for follow action, not initial data load
    try {
      const { data: count, error: countError } = await supabase
        .rpc('get_follower_count', { artist_user_id: artist.id });
      if (countError) console.error("Error fetching follower count:", countError);
      else setFollowerCount(count);

      if (currentUser) {
        const { data: followingStatus, error: followingStatusError } = await supabase
          .rpc('is_following', { p_current_user_id: currentUser.id, p_target_artist_id: artist.id });
        if (followingStatusError) console.error("Error fetching following status:", followingStatusError);
        else setIsFollowing(followingStatus);
      } else {
        setIsFollowing(false);
      }
    } catch (error) {
      console.error("Error fetching follow data:", error);
      // toast({ title: "Erreur", description: "Impossible de charger les informations de suivi.", variant: "destructive" });
    } 
    // finally { setIsLoadingFollow(false); }
  }, [artist?.id, currentUser, supabase]);

  useEffect(() => {
    fetchFollowData();
  }, [fetchFollowData]);

  const handleFollowToggle = async () => {
    if (!currentUser || !artist?.id || isLoadingFollow) return;
    setIsLoadingFollow(true);
    
    const rpcName = isFollowing ? 'unfollow_artist' : 'follow_artist';
    const { error } = await supabase.rpc(rpcName, {
      p_follower_id: currentUser.id,
      p_following_id: artist.id
    });

    if (error) {
      console.error(`Error ${isFollowing ? 'unfollowing' : 'following'} artist:`, error);
      toast({ title: "Erreur", description: `L'action de suivi a échoué.`, variant: "destructive" });
    } else {
      setIsFollowing(!isFollowing);
      setFollowerCount(prev => {
        if (prev === null) return null;
        return isFollowing ? prev - 1 : prev + 1;
      });
      toast({ title: isFollowing ? "Vous ne suivez plus cet artiste." : "Artiste suivi !" });
    }
    setIsLoadingFollow(false);
  };

  if (!artist) {
    return null; 
  }

  const artistDisplayName = artist.display_name || artist.username || 'Artiste inconnu';
  const artistLink = `/artists/${artist.username || artist.id}`;
  const displayFollowerCount = followerCount !== null ? followerCount.toLocaleString() : '-';

  // Debugging log for button condition
  // if (artist) {
  //   console.log("ArtistCardDisplay - Button condition:", {
  //     currentUserExists: !!currentUser,
  //     artistIdExists: !!artist.id,
  //     isDifferentUser: currentUser ? currentUser.id !== artist.id : 'currentUser is null'
  //   });
  // }


  return (
    <Card>
      <CardHeader>
        <CardTitle>Artiste</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <Link href={artistLink}>
            <Avatar className="h-16 w-16">
              <AvatarImage src={artist.avatar_url || undefined} alt={artistDisplayName} />
              <AvatarFallback>{artistDisplayName.charAt(0).toUpperCase()}</AvatarFallback>
            </Avatar>
          </Link>
          <div className="flex-1 min-w-0"> {/* Added min-w-0 for better truncation */}
            <Link href={artistLink} className="font-semibold text-lg hover:underline truncate block">
              {artistDisplayName}
            </Link>
            <p className="text-sm text-muted-foreground">{displayFollowerCount} abonné{followerCount !== 1 ? 's' : ''}</p> 
            {artist.genres && artist.genres.length > 0 && (
              <div className="mt-1 flex flex-wrap gap-1">
                {artist.genres.slice(0, 3).map(genre => ( // Show max 3 genres
                  <Badge key={genre} variant="secondary" className="text-xs">{genre}</Badge>
                ))}
              </div>
            )}
          </div>
          {currentUser && artist && currentUser.id !== artist.id && (
             <Button 
                variant={isFollowing ? "secondary" : "outline"} 
                size="sm" 
                onClick={handleFollowToggle}
                disabled={isLoadingFollow || isFollowing === null}
              >
                {isLoadingFollow ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 
                  isFollowing ? <UserCheck className="mr-2 h-4 w-4" /> : <UserPlus className="mr-2 h-4 w-4" />
                }
                {isFollowing ? "Suivi" : "Suivre"}
              </Button>
          )}
        </div>
        
        {artist.bio && (
          <div className="mt-4">
            <p className={`text-sm text-muted-foreground ${!showFullBio && artist.bio.length > 100 ? 'line-clamp-2' : ''}`}>
              {artist.bio}
            </p>
            {artist.bio.length > 100 && (
              <Button variant="link" size="sm" className="p-0 h-auto text-xs" onClick={() => setShowFullBio(!showFullBio)}>
                {showFullBio ? 'Voir moins' : 'Voir plus'}
              </Button>
            )}
          </div>
        )}

        <div className="mt-3 space-y-1 text-sm text-muted-foreground">
          {(artist.location_city || artist.location_country) && (
            <div className="flex items-center">
              <MapPin className="mr-2 h-4 w-4 flex-shrink-0" /> 
              <span>{artist.location_city}{artist.location_city && artist.location_country ? ', ' : ''}{artist.location_country}</span>
            </div>
          )}
          {artist.website && (
            <div className="flex items-center">
              <Globe className="mr-2 h-4 w-4 flex-shrink-0" />
              <a href={artist.website.startsWith('http') ? artist.website : `https://${artist.website}`} target="_blank" rel="noopener noreferrer" className="hover:underline truncate">
                {artist.website}
              </a>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
