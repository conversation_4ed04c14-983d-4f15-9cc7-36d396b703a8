// lib/chords/utils.ts

import { ChordLibrary, ChordPosition, ChordVariation } from './types';

const CHORD_DATA_PATH = './'; // Assuming the JSON files are in the same directory

/**
 * Asynchronously loads a chord library for a specific instrument and tuning.
 * @param instrument The name of the instrument (e.g., 'guitar').
 * @param tuning The name of the tuning (e.g., 'standard_tuning').
 * @returns A Promise that resolves to the ChordLibrary object.
 */
export async function loadChordLibrary(instrument: string, tuning: string): Promise<ChordLibrary> {
  try {
    const fileName = `${instrument}_${tuning}.json`;
    const library = await import(`${CHORD_DATA_PATH}${fileName}`);
    return library.default as ChordLibrary;
  } catch (error) {
    console.error(`Failed to load chord library for ${instrument} (${tuning}):`, error);
    throw new Error(`Could not load chord library for ${instrument} (${tuning}).`);
  }
}

/**
 * Finds a specific chord variation from a loaded library.
 * @param library The ChordLibrary object.
 * @param root The root note of the chord (e.g., 'C', 'G#').
 * @param suffix The chord suffix (e.g., 'major', 'minor', 'dim7').
 * @returns The ChordVariation object or undefined if not found.
 */
export function findChord(library: ChordLibrary, root: string, suffix: string): ChordVariation | undefined {
  const rootChords = library.chords[root];
  if (!rootChords) {
    return undefined;
  }
  return rootChords.find(variation => variation.suffix.toLowerCase() === suffix.toLowerCase());
}
