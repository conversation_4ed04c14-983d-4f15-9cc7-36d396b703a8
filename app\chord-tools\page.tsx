'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Music } from 'lucide-react';

export default function ChordToolsPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Outils d'Accords</h1>
        <p className="text-muted-foreground">
          Explorez et créez des progressions d'accords avec nos outils avancés.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              Générateur d'Accords
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              <PERSON><PERSON><PERSON><PERSON> des progressions d'accords automatiquement selon votre style musical.
            </p>
            <Button className="w-full">
              Commencer
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              Bibliothèque d'Accords
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Explorez notre vaste collection d'accords pour tous les instruments.
            </p>
            <Button className="w-full" variant="outline">
              Explorer
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              Analyseur d'Harmonie
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Analysez vos progressions et obtenez des suggestions d'amélioration.
            </p>
            <Button className="w-full" variant="outline">
              Analyser
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}