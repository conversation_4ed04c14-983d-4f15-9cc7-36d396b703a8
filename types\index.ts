// This file contains centralized type definitions for the Mouvik application.

export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface SocialLink {
  platform: string;
  url: string;
}

export interface InstrumentPlayed {
  name: string;
  experience_years: number | null;
}

export type AiUsageLevel = 'none' | 'low' | 'medium' | 'high';

export interface Profile {
  id: string;
  created_at: string;
  username: string | null;
  display_name: string | null;
  avatar_url: string | null;
  header_url: string | null;
  bio: string | null;
  location_city: string | null;
  location_country: string | null;
  website: string | null;
  genres: string[] | null;
  influences: string[] | null;
  tags: string[] | null;
  social_links: SocialLink[] | null;
  role_primary: string | null;
  roles_secondary: string[] | null;
  instruments_played: InstrumentPlayed[] | null;
  primary_daw: string | null;
  other_daws: string[] | null;
  equipment: string | null;
  is_profile_public: boolean;
  open_to_collab: boolean;
  ai_usage_level: AiUsageLevel | null;
  theme: 'light' | 'dark' | 'system' | null;
  background_url: string | null;
  // Fields that might exist from older versions or other parts of the app
  subscription_tier?: any;
  user_role?: any;
  status_badges?: string[] | null;
  main_instruments?: string[] | null;
  operating_systems?: string[] | null;
  ai_usage_percent?: number | null;
  ai_tools?: string[] | null;
  coins_balance?: number;
  record_label?: string | null;
  spoken_languages?: string[] | null;
  like_count?: number;
  dislike_count?: number;
  view_count?: number;
}



export interface UserProfile {
  id: string;
  username: string | null;
  display_name: string | null;
  avatar_url: string | null;
  bio?: string | null; // Added for featured artist bio
  // Add other profile fields as needed by cards/views
}


export interface Album {
  id: string;
  title: string;
  cover_url: string | null;
  user_id: string; // Creator of the album
  artist_name?: string; // Denormalized or joined
  release_date?: string | null;
  profiles?: UserProfile | null; // Creator profile data
  created_at?: string; // Added for sorting
  updated_at?: string; // Added for consistency
  is_public?: boolean; // Added for LED indicator and toggling
  slug?: string | null; // Added for potential public album pages
  description?: string | null; // Added description
  genre?: string[] | null; // Column name is 'genre' (singular) but type is ARRAY
  moods?: string[] | null; // Column name is 'moods' (plural), type ARRAY
  instrumentation?: string[] | null; // Column name is 'instrumentation' (singular), type ARRAY
  // Add other album fields
  like_count?: number | null;
  is_liked_by_current_user?: boolean | null;
  follower_count?: number | null;
  is_followed_by_current_user?: boolean | null;
  view_count?: number | null;
  plays?: number | null;
  total_duration_ms?: number;
}

export interface Band {
  id: string;
  name: string;
  avatar_url: string | null;
  cover_url?: string | null; // from schema inspection
  creator_id: string; 
  bio?: string | null; // Added for featured band bio
  description?: string | null;
  location?: string | null;
  banner_url?: string | null;
  created_at?: string;
  updated_at?: string;
  genres?: string[] | null; // from schema inspection
  moods?: string[] | null;
  instrumentation?: string[] | null;
  countries?: string[] | null;
  slug?: string | null; // from schema inspection
  is_public?: boolean; // from schema inspection
  are_comments_public?: boolean;
  follower_count?: number | null; // from schema inspection
  // Add other band fields
}

export interface BandMember {
  id: string; // band_members table's own id
  band_id: string;
  user_id: string;
  role: string | null;
  is_admin?: boolean;
  joined_at?: string;
  permissions?: string[] | null;
  profiles?: UserProfile | null; // Joined user profile data
}

export interface Playlist {
  id: string;
  name: string;
  cover_art_url: string | null;
  user_id: string; // Creator of the playlist
  is_public: boolean;
  created_at: string;
  songs_count?: number; 
  profiles?: UserProfile | null; 
  slug?: string | null; // Added for public links
  genres?: string[] | null; // Added for display
  moods?: string[] | null; // Added for display
  instrumentation?: string[] | null; // Already present in PlaylistFromView, ensure it's here too
  view_count?: number;
  like_count?: number;
  follower_count?: number;
  plays?: number;
  is_liked_by_current_user?: boolean | null;
  is_followed_by_current_user?: boolean | null;
}

// For Plan Limits hook
export interface PlanLimitsData {
  tier: 'free' | 'pro' | 'studio';
  uploads_per_month: number | null;
  vault_space_gb: number | null;
  vault_max_files: number | null;
  ia_credits_month: number | null;
  coins_month: number | null;
  page_customisation: boolean;
  analytics_level: 'basic' | 'extended' | 'pro';
  max_playlists: number | null;
  max_friends: number | null;
}

export interface UserUsageStats {
  monthly_uploads_count: number;
  vault_total_files: number;
  vault_total_size_bytes: number;
  // Add other stats as needed
}

// Type for song data used in AudioContext and players
export type AudioContextSong = Pick<Song, 'id' | 'title' | 'audio_url' | 'cover_url' | 'duration'> & {
  artist?: string; // Simplified artist display name
};

// Type for items in the audio queue
export interface QueueItem extends AudioContextSong {
  queueItemId: string; // Unique ID for this instance in the queue
}
