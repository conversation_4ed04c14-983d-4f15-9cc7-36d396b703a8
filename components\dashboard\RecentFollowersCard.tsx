"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, Clock } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

interface Follower {
  id: string
  follower_id: string
  followed_profile_id: string
  created_at: string
  follower_username: string
  follower_avatar_url: string | null
  follower_display_name: string | null
}

interface RecentFollowersCardProps {
  recentFollowers: Follower[]
}

export function RecentFollowersCard({ recentFollowers }: RecentFollowersCardProps) {
  // Fonction pour formater la date en "il y a X jours/heures"
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true,
        locale: fr
      })
    } catch (error) {
      return "Date inconnue"
    }
  }

  // Fonction pour obtenir les initiales à partir du nom d'utilisateur
  const getInitials = (username: string) => {
    return username.charAt(0).toUpperCase()
  }

  // Fonction pour obtenir le nom à afficher (display_name ou username)
  const getDisplayName = (follower: Follower) => {
    return follower.follower_display_name || follower.follower_username || "Utilisateur"
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-md font-medium flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Nouveaux abonnés
            </CardTitle>
            <CardDescription>Les personnes qui vous suivent récemment</CardDescription>
          </div>
          {recentFollowers.length > 0 && (
            <Button variant="outline" size="sm" asChild>
              <Link href="/profile/followers">
                Voir tout
              </Link>
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {recentFollowers && recentFollowers.length > 0 ? (
          <div className="space-y-4">
            {recentFollowers.map((follower) => (
              <div key={follower.id} className="flex items-center space-x-4">
                <Avatar>
                  <AvatarImage src={follower.follower_avatar_url || ""} alt={follower.follower_username} />
                  <AvatarFallback>{getInitials(follower.follower_username)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center">
                    <p className="text-sm font-medium">{getDisplayName(follower)}</p>
                    <span className="text-xs text-muted-foreground ml-2">@{follower.follower_username}</span>
                  </div>
                  <p className="text-xs text-muted-foreground flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatDate(follower.created_at)}
                  </p>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/profile/${follower.follower_username}`}>
                    Profil
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            <p>Aucun nouvel abonné récent</p>
            <p className="text-xs mt-1">Partagez votre musique pour attirer plus d'abonnés</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
