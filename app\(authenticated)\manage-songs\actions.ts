"use server";

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';

export async function toggleSongVisibility(songId: string, currentIsPublic: boolean) {
  const supabase = createSupabaseServerClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: 'Authentication required.' };
  }

  // First, verify the user owns the song
  const { data: song, error: ownerError } = await supabase
    .from('songs')
    .select('id')
    .eq('id', songId)
    .eq('creator_user_id', user.id)
    .single();

  if (ownerError || !song) {
    return { error: 'Song not found or you do not have permission to edit it.' };
  }

  // Now, perform the update
  const { error: updateError } = await supabase
    .from('songs')
    .update({ is_public: !currentIsPublic })
    .eq('id', songId);

  if (updateError) {
    return { error: `Failed to update song: ${updateError.message}` };
  }

  // Revalidate the path to ensure the UI updates with fresh data
  revalidatePath('/manage-songs');

  return { success: true, newIsPublic: !currentIsPublic };
}
