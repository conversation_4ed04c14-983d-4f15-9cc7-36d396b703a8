-- Fonction pour obtenir les statistiques par genre pour un utilisateur donné et une période donnée
CREATE OR REPLACE FUNCTION get_genre_performance(p_user_id UUID, p_time_range TEXT)
RETURNS TABLE (
  genre TEXT,
  song_count BIGINT,
  total_plays BIGINT,
  total_likes BIGINT
)
LANGUAGE plpgsql
AS $$
DECLARE
  v_start_date TIMESTAMP;
  v_end_date TIMESTAMP;
BEGIN
  -- Déterminer la période
  v_end_date := NOW();
  IF p_time_range = '7d' THEN
    v_start_date := v_end_date - INTERVAL '7 days';
  ELSIF p_time_range = '30d' THEN
    v_start_date := v_end_date - INTERVAL '30 days';
  ELSIF p_time_range = '90d' THEN
    v_start_date := v_end_date - INTERVAL '90 days';
  ELSIF p_time_range = '1y' THEN
    v_start_date := v_end_date - INTERVAL '1 year';
  ELSE -- Par défaut, ou si 'all' est passé (à définir)
    v_start_date := '1970-01-01'::TIMESTAMP; -- Ou une autre date de début pour "tout le temps"
  END IF;

  RETURN QUERY
  WITH user_songs AS (
    SELECT id, title, genres
    FROM songs
    WHERE creator_user_id = p_user_id
  ),
  song_plays_in_period AS (
    SELECT
      p.song_id,
      COUNT(p.id) AS plays_count
    FROM plays p
    JOIN user_songs us ON p.song_id = us.id
    WHERE p.created_at BETWEEN v_start_date AND v_end_date
    GROUP BY p.song_id
  ),
  song_likes_in_period AS (
    SELECT
      l.resource_id AS song_id,
      COUNT(l.id) AS likes_count
    FROM likes l
    JOIN user_songs us ON l.resource_id = us.id
    WHERE l.resource_type = 'song' AND l.created_at BETWEEN v_start_date AND v_end_date
    GROUP BY l.resource_id
  )
  SELECT
    g.genre_name AS genre,
    COUNT(DISTINCT us.id) AS song_count,
    COALESCE(SUM(spp.plays_count), 0)::BIGINT AS total_plays,
    COALESCE(SUM(slp.likes_count), 0)::BIGINT AS total_likes
  FROM
    user_songs us
  CROSS JOIN LATERAL unnest(us.genres) AS g(genre_name) -- Exploser le tableau des genres
  LEFT JOIN song_plays_in_period spp ON us.id = spp.song_id
  LEFT JOIN song_likes_in_period slp ON us.id = slp.song_id
  GROUP BY g.genre_name
  ORDER BY total_plays DESC;

END;
$$;

-- Commentaire sur la fonction
COMMENT ON FUNCTION get_genre_performance(UUID, TEXT) IS 'Récupère les statistiques de performance (nombre de morceaux, écoutes, likes) agrégées par genre pour un utilisateur spécifique et une période donnée.';

-- Exemple d'appel :
-- SELECT * FROM get_genre_performance(' VOTRE_USER_ID_ICI ', '30d');
