"use client";

import React, { useState, useEffect, useRef } from 'react';
import { FilePond, registerPlugin } from 'react-filepond';
import 'filepond/dist/filepond.min.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import { getSupabaseClient } from '@/lib/supabase/client'; // Assuming you have this
import { useUser } from '@/contexts/user-context'; // Assuming you have this
import { toast } from '@/hooks/use-toast'; // Assuming you have this

// Register the plugins
registerPlugin(
  FilePondPluginImagePreview,
  FilePondPluginFileValidateType,
  FilePondPluginFileValidateSize
);

interface AlbumGalleryUploaderProps {
  initialFileUrls?: string[];
  onUpdate: (urls: string[]) => void;
  bucketName: string;
  maxFiles?: number;
  maxFileSize?: string; // e.g., "3MB"
}

interface FilePondProcessResponse {
  id: string; // Supabase storage path
  // any other fields Supabase might return or you want to track
}

export function AlbumGalleryUploader({
  initialFileUrls = [],
  onUpdate,
  bucketName,
  maxFiles = 12,
  maxFileSize = '5MB',
}: AlbumGalleryUploaderProps) {
  const supabase = getSupabaseClient();
  const { user } = useUser();
  const [files, setFiles] = useState<any[]>([]); // FilePond's file items
  const pondRef = useRef<FilePond | null>(null);

  useEffect(() => {
    console.log('[AlbumGalleryUploader] Initializing with initialFileUrls:', initialFileUrls);
    const initialPondFiles = initialFileUrls
      .filter(url => typeof url === 'string' && url.trim() !== '') 
      .map(url => ({
        source: url,
        options: {
          type: 'local', 
          metadata: {
            originalUrl: url, 
          },
        },
      }));
    console.log('[AlbumGalleryUploader] Setting initial FilePond files (on mount):', initialPondFiles);
    // Only set files if there are initial URLs and the current files state is empty,
    // to prevent wiping user-added files on re-renders if initialFileUrls hasn't changed.
    // This effect now runs only on mount.
    if (initialPondFiles.length > 0) {
        setFiles(initialPondFiles);
    }
  }, []); // Empty dependency array: runs only on mount


  const handleProcessFile = async (
    fieldName: any,
    file: any, // Temporarily back to any to suppress TS error and test process call
    metadata: any,
    load: (p: string | { id: string }) => void,
    error: (errorText: string) => void,
    progress: (computable: boolean, loaded: number, total: number) => void,
    abort: () => void
  ): Promise<void> => {
    console.log('[AlbumGalleryUploader] handleProcessFile started for:', file.name);
    if (!user) {
      console.error('[AlbumGalleryUploader] User not authenticated for upload.');
      error('User not authenticated');
      toast({ title: "Erreur", description: "Utilisateur non authentifié.", variant: "destructive" });
      return;
    }

    const fileExt = file.name.split('.').pop();
    // Use crypto.randomUUID for more robust unique filenames
    const randomName = crypto.randomUUID();
    const fileName = `${user.id}-${randomName}.${fileExt}`;
    const filePath = `${fileName}`; 

    progress(true, 0, file.size);

    const { data, error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true, // Changed to true to allow overwriting if a name collision somehow occurs, or for retries
      });

    if (uploadError) {
      console.error('[AlbumGalleryUploader] Error uploading to Supabase Storage:', uploadError);
      error('Upload failed: ' + uploadError.message);
      toast({ title: "Échec du téléversement", description: uploadError.message, variant: "destructive" });
      return;
    }

    if (data) {
      console.log('[AlbumGalleryUploader] Upload successful, Supabase data:', data);
      const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(data.path);
      if (urlData?.publicUrl) {
        console.log('[AlbumGalleryUploader] Public URL generated:', urlData.publicUrl);
        load(urlData.publicUrl); 
      } else {
        console.warn("[AlbumGalleryUploader] Uploaded to Supabase, but couldn't get public URL. Using path as serverId:", data.path);
        load(data.path); 
      }
    } else {
      console.error('[AlbumGalleryUploader] Upload completed but no data returned from Supabase.');
      error('Upload completed but no data returned from Supabase.');
    }
  };
  
  const handleUpdateFiles = (fileItems: any[]) => {
    console.log('[AlbumGalleryUploader] handleUpdateFiles, fileItems:', fileItems);
    setFiles(fileItems); 

    const currentUrls = fileItems.map(fileItem => {
      let url = null;
      if (fileItem.status === 5 && fileItem.serverId) { // FileStatus.PROCESSING_COMPLETE = 5
        if (String(fileItem.serverId).startsWith('http')) {
          url = String(fileItem.serverId);
        } else {
          const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(String(fileItem.serverId));
          console.log(`[AlbumGalleryUploader] Constructed URL for serverId path ${fileItem.serverId}:`, urlData?.publicUrl);
          url = urlData?.publicUrl || null;
        }
      } else if (fileItem.origin === 3 /* FileOrigin.LOCAL */ && fileItem.source && typeof fileItem.source === 'string') {
        // For initial files loaded via 'source' URL, 'source' is the URL.
        url = fileItem.source;
      } else if (fileItem.origin === 3 /* FileOrigin.LOCAL */ && fileItem.getMetadata('originalUrl')) {
        // Fallback for initial files using metadata if set
        url = fileItem.getMetadata('originalUrl') as string;
      }
      return url;
    }).filter(url => url !== null) as string[];
    
    console.log('[AlbumGalleryUploader] Calling onUpdate with URLs:', currentUrls);
    onUpdate(currentUrls);
  };


  return (
    <div className="album-gallery-uploader">
      <FilePond
        ref={pondRef}
        files={files}
        onupdatefiles={handleUpdateFiles}
        onaddfile={(error, file) => {
          if (error) {
            console.error('[FilePond onaddfile error]', error);
            return;
          }
          console.log('[FilePond onaddfile]', 'File added:', file.filename, 'Status:', file.status, 'File object:', file.file);
          // You could potentially call pondRef.current?.processFile(file.id) here if instantUpload was false
        }}
        onprocessfile={(error, file) => {
          if (error) {
            console.error('[FilePond onprocessfile error]', error, 'File:', file.filename);
            // The main onerror handler should also catch this, but logging here is good.
            return;
          }
          console.log('[FilePond onprocessfile success]', 'File:', file.filename, 'serverId:', file.serverId, 'Status:', file.status);
          // Manually trigger an update of URLs to the parent form,
          // as onupdatefiles might not fire immediately or reliably for individual file state changes.
          // We need to get all current files from FilePond instance to build the complete URL list.
          if (pondRef.current) {
            const allFilePondItems = pondRef.current.getFiles();
            console.log('[FilePond onprocessfile] Manually triggering URL update. All items:', allFilePondItems);
            // Use the same logic as handleUpdateFiles to extract URLs
            const currentUrls = allFilePondItems.map(fileItem => {
              let url = null;
              if (fileItem.status === 5 && fileItem.serverId) { // FileStatus.PROCESSING_COMPLETE = 5
                if (String(fileItem.serverId).startsWith('http')) {
                  url = String(fileItem.serverId);
                } else {
                  const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(String(fileItem.serverId));
                  url = urlData?.publicUrl || null;
                }
              } else if (fileItem.origin === 3 /* FileOrigin.LOCAL */ && fileItem.source && typeof fileItem.source === 'string') {
                url = fileItem.source;
              } else if (fileItem.origin === 3 /* FileOrigin.LOCAL */ && fileItem.getMetadata('originalUrl')) {
                url = fileItem.getMetadata('originalUrl') as string;
              }
              return url;
            }).filter(url => url !== null) as string[];
            
            console.log('[FilePond onprocessfile] Calling onUpdate with URLs:', currentUrls);
            onUpdate(currentUrls); // Propagate the full list of valid URLs
          }
        }}
        instantUpload={true} // Explicitly set, though it's default true
        allowMultiple={true}
        maxFiles={maxFiles}
        maxFileSize={maxFileSize}
        name="galleryImages" 
        labelIdle='Glissez-déposez vos images ou <span class="filepond--label-action">Parcourir</span>'
        acceptedFileTypes={['image/png', 'image/jpeg', 'image/gif', 'image/webp']}
        credits={false} // Disables FilePond credits
        onerror={(error, file, status) => {
          // error is of type FilePondErrorDescription, file is FilePondFile
          console.error('[FilePond Global Error]', { error, file, status });
          const mainError = (error as any).body || String(error); // Common way to get main error message
          const subError = (error as any).code ? `Code: ${((error as any).code)}` : 'Veuillez vérifier le type et la taille du fichier.';
          const fileName = file?.filename || 'inconnu'; // FilePondFile has .filename

          toast({ 
            title: `Erreur FilePond: ${mainError}`, 
            description: `${subError} (Fichier: ${fileName})`, 
            variant: "destructive",
            duration: 7000 
          });
        }}
        server={{
          process: handleProcessFile,
          revert: async (uniqueFileId, load, error) => {
            // uniqueFileId is what was passed to `load()` in `handleProcessFile`
            // This could be a public URL or a file path.
            // If it's a URL, we need to parse the path from it.
            // If it's a path, we can use it directly.
            let filePathToDelete = uniqueFileId;
            try {
                if (uniqueFileId.startsWith('http')) {
                    const urlParts = new URL(uniqueFileId);
                    // Path is typically after /object/public/bucketName/
                    const pathSegments = urlParts.pathname.split('/');
                    filePathToDelete = pathSegments.slice(pathSegments.indexOf(bucketName) + 1).join('/');
                }
                
                const { error: deleteError } = await supabase.storage
                .from(bucketName)
                .remove([filePathToDelete]);

                if (deleteError) {
                    console.error('Error reverting file (deleting from Supabase):', deleteError);
                    error('Error deleting file');
                    toast({ title: "Erreur de suppression", description: deleteError.message, variant: "destructive" });
                } else {
                    load(); // Signal success
                    toast({ title: "Image supprimée", description: "L'image a été retirée du stockage." });
                }
            } catch (e: any) {
                console.error('Error parsing URL for revert:', e);
                error('Error processing revert');
            }
          },
          // load: (source, load, error, progress, abort, headers) => {
          //   // Handle loading initial files from URLs if needed, though `files` prop is often better
          // }
        }}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Téléversez jusqu'à {maxFiles} images. Taille max par fichier: {maxFileSize}. Formats acceptés: PNG, JPG, GIF, WEBP.
      </p>
    </div>
  );
}
