"use client"

import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, TrendingUp, Music, Share2, ExternalLink, Lightbulb } from "lucide-react"

interface HighlightSong {
  id: string
  title: string
  cover_url: string | null
  plays: number
  likes: number
  comments: number
  engagement_rate: number
  growth_rate: number
  genre: string | null
}

interface HighlightSongSuggestionCardProps {
  highlightSong: HighlightSong | null
}

export function HighlightSongSuggestionCard({ highlightSong }: HighlightSongSuggestionCardProps) {
  if (!highlightSong) {
    return (
      <Card className="bg-gradient-to-br from-amber-950/20 to-amber-900/10 border-amber-900/50">
        <CardHeader className="pb-2">
          <CardTitle className="text-md font-medium flex items-center text-amber-500">
            <Sparkles className="h-4 w-4 mr-2" />
            Votre Morceau en Or
          </CardTitle>
          <CardDescription>Pas encore assez de données pour identifier votre morceau star</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">
            <p>Continuez à publier et promouvoir votre musique</p>
            <p className="text-xs mt-1">Nous analyserons vos performances pour identifier votre morceau à plus fort potentiel</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Formater les taux pour l'affichage
  const formatRate = (rate: number) => {
    return `${(rate * 100).toFixed(1)}%`
  }

  // Déterminer le message de suggestion en fonction des métriques
  const getSuggestionMessage = () => {
    if (highlightSong.growth_rate > 0.2) {
      return "Ce morceau connaît une forte croissance. C'est le moment idéal pour le promouvoir davantage!"
    } else if (highlightSong.engagement_rate > 0.15) {
      return "Ce morceau génère un excellent engagement. Envisagez de créer plus de contenu dans ce style!"
    } else {
      return "Ce morceau performe bien. Continuez à le promouvoir pour augmenter sa visibilité!"
    }
  }

  return (
    <Card className="bg-gradient-to-br from-amber-950/20 to-amber-900/10 border-amber-900/50">
      <CardHeader className="pb-2">
        <CardTitle className="text-md font-medium flex items-center text-amber-500">
          <Sparkles className="h-4 w-4 mr-2" />
          Votre Morceau en Or
        </CardTitle>
        <CardDescription>Morceau avec le meilleur potentiel de croissance</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <div className="h-16 w-16 rounded-md overflow-hidden bg-muted flex-shrink-0">
            {highlightSong.cover_url ? (
              <img
                src={highlightSong.cover_url}
                alt={highlightSong.title}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-amber-900/30">
                <Music className="h-8 w-8 text-amber-500" />
              </div>
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold">{highlightSong.title}</h3>
            <div className="flex items-center flex-wrap gap-2 mt-1">
              {highlightSong.genre && (
                <Badge variant="outline" className="bg-amber-950/30 text-amber-400 border-amber-900/50">
                  {highlightSong.genre}
                </Badge>
              )}
              <Badge variant="outline" className="bg-amber-950/30 text-amber-400 border-amber-900/50">
                <TrendingUp className="h-3 w-3 mr-1" />
                {formatRate(highlightSong.growth_rate)} de croissance
              </Badge>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-2 mt-4">
          <div className="bg-amber-950/30 rounded-md p-2 text-center">
            <p className="text-xs text-amber-400">Écoutes</p>
            <p className="text-lg font-bold">{highlightSong.plays.toLocaleString()}</p>
          </div>
          <div className="bg-amber-950/30 rounded-md p-2 text-center">
            <p className="text-xs text-amber-400">Likes</p>
            <p className="text-lg font-bold">{highlightSong.likes.toLocaleString()}</p>
          </div>
          <div className="bg-amber-950/30 rounded-md p-2 text-center">
            <p className="text-xs text-amber-400">Engagement</p>
            <p className="text-lg font-bold">{formatRate(highlightSong.engagement_rate)}</p>
          </div>
        </div>
        
        <div className="mt-4 bg-amber-950/20 p-3 rounded-md border border-amber-900/50">
          <div className="flex items-start">
            <Lightbulb className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-sm">{getSuggestionMessage()}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-0">
        <Button variant="outline" size="sm" className="border-amber-900/50 hover:bg-amber-950/50" asChild>
          <Link href={`/song/${highlightSong.id}`}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Voir
          </Link>
        </Button>
        <Button variant="outline" size="sm" className="border-amber-900/50 hover:bg-amber-950/50" asChild>
          <Link href={`/promote/${highlightSong.id}`}>
            <Share2 className="h-4 w-4 mr-2" />
            Promouvoir
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
