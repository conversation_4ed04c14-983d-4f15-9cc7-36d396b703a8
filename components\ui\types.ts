import { ChordPlacement, UnifiedChordPosition, ChordSuggestion } from '@/components/chord-system/types/chord-system';
import { AIHistoryEntry } from '@/components/ai-composer/types/ai-types';

import { AiGenerationType } from '@/types/ai-types';
import { LayoutMode, PanelState, ViewMode } from '@/types/layout';

export interface LayoutState {
  mode: LayoutMode;
  panel: PanelState;
  view: ViewMode;
  aiGenerationType: AiGenerationType;
  isMaximized: boolean;
  isFullscreen: boolean;
  panelSizes: Record<PanelState, number>;
}

export interface ChordPlacementDisplay extends ChordPlacement {
  textPosition?: {
    top: number;
    left: number;
  };
  suggestions?: ChordSuggestion[];
}

export interface ChordSystemStateWithActions {
  state: {
    chords: ChordPlacement[];
    suggestions: ChordSuggestion[];
    selectedChord: ChordPlacement | null;
  };
  actions: {
    addChord: (chord: UnifiedChordPosition, position: number) => void;
    removeChord: (id: string) => void;
    updateChord: (id: string, chord: UnifiedChordPosition) => void;
    addSuggestion: (suggestion: ChordSuggestion) => void;
    removeSuggestion: (id: string) => void;
    selectChord: (id: string | null) => void;
  };
}
