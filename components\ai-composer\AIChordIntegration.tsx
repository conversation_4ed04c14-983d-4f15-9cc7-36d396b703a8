'use client';

import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Music, Guitar, Piano, Volume2, VolumeX, Play, Pause, 
  Plus, Trash2, Edit, Eye, Settings, Layers, Target,
  RotateCcw, Save, Download, Upload, Copy, Zap, Search, BarChart3
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { MidiChordPlayer } from '@/lib/chords/midi-chord-player';

// Import des données d'accords
import guitarChords from '@/lib/chords/guitar.json';
import pianoChords from '@/lib/chords/piano.json';
import ukuleleChords from '@/lib/chords/ukulele_gcea_complete.json';

// Types pour les données d'accords
interface ChordData {
  instrument?: string;
  tuning?: string[];
  keys?: string[];
  suffixes?: string[];
  chords?: Record<string, any[]>;
  [key: string]: any;
}

interface InstrumentData {
  name: string;
  data: ChordData;
  tunings: Array<{
    id: string;
    name: string;
    notes: string[];
  }>;
}

interface ChordPosition {
  id: string;
  position: number;
  chord: string;
  instrument: string;
  tuning?: string;
  fret?: number;
  fingering?: number[];
  preview?: {
    svg?: string;
    audio?: string;
  };
  // Properties required by MidiChordPlayer
  frets: number[];
  fingers: number[];
  barres: Array<{ fret: number; fromString: number; toString: number }>;
  midi: number[];
  difficulty: string;
  baseFret: number;
  // Additional properties used in the component
  notes?: string[];
  category?: string;
  tags?: string[];
}

interface ChordProgression {
  id: string;
  name: string;
  chords: ChordPosition[];
  key: string;
  tempo: number;
  timeSignature: string;
  genre?: string;
  mood?: string;
  description?: string;
}

interface AIChordIntegrationProps {
  sections?: Array<{
    id: string;
    name: string;
    chords: Array<{
      id: string;
      position: number;
      chord: string;
      instrument: string;
      tuning?: string;
      fret?: number;
      fingering?: number[];
      preview?: {
        svg?: string;
        audio?: string;
      };
    }>;
  }>;
  selectedSection?: string;
  onSectionUpdate?: (sectionId: string, updates: any) => void;
  onChordAdd?: (sectionId: string, chord: ChordPosition) => void;
  onChordRemove?: (sectionId: string, chordId: string) => void;
  onChordUpdate?: (sectionId: string, chordId: string, updates: Partial<ChordPosition>) => void;
  availableInstruments?: { id: string; name: string; tunings: { id: string; name: string; notes: string[]; }[]; }[];
  onAIGenerateChords?: (prompt: string, sectionId: string) => Promise<void>;
  onChordSelect?: (chord: ChordPosition) => void;
  onProgressionSelect?: (progression: ChordProgression) => void;
  selectedKey?: string;
  selectedInstrument?: string;
  className?: string;
}

// Données des instruments avec leurs accordages
const INSTRUMENTS_DATA: InstrumentData[] = [
  {
    name: 'Guitare',
    data: guitarChords as ChordData,
    tunings: [
      { id: 'standard', name: 'Standard (E-A-D-G-B-E)', notes: ['E', 'A', 'D', 'G', 'B', 'E'] },
      { id: 'drop_d', name: 'Drop D (D-A-D-G-B-E)', notes: ['D', 'A', 'D', 'G', 'B', 'E'] },
      { id: 'open_g', name: 'Open G (D-G-D-G-B-D)', notes: ['D', 'G', 'D', 'G', 'B', 'D'] }
    ]
  },
  {
    name: 'Piano',
    data: pianoChords as ChordData,
    tunings: [
      { id: 'standard', name: 'Standard', notes: [] }
    ]
  },
  {
    name: 'Ukulélé',
    data: ukuleleChords as ChordData,
    tunings: [
      { id: 'gcea', name: 'GCEA (Standard)', notes: ['G', 'C', 'E', 'A'] },
      { id: 'dgbe', name: 'DGBE (Baritone)', notes: ['D', 'G', 'B', 'E'] }
    ]
  }
];

// Progressions d'accords populaires
const POPULAR_PROGRESSIONS: ChordProgression[] = [
  {
    id: 'vi-IV-I-V',
    name: 'vi-IV-I-V (Pop)',
    chords: [],
    key: 'C',
    tempo: 120,
    timeSignature: '4/4',
    genre: 'Pop',
    mood: 'Uplifting',
    description: 'Progression très populaire dans la musique pop moderne'
  },
  {
    id: 'I-V-vi-IV',
    name: 'I-V-vi-IV (Classic)',
    chords: [],
    key: 'C',
    tempo: 120,
    timeSignature: '4/4',
    genre: 'Rock',
    mood: 'Energetic',
    description: 'Progression classique du rock et de la pop'
  },
  {
    id: 'ii-V-I',
    name: 'ii-V-I (Jazz)',
    chords: [],
    key: 'C',
    tempo: 120,
    timeSignature: '4/4',
    genre: 'Jazz',
    mood: 'Sophisticated',
    description: 'Progression fondamentale du jazz'
  }
];

const AIChordIntegrationComponent: React.FC<AIChordIntegrationProps> = ({
  onChordSelect,
  onProgressionSelect,
  selectedKey = 'C',
  selectedInstrument = 'Guitare',
  className = ''
}) => {
  // États principaux
  const [currentInstrument, setCurrentInstrument] = useState(selectedInstrument);
  const [currentTuning, setCurrentTuning] = useState('standard');
  const [currentKey, setCurrentKey] = useState(selectedKey);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChord, setSelectedChord] = useState<ChordPosition | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  
  // États pour les vues et modales
  const [activeView, setActiveView] = useState<'chords' | 'progressions' | 'library'>('chords');
  const [showChordLibrary, setShowChordLibrary] = useState(false);
  const [showProgressionBuilder, setShowProgressionBuilder] = useState(false);
  
  // États pour les progressions
  const [currentProgression, setCurrentProgression] = useState<ChordProgression | null>(null);
  const [customProgressions, setCustomProgressions] = useState<ChordProgression[]>([]);
  
  // Player MIDI
  const [midiPlayer] = useState(() => new MidiChordPlayer());
  
  // Données de l'instrument actuel
  const currentInstrumentData = useMemo(() => {
    return INSTRUMENTS_DATA.find(inst => inst.name === currentInstrument);
  }, [currentInstrument]);
  
  // Accordages disponibles pour l'instrument actuel
  const availableTunings = useMemo(() => {
    return currentInstrumentData?.tunings || [];
  }, [currentInstrumentData]);
  
  // Accords disponibles pour l'instrument et l'accordage actuels
  const availableChords = useMemo(() => {
    if (!currentInstrumentData?.data.chords) return [];
    
    const chords: ChordPosition[] = [];
    const chordsData = currentInstrumentData.data.chords;
    
    Object.entries(chordsData).forEach(([chordName, positions]) => {
      if (Array.isArray(positions)) {
        positions.forEach((position, index) => {
          chords.push({
            id: `${chordName}-${index}`,
            position: index,
            chord: chordName,
            instrument: currentInstrument,
            tuning: currentTuning,
            frets: position.frets,
            fingers: position.fingers,
            notes: position.notes,
            midi: position.midi,
            difficulty: position.difficulty || 'medium',
            category: position.category,
            tags: position.tags,
            barres: position.barres || [],
            baseFret: position.baseFret || 0
          });
        });
      }
    });
    
    return chords;
  }, [currentInstrumentData, currentInstrument, currentTuning]);
  
  // Filtrage des accords selon la recherche et la tonalité
  const filteredChords = useMemo(() => {
    let filtered = availableChords;
    
    // Filtrage par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(chord => 
        chord.chord.toLowerCase().includes(searchTerm.toLowerCase()) ||
        chord.tags?.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // Filtrage par tonalité (optionnel)
    if (currentKey && currentKey !== 'all') {
      filtered = filtered.filter(chord => {
        const chordRoot = chord.chord.replace(/[^A-G#b]/g, '');
        return chordRoot === currentKey;
      });
    }
    
    return filtered.slice(0, 50); // Limiter à 50 résultats pour les performances
  }, [availableChords, searchTerm, currentKey]);
  
  // Gestion de la lecture des accords
  const playChord = useCallback(async (chord: ChordPosition) => {
    if (isMuted || !chord.midi) return;
    
    try {
      setIsPlaying(true);
      await midiPlayer.playChord(chord, 1000, Math.floor(volume * 127));
      setTimeout(() => setIsPlaying(false), 1000);
    } catch (error: any) {
      console.error('Erreur lors de la lecture de l\'accord:', error);
      toast({
        title: "Erreur",
        description: "Impossible de jouer l'accord",
        variant: "destructive"
      });
    }
  }, [midiPlayer, volume, isMuted]);
  
  // Gestion de la sélection d'accord
  const handleChordSelect = useCallback((chord: ChordPosition) => {
    setSelectedChord(chord);
    onChordSelect?.(chord);
    playChord(chord);
  }, [onChordSelect, playChord]);
  
  // Gestion du volume
  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume);
    // Note: MidiChordPlayer doesn't have setVolume method, volume is handled in playChord
  }, []);
  
  // Basculer le mode muet
  const toggleMute = useCallback(() => {
    setIsMuted(!isMuted);
    // Note: MidiChordPlayer doesn't have setVolume method, mute is handled in playChord
  }, [isMuted]);
  
  // Initialisation du player MIDI
  useEffect(() => {
    // Note: MidiChordPlayer doesn't have initialize method, it's ready to use
    return () => {
      midiPlayer.dispose();
    };
  }, [midiPlayer]);
  
  // Rendu du composant d'accord
  const renderChordCard = useCallback((chord: ChordPosition) => {
    const isSelected = selectedChord?.id === chord.id;
    
    return (
      <Card 
        key={chord.id}
        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
          isSelected ? 'ring-2 ring-primary shadow-lg' : ''
        }`}
        onClick={() => handleChordSelect(chord)}
      >
        <CardContent className="p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="font-medium text-sm">{chord.chord}</div>
            <div className="flex items-center gap-1">
              {chord.difficulty && (
                <Badge 
                  variant={chord.difficulty === 'easy' ? 'default' : 
                          chord.difficulty === 'medium' ? 'secondary' : 'destructive'}
                  className="text-xs"
                >
                  {chord.difficulty}
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  playChord(chord);
                }}
              >
                {isPlaying && selectedChord?.id === chord.id ? 
                  <Pause className="h-3 w-3" /> : 
                  <Play className="h-3 w-3" />
                }
              </Button>
            </div>
          </div>
          
          {/* Visualisation des frettes pour guitare/ukulélé */}
          {chord.frets && currentInstrument !== 'Piano' && (
            <div className="mt-2">
              <div className="text-xs text-muted-foreground mb-1">Frettes:</div>
              <div className="flex gap-1">
                {chord.frets.map((fret, index) => (
                  <div 
                    key={index}
                    className="w-6 h-6 border rounded text-xs flex items-center justify-center bg-muted"
                  >
                    {fret === -1 ? 'X' : fret}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Notes de l'accord */}
          {chord.notes && (
            <div className="mt-2">
              <div className="text-xs text-muted-foreground mb-1">Notes:</div>
              <div className="text-xs font-mono">
                {chord.notes.join(' - ')}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }, [selectedChord, handleChordSelect, playChord, isPlaying, currentInstrument]);
  
  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* En-tête avec contrôles */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Music className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Intégration Accords IA</h2>
          <Badge variant="secondary">Pro</Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Contrôles de volume */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={isMuted ? 0 : volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-16"
            />
          </div>
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* Boutons d'action */}
          <Button variant="outline" size="sm" onClick={() => setShowChordLibrary(true)}>
            <Search className="h-4 w-4 mr-1" />
            Bibliothèque
          </Button>
          
          <Button variant="outline" size="sm" onClick={() => setShowProgressionBuilder(true)}>
            <Layers className="h-4 w-4 mr-1" />
            Progressions
          </Button>
        </div>
      </div>
      
      {/* Sélecteurs d'instrument et d'accordage */}
      <div className="flex items-center gap-4 p-4 bg-muted/50">
        <div className="flex items-center gap-2">
          <Label htmlFor="instrument-select" className="text-sm font-medium">
            Instrument:
          </Label>
          <Select value={currentInstrument} onValueChange={setCurrentInstrument}>
            <SelectTrigger id="instrument-select" className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {INSTRUMENTS_DATA.map(instrument => (
                <SelectItem key={instrument.name} value={instrument.name}>
                  <div className="flex items-center gap-2">
                    {instrument.name === 'Guitare' && <Guitar className="h-4 w-4" />}
                    {instrument.name === 'Piano' && <Piano className="h-4 w-4" />}
                    {instrument.name === 'Ukulélé' && <Music className="h-4 w-4" />}
                    {instrument.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Label htmlFor="tuning-select" className="text-sm font-medium">
            Accordage:
          </Label>
          <Select value={currentTuning} onValueChange={setCurrentTuning}>
            <SelectTrigger id="tuning-select" className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableTunings.map(tuning => (
                <SelectItem key={tuning.id} value={tuning.id}>
                  {tuning.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Label htmlFor="key-select" className="text-sm font-medium">
            Tonalité:
          </Label>
          <Select value={currentKey} onValueChange={setCurrentKey}>
            <SelectTrigger id="key-select" className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes</SelectItem>
              {['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'].map(key => (
                <SelectItem key={key} value={key}>{key}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex-1" />
        
        {/* Barre de recherche */}
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un accord..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-48"
          />
        </div>
      </div>
      
      {/* Onglets de navigation */}
      <Tabs value={activeView} onValueChange={(value) => setActiveView(value as any)} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 mx-4 mt-2">
          <TabsTrigger value="chords">Accords</TabsTrigger>
          <TabsTrigger value="progressions">Progressions</TabsTrigger>
          <TabsTrigger value="library">Ma Bibliothèque</TabsTrigger>
        </TabsList>
        
        {/* Contenu des onglets */}
        <TabsContent value="chords" className="flex-1 p-4">
          <ScrollArea className="h-full">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
              {filteredChords.map(renderChordCard)}
            </div>
            
            {filteredChords.length === 0 && (
              <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                <Music className="h-12 w-12 mb-4" />
                <p className="text-lg font-medium mb-2">Aucun accord trouvé</p>
                <p className="text-sm text-center">
                  Essayez de modifier vos critères de recherche ou de sélectionner un autre instrument.
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
        
        <TabsContent value="progressions" className="flex-1 p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Progressions Populaires</h3>
              <Button onClick={() => setShowProgressionBuilder(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Créer une progression
              </Button>
            </div>
            
            <div className="grid gap-4">
              {POPULAR_PROGRESSIONS.map(progression => (
                <Card key={progression.id} className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => onProgressionSelect?.(progression)}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{progression.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{progression.genre}</Badge>
                        <Badge variant="secondary">{progression.key}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">{progression.description}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Tempo: {progression.tempo} BPM</span>
                      <span>Signature: {progression.timeSignature}</span>
                      <span>Humeur: {progression.mood}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="library" className="flex-1 p-4">
          <div className="text-center py-12">
            <Music className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Votre Bibliothèque</h3>
            <p className="text-muted-foreground mb-6">
              Sauvegardez vos accords et progressions favoris pour un accès rapide.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Ajouter à la bibliothèque
            </Button>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Modal de bibliothèque d'accords */}
      <Dialog open={showChordLibrary} onOpenChange={setShowChordLibrary}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Bibliothèque d'Accords</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-96">
              <div className="grid grid-cols-6 gap-3 p-4">
                {availableChords.slice(0, 100).map(chord => (
                  <Card 
                    key={chord.id}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => {
                      handleChordSelect(chord);
                      setShowChordLibrary(false);
                    }}
                  >
                    <div className="text-center">
                      <div className="font-medium text-sm mb-1">{chord.chord}</div>
                      <div className="text-xs text-muted-foreground mb-2">
                        {chord.difficulty}
                      </div>
                      <Button variant="ghost" size="sm" className="w-full h-8">
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Mémoisation du composant pour éviter les re-rendus inutiles
export const AIChordIntegration = memo(AIChordIntegrationComponent);
export default AIChordIntegration;