import React, { useState, useEffect } from 'react';
// import { ChordsDB, Chord, Instrument } from '@tombatossals/chords-db';
// import guitarDb from '@/lib/chords/guitar.json';
// import ukuleleDb from '@/lib/chords/ukulele.json';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Combobox } from '@/components/ui/combobox'; // Assure-toi d'avoir un composant Combobox ou adapte la logique
import { ChordInstrument, ChordDiagramProps } from './ChordDiagram'; // Assurez-vous que ChordDiagramProps est exporté

interface AddChordDiagramFormProps {
  onAdd: (diagram: Omit<ChordDiagramProps, 'key'>) => void; // Omit 'key' si elle est présente
}

const AddChordDiagramForm: React.FC<AddChordDiagramFormProps> = ({ onAdd }) => {
  // const [db] = useState(new ChordsDB());
  const [instrument, setInstrument] = useState<ChordInstrument>('guitar');
  const [key, setKey] = useState('C');
  const [suffix, setSuffix] = useState('major');
  const [variantIdx, setVariantIdx] = useState<number>(0);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [suffixes, setSuffixes] = useState<string[]>([]);
  const [variants, setVariants] = useState<any[]>([]);
  const [chordsDb, setChordsDb] = useState<any>(null);

  // Charger dynamiquement la base d'accords selon l'instrument
  useEffect(() => {
    let url = '';
    if (instrument === 'guitar') url = '/chords/guitar.json';
    else if (instrument === 'ukulele') url = '/chords/ukulele.json';
    else if (instrument === 'mandolin') url = '/chords/mandolin.json';
    // Ajoute d'autres instruments ici si besoin
    else url = '';
    if (!url) {
      setChordsDb(null);
      setSuggestions([]);
      setSuffixes([]);
      setVariants([]);
      setVariantIdx(0);
      return;
    }
    fetch(url)
      .then(res => res.json())
      .then(data => {
        setChordsDb(data);
        setSuggestions(data.keys || []);
        setSuffixes(data.suffixes || []);
        setVariants([]);
        setVariantIdx(0);
      });
  }, [instrument]);

  // Quand la tonalité ou le type change, chercher les variantes
  useEffect(() => {
    if (!key || !suffix || !chordsDb) return;
    const chordsForInstrument = chordsDb.chords;
    if (chordsForInstrument && chordsForInstrument[key]) {
      const foundChords = chordsForInstrument[key].filter((c: any) => c.suffix === suffix);
      if (foundChords.length > 0 && foundChords[0].positions && foundChords[0].positions.length > 0) {
        setVariants(foundChords[0].positions);
        setVariantIdx(0);
      } else {
        setVariants([]);
      }
    } else {
      setVariants([]);
    }
  }, [key, suffix, chordsDb]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!chordName) return;

    // Trouver l'instrument dans la base de données ChordsDB
    const instrumentDetails = db.getInstrument(instrument as any);
    if (!instrumentDetails) {
      console.error(`Instrument details not found for: ${instrument}`);
      // Gérer l'erreur, peut-être afficher un toast
      return;
    }

    // Obtenir les données de l'accord
    // La méthode getChordByName n'existe pas directement, il faut utiliser getChordsByPattern
    // ou trouver l'accord via l'instrument et le nom.
    // Pour l'instant, on va essayer de trouver une correspondance exacte.
    let chordData: any = undefined;
    let chordsForInstrument: any = null;
    if (instrument === 'guitar') chordsForInstrument = guitarDb.chords;
    else if (instrument === 'ukulele') chordsForInstrument = ukuleleDb.chords;
    // TODO: Ajouter d'autres instruments
    if (chordsForInstrument && chordsForInstrument[chordName]) {
      chordData = chordsForInstrument[chordName][0];
    }

    if (!chordData || !chordData.positions || chordData.positions.length === 0) {
      // Gérer le cas où l'accord n'est pas trouvé ou n'a pas de positions
      // Pour l'instant, on ajoute un diagramme vide/placeholder
      console.warn(`Chord ${chordName} for ${instrument} not found or has no positions. Adding placeholder.`);
      onAdd({
        instrument,
        chord: `${chordName} (Non trouvé)`, 
        positions: [],
      });
      setChordName('');
      return;
    }

    // Utiliser la position sélectionnée (variantIdx)
    const position = chordData.positions[variantIdx] || chordData.positions[0];
    
    const newDiagram: Omit<ChordDiagramProps, 'key'> = {
      instrument,
      chord: chordName, // On garde le nom saisi
      positions: position.frets,
      fingering: position.fingers?.join(''),
    };

    onAdd(newDiagram);
    setChordName(''); // Reset
    setVariants([]);
    setVariantIdx(0);
  };

  return (
    <div className="p-4 border rounded-md space-y-3 bg-muted/20">
      <h4 className="text-sm font-medium">Ajouter un diagramme d'accord</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <div>
          <Label htmlFor="instrument-select">Instrument</Label>
          <Select value={instrument} onValueChange={(value) => setInstrument(value as ChordInstrument)}>
            <SelectTrigger id="instrument-select">
              <SelectValue placeholder="Choisir instrument" />
            </SelectTrigger>
            <SelectContent className="bg-background border shadow-md">
              <SelectItem value="guitar">Guitare</SelectItem>
              <SelectItem value="piano">Piano</SelectItem>
              <SelectItem value="ukulele">Ukulélé</SelectItem>
              <SelectItem value="mandolin">Mandoline</SelectItem>
              <SelectItem value="banjo">Banjo</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="key-select">Tonalité</Label>
          <Select value={key} onValueChange={setKey}>
            <SelectTrigger id="key-select">
            <SelectValue placeholder="Tonalité" />
          </SelectTrigger>
          <SelectContent className="bg-background border shadow-md">
            {suggestions.map((k, idx) => (
              <SelectItem key={idx} value={k}>{k}</SelectItem>
            ))}
          </SelectContent>
          </Select>
          <Label htmlFor="suffix-select" className="mt-2">Type</Label>
          <Select value={suffix} onValueChange={setSuffix}>
          <SelectTrigger id="suffix-select">
            <SelectValue placeholder="Type" />
            </SelectTrigger>
        <SelectContent className="bg-background border shadow-md">
          {suffixes.map((s, idx) => (
            <SelectItem key={idx} value={s}>{s}</SelectItem>
          ))}
        </SelectContent>
      </Select>
        </div>
      </div>
      {/* Sélecteur de variante si plusieurs positions */}
      {variants.length > 1 && (
        <div className="mt-2">
          <Label>Variante</Label>
          <Select value={String(variantIdx)} onValueChange={v => setVariantIdx(Number(v))}>
            <SelectTrigger><SelectValue placeholder="Variante" /></SelectTrigger>
            <SelectContent>
              {variants.map((v, idx) => (
                <SelectItem key={idx} value={String(idx)}>Variante {idx + 1}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
      <Button type="button" onClick={handleSubmit} size="sm">Ajouter le diagramme</Button>
    </div>
  );
};

export default AddChordDiagramForm;
