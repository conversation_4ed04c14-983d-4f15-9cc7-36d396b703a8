"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Globe } from "lucide-react";
import { toast } from "sonner";

interface LanguageOption {
  id: string;
  name: string;
  nativeName: string;
  flag: string;
}

export function LanguageTab() {
  const [currentLanguage, setCurrentLanguage] = useState("fr");
  const [dateFormat, setDateFormat] = useState("system");
  const [saving, setSaving] = useState(false);

  const languages: LanguageOption[] = [
    { id: "fr", name: "Français", nativeName: "Français", flag: "🇫🇷" },
    { id: "en", name: "English", nativeName: "English", flag: "🇬🇧" },
    { id: "es", name: "Spanish", nativeName: "Espa<PERSON><PERSON>", flag: "🇪🇸" },
    { id: "de", name: "German", nativeName: "Deutsch", flag: "🇩🇪" },
    { id: "it", name: "Italian", nativeName: "Italiano", flag: "🇮🇹" },
    { id: "pt", name: "Portuguese", nativeName: "Português", flag: "🇵🇹" },
    { id: "ja", name: "Japanese", nativeName: "日本語", flag: "🇯🇵" },
    { id: "zh", name: "Chinese", nativeName: "中文", flag: "🇨🇳" },
    { id: "ko", name: "Korean", nativeName: "한국어", flag: "🇰🇷" },
    { id: "ru", name: "Russian", nativeName: "Русский", flag: "🇷🇺" },
    { id: "ar", name: "Arabic", nativeName: "العربية", flag: "🇸🇦" },
  ];

  const handleSaveLanguage = async () => {
    setSaving(true);
    try {
      // Simulation d'un appel API pour sauvegarder la langue
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Langue mise à jour", {
        description: `La langue de l'interface a été changée en ${languages.find(l => l.id === currentLanguage)?.name}.`
      });
    } catch (error) {
      toast.error("Erreur lors du changement de langue", {
        description: "Une erreur est survenue. Veuillez réessayer."
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Langue</h2>
        <p className="text-muted-foreground">
          Choisissez la langue de l'interface utilisateur.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Langue de l'interface</CardTitle>
          <CardDescription>
            Cette option modifie uniquement la langue de l'interface utilisateur, pas le contenu créé.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={currentLanguage}
            onValueChange={setCurrentLanguage}
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            {languages.map((language) => (
              <div 
                key={language.id} 
                className={`flex items-center space-x-3 rounded-md border p-4 cursor-pointer ${currentLanguage === language.id ? 'border-primary bg-primary/5' : ''}`}
                onClick={() => setCurrentLanguage(language.id)}
              >
                <div className="flex items-center h-5">
                  <RadioGroupItem 
                    value={language.id} 
                    id={`language-${language.id}`} 
                  />
                </div>
                <Label 
                  htmlFor={`language-${language.id}`}
                  className="flex items-center gap-2 cursor-pointer font-normal flex-1"
                >
                  <span className="text-xl">{language.flag}</span>
                  <div>
                    <div>{language.name}</div>
                    <div className="text-xs text-muted-foreground">{language.nativeName}</div>
                  </div>
                </Label>
              </div>
            ))}
          </RadioGroup>
          
          <div className="mt-6 flex justify-end">
            <Button 
              onClick={handleSaveLanguage} 
              disabled={saving}
            >
              {saving ? (
                <>Mise à jour...</>
              ) : (
                <>
                  <Globe className="mr-2 h-4 w-4" />
                  Appliquer le changement de langue
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Format de date et d'heure</CardTitle>
          <CardDescription>
            Choisissez comment les dates et heures sont affichées dans l'application.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={dateFormat}
            onValueChange={setDateFormat}
            className="grid gap-4"
          >
            <div 
              className={`flex items-center space-x-3 rounded-md border p-4 cursor-pointer ${dateFormat === 'system' ? 'border-primary bg-primary/5' : ''}`}
              onClick={() => setDateFormat('system')}
            >
              <div className="flex items-center h-5">
                <RadioGroupItem 
                  value="system" 
                  id="date-system" 
                />
              </div>
              <Label 
                htmlFor="date-system"
                className="cursor-pointer font-normal flex-1"
              >
                <div>Utiliser les paramètres système</div>
                <div className="text-sm text-muted-foreground">
                  Exemple: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
                </div>
              </Label>
            </div>

            <div 
              className={`flex items-center space-x-3 rounded-md border p-4 cursor-pointer ${dateFormat === 'us' ? 'border-primary bg-primary/5' : ''}`}
              onClick={() => setDateFormat('us')}
            >
              <div className="flex items-center h-5">
                <RadioGroupItem 
                  value="us" 
                  id="date-us" 
                />
              </div>
              <Label 
                htmlFor="date-us"
                className="cursor-pointer font-normal flex-1"
              >
                <div>Format américain (MM/JJ/AAAA)</div>
                <div className="text-sm text-muted-foreground">
                  Exemple: {new Date().toLocaleDateString('en-US')} {new Date().toLocaleTimeString('en-US')}
                </div>
              </Label>
            </div>

            <div 
              className={`flex items-center space-x-3 rounded-md border p-4 cursor-pointer ${dateFormat === 'eu' ? 'border-primary bg-primary/5' : ''}`}
              onClick={() => setDateFormat('eu')}
            >
              <div className="flex items-center h-5">
                <RadioGroupItem 
                  value="eu" 
                  id="date-eu" 
                />
              </div>
              <Label 
                htmlFor="date-eu"
                className="cursor-pointer font-normal flex-1"
              >
                <div>Format européen (JJ/MM/AAAA)</div>
                <div className="text-sm text-muted-foreground">
                  Exemple: {new Date().toLocaleDateString('fr-FR')} {new Date().toLocaleTimeString('fr-FR')}
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>
    </div>
  );
}
