-- Script pour créer la fonction get_data_for_playlist_edit_page
-- À exécuter directement dans l'interface SQL de Supabase

-- Créer la fonction RPC pour la page d'édition de playlist
CREATE OR REPLACE FUNCTION public.get_data_for_playlist_edit_page(p_playlist_id uuid, p_requesting_user_id uuid)
RETURNS json
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
DECLARE
    v_playlist_owner_id uuid;
    v_playlist_details json;
    v_playlist_songs json;
    v_available_songs json;
BEGIN
    -- 1. Get playlist details and verify ownership
    SELECT
        json_build_object(
            'id', p.id,
            'name', p.name,
            'description', p.description,
            'is_public', p.is_public,
            'cover_art_url', p.cover_art_url,
            'banner_url', p.banner_url,
            'user_id', p.user_id
        ),
        p.user_id
    INTO v_playlist_details, v_playlist_owner_id
    FROM public.playlists p
    WHERE p.id = p_playlist_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Playlist not found';
    END IF;

    IF v_playlist_owner_id IS DISTINCT FROM p_requesting_user_id THEN
        RAISE EXCEPTION 'Permission denied';
    END IF;

    -- 2. Get songs already in the playlist
    SELECT COALESCE(json_agg(
        json_build_object(
            'id', s.id,
            'title', s.title,
            'artist_name', s.artist_name,
            'cover_art_url', s.cover_art_url,
            'duration_ms', s.duration_ms,
            'profiles', (SELECT json_build_object('display_name', pr.display_name, 'username', pr.username) FROM public.profiles pr WHERE pr.id = s.creator_user_id)
        ) ORDER BY ps.position
    ), '[]'::json)
    INTO v_playlist_songs
    FROM public.playlist_songs ps
    JOIN public.songs s ON ps.song_id = s.id
    WHERE ps.playlist_id = p_playlist_id;

    -- 3. Get all songs available for the user to add
    SELECT COALESCE(json_agg(
        json_build_object(
            'id', s.id,
            'title', s.title,
            'artist_name', s.artist_name,
            'cover_art_url', s.cover_art_url,
            'duration_ms', s.duration_ms,
            'profiles', (SELECT json_build_object('display_name', pr.display_name, 'username', pr.username) FROM public.profiles pr WHERE pr.id = s.creator_user_id)
        ) ORDER BY s.title
    ), '[]'::json)
    INTO v_available_songs
    FROM public.songs s
    WHERE s.creator_user_id = p_requesting_user_id
      AND s.status = 'published';

    -- 4. Combine and return
    RETURN json_build_object(
        'playlistDetails', v_playlist_details,
        'playlistSongs', v_playlist_songs,
        'availableSongs', v_available_songs
    );
END;
$$;

-- Ajouter les permissions nécessaires
GRANT EXECUTE ON FUNCTION public.get_data_for_playlist_edit_page(uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_data_for_playlist_edit_page(uuid, uuid) TO service_role;
