CREATE OR REPLACE FUNCTION public.get_dashboard_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_profile JSONB;
    v_tracks_summary JSONB;
    v_albums_summary JSONB;
    v_todos_summary JSONB;
    v_total_plays BIGINT;
    v_total_views BIGINT;
    v_total_likes BIGINT;
    v_total_followers BIGINT;
    v_daily_plays JSONB;
    v_weekly_plays JSONB;
    v_monthly_plays JSONB;
    v_recent_tracks JSONB;
    v_recent_albums JSONB;
    v_top_tracks JSONB;
    v_recent_comments JSONB;
    v_recent_followers JSONB;
    v_recent_plays_of_my_tracks JSONB;
    v_my_tracks_in_playlists JSONB;
    v_playlists_followed JSONB;
    v_creation_costs JSONB;
    v_playlist_tracks_details JSONB;
    v_highlight_track JSONB;
    v_result JSONB;
BEGIN
    -- 1. Profil utilisateur
    SELECT jsonb_build_object(
        'id', id,
        'username', username,
        'display_name', display_name,
        'avatar_url', avatar_url,
        'bio', bio,
        'website', website,
        'location', location,
        'coins_balance', coins_balance
    )
    INTO v_profile
    FROM public.profiles
    WHERE id = p_user_id;

    -- 2. Résumé des morceaux
    SELECT jsonb_build_object(
        'total_count', COUNT(*),
        'published_count', COUNT(*) FILTER (WHERE visibility = 'public')
    )
    INTO v_tracks_summary
    FROM public.tracks
    WHERE creator_user_id = p_user_id AND is_archived = false;

    -- 3. Résumé des albums
    SELECT jsonb_build_object(
        'total_count', COUNT(*),
        'published_count', COUNT(*) FILTER (WHERE visibility = 'public')
    )
    INTO v_albums_summary
    FROM public.albums
    WHERE creator_user_id = p_user_id;

    -- 4. Total des écoutes
    SELECT COUNT(*)
    INTO v_total_plays
    FROM public.activities
    WHERE action = 'play' 
      AND resource_type = 'track' 
      AND resource_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id);

    -- 5. Total des vues
    SELECT COUNT(*)
    INTO v_total_views
    FROM public.activities
    WHERE action = 'view' 
      AND ((resource_type = 'track' AND resource_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id))
        OR (resource_type = 'album' AND resource_id IN (SELECT id FROM public.albums WHERE creator_user_id = p_user_id)));

    -- 6. Total des likes
    SELECT COUNT(*)
    INTO v_total_likes
    FROM public.likes
    WHERE (track_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id))
       OR (album_id IN (SELECT id FROM public.albums WHERE creator_user_id = p_user_id));

    -- 7. Total des followers
    SELECT COUNT(*)
    INTO v_total_followers
    FROM public.follows
    WHERE followed_id = p_user_id;

    -- 8. Morceaux récents
    SELECT jsonb_agg(jsonb_build_object(
        'id', t.id,
        'title', t.title,
        'cover_url', t.cover_art_url,
        'genres', t.genres,
        'plays', (SELECT COUNT(*) FROM public.activities WHERE action = 'play' AND resource_type = 'track' AND resource_id = t.id),
        'created_at', t.created_at,
        'updated_at', t.updated_at,
        'status', CASE WHEN t.visibility = 'public' THEN 'published' ELSE 'draft' END
    ) ORDER BY t.created_at DESC NULLS LAST)
    INTO v_recent_tracks
    FROM public.tracks t
    WHERE t.creator_user_id = p_user_id AND t.is_archived = false
    LIMIT 5;

    -- 9. Albums récents
    SELECT jsonb_agg(jsonb_build_object(
        'id', a.id,
        'title', a.title,
        'cover_url', a.cover_art_url,
        'genres', a.genres,
        'created_at', a.created_at,
        'updated_at', a.updated_at,
        'status', CASE WHEN a.visibility = 'public' THEN 'published' ELSE 'draft' END
    ) ORDER BY a.created_at DESC NULLS LAST)
    INTO v_recent_albums
    FROM public.albums a
    WHERE a.creator_user_id = p_user_id
    LIMIT 5;

    -- 10. Morceaux les plus écoutés
    SELECT jsonb_agg(jsonb_build_object(
        'id', t.id,
        'title', t.title,
        'cover_url', t.cover_art_url,
        'genres', t.genres,
        'plays', plays,
        'created_at', t.created_at
    ) ORDER BY plays DESC NULLS LAST)
    INTO v_top_tracks
    FROM public.tracks t
    JOIN (
        SELECT resource_id, COUNT(*) as plays
        FROM public.activities
        WHERE action = 'play' AND resource_type = 'track'
        GROUP BY resource_id
    ) plays ON t.id = plays.resource_id
    WHERE t.creator_user_id = p_user_id AND t.is_archived = false
    LIMIT 5;

    -- 11. Métriques par période pour les graphiques multi-métriques
    -- Données quotidiennes (30 derniers jours)
    WITH 
    -- Écoutes quotidiennes
    plays_last_30_days AS (
      SELECT date_trunc('day', created_at) AS metric_day, COUNT(*) AS plays_count
      FROM public.activities
      WHERE action = 'play' 
        AND resource_type = 'track' 
        AND resource_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id)
        AND created_at >= (NOW() - INTERVAL '30 days')
      GROUP BY metric_day
    ),
    -- Vues quotidiennes
    views_last_30_days AS (
      SELECT date_trunc('day', created_at) AS metric_day, COUNT(*) AS views_count
      FROM public.activities
      WHERE action = 'view'
        AND ((resource_type = 'track' AND resource_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id))
          OR (resource_type = 'album' AND resource_id IN (SELECT id FROM public.albums WHERE creator_user_id = p_user_id)))
        AND created_at >= (NOW() - INTERVAL '30 days')
      GROUP BY metric_day
    ),
    -- Likes quotidiens
    likes_last_30_days AS (
      SELECT date_trunc('day', created_at) AS metric_day, COUNT(*) AS likes_count
      FROM public.likes
      WHERE (track_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id))
         OR (album_id IN (SELECT id FROM public.albums WHERE creator_user_id = p_user_id))
        AND created_at >= (NOW() - INTERVAL '30 days')
      GROUP BY metric_day
    ),
    -- Commentaires quotidiens
    comments_last_30_days AS (
      SELECT date_trunc('day', created_at) AS metric_day, COUNT(*) AS comments_count
      FROM public.comments
      WHERE (resource_type = 'track' AND resource_id IN (SELECT id FROM public.tracks WHERE creator_user_id = p_user_id))
         OR (resource_type = 'album' AND resource_id IN (SELECT id FROM public.albums WHERE creator_user_id = p_user_id))
        AND created_at >= (NOW() - INTERVAL '30 days')
      GROUP BY metric_day
    ),
    -- Nouveaux followers quotidiens
    followers_last_30_days AS (
      SELECT date_trunc('day', created_at) AS metric_day, COUNT(*) AS followers_count
      FROM public.follows
      WHERE followed_id = p_user_id
        AND created_at >= (NOW() - INTERVAL '30 days')
      GROUP BY metric_day
    ),
    -- Série de dates pour les 30 derniers jours
    days_series_30 AS (
      SELECT generate_series(
        date_trunc('day', NOW() - INTERVAL '29 days'),
        date_trunc('day', NOW()),
        INTERVAL '1 day'
      ) AS day_date
    )
    SELECT jsonb_agg(
              jsonb_build_object(
                  'date', TO_CHAR(ds.day_date, 'YYYY-MM-DD'),
                  'plays', COALESCE(p30.plays_count, 0),
                  'views', COALESCE(v30.views_count, 0),
                  'likes', COALESCE(l30.likes_count, 0),
                  'comments', COALESCE(c30.comments_count, 0),
                  'followers', COALESCE(f30.followers_count, 0)
              ) ORDER BY ds.day_date ASC
           )
    INTO v_daily_plays
    FROM days_series_30 ds
    LEFT JOIN plays_last_30_days p30 ON ds.day_date = p30.metric_day
    LEFT JOIN views_last_30_days v30 ON ds.day_date = v30.metric_day
    LEFT JOIN likes_last_30_days l30 ON ds.day_date = l30.metric_day
    LEFT JOIN comments_last_30_days c30 ON ds.day_date = c30.metric_day
    LEFT JOIN followers_last_30_days f30 ON ds.day_date = f30.metric_day;

    -- Construction du JSON final
    v_result := jsonb_build_object(
        'userProfile', COALESCE(v_profile, '{}'::jsonb),
        'tracks_summary', COALESCE(v_tracks_summary, '{}'::jsonb),
        'albums_summary', COALESCE(v_albums_summary, '{}'::jsonb),
        'todos_summary', COALESCE(v_todos_summary, '{}'::jsonb),
        'totalPlays', COALESCE(v_total_plays, 0),
        'totalViews', COALESCE(v_total_views, 0),
        'totalFollowers', COALESCE(v_total_followers, 0),
        'daily_plays_for_dashboard', COALESCE(v_daily_plays, '[]'::jsonb),
        'weekly_plays_for_dashboard', COALESCE(v_weekly_plays, '[]'::jsonb),
        'monthly_plays_for_dashboard', COALESCE(v_monthly_plays, '[]'::jsonb),
        'recentTracks', COALESCE(v_recent_tracks, '[]'::jsonb),
        'recentAlbums', COALESCE(v_recent_albums, '[]'::jsonb),
        'topTracks', COALESCE(v_top_tracks, '[]'::jsonb),
        'recentComments', COALESCE(v_recent_comments, '[]'::jsonb),
        'recentFollowers', COALESCE(v_recent_followers, '[]'::jsonb),
        'recentPlaysOfMyTracks', COALESCE(v_recent_plays_of_my_tracks, '[]'::jsonb),
        'myTracksInPlaylists', COALESCE(v_my_tracks_in_playlists, '[]'::jsonb),
        'highlightTrack', COALESCE(v_highlight_track, '{}'::jsonb)
    );

    RETURN v_result;
END;
$$;
