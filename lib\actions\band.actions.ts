"use server";

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers'; // To get current path for potential revalidation or redirect

export async function deleteBand(bandId: string): Promise<{ success: boolean; error?: string; message?: string }> {
  const supabase = await createSupabaseServerClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return { success: false, error: "Utilisateur non authentifié." };
  }

  try {
    // 1. Verify ownership/permissions
    const { data: bandData, error: fetchError } = await supabase
      .from('bands')
      .select('creator_id')
      .eq('id', bandId)
      .single();

    if (fetchError || !bandData) {
      return { success: false, error: "Groupe non trouvé ou erreur lors de la récupération." };
    }

    if (bandData.creator_id !== user.id) {
      // Add more sophisticated role/admin checks if necessary in the future
      return { success: false, error: "Vous n'avez pas la permission de supprimer ce groupe." };
    }

    // 2. Delete related records (order matters due to foreign keys, unless CASCADE is set up)
    // For simplicity, starting with band_members.
    // Ideally, also delete related songs, albums, projects, comments, likes, storage items etc.
    // This part needs to be comprehensive based on the actual schema and desired cleanup.

    const { error: membersError } = await supabase
      .from('band_members')
      .delete()
      .eq('band_id', bandId);

    if (membersError) {
      console.error(`Error deleting band members for band ${bandId}:`, membersError.message);
      return { success: false, error: `Erreur lors de la suppression des membres du groupe: ${membersError.message}` };
    }
    
    // TODO: Add deletion for other related entities:
    // - songs where band_id = bandId
    // - albums where band_id = bandId
    // - band_projects where band_id = bandId
    // - comments where resource_id = bandId AND resource_type = 'band'
    // - likes where resource_id = bandId AND resource_type = 'band'
    // - dislikes where resource_id = bandId AND resource_type = 'band'
    // - storage objects (avatar, cover, project files etc.) - this is more complex

    // 3. Delete the band itself
    const { error: bandDeleteError } = await supabase
      .from('bands')
      .delete()
      .eq('id', bandId);

    if (bandDeleteError) {
      console.error(`Error deleting band ${bandId}:`, bandDeleteError.message);
      return { success: false, error: `Erreur lors de la suppression du groupe: ${bandDeleteError.message}` };
    }

    // 4. Revalidate paths
    revalidatePath('/bands'); // Revalidate the band listing page
    // If the user could be on the band's page when deleting, might need to redirect or handle that.
    // For now, this action is called from the listing page.

    return { success: true, message: "Groupe supprimé avec succès." };

  } catch (error: any) {
    console.error("Unexpected error deleting band:", error);
    return { success: false, error: error.message || "Une erreur inattendue s'est produite." };
  }
}

export async function toggleBandPublicStatus(bandId: string): Promise<{ success: boolean; error?: string; newStatus?: boolean; message?: string }> {
  const supabase = await createSupabaseServerClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return { success: false, error: "Utilisateur non authentifié." };
  }

  try {
    // 1. Verify ownership and get current status
    const { data: bandData, error: fetchError } = await supabase
      .from('bands')
      .select('creator_id, is_public, slug') // also fetch slug for revalidation
      .eq('id', bandId)
      .single();

    if (fetchError || !bandData) {
      return { success: false, error: "Groupe non trouvé ou erreur lors de la récupération." };
    }

    if (bandData.creator_id !== user.id) {
      return { success: false, error: "Vous n'avez pas la permission de modifier ce groupe." };
    }

    // 2. Toggle the status
    const newPublicStatus = !bandData.is_public;
    const { error: updateError } = await supabase
      .from('bands')
      .update({ is_public: newPublicStatus })
      .eq('id', bandId);

    if (updateError) {
      console.error(`Error updating band public status ${bandId}:`, updateError.message);
      return { success: false, error: `Erreur lors de la mise à jour du statut public: ${updateError.message}` };
    }

    // 3. Revalidate paths
    revalidatePath('/bands'); // Band listing page
    revalidatePath(`/bands/${bandId}`, 'page'); // Authenticated band page by ID
    if (bandData.slug) {
      revalidatePath(`/bands/${bandData.slug}`, 'page'); // Public band page by slug
    }
    
    return { success: true, newStatus: newPublicStatus, message: `Statut du groupe mis à jour (${newPublicStatus ? 'public' : 'privé'}).` };

  } catch (error: any) {
    console.error("Unexpected error toggling band public status:", error);
    return { success: false, error: error.message || "Une erreur inattendue s'est produite." };
  }
}
