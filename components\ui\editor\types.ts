import type { ChordSuggestion } from '@/components/chord-system/types/chord-system';
import { ChordPlacement, UnifiedChordPosition } from '@/components/chord-system/types/chord-system';

export interface EditorUtils {
  handleInsertText: (quill: any, text: string, index: number, source: string) => void;
  handleInsertChord: (quill: any, chord: UnifiedChordPosition, index: number, source: string) => void;
  handleUpdateChord: (quill: any, chord: UnifiedChordPosition, index: number, source: string) => void;
}

export interface ChordPlacementDisplay {
  chord: UnifiedChordPosition;
  position: number;
  textPosition?: {
    top: number;
    left: number;
  };
  suggestions?: ChordSuggestion[];
}
