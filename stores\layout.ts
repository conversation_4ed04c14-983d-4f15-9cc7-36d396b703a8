import { create } from 'zustand';
import { LayoutState, LayoutMode, PanelState, ViewMode, AiGenerationType } from '@/types/layout-state';

interface LayoutStore {
  state: LayoutState;
  setLayoutState: (partial: Partial<LayoutState>) => void;
}

export const useLayoutStore = create<LayoutStore>((set) => ({
  state: {
    mode: 'normal' as LayoutMode,
    panels: {
      left: 'collapsed' as PanelState,
      right: 'collapsed' as PanelState,
      bottom: 'collapsed' as PanelState,
    },
    view: 'editor' as ViewMode,
    generationType: 'chords' as AiGenerationType,
    isMaximized: false,
    lastFocusedPanel: null,
  },
  setLayoutState: (partial) => set((state) => ({
    state: { ...state.state, ...partial },
  })),
}));
