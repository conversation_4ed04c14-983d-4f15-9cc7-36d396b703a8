-- Enable <PERSON><PERSON> on the comments table if not already enabled
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies on comments table to start fresh for UPDATE logic
-- (Be careful if you have other SELECT/INSERT/DELETE policies you want to keep, list them to drop individually)
-- For this example, I'm assuming we are redefining all relevant policies.
DROP POLICY IF EXISTS "Allow authenticated users to insert comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to update content of their own comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to mark their own comments as deleted_by_user" ON public.comments;
DROP POLICY IF EXISTS "Allow public read access to visible comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to see their own non-visible comments" ON public.comments;
DROP POLICY IF EXISTS "Allow resource creators to update comments on their resources" ON public.comments; -- This one is from comment_moderation_helpers.sql

-- Policy: Allow authenticated users to insert comments
CREATE POLICY "Allow authenticated users to insert comments"
ON public.comments
FOR INSERT
TO authenticated 
WITH CHECK (auth.uid() = user_id); 

-- Policy: Allow users to update ONLY THE CONTENT of their own comments.
-- Status changes must go through specific functions.
CREATE POLICY "Allow users to update ONLY content of their own comments"
ON public.comments
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id AND status <> 'deleted_by_moderator'::text)
WITH CHECK (auth.uid() = user_id AND status <> 'deleted_by_moderator'::text AND status = OLD.status); -- Crucially, status cannot be changed by this policy

-- Policy: Allow public read access to 'visible' comments
CREATE POLICY "Allow public read access to visible comments"
ON public.comments
FOR SELECT
USING (status = 'visible'::text);

-- Policy: Allow authenticated users to see their own comments regardless of status, unless deleted by moderator
CREATE POLICY "Allow users to see their own non-visible comments"
ON public.comments
FOR SELECT
TO authenticated
USING (auth.uid() = user_id AND status <> 'deleted_by_moderator'::text);

-- NO general UPDATE policy for moderators here. All moderator actions will be via functions.
-- NO policy for users to change status to 'deleted_by_user' directly. This will also be a function.
