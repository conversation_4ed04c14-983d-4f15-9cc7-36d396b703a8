"use client"

import Link from "next/link"
import { Music, Disc, ListTodo } from "lucide-react" // Removed Plus
import { Button } from "@/components/ui/button"
// Removed Card, CardContent, CardDescription, CardHeader, CardTitle

export function QuickActions() {
  const actions = [
    {
      title: "Nouveau Morceau", // Shorter title
      href: "/dashboard/songs/create",
      icon: Music,
      color: "text-blue-500 dark:text-blue-400",
      bgColor: "hover:bg-blue-500/10",
      haloColor: "hover:shadow-[0_0_15px_3px_rgba(59,130,246,0.3)] dark:hover:shadow-[0_0_15px_3px_rgba(96,165,250,0.2)]", // Blue halo
    },
    {
      title: "Nouvel Album", // Shorter title
      href: "/dashboard/albums/create",
      icon: Disc,
      color: "text-purple-500 dark:text-purple-400",
      bgColor: "hover:bg-purple-500/10",
      haloColor: "hover:shadow-[0_0_15px_3px_rgba(168,85,247,0.3)] dark:hover:shadow-[0_0_15px_3px_rgba(192,132,252,0.2)]", // Purple halo
    },
    {
      title: "Gérer Tâches", // Shorter title
      href: "/dashboard/todos", // Link to todos list page
      icon: ListTodo,
      color: "text-green-500 dark:text-green-400",
      bgColor: "hover:bg-green-500/10",
      haloColor: "hover:shadow-[0_0_15px_3px_rgba(34,197,94,0.3)] dark:hover:shadow-[0_0_15px_3px_rgba(74,222,128,0.2)]", // Green halo
    },
  ]

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-3">Actions rapides</h3>
      <div className="flex flex-wrap gap-3"> {/* Changed to flex for inline display */}
        {actions.map((action) => (
          <Link href={action.href} key={action.href} passHref>
            <Button
              variant="outline"
              className={`flex flex-col items-center justify-center h-24 w-24 p-3 rounded-lg group transition-all duration-200 ease-out ${action.bgColor} ${action.haloColor}`}
            >
              <action.icon className={`h-7 w-7 mb-1.5 ${action.color} transition-transform duration-200 group-hover:scale-110`} />
              <span className={`text-xs font-medium text-center ${action.color}`}>{action.title}</span>
            </Button>
          </Link>
        ))}
      </div>
    </div>
  )
}
