'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { songSchema, aiHistoryItemSchema } from '@/components/songs/song-schema';
import { LyricsEditorWithAI } from '@/components/songs/LyricsEditorWithAI';
import { SimplifiedAIAssistant } from '@/components/songs/SimplifiedAIAssistant';
import SongVault from '@/components/songs/SongVault';
import { useSongVersioning } from '@/components/songs/hooks/useSongVersioning';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { useState, useRef, useEffect, useMemo } from 'react';
import { createBrowserClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

// Inférez les types directement à partir des schémas Zod
export type SongFormValues = z.infer<typeof songSchema>;
export type AiHistoryItem = z.infer<typeof aiHistoryItemSchema>;

interface ComposerClientProps {
  songId: string;
}

export function ComposerClient({ songId }: ComposerClientProps) {
  const form = useForm<SongFormValues>({
    resolver: zodResolver(songSchema),
    defaultValues: {
      title: 'Nouveau morceau',
      artist: 'Artiste inconnu',
      lyrics: '',
      ai_config: {
        provider: 'Ollama',
        model: 'llama2',
        temperature: 0.7,
        max_tokens: 1024,
        systemPrompt: 'You are a helpful assistant for a songwriter.',
      },
    },
  });

  const supabaseClient = useMemo(() => createBrowserClient(), []);
  const { user: userProfile } = useUser(); // Renommé pour éviter la confusion
  const [user, setUser] = useState<any>(null); // Utiliser le type User de supabase si disponible

  useEffect(() => {
    const fetchUser = async () => {
      const { data } = await supabaseClient.auth.getUser();
      setUser(data.user);
    };
    fetchUser();
  }, [supabaseClient]);
  const router = useRouter();

  const versioning = useSongVersioning({
    songId,
    supabaseClient,
    user,
    router,
    setValue: form.setValue,
    reset: form.reset,
    getValues: form.getValues,
    toast,
    setCurrentTab: () => {}, // Pas de tabs pour l'instant
  });

  const quillRef = useRef<any>(null);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [showAiHistory, setShowAiHistory] = useState(true);

  const handleLyricsChange = (content: string) => {
    form.setValue('lyrics', content, { shouldDirty: true });
  };

  const handleSuggestionApply = (suggestion: string, type: 'lyrics' | 'chords' | 'structure') => {
    const currentLyrics = form.getValues('lyrics') || '';
    const newContent = `${currentLyrics}\n\n-- Suggestion (${type}) --\n${suggestion}`;
    form.setValue('lyrics', newContent);
    if (quillRef.current) {
      const quill = quillRef.current.getEditor();
      quill.setText(newContent);
    }
  };

  const addAiHistory = (userPrompt: string, assistantResponse: string) => {
    const newHistoryItem: AiHistoryItem = {
      id: new Date().toISOString(),
      timestamp: new Date().toISOString(),
      action: 'generic_action',
      input: userPrompt,
      output: assistantResponse,
      model: form.getValues('ai_config.model'),
      provider: form.getValues('ai_config.provider'),
    };
    setAiHistory(prev => [...prev, newHistoryItem]);
  };

  const watchedLyrics = form.watch('lyrics') || '';

  return (
    <FormProvider {...form}>
      <PanelGroup direction="horizontal" className="h-full">
        <Panel defaultSize={25} minSize={20} collapsible className="p-1">
          <SongVault
            songId={songId}
            activeVersionId={versioning.activeVersionId}
            versions={versioning.currentSongVersions}
            onLoadVersion={versioning.handleLoadVersion}
            onDeleteVersion={versioning.handleDeleteVersion}
            onSaveNewVersion={(name, notes, isMajor) => {
              versioning.setNewVersionName(name);
              versioning.setNewVersionNotes(notes);
              return versioning.handleSaveNewVersion();
            }}
            isLoadingSaveVersion={versioning.isSubmittingVersion}
            isLoadingDeleteVersion={versioning.isLoadingDeleteVersion}
            onUpdateVersion={versioning.handleUpdateVersionDetails}
            isLoadingUpdateVersion={versioning.isLoadingUpdateVersion}
          />
        </Panel>
        <PanelResizeHandle className="w-2 bg-muted/20 transition-colors hover:bg-muted" />
        <Panel defaultSize={75}>
          <PanelGroup direction="vertical">
            <Panel defaultSize={70} minSize={50} className="p-1">
              <LyricsEditorWithAI
                lyricsContent={watchedLyrics}
                handleLyricsChange={handleLyricsChange}
                quillRef={quillRef}
                aiConfig={form.watch('ai_config')}
                aiGeneralPrompt={form.watch('ai_config.systemPrompt') || ''}
                addAiHistory={addAiHistory}
                aiHistory={aiHistory}
                showAiHistory={showAiHistory}
                setShowAiHistory={setShowAiHistory}
              />
            </Panel>
            <PanelResizeHandle className="h-2 bg-muted/20 transition-colors hover:bg-muted" />
            <Panel defaultSize={30} minSize={25} collapsible className="p-4 overflow-y-auto">
              <SimplifiedAIAssistant
                songId={songId}
                currentLyrics={watchedLyrics}
                onSuggestionApply={handleSuggestionApply}
                onLyricsChange={handleLyricsChange}
              />
            </Panel>
          </PanelGroup>
        </Panel>
      </PanelGroup>
    </FormProvider>
  );
}
