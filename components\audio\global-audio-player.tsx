"use client";

import { AnimatePresence, motion } from 'framer-motion';
import { 
  GripVertical, ListMusic, Maximize2, Minimize2, 
  Pause, Play, SkipBack, SkipForward, 
  Volume2, VolumeX 
} from 'lucide-react';
import Image from 'next/image';
import * as React from 'react';

import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button';
import { LikeButton } from '@/components/social/like-button';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { useSidebar } from '@/components/ui/sidebar-engine';
import { useUser } from '@/contexts/user-context';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore';
import { cn } from '@/lib/utils';
import { type Song } from '@/components/songs/song-schema';
import { WaveformPlayer } from './waveform-player';

const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Main Component
export function GlobalAudioPlayer() {
  const {
    currentSong,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    duration,
    playerMode,
    setPlayerMode,
    togglePlayPause,
    seek,
    setVolume,
    toggleMute,
    playNext,
    playPrevious,
  } = useAudioPlayerStore();

    const { state: sidebarState } = useSidebar();
  const { user } = useUser();

  if (!currentSong || !user) return null;

  const isLarge = playerMode === 'large';

  const commonProps = {
    song: currentSong,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    duration,
    togglePlayPause,
    seek,
    setVolume,
    toggleMute,
    playNext,
    playPrevious,
    setPlayerMode,
  };

  return (
    <motion.div
      className={cn(
        'fixed bottom-0 z-40',
        isLarge
          ? 'h-screen w-screen top-0 left-0'
          : 'left-0 right-0 bg-background/80 backdrop-blur-lg border-t border-border',
        sidebarState === 'expanded' && !isLarge ? 'lg:left-[16rem]' : 'lg:left-[3rem]',
        'transition-all duration-300 ease-in-out'
      )}
      initial={{ y: isLarge ? '100vh' : 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: isLarge ? '100vh' : 100, opacity: 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      <AnimatePresence mode="wait">
        {playerMode === 'normal' && <PlayerNormal key="normal" {...commonProps} />}
        {playerMode === 'large' && <PlayerLarge key="large" {...commonProps} />}
      </AnimatePresence>
    </motion.div>
  );
}

// Props for player components
interface PlayerProps {
  song: Song;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
  currentTime: number;
  duration: number;
  togglePlayPause: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  playNext: () => void;
  playPrevious: () => void;
  setPlayerMode: (mode: 'normal' | 'large' | 'compact' | 'mini' | 'micro') => void;
}

// Normal Player Component
function PlayerNormal({ 
  song, isPlaying, volume, isMuted, currentTime, duration, 
  togglePlayPause, seek, setVolume, toggleMute, playNext, playPrevious, setPlayerMode 
}: PlayerProps) {
  const coverArtUrl = song.cover_art_url || '/placeholder.svg';
  const artistName = song.artist_name || 'Unknown Artist';

  return (
    <div className="h-[70px] flex items-center px-4 gap-4">
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <Image
          src={coverArtUrl}
          alt={song.title ?? 'Song cover'}
          width={48}
          height={48}
          className="rounded-md aspect-square object-cover"
        />
        <div className="flex flex-col min-w-0">
          <h3 className="font-semibold truncate text-sm">{song.title}</h3>
          <p className="text-xs text-muted-foreground truncate">{artistName}</p>
        </div>
        <div className="flex items-center gap-2 ml-4">
            <LikeButton songId={song.id} />
            <AddToPlaylistButton songId={song.id} />
        </div>
      </div>

      <div className="flex-1 flex items-center justify-center gap-2 max-w-2xl">
        <Button variant="ghost" size="icon" onClick={playPrevious} className="h-10 w-10">
          <SkipBack className="h-5 w-5" />
        </Button>
        <Button
          variant="default"
          size="icon"
          onClick={togglePlayPause}
          className="h-12 w-12 rounded-full bg-primary hover:bg-primary/90 shadow-lg"
        >
          {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
        </Button>
        <Button variant="ghost" size="icon" onClick={playNext} className="h-10 w-10">
          <SkipForward className="h-5 w-5" />
        </Button>
        <div className="w-full h-10 flex items-center cursor-pointer ml-4" onClick={(e) => {
            const rect = e.currentTarget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const percentage = clickX / rect.width;
            seek(duration * percentage);
        }}>
            {song.audio_url && <WaveformPlayer song={song} height={40} />}
        </div>
        <div className="flex items-center gap-2 text-xs text-muted-foreground w-24 justify-end">
            <span>{formatTime(currentTime)}</span>
            <span>/</span>
            <span>{formatTime(duration)}</span>
        </div>
      </div>

      <div className="flex items-center gap-4 flex-1 justify-end">
        <Button variant="ghost" size="icon">
          <ListMusic className="h-5 w-5" />
        </Button>
        <div className="flex items-center gap-2 w-[120px]">
          <Button variant="ghost" size="icon" onClick={toggleMute}>
            {isMuted || volume === 0 ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
          </Button>
          <Slider
            value={[isMuted ? 0 : volume * 100]}
            onValueChange={([value]) => setVolume(value / 100)}
            max={100}
            step={1}
          />
        </div>
        <Button variant="ghost" size="icon" onClick={() => setPlayerMode('large')}>
          <Maximize2 className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon" className="cursor-grab">
          <GripVertical className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}

// Large Player Component
function PlayerLarge({ song, isPlaying, currentTime, duration, togglePlayPause, seek, playNext, playPrevious, setPlayerMode }: PlayerProps) {
  const coverArtUrl = song.cover_art_url || '/placeholder.svg';
  const artistName = song.artist_name || 'Unknown Artist';

  return (
    <div className="h-full w-full bg-gradient-to-b from-primary/20 to-background flex flex-col items-center justify-center p-8 relative">
      <Button 
        variant="ghost" 
        size="icon" 
        onClick={() => setPlayerMode('normal')} 
        className="absolute top-4 right-4"
      >
        <Minimize2 className="h-6 w-6" />
      </Button>
      <div className="flex flex-col items-center gap-8 w-full max-w-md">
        <Image
          src={coverArtUrl}
          alt={song.title ?? 'Song cover'}
          width={400}
          height={400}
          className="rounded-lg shadow-2xl aspect-square object-cover"
        />
        <div className="text-center">
          <h2 className="text-4xl font-bold truncate">{song.title}</h2>
          <p className="text-xl text-muted-foreground truncate">{artistName}</p>
        </div>
        <div className="w-full flex flex-col gap-2">
           <div className="w-full h-16 flex items-center cursor-pointer" onClick={(e) => {
            const rect = e.currentTarget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const percentage = clickX / rect.width;
            seek(duration * percentage);
          }}>
            {song.audio_url && <WaveformPlayer song={song} height={64} waveColor="#a78bfa" progressColor="#8b5cf6" />}
          </div>
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
        <div className="flex items-center gap-6">
          <Button variant="ghost" size="icon" onClick={playPrevious} className="h-14 w-14">
            <SkipBack className="h-8 w-8" />
          </Button>
          <Button
            variant="default"
            size="icon"
            onClick={togglePlayPause}
            className="h-20 w-20 rounded-full bg-primary hover:bg-primary/90 shadow-lg"
          >
            {isPlaying ? <Pause className="h-10 w-10" /> : <Play className="h-10 w-10" />}
          </Button>
          <Button variant="ghost" size="icon" onClick={playNext} className="h-14 w-14">
            <SkipForward className="h-8 w-8" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default GlobalAudioPlayer;