# Préférences Utilisateur et Configuration IA

Ce document décrit l'architecture et les fonctionnalités des modules de préférences utilisateur et de configuration IA dans Mouvik.

## Architecture des Préférences

Le système de préférences utilisateur est organisé en onglets thématiques pour une meilleure expérience utilisateur :

1. **Profil** - Informations de base du profil utilisateur
2. **Abonnement** - Gestion des plans d'abonnement et facturation
3. **Langue** - Préférences linguistiques et formats de date/heure
4. **Paramètres Avancés** - Options techniques et configuration IA

### Structure des Composants

```
app/(authenticated)/preferences/
└── page.tsx                  # Page principale des préférences

components/preferences/
├── subscription-tab.tsx      # Gestion des abonnements
├── language-tab.tsx          # Préférences linguistiques
├── advanced-settings-tab.tsx # Paramètres avancés et IA
└── profile-theme-tab.tsx     # Personnalisation du profil
```

## Configuration IA

La configuration IA est centralisée pour assurer une cohérence dans toutes les fonctionnalités IA de l'application.

### Composants IA

```
components/ia/
├── ai-config-menu.tsx        # Menu de configuration IA central
├── ai-quick-actions.tsx      # Actions rapides IA pour l'interface
├── PromptEditDialog.tsx      # Dialogue d'édition de prompts
└── tokenizer.ts              # Utilitaire de tokenization
```

### Fournisseurs IA Supportés

- **OpenAI** - API standard avec modèles GPT-3.5, GPT-4, etc.
- **Ollama** - Modèles locaux (Llama, Mistral, etc.)
- **Anthropic** - Modèles Claude
- **OpenRouter** - Agrégateur d'API IA
- **Mistral AI** - Modèles Mistral

#### Ollama Integration Details & Troubleshooting

L'intégration avec Ollama permet d'utiliser des modèles de langage hébergés localement pour diverses fonctionnalités IA, notamment l'analyse de profil musical.

**Fichiers et Composants Clés :**

*   **`components/preferences/advanced-settings-tab.tsx`**:
    *   Gère la sélection du modèle Ollama via `localStorage` (`ollama_selected_model_mouvik`).
    *   Construit la requête (prompt) pour l'analyse de profil.
    *   Appelle la route API `/api/chat` pour interagir avec Ollama.
    *   Contient la logique `generateProfileAnalysis` et `testAiConnection` (pour Ollama).
*   **`components/ia/ai-config-menu.tsx`**:
    *   Permet la sélection du fournisseur IA (Ollama) et du modèle Ollama spécifique.
    *   Récupère la liste des modèles Ollama disponibles via `http://localhost:11434/api/tags`.
    *   Sauvegarde la sélection dans `localStorage`.
*   **`app/api/chat/route.ts`**:
    *   Route Next.js qui agit comme un proxy vers le service Ollama local (`http://localhost:11434/api/chat`).
    *   Transmet le modèle, le prompt, et les options à Ollama.
    *   Retourne la réponse JSON d'Ollama au frontend.

**Démarche de débogage et état actuel :**

1.  **Sélection du Modèle et `localStorage`**: La sélection du modèle Ollama dans l'interface utilisateur (`ai-config-menu.tsx`) et sa sauvegarde/lecture depuis `localStorage` (`ollama_selected_model_mouvik`) fonctionnent correctement.
2.  **Construction du Prompt**: La construction du prompt dans `advanced-settings-tab.tsx` a été améliorée pour formater correctement les données du profil (ex: `instruments_played`).
3.  **Appel API Backend**: L'appel `POST` depuis `advanced-settings-tab.tsx` vers `/api/chat` est effectué avec les bons paramètres (modèle, prompt).
4.  **Route API `/api/chat`**: La route proxy dans `app/api/chat/route.ts` transmet la requête à Ollama et reçoit une réponse. Des logs détaillés y ont été ajoutés pour inspecter la réponse d'Ollama.
5.  **Problème Actuel**:
    *   Le frontend reçoit un statut `200 OK` de `/api/chat`.
    *   Cependant, la réponse d'Ollama (transmise par `/api/chat`) contient un champ `message.content` vide (`""`).
    *   Le champ `done_reason` dans la réponse d'Ollama est `"load"`. Cela suggère que le modèle Ollama n'était pas complètement chargé ou prêt au moment de la requête.
6.  **Étapes de Dépannage Effectuées/Prévues**:
    *   Vérification des logs du serveur Next.js pour la réponse complète d'Ollama (via les logs ajoutés dans `route.ts`).
    *   Tentatives avec différents modèles Ollama (ex: `phi3:latest`, `qwen3:4b`).
    *   **Recommandation en cours**: Effectuer des tests directs sur l'API Ollama avec `curl` pour isoler le problème de l'application Mouvik.
        *   Exemple: `curl http://localhost:11434/api/chat -d '{ "model": "qwen3:4b", "prompt": "Why is the sky blue?", "stream": false }'`
    *   Vérifier les logs du serveur Ollama.
    *   S'assurer que les modèles sont correctement téléchargés et listés via `ollama list`.
    *   Redémarrer le service Ollama.
    *   Investigation d'une potentielle mauvaise GESTION du formatage des données dans le prompt (ex: listes de chansons, genres musicaux apparaissant comme des chaînes JSON imbriquées `Genre: ["[\"ambient\"]"]` au lieu de `Genre: ["ambient"]`).

**Objectif**: Obtenir une réponse complète et pertinente d'Ollama pour l'analyse de profil.

### Fonctionnalités IA par Module

| Module | Fonctionnalité | Description |
|--------|----------------|-------------|
| Composition | Suggestions d'accords | Génère des progressions d'accords basées sur le genre et l'ambiance |
| Composition | Structure musicale | Suggère des structures de morceaux (couplet, refrain, etc.) |
| Paroles | Aide à l'écriture | Suggestions de paroles et corrections |
| Paroles | Thèmes et concepts | Génération d'idées thématiques |
| Analyse | Genre et style | Analyse du genre musical et suggestions stylistiques |
| Social | Suggestions de collaboration | Recommandations d'artistes pour collaboration |

## Stockage et Persistance

### Stockage Local

Les préférences IA sont stockées localement pour un accès rapide :

```javascript
// Clés de stockage local
const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";
const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik";
const LOCAL_STORAGE_OPENAI_API_KEY = "openai_api_key_mouvik";
// etc.
```

### Stockage Serveur (Supabase)

Pour une persistance à long terme, les préférences sont également stockées dans Supabase :

```sql
-- Structure de la table des préférences (à implémenter)
CREATE TABLE user_preferences (
  user_id UUID REFERENCES auth.users(id) PRIMARY KEY,
  language TEXT DEFAULT 'fr',
  date_format TEXT DEFAULT 'DD/MM/YYYY',
  ai_provider TEXT DEFAULT 'openai',
  ai_model TEXT,
  ai_temperature FLOAT DEFAULT 0.7,
  ai_features JSONB DEFAULT '{"composition": true, "lyrics": true, "genre_analysis": true, "collab_suggestions": true}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Sécurité des Clés API

Les clés API des fournisseurs IA sont traitées avec un niveau de sécurité élevé :

1. **Stockage local** - Stockées temporairement pour les sessions actives
2. **Stockage serveur** - Chiffrées avant stockage dans Supabase
3. **Transmission** - Uniquement via HTTPS avec en-têtes sécurisés

## Intégration avec d'autres Modules

### Éditeur de Paroles

L'éditeur de paroles utilise la configuration IA pour :
- Générer des suggestions contextuelles
- Analyser le sentiment et le thème des paroles
- Proposer des améliorations stylistiques

### Compositeur Musical

Le compositeur musical utilise la configuration IA pour :
- Suggérer des progressions d'accords
- Proposer des structures de morceaux
- Analyser l'harmonie et la mélodie

## Bonnes Pratiques

1. **Toujours utiliser le composant `AiConfigMenu` central** pour la configuration IA
2. **Respecter les préférences utilisateur** concernant l'activation/désactivation des fonctionnalités IA
3. **Vérifier la disponibilité des clés API** avant d'effectuer des appels aux services externes
4. **Gérer les erreurs d'API** avec des messages utilisateur clairs
5. **Limiter la consommation de tokens** selon les préférences utilisateur

## Module d'Analyse de Profil IA

Le module d'analyse de profil IA est un composant central qui permet d'analyser le profil musical d'un utilisateur en se basant sur ses créations, ses préférences et ses objectifs. Ce module est conçu pour être réutilisable dans d'autres contextes comme l'analyse de morceaux, d'albums ou de statistiques utilisateur.

### Architecture et Composants

```
components/preferences/
└── advanced-settings-tab.tsx  # Contient la logique principale d'analyse de profil

app/api/
└── chat/
    └── route.ts             # Route API pour communiquer avec les fournisseurs IA
```

### Fonctionnalités Principales

#### 1. Génération d'Analyse de Profil

- **Fonction**: `generateProfileAnalysis()`
- **Emplacement**: `components/preferences/advanced-settings-tab.tsx`
- **Description**: Analyse le profil musical de l'utilisateur en utilisant l'IA pour fournir des insights personnalisés.

#### 2. Personnalisation de l'Analyse

- **Mots-clés**: Permet à l'utilisateur de spécifier des mots-clés ou thèmes pour orienter l'analyse
- **Mini-prompt**: Permet d'ajouter une question ou instruction spécifique pour personnaliser davantage l'analyse
- **Exemples d'utilisation**:
  - Mots-clés: "jazz, improvisation, composition"
  - Mini-prompt: "Comment orienter mon style vers du grunge ?" ou "Conseils pour améliorer mes compositions ?"

#### 3. Persistance des Analyses

- **Stockage**: LocalStorage (`mouvik_profile_analysis`)
- **Structure des données**:
  ```json
  {
    "content": "<HTML formatté de l'analyse>",
    "provider": "OpenAI" ou "Ollama",
    "timestamp": "2025-06-14T19:45:28.000Z",
    "keywords": "jazz, improvisation",
    "prompt": "Comment améliorer mes compositions ?"
  }
  ```
- **Récupération**: Au chargement du composant, l'analyse sauvegardée est récupérée et affichée

### Intégration avec les Fournisseurs IA

#### Configuration

- **Paramètres ajustables**:
  - `aiTemperature`: Contrôle la créativité de l'IA (0.0 - 1.0)
  - `aiTokenLimit`: Limite le nombre de tokens générés
  - `analysisDepth`: Profondeur de l'analyse (basique, standard, approfondie)
  - `wordLimit`: Limite de mots pour l'analyse générée

#### Streaming de Texte

- **Fonction**: `fetchStreamingText()`
- **Processus**:
  1. Envoi de la requête à l'API
  2. Réception du stream de texte
  3. Décodage progressif et mise à jour de l'interface
  4. Formatage HTML (gras, italique, sauts de ligne)
  5. Sauvegarde dans le localStorage

### Réutilisation du Module

Ce module est conçu pour être réutilisé dans différents contextes :

#### Analyse de Morceaux

```javascript
// Exemple d'utilisation pour l'analyse d'un morceau
const analyzeSong = async (songData, keywords, customPrompt) => {
  // Construction du prompt spécifique au morceau
  const prompt = `Analyse ce morceau: ${songData.title}\n`;
  // Utilisation de la même fonction fetchStreamingText
  return fetchStreamingText([{ role: "user", content: prompt }]);
};
```

#### Analyse d'Albums

```javascript
// Exemple d'utilisation pour l'analyse d'un album
const analyzeAlbum = async (albumData, keywords, customPrompt) => {
  // Construction du prompt spécifique à l'album
  const prompt = `Analyse cet album: ${albumData.title}\n`;
  // Utilisation de la même fonction fetchStreamingText
  return fetchStreamingText([{ role: "user", content: prompt }]);
};
```

#### Analyse de Statistiques

```javascript
// Exemple d'utilisation pour l'analyse de statistiques
const analyzeStats = async (userStats, keywords, customPrompt) => {
  // Construction du prompt spécifique aux statistiques
  const prompt = `Analyse ces statistiques utilisateur:\n${JSON.stringify(userStats)}\n`;
  // Utilisation de la même fonction fetchStreamingText
  return fetchStreamingText([{ role: "user", content: prompt }]);
};
```

### Bonnes Pratiques pour la Réutilisation

1. **Extraction des fonctions génériques**:
   - Utiliser `fetchStreamingText()` comme fonction de base pour toutes les analyses
   - Adapter la construction du prompt selon le contexte

2. **Gestion cohérente des états**:
   - Utiliser les mêmes états pour le chargement, les erreurs et l'affichage
   - Réutiliser le composant d'affichage de l'analyse avec ses badges et formatage

3. **Persistance contextuelle**:
   - Adapter la clé de stockage selon le contexte (ex: `mouvik_song_analysis_${songId}`)
   - Conserver la même structure de données pour la persistance

4. **Personnalisation**:
   - Toujours inclure les champs de mots-clés et mini-prompt pour la personnalisation
   - Adapter les placeholders selon le contexte d'utilisation

## Prochaines Étapes

- [x] Implémentation de la persistance des analyses dans le localStorage
- [x] Ajout de champs de personnalisation (mots-clés et mini-prompt)
- [ ] Extraction des fonctions d'analyse en composants réutilisables
- [ ] Implémentation de la persistance des préférences dans Supabase
- [ ] Ajout d'un système de quotas pour les appels API IA
- [ ] Intégration de nouveaux fournisseurs IA (Cohere, etc.)
- [ ] Amélioration des tests de connexion IA avec feedback détaillé
- [ ] Développement d'un tableau de bord d'utilisation IA
