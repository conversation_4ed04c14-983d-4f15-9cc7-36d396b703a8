"use client";

import { use<PERSON>em<PERSON>, useState } from "react";
import { useRouter } from "next/navigation";
import { createBrowserClient } from "@/lib/supabase/client";
import { toast } from 'sonner';
import { User } from '@supabase/supabase-js';

import { LayoutGrid, List, Plus, Search, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, Select<PERSON>rigger, SelectValue } from "@/components/ui/select";

import { Song<PERSON>ardCompact } from '@/components/songs/song-card-compact';
import { SongListItem } from "@/components/songs/song-list-item";
import type { Song } from '@/components/songs/song-schema';
import type { SongForListItem } from '@/components/songs/song-list-item';
import type { ComposerSong } from './page';

interface AIComposerClientPageProps {
  songs: ComposerSong[];
  user: User;
}

export default function AIComposerClientPage({ songs = [], user }: AIComposerClientPageProps) {
  const supabase = createBrowserClient();
  const router = useRouter();
  const [songsState, setSongs] = useState<ComposerSong[]>(songs);
  const [searchTerm, setSearchTerm] = useState('');
  const [songToDelete, setSongToDelete] = useState<ComposerSong | null>(null);
  const [selectedSongIds, setSelectedSongIds] = useState<string[]>([]);
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [zoomLevel, setZoomLevel] = useState(100);
  const [sortOption, setSortOption] = useState('updated_at-desc');

  const sortedAndFilteredSongs = useMemo(() => {
    const [sortBy, sortOrder] = sortOption.split('-');
    return [...songsState]
      .filter(song => song.title.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => {
        let valA, valB;
        if (sortBy === 'title') {
          valA = a.title.toLowerCase();
          valB = b.title.toLowerCase();
        } else { // updated_at
          valA = new Date(a.updated_at || 0).getTime();
          valB = new Date(b.updated_at || 0).getTime();
        }
        if (valA < valB) return sortOrder === 'asc' ? -1 : 1;
        if (valA > valB) return sortOrder === 'asc' ? 1 : -1;
        return 0;
      });
  }, [songsState, searchTerm, sortOption]);

    const handleSelectSong = (song: Song) => {
    if (!song.id) return;
    setSelectedSongIds(prev =>
      prev.includes(song.id!)
        ? prev.filter(id => id !== song.id)
        : [...prev, song.id!]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSongIds(sortedAndFilteredSongs.map(s => s.id!));
    } else {
      setSelectedSongIds([]);
    }
  };

  const isAllSelected = selectedSongIds.length > 0 && sortedAndFilteredSongs.length > 0 && selectedSongIds.length === sortedAndFilteredSongs.length;

  const handleBulkDelete = async () => {
    if (selectedSongIds.length === 0) return;

    const { error } = await supabase
      .from('songs')
      .delete()
      .in('id', selectedSongIds);

    if (error) {
      toast.error(`Failed to delete songs: ${error.message}`);
    } else {
      setSongs(prev => prev.filter(song => !selectedSongIds.includes(song.id!)));
      setSelectedSongIds([]);
      toast.success(`${selectedSongIds.length} song(s) deleted successfully.`);
    }
  };

  const handleCreateNewSong = () => {
    router.push('/ai-composer/workspace/new');
  };

  const handleDeleteSong = async () => {
    if (!songToDelete) return;

    const { error } = await supabase
      .from('songs')
      .delete()
      .match({ id: songToDelete.id });

    if (error) {
      toast.error(`Failed to delete song: ${error.message}`);
    } else {
      setSongs(prev => prev.filter(s => s.id !== songToDelete.id));
      toast.success(`Song "${songToDelete.title}" deleted.`);
    }
    setSongToDelete(null);
  };

  return (
    <>
      <div className="container mx-auto p-4 md:p-6 lg:p-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <h1 className="text-3xl font-bold tracking-tight">AI Composer</h1>
          <div className="flex items-center gap-2">
            <Button onClick={handleCreateNewSong}>
              <Plus className="mr-2 h-4 w-4" />
              Nouveau morceau
            </Button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6 p-4 bg-card border rounded-lg">
          <div className="flex items-center gap-2 flex-grow">
            <Search className="h-5 w-5 text-muted-foreground" />
            <Input
              placeholder="Rechercher par titre..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
          <div className="flex items-center gap-4">
            <Select value={sortOption} onValueChange={setSortOption}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Trier par" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updated_at-desc">Plus récent</SelectItem>
                <SelectItem value="updated_at-asc">Plus ancien</SelectItem>
                <SelectItem value="title-asc">Titre (A-Z)</SelectItem>
                <SelectItem value="title-desc">Titre (Z-A)</SelectItem>
              </SelectContent>
            </Select>

            {viewMode === 'grid' && (
              <Slider
                defaultValue={[100]}
                min={50}
                max={200}
                step={10}
                onValueChange={(value) => setZoomLevel(value[0])}
                className="w-[100px]"
              />
            )}

            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => { if (value) setViewMode(value as 'grid' | 'list')}}>
              <ToggleGroupItem value="grid" aria-label="Toggle grid view">
                <LayoutGrid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="Toggle list view">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>

        {/* Bulk Actions Header */}
        {selectedSongIds.length > 0 && (
          <div className="flex items-center justify-between gap-4 mb-6 p-3 bg-primary/10 border border-primary/20 rounded-lg">
            <div className="flex items-center gap-3">
              <Checkbox
                id="select-all"
                checked={isAllSelected}
                onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
              />
              <label htmlFor="select-all" className="text-sm font-medium">
                {selectedSongIds.length} sélectionné(s)
              </label>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Supprimer
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Cette action est irréversible. {selectedSongIds.length} morceau(x) seront définitivement supprimés.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Annuler</AlertDialogCancel>
                  <AlertDialogAction onClick={handleBulkDelete}>Supprimer</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}

        {/* Main Content */}
        <main>
          {sortedAndFilteredSongs.length > 0 ? (
            viewMode === 'grid' ? (
              <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(auto-fill, minmax(${zoomLevel * 1.8}px, 1fr))` }}>
                {sortedAndFilteredSongs.map((song) => (
                  <div key={song.id} className="relative group">
                    <Checkbox
                      checked={selectedSongIds.includes(song.id!)}
                      onCheckedChange={() => handleSelectSong(song)}
                      className="absolute top-2 left-2 z-10 bg-background/80"
                      aria-label={`Select song ${song.title}`}
                    />
                    <SongCardCompact
                      song={{...song, duration_ms: song.duration_ms ?? null}}
                      onInfoClick={() => handleSelectSong(song)}
                      onDelete={() => setSongToDelete(song)}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="border rounded-md">
                {sortedAndFilteredSongs.map((song) => (
                  <SongListItem 
                    key={song.id} 
                    song={song} 
                    onInfoClick={() => handleSelectSong(song)}
                    onDelete={() => setSongToDelete(song)}
                    
                  />
                ))}
              </div>
            )
          ) : (
            <div className="text-center py-16 text-muted-foreground">
              <p>Aucun morceau trouvé.</p>
              <p className="text-sm">Essayez un autre terme de recherche ou créez un nouveau morceau.</p>
            </div>
          )}
        </main>
      </div>

      {/* Single Delete Dialog */}
      <AlertDialog open={!!songToDelete} onOpenChange={(open) => !open && setSongToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer "{songToDelete?.title}" ?</AlertDialogTitle>
            <AlertDialogDescription>
              Cette action est irréversible. Le morceau sera définitivement supprimé.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSongToDelete(null)}>Annuler</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteSong}>Supprimer</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
