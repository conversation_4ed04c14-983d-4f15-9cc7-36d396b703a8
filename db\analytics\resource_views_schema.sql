-- Table to store views for various resources
CREATE TABLE IF NOT EXISTS public.resource_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID NOT NULL,
    resource_type TEXT NOT NULL, -- e.g., 'song', 'album', 'profile', 'playlist', 'band'
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- User who viewed, can be null for anonymous views
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    ip_address INET, -- Optional: for anonymous view tracking / rate limiting
    user_agent TEXT -- Optional: for analytics
);

COMMENT ON TABLE public.resource_views IS 'Stores view counts for different types of resources.';
COMMENT ON COLUMN public.resource_views.resource_id IS 'The ID of the resource that was viewed.';
COMMENT ON COLUMN public.resource_views.resource_type IS 'The type of resource (e.g., song, album).';
COMMENT ON COLUMN public.resource_views.user_id IS 'The ID of the user who viewed the resource, if logged in.';
COMMENT ON COLUMN public.resource_views.viewed_at IS 'Timestamp of when the view occurred.';

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_resource_views_resource ON public.resource_views(resource_id, resource_type);
CREATE INDEX IF NOT EXISTS idx_resource_views_user_id ON public.resource_views(user_id);
CREATE INDEX IF NOT EXISTS idx_resource_views_viewed_at ON public.resource_views(viewed_at);

-- RLS Policies (Example - adjust as needed)
-- Enable RLS
ALTER TABLE public.resource_views ENABLE ROW LEVEL SECURITY;

-- Allow anonymous or authenticated users to insert their own views
CREATE POLICY "Allow insert for own views"
ON public.resource_views
FOR INSERT
WITH CHECK (user_id IS NULL OR user_id = auth.uid());

-- Allow public read access (e.g., for aggregated counts, but direct row access might be restricted)
-- Or, more likely, counts are exposed via RPC functions that aggregate data.
-- For now, let's assume direct select might be needed by some admin/analytics roles.
CREATE POLICY "Allow public read access"
ON public.resource_views
FOR SELECT
USING (true); 
-- Consider if you need more restrictive read policies.
