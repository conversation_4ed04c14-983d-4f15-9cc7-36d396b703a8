"use client"

import { useAudio } from "@/contexts/audio-context"
import type { Song } from "@/types"
import { useCallback } from "react"

export function usePlaySong() {
  const { 
    playSong: contextPlaySong, 
    pauseSong, 
    resumeSong, 
    currentSong, 
    isPlaying, 
    addToQueue, 
    setQueue 
  } = useAudio()

  const play = useCallback(
    (songToPlay: Song & { artist?: string }) => {
      if (currentSong?.id === songToPlay.id) {
        if (isPlaying) {
          pauseSong();
        } else {
          resumeSong();
        }
      } else {
        contextPlaySong(songToPlay);
      }
    },
    [contextPlaySong, pauseSong, resumeSong, currentSong, isPlaying],
  )

  const playCollection = useCallback(
    (songs: (Song & { artist?: string })[], startIndex = 0) => {
      if (songs.length === 0) return

      const firstSong = songs[startIndex]
      const remainingSongs = [...songs.slice(startIndex + 1), ...songs.slice(0, startIndex)]

      contextPlaySong(firstSong) // Use aliased name
      setQueue(remainingSongs)
    },
    [contextPlaySong, setQueue], // Use aliased name in dependency array
  )

  const addSongToQueue = useCallback(
    (song: Song & { artist?: string }) => {
      addToQueue(song)
    },
    [addToQueue],
  )

  return { play, playCollection, addSongToQueue }
}
