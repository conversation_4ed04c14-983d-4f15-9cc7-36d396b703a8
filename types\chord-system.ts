import { DifficultyLevel, InstrumentType } from './music';

export interface UnifiedChordPosition {
  id: string;
  chord: string;
  instrument: InstrumentType;
  tuning: string;
  frets: number[];
  baseFret: number;
  difficulty: DifficultyLevel;
  category: string;
  createdAt: string;
  updatedAt: string;
}

export interface ChordSuggestion {
  chord: UnifiedChordPosition;
  position: number;
  timestamp: string;
  metadata?: {
    beat?: number;
    measure?: number;
    emphasis?: 'strong' | 'medium' | 'weak';
  };
}
