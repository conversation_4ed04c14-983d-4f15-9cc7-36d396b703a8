'use client';

import React, { forwardRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import '@/styles/editor.css'; // Import des styles personnalisés
import { Bold, Italic, Underline, List, ListOrdered, Heading1, Heading2, Pilcrow, Music4 } from 'lucide-react';
import type ReactQuillType from 'react-quill';

// Import dynamique de ReactQuill pour la compatibilité avec Next.js (SSR)
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false }) as any;

// Barre d'outils personnalisée
const CreativeToolbar = () => (
  <div id="toolbar" className="flex items-center space-x-2 p-2 bg-muted rounded-t-md border-b border-border">
    <span className="ql-formats flex items-center space-x-1">
      <button className="ql-bold"><Bold className="h-5 w-5" /></button>
      <button className="ql-italic"><Italic className="h-5 w-5" /></button>
      <button className="ql-underline"><Underline className="h-5 w-5" /></button>
    </span>
    <span className="ql-formats flex items-center space-x-1">
      <button className="ql-header" value="1"><Heading1 className="h-5 w-5" /></button>
      <button className="ql-header" value="2"><Heading2 className="h-5 w-5" /></button>
    </span>
    <span className="ql-formats flex items-center space-x-1">
       <button className="ql-list" value="ordered"><ListOrdered className="h-5 w-5" /></button>
       <button className="ql-list" value="bullet"><List className="h-5 w-5" /></button>
    </span>
     <span className="ql-formats flex items-center space-x-1">
        <button className="ql-script" value="sub"><Pilcrow className="h-5 w-5" /></button> {/* Pour les annotations */}
        <button className="ql-script" value="super"><Music4 className="h-5 w-5" /></button> {/* Pour les accords */}
    </span>
  </div>
);

const modules = {
  toolbar: {
    container: '#toolbar',
  },
};

const formats = [
  'header',
  'bold', 'italic', 'underline', 'strike', 'blockquote',
  'list', 'bullet', 'indent',
  'link', 'image',
  'script'
];

interface CreativeEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

const CreativeEditor = forwardRef<ReactQuillType, CreativeEditorProps>(({ value, onChange, placeholder, className }, ref) => {
  return (
    <div className="bg-card rounded-md border border-border shadow-sm">
      <CreativeToolbar />
      <ReactQuill
        ref={ref}
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder || 'Commencez à écrire votre prochaine chanson...'}
        className="editor-container"
      />
    </div>
  );
});

CreativeEditor.displayName = 'CreativeEditor';

export default CreativeEditor;
