'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Zap } from 'lucide-react';

interface QuickToolsProps {
  onInsertText?: (text: string) => void;
}

const QUICK_STRUCTURES = [
  { label: 'Couplet', value: '[Couplet 1]' },
  { label: 'Refrain', value: '[Refrain]' },
  { label: 'Pont', value: '[Pont]' },
  { label: 'Outro', value: '[Outro]' },
  { label: 'Intro', value: '[Intro]' },
  { label: 'Pré-refrain', value: '[Pré-refrain]' },
];

export const QuickTools: React.FC<QuickToolsProps> = ({ onInsertText }) => {
  const handleInsert = (text: string) => {
    onInsertText?.(`\n${text}\n`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Zap className="mr-2 h-5 w-5 text-blue-400" />
          Outils Rapides
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          {QUICK_STRUCTURES.map(item => (
            <Button
              key={item.label}
              variant="outline"
              onClick={() => handleInsert(item.value)}
            >
              + {item.label}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickTools;
