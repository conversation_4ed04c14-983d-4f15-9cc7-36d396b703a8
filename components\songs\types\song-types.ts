export interface SongPrivacySettings {
  is_public: boolean;
  is_favorite: boolean;
  is_incomplete: boolean;
  is_cover: boolean;
  is_instrumental: boolean;
  is_explicit: boolean;
}

export interface SongContributor {
  id: string;
  role: string;
  percentage: number;
}

export interface SongExternalLink {
  platform: string;
  url: string;
}

export interface SongCollaborator {
  id: string;
  role: string;
  status: string;
}

export interface SongVersion {
  id: string;
  version_of_song_id: string;
  version_number: number;
  changes_description: string;
  timestamp: string;
}
