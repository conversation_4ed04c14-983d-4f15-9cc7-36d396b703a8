import React from 'react';
import { EnhancedLyricsEditor } from '@/components/enhanced-lyrics-editor/EnhancedLyricsEditor';
import { ChordPlacement, ChordPlacementDisplay } from '@/types/composer';
import { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';

interface NormalLayoutProps {
  quillRef?: React.RefObject<any>;
  value?: string;
  chords?: ChordPlacement[];
  onLyricsChange?: (lyrics: string) => void;
  onChordsChange?: (chords: ChordPlacement[]) => void;
}

export const NormalLayout: React.FC<NormalLayoutProps> = ({
  quillRef,
  value,
  chords,
  onLyricsChange,
  onChordsChange
}) => {
  return (
    <div className="flex h-full w-full">
      <EnhancedLyricsEditor
        quillRef={quillRef}
        value={value || ''}
        chords={chords || []}
        onChange={onLyricsChange || (() => {})}
        onChordsChange={onChordsChange || (() => {})}
        className="h-full flex-grow"
      />
    </div>
  );
};
