// c:\_DEV_projects\TOOL\mouvik-5v0\components\AudioRecorder.tsx
import React, { useState, useRef, useCallback, useEffect, forwardRef, useImperativeHandle } from 'react'; // Added useEffect, forwardRef, useImperativeHandle
import { Button } from '@/components/ui/button';
import { Mic, StopCircle, AlertTriangle, Loader2, Download, Trash2, Settings } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";

interface AudioRecorderProps {
  onRecordingComplete: (audioBlob: Blob | null, previewUrl: string | null) => void;
  onRecordingError: (error: string) => void;
  isRecordingActiveUpdate?: (isActive: boolean) => void; // Optional: to inform parent
  disabled?: boolean; // To disable the button from parent
  // We might add props for initial audio later if this component also needs to display/download existing audio
}

export interface AudioRecorderHandle {
  toggleRecording: () => void;
  openSettingsModal: () => void;
  isCurrentlyRecording: () => boolean;
}

const AudioRecorder = forwardRef<AudioRecorderHandle, AudioRecorderProps>(({ 
  onRecordingComplete,
  onRecordingError,
  isRecordingActiveUpdate,
  disabled = false,
}, ref) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioPreviewUrlForDownload, setAudioPreviewUrlForDownload] = useState<string | null>(null);
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  // VU Meter refs and state
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const [volume, setVolume] = useState(0); // Volume level (0-100)
  const [audioInputDevices, setAudioInputDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | undefined>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('selectedAudioInputId') || undefined;
    }
    return undefined;
  });
  const [showDeviceModal, setShowDeviceModal] = useState(false);
  const [modalVolume, setModalVolume] = useState(0); // VU meter for device selection modal
  const [recorderStatusMessage, setRecorderStatusMessage] = useState<string>("Prêt");

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    toggleRecording: () => {
      handleButtonClick(); // This function already handles start/stop logic
    },
    openSettingsModal: () => {
      handleSettingsClick(); // This function opens the device selection modal
    },
    isCurrentlyRecording: () => {
      return isRecording;
    }
  }));

  // Refs for modal's live VU meter
  const modalAudioContextRef = useRef<AudioContext | null>(null);
  const modalAnalyserRef = useRef<AnalyserNode | null>(null);
  const modalSourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const modalDataArrayRef = useRef<Uint8Array | null>(null);
  const modalAnimationIdRef = useRef<number | null>(null);
  const modalStreamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (selectedDeviceId) {
        localStorage.setItem('selectedAudioInputId', selectedDeviceId);
      } else {
        localStorage.removeItem('selectedAudioInputId');
      }
    }
  }, [selectedDeviceId]); // New state for download URL


  const cleanupAudioProcessing = useCallback(() => {
    if (animationFrameIdRef.current) {
      cancelAnimationFrame(animationFrameIdRef.current);
      animationFrameIdRef.current = null;
    }
    sourceNodeRef.current?.disconnect();
    analyserRef.current?.disconnect();
    // Do not close audioContextRef.current here if it might be reused or managed globally.
    // However, for a component-specific context, it's often closed.
    // For now, let's assume it can be closed if created per recording session.
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
         // audioContextRef.current.close().catch(e => console.error("Error closing audio context", e)); // Let's hold off on closing for now
    }
    sourceNodeRef.current = null;
    analyserRef.current = null;
    // audioContextRef.current = null; // Hold off
    dataArrayRef.current = null;
    setVolume(0);
  }, []);

  const cleanupModalAudioProcessing = useCallback(() => {
    if (modalAnimationIdRef.current) {
      cancelAnimationFrame(modalAnimationIdRef.current);
      modalAnimationIdRef.current = null;
    }
    modalSourceNodeRef.current?.disconnect();
    modalAnalyserRef.current?.disconnect();
    if (modalAudioContextRef.current && modalAudioContextRef.current.state !== 'closed') {
      modalAudioContextRef.current.close().catch(e => console.error("Error closing modal audio context", e));
    }
    modalStreamRef.current?.getTracks().forEach(track => track.stop());
    modalSourceNodeRef.current = null;
    modalAnalyserRef.current = null;
    modalAudioContextRef.current = null;
    modalDataArrayRef.current = null;
    modalStreamRef.current = null;
    setModalVolume(0);
  }, []);

  // Effect for live VU meter in device selection modal
/*
  useEffect(() => {
    if (showDeviceModal && selectedDeviceId) {
      let isActive = true; // To prevent updates if component unmounts or selection changes quickly

      const setupModalVuMeter = () => {
        cleanupModalAudioProcessing();
        if (!isActive) return;

        setRecorderStatusMessage(`Modal: Demande accès ${selectedDeviceId}...`);
        navigator.mediaDevices.getUserMedia({ audio: { deviceId: { exact: selectedDeviceId } } })
          .then(stream => {
            if (!isActive) { // Re-check isActive after promise resolves
              stream.getTracks().forEach(track => track.stop());
              cleanupModalAudioProcessing();
              return;
            }
            modalStreamRef.current = stream;
            console.log('AudioRecorder Modal: Stream obtained for modal VU meter.', modalStreamRef.current);

            if (modalStreamRef.current && modalStreamRef.current.getTracks().length > 0) {
              console.log('AudioRecorder Modal: Stream tracks:', modalStreamRef.current.getTracks());
            } else {
              console.warn('AudioRecorder Modal: Stream obtained but has no tracks.');
              toast({ title: "Erreur Preview Audio", description: "Le microphone sélectionné n'a fourni aucune piste audio.", variant: "destructive" });
              cleanupModalAudioProcessing();
              return;
            }

            setRecorderStatusMessage("Modal: Micro activé, init VU mètre...");

            modalAudioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
            console.log('AudioRecorder Modal: AudioContext state after creation:', modalAudioContextRef.current.state);

            const initializeAudioProcessing = () => {
              if (!modalAudioContextRef.current || !modalStreamRef.current || !isActive) {
                cleanupModalAudioProcessing();
                return;
              }
              modalAnalyserRef.current = modalAudioContextRef.current.createAnalyser();
              modalAnalyserRef.current.fftSize = 256;
              modalSourceNodeRef.current = modalAudioContextRef.current.createMediaStreamSource(modalStreamRef.current);
              modalSourceNodeRef.current.connect(modalAnalyserRef.current);
              modalDataArrayRef.current = new Uint8Array(modalAnalyserRef.current.frequencyBinCount);

              const drawModalVuMeter = () => {
                if (!modalAnalyserRef.current || !modalDataArrayRef.current || !isActive) {
                  setModalVolume(0);
                  return;
                }
                modalAnalyserRef.current.getByteFrequencyData(modalDataArrayRef.current);
                let sum = 0;
                for (let i = 0; i < modalDataArrayRef.current.length; i++) {
                  sum += modalDataArrayRef.current[i];
                }
                const average = modalDataArrayRef.current.length > 0 ? sum / modalDataArrayRef.current.length : 0;
                const newVolume = (average / 255) * 100;
                console.log(`AudioRecorder Modal: VU Meter Data - Sum: ${sum}, Avg: ${average.toFixed(2)}, Vol: ${newVolume.toFixed(2)}`);
                setModalVolume(newVolume);
                if (isActive) modalAnimationIdRef.current = requestAnimationFrame(drawModalVuMeter);
              };
              drawModalVuMeter();
            };

            if (modalAudioContextRef.current.state === 'suspended') {
              console.log('AudioRecorder Modal: AudioContext is suspended, attempting to resume...');
              modalAudioContextRef.current.resume()
                .then(() => {
                  console.log('AudioRecorder Modal: AudioContext state after resume attempt:', modalAudioContextRef.current!.state);
                  if (!isActive) { cleanupModalAudioProcessing(); return; }
                  initializeAudioProcessing();
                })
                .catch(resumeError => {
                  console.error('AudioRecorder Modal: Error resuming AudioContext for modal VU meter', resumeError);
                  toast({ title: "Erreur Audio (Modal)", description: `N'a pas pu reprendre le contexte audio modal: ${resumeError instanceof Error ? resumeError.message : String(resumeError)}`, variant: "destructive" });
                  setRecorderStatusMessage("Erreur contexte audio modal.");
                  cleanupModalAudioProcessing();
                });
            } else {
              initializeAudioProcessing();
            }
          })
          .catch(err => {
            console.error("AudioRecorder Modal: Error setting up modal VU meter (getUserMedia or subsequent setup):", err);
            const errorMsg = err instanceof Error ? err.message : String(err);
            toast({ title: "Erreur Preview Audio", description: `Impossible d'activer la prévisualisation: ${errorMsg}`, variant: "destructive" });
            setRecorderStatusMessage(`Modal: Erreur VU ${errorMsg}`);
            cleanupModalAudioProcessing();
          });
      }; // End of setupModalVuMeter function

      if (isActive) { // Ensure setup is only called if component is still active
        setupModalVuMeter();
      }

      return () => { // This is the cleanup function for the useEffect
        isActive = false;
        console.log('AudioRecorder Modal: Cleanup effect for modal VU meter.');
        cleanupModalAudioProcessing();
      };
    } else {
      // If modal is not shown or no device is selected, ensure cleanup
      // console.log('AudioRecorder Modal: Conditions not met for modal VU meter, cleaning up.');
      cleanupModalAudioProcessing();
    }
  }, [showDeviceModal, selectedDeviceId, cleanupModalAudioProcessing]);
*/


  const getAudioInputDevices = useCallback(async () => {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        toast({ title: "Erreur", description: "Impossible de lister les périphériques audio.", variant: "destructive" });
        return;
      }
      // Ensure permissions are granted to get device labels
      const tempStream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
      tempStream.getTracks().forEach(track => track.stop()); // Stop the temporary stream immediately

      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDevices = devices.filter(device => device.kind === 'audioinput');
      setAudioInputDevices(audioDevices);

      if (audioDevices.length > 0) {
        const currentSelectedIsValid = selectedDeviceId && audioDevices.some(d => d.deviceId === selectedDeviceId);
        if (!currentSelectedIsValid) { 
          const defaultDevice = audioDevices.find(d => d.deviceId === 'default');
          const newDeviceId = defaultDevice?.deviceId || audioDevices[0].deviceId;
          setSelectedDeviceId(newDeviceId);
        }
      } else {
        setSelectedDeviceId(undefined);
      }
      return audioDevices;
    } catch (err) {
      console.error("Error enumerating audio devices:", err);
      let errorMsg = "Erreur lors de la récupération des périphériques audio. ";
      if (err instanceof Error && (err.name === "NotAllowedError" || err.name === "PermissionDeniedError")) {
          errorMsg += "Veuillez autoriser l'accès au microphone.";
      }
      toast({ title: "Erreur Périphériques", description: errorMsg, variant: "destructive" });
      setAudioInputDevices([]); // Clear devices on error
      setSelectedDeviceId(undefined);
    }
  }, [selectedDeviceId, setSelectedDeviceId]);

  // Effect to potentially validate selected device on mount if list is empty
  useEffect(() => {
    // This effect is subtle. If a deviceId is stored, but we haven't fetched devices yet,
    // we might want to trigger a fetch or validation. However, fetching devices requires
    // permissions, which is best done on user interaction (like clicking settings or record).
    // For now, this effect serves as a placeholder for potential future enhancements.
    if (selectedDeviceId && audioInputDevices.length === 0) {
      // console.log("Selected device ID exists, but device list is empty. Consider validating on next interaction.");
    }
  }, [selectedDeviceId, audioInputDevices.length]);

  const setupVuMeter = useCallback((stream: MediaStream) => {
    console.log("AudioRecorder: Setting up main VU meter. Stream tracks:", stream.getTracks());
    if (!audioContextRef.current || audioContextRef.current.state === 'closed') {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    console.log('AudioRecorder: Main AudioContext state after creation:', audioContextRef.current.state);
    }
    // At this point, audioContextRef.current should be non-null if it was previously null or closed.

    const localAudioContext = audioContextRef.current;

    if (!localAudioContext) {
      console.error("AudioRecorder: Main AudioContext is unexpectedly null after initialization. Cannot setup VU meter.");
      toast({ title: "Erreur Critique Audio", description: "Contexte audio principal non initialisé.", variant: "destructive" });
      return;
    }

    const setupAudioNodes = () => {
      // This function assumes localAudioContext is non-null and should be in 'running' state.
      if (localAudioContext.state !== 'running') {
        console.warn(`AudioRecorder: Main AudioContext not in 'running' state (${localAudioContext.state}) when trying to setup nodes. VU meter may not function.`);
        // Optionally, notify the user if it's critical for VU meter to work immediately
        // toast({ title: "Alerte Audio", description: `VU mètre non actif (état: ${localAudioContext.state}).`, variant: "default" });
        return; // Do not proceed if not running
      }
      analyserRef.current = localAudioContext.createAnalyser();
      analyserRef.current.fftSize = 256;
      sourceNodeRef.current = localAudioContext.createMediaStreamSource(stream);
      sourceNodeRef.current.connect(analyserRef.current);
      console.log('AudioRecorder: Main AnalyserNode connected to source.');
      dataArrayRef.current = new Uint8Array(analyserRef.current.frequencyBinCount);
      // The drawVuMeter loop is typically started by startRecording after this setup.
    };

    if (localAudioContext.state === 'suspended') {
      console.log('AudioRecorder: Main AudioContext is suspended, attempting to resume...');
      localAudioContext.resume()
        .then(() => {
          console.log('AudioRecorder: Main AudioContext state after resume attempt:', localAudioContext.state);
          if (localAudioContext.state === 'running') {
            setupAudioNodes();
          } else {
            console.warn(`AudioRecorder: Main AudioContext did not resume to 'running' state (${localAudioContext.state}).`);
            toast({ title: "Alerte Audio", description: "Le contexte audio principal n'a pas pu démarrer.", variant: "default" });
          }
        })
        .catch(e => {
          console.error('AudioRecorder: Error resuming main AudioContext', e);
          toast({ title: "Erreur Audio", description: `Impossible de réactiver le contexte audio: ${e instanceof Error ? e.message : String(e)}`, variant: "destructive" });
        });
    } else if (localAudioContext.state === 'running') {
      console.log('AudioRecorder: Main AudioContext is already running.');
      setupAudioNodes();
    } else {
      console.warn(`AudioRecorder: Main AudioContext in unexpected state: ${localAudioContext.state}. VU meter nodes not set up.`);
      toast({ title: "Alerte Audio", description: `État inattendu du contexte audio: ${localAudioContext.state}`, variant: "default" });
    }



    const drawVuMeter = () => {
      const currentAnalyser = analyserRef.current;
      const currentDataArray = dataArrayRef.current;

      if (!currentAnalyser || !currentDataArray) {
        // If refs aren't set up yet, request another frame and try again.
        // This is expected if setupVuMeter's async operations haven't completed.
        animationFrameIdRef.current = requestAnimationFrame(drawVuMeter);
        return;
      }

      currentAnalyser.getByteFrequencyData(currentDataArray);
      let sum = 0;
      for (let i = 0; i < currentDataArray.length; i++) {
        sum += currentDataArray[i];
      }
      const average = currentDataArray.length > 0 ? sum / currentDataArray.length : 0;
      const newVolume = (average / 255) * 100;
      console.log("AudioRecorder: Main VU Meter Data Sum:", sum, "Average:", average.toFixed(2), "New Volume:", newVolume.toFixed(2));
      setVolume(newVolume);
      animationFrameIdRef.current = requestAnimationFrame(drawVuMeter);
    };
    drawVuMeter();
  }, []);

  const startRecording = useCallback(async () => {
    setError(null);
    setRecorderStatusMessage("Demande d'accès au microphone...");
    setError(null);
    setAudioPreviewUrlForDownload(null);
    setRecordedAudioBlob(null);
    setRecorderStatusMessage("Initialisation...");
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      const errMsg = "L'API d'enregistrement audio n'est pas supportée sur ce navigateur.";
      setError(errMsg);
      onRecordingError(errMsg);
      return;
    }

    try {
      console.log(`AudioRecorder: startRecording called. Selected Device ID: ${selectedDeviceId}`);
      setIsProcessing(true); // Set processing true while initializing
      const stream = await navigator.mediaDevices.getUserMedia({ audio: { deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined } });
      console.log('AudioRecorder: Media stream obtained:', stream);
      if (!stream || stream.getAudioTracks().length === 0) {
        console.error('AudioRecorder: No audio tracks found in the stream.');
        throw new Error('No audio tracks found in the stream.');
      }
      streamRef.current = stream;
      setRecorderStatusMessage("Microphone activé. Initialisation...");
      
      setupVuMeter(stream); // This will set up AudioContext, Analyser etc.
      console.log('AudioRecorder: VU Meter setup initiated.');

      mediaRecorderRef.current = new MediaRecorder(stream);
      console.log('AudioRecorder: MediaRecorder instance created.');
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstart = () => {
        console.log('AudioRecorder: MediaRecorder onstart event fired.');
        setRecorderStatusMessage(`Enregistrement en cours... Vol: ${volume.toFixed(2)}%`); // Debug volume in status
        setIsRecording(true); // Explicitly set isRecording true here
        setIsProcessing(false); // Recording started, no longer just processing initialization
        console.log(`AudioRecorder: onstart - isRecording: ${true}, isProcessing: ${false}`);
        if(isRecordingActiveUpdate) isRecordingActiveUpdate(true);
      };

      mediaRecorderRef.current.onstop = () => {
        setRecorderStatusMessage("Traitement de l'audio...");
        setIsProcessing(true);
        cleanupAudioProcessing(); // Clean up VU meter stuff
        const audioBlob = new Blob(audioChunksRef.current, { type: mediaRecorderRef.current?.mimeType || 'audio/webm' });
        const objectUrl = URL.createObjectURL(audioBlob);
        
        setRecordedAudioBlob(audioBlob);
        setAudioPreviewUrlForDownload(objectUrl); // For download button
        if (audioBlob && audioBlob.size > 100) { // Basic check for non-empty blob
          onRecordingComplete(audioBlob, objectUrl);
          setRecorderStatusMessage("Prêt. Enregistrement terminé.");
        } else {
          onRecordingError("L'enregistrement est vide ou trop court.");
          setRecorderStatusMessage("Erreur: Enregistrement vide.");
          onRecordingComplete(null, null); // Notify parent of failure
        }
        setIsRecording(false);
        setIsProcessing(false);
        if (isRecordingActiveUpdate) isRecordingActiveUpdate(false);
        streamRef.current?.getTracks().forEach(track => track.stop()); // Release microphone
        streamRef.current = null;
      };
      
      mediaRecorderRef.current.onerror = (event) => {
        cleanupAudioProcessing(); // Clean up VU meter stuff
        const errMsg = `Erreur MediaRecorder: ${(event as any)?.error?.message || 'Erreur inconnue'}`;
        setError(errMsg);
        onRecordingError(errMsg);
        setIsRecording(false);
        setIsProcessing(false);
        setAudioPreviewUrlForDownload(null);
        if (isRecordingActiveUpdate) isRecordingActiveUpdate(false);
        streamRef.current?.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      };

      mediaRecorderRef.current.start();
      console.log('AudioRecorder: mediaRecorder.start() called.');
      // setIsRecording and setIsProcessing will be handled by onstart event handler now
      // This ensures UI updates after mediaRecorder actually confirms start.
    } catch (err) {
      cleanupAudioProcessing(); // Clean up VU meter stuff
      let errMsg = "Erreur lors de l'accès au microphone. ";
      if (err instanceof Error) {
        if (err.name === "NotAllowedError" || err.name === "PermissionDeniedError") {
          errMsg += "Permission refusée.";
        } else if (err.name === "NotFoundError" || err.name === "DevicesNotFoundError") {
          errMsg += "Aucun microphone trouvé.";
        } else {
          errMsg += err.message;
        }
      } else {
        errMsg += "Une erreur inconnue est survenue.";
      }
      setError(errMsg);
      onRecordingError(errMsg);
      setRecorderStatusMessage(`Erreur: ${errMsg.substring(0, 50)}...`);
      setIsProcessing(false);
      setAudioPreviewUrlForDownload(null);
      setRecordedAudioBlob(null);
      if (isRecordingActiveUpdate) isRecordingActiveUpdate(false);
    }
  }, [onRecordingComplete, onRecordingError, isRecordingActiveUpdate, cleanupAudioProcessing]);

  const stopRecording = useCallback(() => {
    setRecorderStatusMessage("Arrêt de l'enregistrement...");
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
      mediaRecorderRef.current.stop();
      // onstop will handle the rest, including VU meter cleanup
    } else {
      // If recording wasn't active but we need to cleanup (e.g. error before start)
      cleanupAudioProcessing();
    }
  }, [cleanupAudioProcessing]);

  useEffect(() => {
    const mediaRecorder = mediaRecorderRef.current; // Capture ref for cleanup scope
    return () => {
      console.log('AudioRecorder: Unmounting component.');
      // If mediaRecorder exists and is recording, stop it to trigger onstop
      if (mediaRecorder && mediaRecorder.state === "recording") {
        console.log('AudioRecorder: Component unmounting, stopping active recording to finalize.');
        mediaRecorder.stop(); // This should trigger the onstop handler, leading to onRecordingComplete
      } else {
        // If not recording or no recorder, just clean up the main VU meter and stream
        cleanupAudioProcessing(); // Cleans up main VU meter (analyser, source, animation frame)
        streamRef.current?.getTracks().forEach(track => track.stop()); // Stop the main stream
      }

      // Close the main audio context if it exists and is not already closed
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close().catch(e => console.error("AudioRecorder: Error closing main audio context on unmount", e));
      }
      // Note: modal audio resources (modalAudioContextRef, etc.) are handled by the modal's own useEffect cleanup
      // or when the modal is closed via cleanupModalAudioProcessing.
    };
  }, [cleanupAudioProcessing]); // cleanupAudioProcessing is a dependency

  const handleClearRecording = useCallback(() => {
    if (audioPreviewUrlForDownload) {
      URL.revokeObjectURL(audioPreviewUrlForDownload);
    }
    setAudioPreviewUrlForDownload(null);
    setRecordedAudioBlob(null);
    setError(null);
    setRecorderStatusMessage("Prêt à enregistrer");
    onRecordingComplete(null, null); // Inform parent that recording is cleared
    // VU meter should already be cleaned up by stopRecording or error handling
  }, [audioPreviewUrlForDownload, onRecordingComplete]);

  const handleDownload = useCallback(() => {
    // ... (download logic remains the same)
    if (!audioPreviewUrlForDownload) return;
    const a = document.createElement('a');
    a.href = audioPreviewUrlForDownload;
    // Extract extension from mimeType or default to webm
    const mimeType = mediaRecorderRef.current?.mimeType || 'audio/webm';
    const extension = mimeType.split('/')[1]?.split(';')[0] || 'webm';
    a.download = `recording-${Date.now()}.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    // Note: We are not calling URL.revokeObjectURL(audioPreviewUrlForDownload) here
    // because the parent component (SongForm) might still be using this URL for preview.
    // The parent component is responsible for revoking it when it's no longer needed.
  }, [audioPreviewUrlForDownload]);

  const handleButtonClick = () => {
    console.log(`AudioRecorder: handleButtonClick - disabled (prop): ${disabled}, isProcessing: ${isProcessing}, isRecording: ${isRecording}`);
    if (disabled) return;

    if (isRecording) {
      stopRecording();
    } else if (recordedAudioBlob) {
      // If already recorded, this button could mean 'record again'
      // For now, let's assume clear is separate. If main button is clicked when recording exists,
      // it could also trigger a clear then record, or be disabled until cleared.
      // Let's make it so that if a recording exists, this button does nothing until cleared.
      // Or, it could re-trigger recording, clearing the old one implicitly.
      // For simplicity, let's clear then start.
      handleClearRecording(); // Clear first
      startRecording(); // Then start new one
    } else {
      startRecording();
    }
  };

  const handleSettingsClick = useCallback(async () => {
    const devices = await getAudioInputDevices();
    if (devices && devices.length > 0) {
      setShowDeviceModal(true);
    } else if (devices && devices.length === 0) {
      toast({ title: "Aucun Microphone", description: "Aucun microphone n'a été détecté.", variant: "default" });
    }
    // If devices is undefined (error occurred), toast is handled in getAudioInputDevices
  }, [getAudioInputDevices]);

  return (
    <>
      {showDeviceModal && (
        <Dialog open={showDeviceModal} onOpenChange={setShowDeviceModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Choisir le Périphérique d'Entrée Audio</DialogTitle>
              <DialogDescription>
                Sélectionnez le microphone que vous souhaitez utiliser pour l'enregistrement. Une prévisualisation du niveau sonore s'affichera ci-dessous.
              </DialogDescription>
            </DialogHeader>
            {audioInputDevices.length > 0 ? (
              <>
                <Select
                value={selectedDeviceId || ''} // Ensure value is not undefined for Select
                onValueChange={(deviceId) => {
                  setSelectedDeviceId(deviceId);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Sélectionner un microphone" />
                </SelectTrigger>
                <SelectContent>
                  {audioInputDevices.map((device, index) => (
                    <SelectItem key={device.deviceId} value={device.deviceId}>
                      {device.label || `Microphone ${index + 1}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedDeviceId && audioInputDevices.length > 0 && (
                <div className="mt-4 space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Prévisualisation du niveau sonore:</p>
                  <div className="w-full h-6 bg-muted rounded-full overflow-hidden my-1">
                    <div 
                      className="h-full bg-green-500 transition-all duration-50 ease-linear rounded-full"
                      style={{ width: `${modalVolume}%` }}
                    />
                  </div>
                </div>
              )}
              </>
            ) : (
              <p className="text-sm text-muted-foreground py-4">
                Aucun microphone n'a été détecté ou l'accès au microphone n'a pas encore été accordé. Veuillez vérifier les permissions de votre navigateur.
              </p>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeviceModal(false)}>Fermer</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      <div className="flex flex-col items-center space-y-3 p-4 border rounded-lg shadow-md bg-card">
      <div className="flex w-full items-center space-x-2">
        <Button 
          onClick={handleButtonClick} 
          disabled={disabled || isProcessing}
          variant={isRecording ? "destructive" : (recordedAudioBlob ? "secondary" : "default")}
          className="flex-grow px-6 py-3 text-lg rounded-full transition-all duration-150 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          title={recordedAudioBlob && !isRecording ? "Enregistrer à nouveau (efface l'actuel)" : (isRecording ? "Arrêter l'enregistrement" : "Démarrer l'enregistrement")}
        >
          {isProcessing && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
          {!isProcessing && (isRecording ? <StopCircle className="mr-2 h-5 w-5" /> : <Mic className="mr-2 h-5 w-5" />)}
          {isProcessing ? 'Traitement...' : (isRecording ? 'Arrêter' : 'Enregistrer')}
        </Button>
        <Button onClick={handleSettingsClick} variant="outline" size="icon" className="rounded-full" title="Réglages audio" disabled={isRecording || isProcessing}>
          <Settings className="h-5 w-5" />
        </Button>
      </div>

      {/* VU Meter */} 
      {isRecording && (
        <>
          <div className="text-xs text-muted-foreground mb-1">{isRecording ? `Enregistrement... VU: ${volume.toFixed(2)}%` : recorderStatusMessage}</div>
          <div className="w-full h-2 bg-muted rounded-full overflow-hidden my-2">
            <div 
              className="h-full bg-green-500 transition-all duration-50 ease-linear rounded-full"
              style={{ width: `${Math.min(100, volume * 5)}%` }}
            />
          </div>
        </>
      )}

      {/* Status Message Area */}
      {!error && recorderStatusMessage && (
        <p className={`text-sm ${recorderStatusMessage.startsWith('Erreur') ? 'text-destructive' : 'text-muted-foreground'} mt-2 text-center`}>
          {recorderStatusMessage}
        </p>
      )}

      {/* Error Display */}
      {error && (
        <p className="text-sm text-destructive flex items-center mt-2 text-center">
          <AlertTriangle className="mr-1 h-4 w-4 flex-shrink-0" />
          <span className="ml-1">{error}</span>
        </p>
      )}
    </div>
    </>
  );
});

AudioRecorder.displayName = 'AudioRecorder';

export default AudioRecorder;
