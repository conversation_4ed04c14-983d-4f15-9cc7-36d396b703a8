-- Migration to fix album and playlist data fetching functions

-- Fix for get_albums_for_user
CREATE OR REPLACE FUNCTION get_albums_for_user(p_user_id UUID)
RETURNS TABLE(
    id UUID,
    name TEXT,
    is_public BOOLEAN,
    release_date DATE,
    cover_art_url TEXT,
    song_count BIGINT,
    total_duration_ms BIGINT,
    like_count BIGINT,
    view_count BIGINT,
    profiles json
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.is_public,
        a.release_date,
        a.cover_art_url,
        COUNT(s.id) AS song_count,
        COALESCE(SUM(s.duration_ms), 0) AS total_duration_ms,
        (SELECT COUNT(*) FROM album_likes al WHERE al.album_id = a.id) AS like_count,
        (SELECT COUNT(*) FROM album_views av WHERE av.album_id = a.id) AS view_count,
        json_build_object(
            'id', p.id,
            'username', p.username,
            'display_name', p.display_name,
            'avatar_url', p.avatar_url
        ) AS profiles
    FROM
        albums a
    LEFT JOIN
        profiles p ON a.user_id = p.id
    LEFT JOIN
        song_albums sa ON a.id = sa.album_id
    LEFT JOIN
        songs s ON sa.song_id = s.id
    WHERE
        a.user_id = p_user_id
    GROUP BY
        a.id, p.id;
END;
$$ LANGUAGE plpgsql;

-- Fix for get_playlist_details_for_view
DROP FUNCTION IF EXISTS public.get_playlist_details_for_view(uuid, uuid);
CREATE OR REPLACE FUNCTION public.get_playlist_details_for_view(p_playlist_id uuid, p_requesting_user_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    is_public boolean,
    cover_art_url text,
    banner_url text,
    user_id uuid,
    created_at timestamptz,
    updated_at timestamptz,
    slug text,
    creator_profile json,
    songs json,
    song_count bigint,
    total_duration_ms numeric,
    like_count bigint,
    view_count bigint,
    is_liked boolean,
    is_bookmarked boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.description,
        p.is_public,
        p.cover_art_url,
        p.banner_url,
        p.user_id,
        p.created_at,
        p.updated_at,
        p.slug,
        json_build_object(
            'id', u.id,
            'username', u.username,
            'avatar_url', u.avatar_url
        ) AS creator_profile,
        COALESCE((
            SELECT json_agg(songs_data)
            FROM (
                SELECT
                    s.id,
                    s.title,
                    s.duration_ms,
                    s.cover_image_url,
                    s.profiles
                FROM playlist_songs ps
                JOIN songs_with_profiles s ON ps.song_id = s.id
                WHERE ps.playlist_id = p.id
                ORDER BY ps.position
            ) AS songs_data
        ), '[]'::json) AS songs,
        (SELECT COUNT(*) FROM playlist_songs ps WHERE ps.playlist_id = p.id) AS song_count,
        (SELECT COALESCE(SUM(s.duration_ms), 0) FROM playlist_songs ps JOIN songs s ON ps.song_id = s.id WHERE ps.playlist_id = p.id) AS total_duration_ms,
        (SELECT COUNT(*) FROM playlist_likes pl WHERE pl.playlist_id = p.id) AS like_count,
        p.view_count,
        (p_requesting_user_id IS NOT NULL AND EXISTS (SELECT 1 FROM playlist_likes pl WHERE pl.playlist_id = p.id AND pl.user_id = p_requesting_user_id)) AS is_liked,
        (p_requesting_user_id IS NOT NULL AND EXISTS (SELECT 1 FROM user_playlist_bookmarks upb WHERE upb.playlist_id = p.id AND upb.user_id = p_requesting_user_id)) AS is_bookmarked
    FROM
        playlists p
    LEFT JOIN
        profiles u ON p.user_id = u.id
    WHERE
        p.id = p_playlist_id
        AND (p.is_public = TRUE OR p.user_id = p_requesting_user_id);
END;
$$ LANGUAGE plpgsql STABLE;
