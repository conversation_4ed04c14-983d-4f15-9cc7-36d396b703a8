"use client";

import { ReactNode } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"; // Kept for potential use in headerActions
import { SidebarTrigger } from "@/components/ui/sidebar-engine"; // Corrected import
import { cn } from "@/lib/utils";

// Define UserProfileForSidebar inline for now, ideally this comes from a shared types file
interface UserProfileForSidebar {
  email?: string | null | undefined;
  username?: string | null | undefined;
  avatarUrl?: string | null | undefined; // Added null for consistency
  firstName?: string | null | undefined; // Added null for consistency
  lastName?: string | null | undefined;  // Added null for consistency
  bio?: string | null | undefined;       // Added null for consistency
  id?: string | null | undefined;        // Added null for consistency
  // Add any other fields that finalUserProfile might have and are relevant for layout components
}

interface ComposerLayoutProps {
  /** Main page content */
  children: ReactNode;
  /** Optional toolbar rendered at top of page */
  toolbar?: ReactNode;
  /** Optional sidebar content (eg. Hub IA) */
  sidebar?: ReactNode;
  /** Optional header actions (save, share, etc.) */
  headerActions?: ReactNode;
  className?: string;
  userProfile?: UserProfileForSidebar | null; // Added userProfile prop
}

/**
 * Reusable layout for the Composer page.
 *
 * - Collapsible sidebar (Sheet) right-docked by default.
 * - Sticky header with title & actions.
 * - Toolbar zone just under header.
 */
export function ComposerLayout({
  children,
  toolbar,
  sidebar,
  headerActions,
  className,
  userProfile, // Destructure userProfile
}: ComposerLayoutProps) {
  

  return (
    <div className={cn("flex flex-col h-full w-full", className)}>
      {/* Header */}
      <header className="flex items-center justify-between px-4 py-2 border-b bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center gap-2">
          <SidebarTrigger />
          {/* ...autres éléments du header... */}
        </div>
        <div className="flex items-center gap-2">
          {headerActions}
        </div>
      </header>
      {/* Toolbar */}
      {toolbar && <div className="border-b bg-muted/10 px-4 py-2">{toolbar}</div>}
      {/* Main content & Sidebar */}
      <div className="flex flex-1 overflow-hidden">
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
        {sidebar && (
          <aside className="w-[320px] border-l bg-muted/10 p-4 overflow-y-auto">
            {sidebar} {/* AI specific sidebar (right) */}
          </aside>
        )}
      </div>
    </div>
  );
}
