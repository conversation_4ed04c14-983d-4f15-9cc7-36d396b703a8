'use client';

import { Song } from '@/components/songs/song-schema';
import { Button } from '@/components/ui/button';
import { useState } from 'react';

export function SongInfoHeaderComplete({ song }: { song: Song | null }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(song?.title || '');
  const [editedArtist, setEditedArtist] = useState(song?.artist || '');

  const handleSave = async () => {
    if (!song) return;
    // TODO: Implement save logic
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedTitle(song?.title || '');
    setEditedArtist(song?.artist || '');
    setIsEditing(false);
  };

  if (!song) return null;

  return (
    <div className="flex items-center gap-4">
      {isEditing ? (
        <div className="flex-1">
          <input
            type="text"
            value={editedTitle}
            onChange={(e) => setEditedTitle(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="Song title"
          />
          <input
            type="text"
            value={editedArtist}
            onChange={(e) => setEditedArtist(e.target.value)}
            className="w-full p-2 border rounded mt-2"
            placeholder="Artist"
          />
        </div>
      ) : (
        <div className="flex-1">
          <h1 className="text-2xl font-bold">{song.title}</h1>
          <p className="text-gray-600">{song.artist}</p>
        </div>
      )}
      <div className="flex items-center gap-2">
        {isEditing ? (
          <>
            <Button onClick={handleSave}>Save</Button>
            <Button variant="outline" onClick={handleCancel}>Cancel</Button>
          </>
        ) : (
          <Button onClick={() => setIsEditing(true)}>Edit</Button>
        )}
      </div>
    </div>
  );
}
