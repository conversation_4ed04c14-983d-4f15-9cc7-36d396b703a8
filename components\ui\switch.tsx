"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      // Base élégante et compacte
      "peer inline-flex h-5 w-10 shrink-0 cursor-pointer items-center rounded-full transition-all duration-200 ease-in-out",
      // Focus et accessibilité
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
      // État ON : vert élégant avec contour vif
      "data-[state=checked]:bg-emerald-100 data-[state=checked]:border-2 data-[state=checked]:border-emerald-500",
      // État OFF : rouge subtil avec contour vif
      "data-[state=unchecked]:bg-rose-100 data-[state=unchecked]:border-2 data-[state=unchecked]:border-rose-500",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        // Base du thumb
        "pointer-events-none block rounded-full transition-transform duration-200 ease-in-out",
        // Taille et position
        "h-4 w-4",
        // État ON : vert vif, position droite
        "data-[state=checked]:bg-emerald-500 data-[state=checked]:translate-x-5",
        // État OFF : rouge vif, position gauche
        "data-[state=unchecked]:bg-rose-500 data-[state=unchecked]:translate-x-0.5"
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
