-- Fonction pour obtenir les playlists les plus populaires d'un utilisateur pour une période donnée
CREATE OR REPLACE FUNCTION get_top_playlists_by_user(p_user_id UUID, p_time_range TEXT, p_limit INT DEFAULT 5)
RETURNS TABLE (
  id UUID,
  title TEXT,
  cover_url TEXT,
  song_count BIGINT,
  total_plays BIGINT,
  total_likes BIGINT
)
LANGUAGE plpgsql
AS $$
DECLARE
  v_start_date TIMESTAMP;
  v_end_date TIMESTAMP;
BEGIN
  v_end_date := NOW();
  IF p_time_range = '7d' THEN
    v_start_date := v_end_date - INTERVAL '7 days';
  ELSIF p_time_range = '30d' THEN
    v_start_date := v_end_date - INTERVAL '30 days';
  ELSIF p_time_range = '90d' THEN
    v_start_date := v_end_date - INTERVAL '90 days';
  ELSIF p_time_range = '1y' THEN
    v_start_date := v_end_date - INTERVAL '1 year';
  ELSE
    v_start_date := '1970-01-01'::TIMESTAMP;
  END IF;

  RETURN QUERY
  WITH playlist_song_stats AS (
    SELECT
      pls.playlist_id,
      s.id AS song_id,
      COALESCE(SUM(CASE WHEN p.created_at BETWEEN v_start_date AND v_end_date THEN 1 ELSE 0 END), 0) AS song_plays,
      COALESCE(SUM(CASE WHEN l.created_at BETWEEN v_start_date AND v_end_date THEN 1 ELSE 0 END), 0) AS song_likes
    FROM
      playlist_songs pls
    JOIN
      songs s ON pls.song_id = s.id
    LEFT JOIN
      plays p ON s.id = p.song_id
    LEFT JOIN
      likes l ON s.id = l.resource_id AND l.resource_type = 'song'
    -- Pas besoin de filtrer s.user_id ici car une playlist peut contenir des chansons d'autres artistes
    WHERE s.visibility = 'public' -- Uniquement les chansons publiées
    GROUP BY
      pls.playlist_id, s.id
  )
  SELECT
    pl.id,
    pl.name::TEXT AS title,
    pl.cover_url::TEXT,
    COUNT(DISTINCT pss.song_id)::BIGINT AS song_count,
    COALESCE(SUM(pss.song_plays), 0)::BIGINT AS total_plays,
    COALESCE(SUM(pss.song_likes), 0)::BIGINT AS total_likes
  FROM
    playlists pl
  JOIN
    playlist_song_stats pss ON pl.id = pss.playlist_id
  WHERE
    pl.user_id = p_user_id -- Uniquement les playlists créées par l'utilisateur
    AND pl.is_public = TRUE -- Ou selon les critères de visibilité souhaités
  GROUP BY
    pl.id
  ORDER BY
    total_plays DESC, total_likes DESC, pl.created_at DESC
  LIMIT p_limit;

END;
$$;

COMMENT ON FUNCTION get_top_playlists_by_user(UUID, TEXT, INT) IS 'Récupère les playlists les plus populaires (basé sur les écoutes et likes des chansons) créées par un utilisateur pour une période donnée.';

-- Exemple d'appel :
-- SELECT * FROM get_top_playlists_by_user('VOTRE_USER_ID_ICI', '30d', 5);
