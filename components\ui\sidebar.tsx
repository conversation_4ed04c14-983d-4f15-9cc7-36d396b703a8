"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { BarChart3, Music, Disc, ListTodo, Activity, Users, Compass, Settings, LogOut, Plus, Radio, ShieldCheck, UserCog as UserCogIcon, Star, Gem, BarChartBig, CircleDollarSign, PanelLeft, ListMusic, Sparkles, Home } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Sidebar as SidebarContainer,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuAction,
  useSidebar,
  SidebarSeparator,
} from "@/components/ui/sidebar-engine"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger } from "@/components/ui/tooltip"

// Updated interface to include all user details for the sidebar
export interface UserProfileForSidebar {
  id: string;
  name?: string | null;
  display_name?: string | null;
  email?: string | null;
  avatar_url?: string | null;
  username?: string | null;
  role_primary?: string | null;
  subscription_tier?: string | null;
  user_role?: string | null;
  custom_uploads_per_month?: number | null;
  custom_vault_space_gb?: number | null;
  custom_ia_credits_month?: number | null;
  custom_coins_month?: number | null;
  custom_max_playlists?: number | null;
  custom_max_friends?: number | null;
  custom_vault_max_files?: number | null;
}

// Main Sidebar Component
export function AppSidebar({ user }: { user?: UserProfileForSidebar | null }) {
  const pathname = usePathname();
  const { toast } = useToast();
  const { state: sidebarState } = useSidebar();
  const isUiCollapsed = sidebarState === "collapsed";
  const isAdmin = user?.user_role === "admin";

  const handleLogout = async () => {
    const supabase = createBrowserClient();
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast({ title: "Logout Error", description: error.message, variant: "destructive" });
    } else {
      toast({ title: "Logged Out", description: "You have been successfully logged out." });
      // Redirect will be handled by auth listener or route protection
      window.location.href = '/login';
    }
  };

  const mainNav = [
    { href: "/", icon: Home, label: "Dashboard" },
    { href: "/create", icon: Plus, label: "Create" },
    { href: "/library", icon: Music, label: "My Library" },
    { href: "/community", icon: Users, label: "Community" },
    { href: "/discover", icon: Compass, label: "Discover" },
  ];

  const secondaryNav = [
    { href: "/playlists", icon: ListMusic, label: "Playlists" },
    { href: "/library/liked", icon: Star, label: "Liked Songs" },
    { href: "/library/uploads", icon: Radio, label: "My Radio" },
    { href: "/ai-tools", icon: Sparkles, label: "AI Tools" },
  ];

  const adminNav = [
    { href: "/admin", icon: ShieldCheck, label: "Admin Dashboard" },
    { href: "/admin/users", icon: UserCogIcon, label: "User Management" },
    { href: "/admin/analytics", icon: BarChartBig, label: "Analytics" },
    { href: "/admin/monetization", icon: CircleDollarSign, label: "Monetization" },
  ];

  const renderMenu = (items: typeof mainNav) => (
    <SidebarMenu>
      {items.map((item) => (
        <SidebarMenuItem key={item.href}>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href={item.href} passHref legacyBehavior>
                  <SidebarMenuButton isActive={pathname === item.href} isCollapsed={isUiCollapsed}>
                    <item.icon className="h-5 w-5" />
                    <span className="pl-2">{item.label}</span>
                  </SidebarMenuButton>
                </Link>
              </TooltipTrigger>
              {isUiCollapsed && <TooltipContent side="right">{item.label}</TooltipContent>}
            </Tooltip>
          </TooltipProvider>
        </SidebarMenuItem>
      ))}
    </SidebarMenu>
  );

  return (
    <SidebarContainer className="relative z-50">
      <SidebarHeader isCollapsed={isUiCollapsed}>
        <Link href="/" className="flex items-center gap-2 font-bold text-lg">
          <Music className="h-7 w-7 text-primary" />
          {!isUiCollapsed && <span>MOUVIK</span>}
        </Link>
      </SidebarHeader>

      <SidebarContent className="p-2">
        {renderMenu(mainNav)}
        <SidebarSeparator className="my-4" />
        {renderMenu(secondaryNav)}
        {isAdmin && (
          <>
            <SidebarSeparator className="my-4" />
            {renderMenu(adminNav)}
          </>
        )}
      </SidebarContent>

      <SidebarFooter isCollapsed={isUiCollapsed} className="p-2">
        {user && (
          <div className="flex items-center gap-3 w-full">
            <Avatar className="h-9 w-9">
              <AvatarImage src={user.avatar_url || undefined} alt={user.display_name || "User"} />
              <AvatarFallback>{user.display_name?.[0].toUpperCase() || "U"}</AvatarFallback>
            </Avatar>
            {!isUiCollapsed && (
              <div className="flex flex-col truncate">
                <span className="font-semibold text-sm truncate">{user.display_name || user.username}</span>
                <span className="text-xs text-muted-foreground truncate">{user.email}</span>
              </div>
            )}
            <SidebarMenuAction isCollapsed={isUiCollapsed} onClick={handleLogout} className="ml-auto">
              <LogOut className="h-5 w-5" />
            </SidebarMenuAction>
          </div>
        )}
      </SidebarFooter>
    </SidebarContainer>
  );
}

export default AppSidebar;
