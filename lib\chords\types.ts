// lib/chords/types.ts

/**
 * Represents a barre across multiple strings.
 */
export interface Barre {
  fret: number;
  fromString: number;
  toString: number;
}

/**
 * Represents a single position for playing a chord on the fretboard.
 */
export interface ChordPosition {
  frets: (number | string)[]; // Use string for 'x' (muted)
  fingers: number[];
  baseFret: number;
  barres: Barre[];
  midi: number[];
  difficulty: string | number;
  position?: string;
  notes?: string[];
}

/**
 * Represents a specific type of chord (e.g., major, minor) for a root note.
 */
export interface ChordVariation {
  suffix: string;
  name?: string;
  positions: ChordPosition[];
}

/**
 * Represents the entire chord library for a specific instrument and tuning.
 */
export interface ChordLibrary {
  instrument: string;
  tuning: string[];
  strings: number;
  fretRange: [number, number];
  chords: Record<string, ChordVariation[]>;
}
