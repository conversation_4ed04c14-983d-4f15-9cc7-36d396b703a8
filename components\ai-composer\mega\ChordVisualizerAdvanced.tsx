import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Music, Guitar, Piano, Volume2, Play, Pause, 
  RotateCcw, Settings, Info, Copy, Download,
  Zap, Target, Layers, Hash
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

// Types pour les accords
interface ChordPosition {
  fret: number;
  finger: number;
  string: number;
}

interface ChordData {
  name: string;
  root: string;
  quality: string;
  positions: {
    guitar: ChordPosition[];
    piano: number[];
    ukulele: ChordPosition[];
  };
  difficulty: 'easy' | 'medium' | 'hard';
  alternatives: string[];
  theory: {
    intervals: string[];
    notes: string[];
    function: string;
  };
}

interface ChordVisualizerAdvancedProps {
  chord: string;
  instrument?: 'guitar' | 'piano' | 'ukulele';
  onChordChange?: (chord: string) => void;
  showTheory?: boolean;
  showAlternatives?: boolean;
}

// Base de données d'accords simplifiée
const CHORD_DATABASE: Record<string, ChordData> = {
  'C': {
    name: 'C Major',
    root: 'C',
    quality: 'Major',
    positions: {
      guitar: [{ fret: 1, finger: 1, string: 2 }, { fret: 2, finger: 2, string: 4 }, { fret: 3, finger: 3, string: 5 }],
      piano: [0, 4, 7], // C, E, G
      ukulele: [{ fret: 0, finger: 0, string: 1 }, { fret: 0, finger: 0, string: 2 }, { fret: 0, finger: 0, string: 3 }, { fret: 3, finger: 3, string: 4 }]
    },
    difficulty: 'easy',
    alternatives: ['Cmaj', 'C△'],
    theory: {
      intervals: ['1', '3', '5'],
      notes: ['C', 'E', 'G'],
      function: 'Tonic'
    }
  },
  'Am': {
    name: 'A Minor',
    root: 'A',
    quality: 'Minor',
    positions: {
      guitar: [{ fret: 1, finger: 1, string: 2 }, { fret: 2, finger: 2, string: 3 }, { fret: 2, finger: 3, string: 4 }],
      piano: [9, 0, 4], // A, C, E
      ukulele: [{ fret: 2, finger: 1, string: 4 }, { fret: 0, finger: 0, string: 1 }, { fret: 0, finger: 0, string: 2 }, { fret: 0, finger: 0, string: 3 }]
    },
    difficulty: 'easy',
    alternatives: ['Amin', 'A-'],
    theory: {
      intervals: ['1', '♭3', '5'],
      notes: ['A', 'C', 'E'],
      function: 'Relative Minor'
    }
  },
  'F': {
    name: 'F Major',
    root: 'F',
    quality: 'Major',
    positions: {
      guitar: [{ fret: 1, finger: 1, string: 1 }, { fret: 1, finger: 1, string: 2 }, { fret: 2, finger: 2, string: 3 }, { fret: 3, finger: 3, string: 4 }, { fret: 3, finger: 4, string: 5 }, { fret: 1, finger: 1, string: 6 }],
      piano: [5, 9, 0], // F, A, C
      ukulele: [{ fret: 2, finger: 2, string: 1 }, { fret: 0, finger: 0, string: 2 }, { fret: 1, finger: 1, string: 3 }, { fret: 3, finger: 3, string: 4 }]
    },
    difficulty: 'hard',
    alternatives: ['Fmaj', 'F△'],
    theory: {
      intervals: ['1', '3', '5'],
      notes: ['F', 'A', 'C'],
      function: 'Subdominant'
    }
  },
  'G': {
    name: 'G Major',
    root: 'G',
    quality: 'Major',
    positions: {
      guitar: [{ fret: 2, finger: 1, string: 5 }, { fret: 3, finger: 2, string: 6 }, { fret: 3, finger: 3, string: 1 }],
      piano: [7, 11, 2], // G, B, D
      ukulele: [{ fret: 0, finger: 0, string: 1 }, { fret: 2, finger: 1, string: 2 }, { fret: 3, finger: 2, string: 3 }, { fret: 2, finger: 3, string: 4 }]
    },
    difficulty: 'easy',
    alternatives: ['Gmaj', 'G△'],
    theory: {
      intervals: ['1', '3', '5'],
      notes: ['G', 'B', 'D'],
      function: 'Dominant'
    }
  }
};

// Composant pour afficher le manche de guitare
const GuitarFretboard: React.FC<{ positions: ChordPosition[] }> = ({ positions }) => {
  const strings = 6;
  const frets = 5;
  
  return (
    <div className="guitar-fretboard bg-amber-50 dark:bg-amber-950 p-4 rounded-lg border">
      <div className="grid grid-cols-6 gap-1 mb-2">
        {Array.from({ length: strings }, (_, i) => (
          <div key={i} className="text-xs text-center font-mono text-muted-foreground">
            {6 - i}
          </div>
        ))}
      </div>
      
      <div className="relative">
        {/* Cordes */}
        {Array.from({ length: strings }, (_, stringIndex) => (
          <div key={stringIndex} className="flex items-center mb-2">
            {Array.from({ length: frets + 1 }, (_, fretIndex) => {
              const position = positions.find(p => p.string === 6 - stringIndex && p.fret === fretIndex);
              return (
                <div key={fretIndex} className="relative">
                  <div className={`w-8 h-1 ${fretIndex === 0 ? 'bg-gray-800 dark:bg-gray-200' : 'bg-gray-400'}`} />
                  {position && (
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
                      {position.finger}
                    </div>
                  )}
                  {fretIndex < frets && (
                    <div className="absolute top-0 right-0 w-px h-6 bg-gray-600 dark:bg-gray-300" />
                  )}
                </div>
              );
            })}
          </div>
        ))}
        
        {/* Numéros de frettes */}
        <div className="flex mt-2">
          {Array.from({ length: frets + 1 }, (_, i) => (
            <div key={i} className="w-8 text-xs text-center text-muted-foreground">
              {i}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Composant pour afficher le piano
const PianoKeyboard: React.FC<{ notes: number[] }> = ({ notes }) => {
  const whiteKeys = [0, 2, 4, 5, 7, 9, 11]; // C, D, E, F, G, A, B
  const blackKeys = [1, 3, 6, 8, 10]; // C#, D#, F#, G#, A#
  const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
  
  return (
    <div className="piano-keyboard bg-gray-100 dark:bg-gray-800 p-4 rounded-lg border">
      <div className="relative flex">
        {/* Touches blanches */}
        {whiteKeys.map((note, index) => (
          <div
            key={note}
            className={`w-8 h-20 border border-gray-300 dark:border-gray-600 ${
              notes.includes(note) ? 'bg-blue-500' : 'bg-white dark:bg-gray-100'
            } flex items-end justify-center pb-2`}
          >
            <span className={`text-xs ${
              notes.includes(note) ? 'text-white' : 'text-gray-600'
            }`}>
              {noteNames[note]}
            </span>
          </div>
        ))}
        
        {/* Touches noires */}
        <div className="absolute top-0 flex">
          {whiteKeys.map((note, index) => {
            if (index < whiteKeys.length - 1) {
              const blackNote = note + 1;
              if (blackKeys.includes(blackNote)) {
                return (
                  <div key={blackNote} className="relative">
                    <div className="w-8" /> {/* Espacement */}
                    <div
                      className={`absolute top-0 left-6 w-5 h-12 ${
                        notes.includes(blackNote) ? 'bg-blue-600' : 'bg-gray-800 dark:bg-gray-900'
                      } flex items-end justify-center pb-1`}
                    >
                      <span className={`text-xs ${
                        notes.includes(blackNote) ? 'text-white' : 'text-gray-300'
                      }`}>
                        {noteNames[blackNote]}
                      </span>
                    </div>
                  </div>
                );
              }
            }
            return <div key={`spacer-${index}`} className="w-8" />;
          })}
        </div>
      </div>
    </div>
  );
};

export const ChordVisualizerAdvanced: React.FC<ChordVisualizerAdvancedProps> = ({
  chord,
  instrument = 'guitar',
  onChordChange,
  showTheory = true,
  showAlternatives = true
}) => {
  const [selectedInstrument, setSelectedInstrument] = useState(instrument);
  const [isPlaying, setIsPlaying] = useState(false);
  const [activeTab, setActiveTab] = useState('visual');
  
  const chordData = CHORD_DATABASE[chord];
  
  useEffect(() => {
    setSelectedInstrument(instrument);
  }, [instrument]);
  
  const handlePlayChord = () => {
    setIsPlaying(true);
    // Simulation de lecture d'accord
    setTimeout(() => setIsPlaying(false), 1000);
    toast({
      title: "Lecture d'accord",
      description: `Lecture de l'accord ${chord}`,
    });
  };
  
  const handleCopyChord = () => {
    navigator.clipboard.writeText(chord);
    toast({
      title: "Copié",
      description: `Accord ${chord} copié dans le presse-papiers`,
    });
  };
  
  if (!chordData) {
    return (
      <Card className="w-full">
        <CardContent className="p-4">
          <div className="text-center text-muted-foreground">
            <Music className="h-8 w-8 mx-auto mb-2" />
            <p>Accord non trouvé: {chord}</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg">{chordData.name}</CardTitle>
            <Badge className={`${getDifficultyColor(chordData.difficulty)} text-white`}>
              {chordData.difficulty}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button size="sm" variant="ghost" onClick={handlePlayChord} disabled={isPlaying}>
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button size="sm" variant="ghost" onClick={handleCopyChord}>
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={selectedInstrument} onValueChange={setSelectedInstrument}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="guitar">
                <div className="flex items-center gap-2">
                  <Guitar className="h-4 w-4" />
                  Guitare
                </div>
              </SelectItem>
              <SelectItem value="piano">
                <div className="flex items-center gap-2">
                  <Piano className="h-4 w-4" />
                  Piano
                </div>
              </SelectItem>
              <SelectItem value="ukulele">
                <div className="flex items-center gap-2">
                  <Music className="h-4 w-4" />
                  Ukulélé
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="visual">Visuel</TabsTrigger>
            <TabsTrigger value="theory">Théorie</TabsTrigger>
            <TabsTrigger value="alternatives">Variantes</TabsTrigger>
          </TabsList>
          
          <TabsContent value="visual" className="mt-4">
            {selectedInstrument === 'guitar' && (
              <GuitarFretboard positions={chordData.positions.guitar} />
            )}
            {selectedInstrument === 'piano' && (
              <PianoKeyboard notes={chordData.positions.piano} />
            )}
            {selectedInstrument === 'ukulele' && (
              <GuitarFretboard positions={chordData.positions.ukulele} />
            )}
          </TabsContent>
          
          <TabsContent value="theory" className="mt-4">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Notes
                </h4>
                <div className="flex gap-2">
                  {chordData.theory.notes.map((note, index) => (
                    <Badge key={index} variant="outline">{note}</Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Intervalles
                </h4>
                <div className="flex gap-2">
                  {chordData.theory.intervals.map((interval, index) => (
                    <Badge key={index} variant="secondary">{interval}</Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Layers className="h-4 w-4" />
                  Fonction
                </h4>
                <Badge className="bg-blue-500 text-white">{chordData.theory.function}</Badge>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="alternatives" className="mt-4">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Notations alternatives</h4>
                <div className="flex gap-2 flex-wrap">
                  {chordData.alternatives.map((alt, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => onChordChange?.(alt)}
                    >
                      {alt}
                    </Button>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-medium mb-2">Accords similaires</h4>
                <div className="grid grid-cols-2 gap-2">
                  {Object.keys(CHORD_DATABASE)
                    .filter(key => key !== chord && CHORD_DATABASE[key].root === chordData.root)
                    .slice(0, 4)
                    .map((similarChord) => (
                      <Button
                        key={similarChord}
                        variant="ghost"
                        size="sm"
                        onClick={() => onChordChange?.(similarChord)}
                        className="justify-start"
                      >
                        {similarChord}
                      </Button>
                    ))
                  }
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};