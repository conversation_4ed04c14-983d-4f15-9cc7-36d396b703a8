"use client";
import { useEffect, useState } from "react";
import { createBrowserClient } from "@/lib/supabase/client";
import { useUser } from "@/contexts/user-context";
import { usePlanLimits } from "@/hooks/use-plan-limits";
import {
  BarChart<PERSON>ig, Eye, Heart, Users, Music, Loader2, BarChart2, Library, Globe,
  ListMusic, BrainCircuit, Blend, UserCheck, Tags, Smile, Guitar, TrendingUp,
  RefreshCw, Download
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { AnalyticsOverview } from "@/components/stats/analytics-overview";
import { ActivityTimeline } from "@/components/stats/activity-timeline";
import { GenreAnalysis } from "@/components/stats/genre-analysis";
import { MusicMoodVisualization } from "@/components/stats/music-mood-visualization";
import { AudienceAnalysis } from "@/components/stats/audience-analysis";
import { MusicCharacteristics } from "@/components/stats/music-characteristics";
import { TopContent } from "@/components/stats/top-content";
import { ArtistComparison } from "@/components/stats/artist-comparison";
import { CatalogPerformanceTab } from "./_components/catalog-performance-tab";

// OverviewStatsData interface is no longer needed here as AnalyticsOverview fetches its own data.

// =============================================
// RUBRIQUE STATISTIQUES (GLOBALE)
// ---------------------------------------------
// Cette page correspond à la rubrique "Statistiques" du site,
// accessible via la sidebar (navigation principale).
// Elle affiche les analyses globales de l'utilisateur (tous morceaux).
// NE PAS confondre avec l'onglet "Progression et Stats" local à la page de création/édition d'un morceau !
// =============================================
export default function StatsPage() {
  const [tab, setTab] = useState("overview");
  type TimeRange = "7d" | "30d" | "90d" | "1y" | "all"; // Added 'all'
  const [timeRange, setTimeRange] = useState<TimeRange>("30d");
  const { user } = useUser();
  const router = useRouter();

  // Removed useEffect for fetching single song progress data

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
        <p className="text-lg font-semibold">Chargement des statistiques...</p>
      </div>
    );
  }

  return (
    <div className="w-full px-4 md:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-6 flex items-center gap-3">
        <BarChartBig className="h-8 w-8 text-primary" /> Statistiques & Progression
      </h1>
      <Tabs value={tab} onValueChange={setTab} className="w-full">
        <TabsList className="mb-6 flex-wrap">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart2 className="h-4 w-4" />
            <span>Vue d'ensemble</span>
          </TabsTrigger>
          <TabsTrigger value="audience" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Audience</span>
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-2">
            <Music className="h-4 w-4" />
            <span>Contenu</span>
          </TabsTrigger>
          <TabsTrigger value="progression" className="flex items-center gap-2">
            <Library className="h-4 w-4" />
            <span>Performance Catalogue</span>
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <BrainCircuit className="h-4 w-4" />
            <span>Insights</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {user?.id && <AnalyticsOverview userId={user.id} timeRange={timeRange} />}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ActivityTimeline userId={user?.id} timeRange={timeRange} />
            <GenreAnalysis userId={user?.id} />
          </div>
          <MusicMoodVisualization userId={user?.id} />
          <TopContent userId={user?.id} timeRange={timeRange} />
        </TabsContent>

        <TabsContent value="audience" className="space-y-6">
          <AudienceAnalysis userId={user?.id} />
          <ArtistComparison userId={user?.id} />
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <TopContent userId={user?.id} timeRange={timeRange} />
          <MusicCharacteristics userId={user?.id} />
        </TabsContent>

        <TabsContent value="progression" className="space-y-6">
          {user?.id && <CatalogPerformanceTab userId={user.id} timeRange={timeRange} />}
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BrainCircuit className="h-5 w-5 text-primary" />
                Insights et recommandations
              </CardTitle>
              <CardDescription>Suggestions basées sur vos données pour améliorer votre performance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Contenu statique pour l'instant */}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
