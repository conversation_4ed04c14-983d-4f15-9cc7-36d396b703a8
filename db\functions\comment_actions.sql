-- Function to check if a user can moderate a specific comment.
-- (This function is assumed to exist from comment_moderation_helpers.sql, or defined here)
CREATE OR REPLACE FUNCTION public.can_moderate_comment(
  p_comment_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_resource_type TEXT;
  v_resource_id UUID;
  v_resource_creator_id UUID;
BEGIN
  SELECT resource_type, resource_id
  INTO v_resource_type, v_resource_id
  FROM public.comments
  WHERE id = p_comment_id;

  IF NOT FOUND THEN RETURN FALSE; END IF;

  IF v_resource_type = 'album' THEN
    SELECT user_id INTO v_resource_creator_id FROM public.albums WHERE id = v_resource_id;
  ELSIF v_resource_type = 'song' THEN
    SELECT user_id INTO v_resource_creator_id FROM public.songs WHERE id = v_resource_id;
  ELSIF v_resource_type = 'playlist' THEN
    SELECT user_id INTO v_resource_creator_id FROM public.playlists WHERE id = v_resource_id;
  ELSIF v_resource_type = 'artist' THEN 
    v_resource_creator_id := v_resource_id;
  ELSIF v_resource_type = 'band' THEN
    SELECT creator_id INTO v_resource_creator_id FROM public.bands WHERE id = v_resource_id;
  ELSE
    RETURN FALSE;
  END IF;

  IF NOT FOUND OR v_resource_creator_id IS NULL THEN RETURN FALSE; END IF;
  RETURN v_resource_creator_id = p_user_id;
END;
$$;

-- RPC Function: Moderate a comment's status (hide, show, delete_by_moderator)
CREATE OR REPLACE FUNCTION public.moderate_comment_status(
  p_comment_id UUID,
  p_new_status TEXT -- Expected values: 'visible', 'hidden_by_moderator', 'deleted_by_moderator'
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER -- To bypass RLS for the UPDATE, permission is checked by can_moderate_comment
AS $$
DECLARE
  current_user_id UUID := auth.uid();
BEGIN
  IF NOT public.can_moderate_comment(p_comment_id, current_user_id) THEN
    RETURN 'Error: Permission denied.';
  END IF;

  IF p_new_status NOT IN ('visible', 'hidden_by_moderator', 'deleted_by_moderator') THEN
    RETURN 'Error: Invalid status value.';
  END IF;

  UPDATE public.comments
  SET status = p_new_status
  WHERE id = p_comment_id;

  IF FOUND THEN
    RETURN 'Success: Comment status updated to ' || p_new_status;
  ELSE
    RETURN 'Error: Comment not found or update failed.';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'Error: ' || SQLERRM;
END;
$$;

-- RPC Function: User deletes their own comment
CREATE OR REPLACE FUNCTION public.user_delete_own_comment(p_comment_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  current_user_id UUID := auth.uid();
  v_comment_user_id UUID;
  v_current_status TEXT;
BEGIN
  SELECT user_id, status INTO v_comment_user_id, v_current_status FROM public.comments WHERE id = p_comment_id;

  IF NOT FOUND THEN
    RETURN 'Error: Comment not found.';
  END IF;

  IF v_comment_user_id <> current_user_id THEN
    RETURN 'Error: Permission denied. Not your comment.';
  END IF;

  IF v_current_status = 'deleted_by_moderator' THEN
    RETURN 'Error: Cannot delete a comment already deleted by a moderator.';
  END IF;

  UPDATE public.comments
  SET status = 'deleted_by_user'
  WHERE id = p_comment_id;
  
  RETURN 'Success: Comment marked as deleted_by_user.';
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'Error: ' || SQLERRM;
END;
$$;

-- (Optional) RPC Function: User edits content of their own comment
-- CREATE OR REPLACE FUNCTION public.user_edit_comment_content(p_comment_id UUID, p_new_content TEXT) ...
