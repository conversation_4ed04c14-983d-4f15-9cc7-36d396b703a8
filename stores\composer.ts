import { create } from 'zustand';
import { z } from 'zod';
import { ChordPlacementDisplay, ChordPlacementStorage } from '@/types/composer';
import { LayoutState } from '@/types/layout-state';
import { songSchema, aiConfigSchema } from '@/components/songs/song-schema';
import { ComposerStore, ChordPlacement } from '@/types/composer-store';

const formSchema = songSchema.extend({
  ai_config: aiConfigSchema.optional()
});

type FormValues = z.infer<typeof formSchema>;

const composerStore = create<ComposerStore>((set) => ({
  currentSong: null,
  unsavedChanges: false,
  isGeneratingAI: false,
  selectedChords: [],
  currentProgression: [],
  selectedInstrument: 'guitar',
  selectedTuning: 'standard',
  layoutState: {
    mode: 'normal',
    panels: {
      left: 'collapsed',
      right: 'collapsed',
      bottom: 'collapsed'
    },
    view: 'editor',
    generationType: 'chords',
    isMaximized: false,
    lastFocusedPanel: null
  },
  setCurrentSong: (song: z.infer<typeof songSchema> | null) => set({ currentSong: song }),
  setUnsavedChanges: (value: boolean) => set({ unsavedChanges: value }),
  setIsGeneratingAI: (value: boolean) => set({ isGeneratingAI: value }),
  setSelectedChords: (chords: ChordPlacement[]) => set({ selectedChords: chords }),
  setCurrentProgression: (progression: ChordPlacement[]) => set({ currentProgression: progression }),
  setSelectedInstrument: (instrument: string) => set({ selectedInstrument: instrument }),
  setSelectedTuning: (tuning: string) => set({ selectedTuning: tuning }),
  setLayoutState: (state: Partial<LayoutState>) => set((prev) => ({
    layoutState: { ...prev.layoutState, ...state }
  })),
  updateSong: (song: Partial<z.infer<typeof songSchema>>) => set((state) => ({
    currentSong: state.currentSong ? { ...state.currentSong, ...song } : null
  })),
  hasUnsavedChanges: () => false,
  updateSongFields: (fields: Partial<z.infer<typeof songSchema>>) => set((state) => ({
    currentSong: state.currentSong ? { ...state.currentSong, ...fields } : null
  }))
}));

export const useComposerStore = composerStore;
