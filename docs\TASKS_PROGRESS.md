# Suivi des Tâches - Projet Mouvik

Ce document suit l'avancement des tâches de développement et de débogage.

## Tâches Terminées (DONE)

-   **Module d'Accords (Universal Chord Module)**
    -   [x] **Intégration de base** : Implémentation du module d'accords universel avec support pour guitare et ukulélé.
    -   [x] **Composants UI** : Création des composants `ChordDiagram`, `ChordCreationPanel`, et `ChordProgressionEditor`.
    -   [x] **Base de données** : Ajout des champs `chords_diagrams` et `chord_progressions` à la table `songs`.
    -   [x] **Bibliothèque d'accords** : Intégration d'une bibliothèque d'accords côté client sans dépendance à des modules Node externes.
    -   [x] **Recherche d'accords** : Implémentation de la recherche d'accords par nom avec affichage des variantes.
    -   [x] **Gestion des accords** : Fonctionnalités d'ajout, modification et suppression d'accords dans les chansons.

-   **Interface Utilisateur (Général)**
    -   [x] **Prévisualisation des albums (page publique)** : Corrigé le problème où la prévisualisation des albums ne fonctionnait plus sur `http://localhost:3000/albums/f2822e45-7992-4c23-a044-c750afca6919` (et pages similaires).
    -   [x] **Lien des pochettes d'album (page `/albums`)** : Corrigé les liens sur les pochettes pour qu'ils pointent vers la page publique de l'album (`/album/:slug`) si l'album est public et a un slug.
    -   [x] **Page publique d'album (`/album/:slug`)** :
        -   [x] Corrigé l'erreur de compilation due à la directive `"use client"` mal placée.
        -   [x] Corrigé l'erreur de compilation due à l'utilisation de `createServerClient` dans un Client Component (remplacé par `getClientComponentSupabase`).
        -   [x] Corrigé les erreurs de requête Supabase (ParserError) en ajustant la sélection des colonnes et la gestion des types (en attente de confirmation que le rafraîchissement du schéma PostgREST a résolu les problèmes de fond si `like_count` ou `view_count` ne sont pas des colonnes directes).
        -   [x] Amélioration de la page : ajout d'informations utilisateur, boutons d'action (Edit, Like, Dislike, Share), statistiques (like/dislike/view counts), lecture de l'album, suivi des vues.
-   **Upload d'Images (Bands)**
    -   [x] Assouplissement des contraintes pour l'upload des 2 images dans la section "BAND".
    -   [x] Implémentation du recadrage et de l'optimisation si besoin, et gestion des formats d'entrée incorrects.
-   **Pages de Liste (Songs, Albums, Playlists - `/songs`, `/albums`, `/playlists`)**
    -   [x] **Suppression de la vue "grid-compact"** : Retrait de cette option de vue et de ses contrôles.
    -   [x] **Logique des sliders de densité** : Ajustement pour que le slider vers la gauche signifie "plus petit" et vers la droite "plus grand", avec des boutons +/- correspondants.
    -   [x] **Indicateur de statut public/privé** : Amélioration de la visibilité (taille, couleur, halo) sur les cartes (SongCard, AlbumCard, PlaylistCard).
    -   [x] **Synchronisation des statistiques (Like/Dislike)** : Mise en place de callbacks (`onStatsChange`) pour que les pages de liste mettent à jour les comptes de likes/dislikes de leurs éléments.
-   **Composant `SongCard`**
    -   [x] **Boutons d'action** :
        -   [x] Bouton "Play" rendu fonctionnel (via `PlayButton` et `usePlaySong`).
        -   [x] Bouton "Ajouter à la file d'attente" connecté.
        -   [x] Boutons `LikeButton` et `DislikeButton` intégrés et fonctionnels.
        -   [x] Bouton "Ajouter à une playlist" (directement sur la carte) ajouté et fonctionnel.
    -   [x] **Affichage des statistiques** : Ajout de l'affichage des lectures, vues, likes, dislikes.
-   **Lecteur Audio (`GlobalAudioPlayer` et `PlayButton`)**
    -   [x] **Synchronisation de la lecture** : Amélioration de la logique dans `usePlaySong` et `GlobalAudioPlayer` pour résoudre les problèmes de double-clic et de pause/reprise non synchronisée entre les cartes et le lecteur global.
-   **Playlists**
    -   [x] **Modal "Ajouter à une playlist"** : Correction des problèmes (par exemple, s'il ne se fermait pas ou n'ajoutait pas correctement).
    -   [x] **Page d'une playlist individuelle (`/playlists/:id`)** :
        -   [x] Lecture par morceau.
        -   [x] Affichage des informations de la playlist.
        -   [x] Réorganisation des morceaux par glisser-déposer (Drag and Drop) avec persistance (via RPC `update_playlist_song_positions`).

## Tâches en Cours / À Faire (TODO)

-   **Éditeur d'Album (`/albums/:id/edit` et `/albums/create`)**
    -   [x] **Fonctionnalité "Add song"** :
        -   [x] Les morceaux (publiés et brouillons) s'affichent dans le modal.
        -   [x] Remplacement de la `Checkbox` par un bouton icône pour une meilleure visibilité de la sélection dans le modal d'ajout de morceaux.
        -   [x] Amélioration des types pour `AvailableSong` et `AlbumEditorTrack`.
        -   [x] Logique de sauvegarde des `album_songs` avec `track_number` en place.
    -   [x] **Affichage des pistes dans l'éditeur** : Amélioré pour inclure pochette (si dispo), nom d'artiste (si dispo), et durée formatée (lecture seule).
    -   [x] **Synchronisation du titre du morceau** : La modification du titre d'un morceau dans l'éditeur d'album met à jour le titre du morceau original dans la table `songs` lors de la sauvegarde.
    -   [x] **Réorganisation des morceaux (Drag and Drop)** : Fonctionnalité en place.
    -   [x] **Affichage de la durée totale** : Fonctionnalité en place.
-   **Modal d'ajout à une playlist (`components/playlists/add-to-playlist-modal.tsx`)**
    -   [x] Remplacement de la `Checkbox` par un bouton icône pour une meilleure visibilité de la sélection des playlists.
-   **Page Publique d'Album (`/album/:slug`)**
    -   [x] **Sidebar** : Création d'un layout `app/(public)/layout.tsx` qui inclut `AppSidebar`.
    -   [x] **Réorganisation visuelle (Header)** : La section header de la page a été réorganisée pour se rapprocher de l'image de référence.
    -   [x] **Bouton "Éditer l'album"** : Rendu plus visible avec du texte sur la page publique.
    -   [x] **Statistiques (Likes, Vues)** : Création de fonctions SQL (`get_like_count`, `get_view_count`) et modification de la page pour les appeler via RPC. (Nécessite application du script SQL et rafraîchissement du schéma PostgREST par l'utilisateur).
    -   [x] **Section Commentaires** :
        -   [x] Modification du schéma de la table `comments` pour la rendre générique (script `db/update_comments_table.sql` créé et rendu plus robuste). (Nécessite application du script SQL et rafraîchissement du schéma PostgREST par l'utilisateur).
        -   [x] Création du composant `CommentSection` avec affichage de base et formulaire d'ajout (avec sélecteur d'emojis).
        -   [x] Intégration de `CommentSection` sur la page publique de l'album.
        -   [ ] Implémenter la gestion des réponses aux commentaires.
        -   [ ] Implémenter la modération des commentaires dans les pages d'édition.
    -   [x] **Section "Artiste"** : Création du composant `ArtistCardDisplay` et intégration sur la page publique de l'album.
    -   [x] **Section "Albums Similaires"** : Création du composant `SimilarAlbums` et intégration sur la page publique de l'album.
    -   [ ] **Compléter le design** : Finaliser les détails des sections Artiste (followers, bouton suivre) et Albums Similaires (logique de similarité avancée).
-   **Page Publique d'une Chanson (`/song/:slug`)**
    -   [x] Conversion en Client Component.
    -   [x] Adaptation de la récupération des données (stats, artiste, album).
    -   [x] Réorganisation du layout (header, boutons d'action, contenu principal avec onglets).
    -   [x] Intégration de `ArtistCardDisplay` et `CommentSection`.
    -   [ ] Ajouter section "Fait partie de l'album X".
    -   [ ] Ajouter section "Morceaux similaires".
-   **Synchronisation des états visuels (Like/Dislike)**
    -   [ ] Étudier une solution plus globale pour synchroniser l'état visuel des boutons Like/Dislike.
-   **Vérification générale et débogage**
    -   [ ] Tester toutes les fonctionnalités modifiées pour s'assurer qu'il n'y a pas de régressions ou de nouveaux problèmes.
    -   [ ] S'assurer que le problème de `ParserError` sur la page publique de l'album est définitivement résolu après le rafraîchissement du schéma PostgREST.

## Plan de Consolidation et Harmonisation de la Documentation

**Objectif :** Assurer que `TECHNICAL_DEBT_AND_TODO.md` et `TODO_GENERAL_ET_CHORD_MODULE.md` reflètent l'état actuel du projet, en intégrant les informations de progression de ce document (`TASKS_PROGRESS.md`) et en identifiant les éléments déjà implémentés dans le code base (`/app`, `/components`).

**Documents de Référence Principaux pour la Mise à Jour :**

1.  **`TODO_GENERAL_ET_CHORD_MODULE.md`**: Sert de feuille de route principale pour les fonctionnalités. Les éléments marqués comme [DONE] ici ou dans `TASKS_PROGRESS.md` devraient être vérifiés dans le code et, si confirmés, leur statut mis à jour dans `TECHNICAL_DEBT_AND_TODO.md` (si applicable) ou marqués comme complétés dans `TODO_GENERAL_ET_CHORD_MODULE.md`.
2.  **`TECHNICAL_DEBT_AND_TODO.md`**: Sert de liste pour les corrections, améliorations techniques et fonctionnalités spécifiques non couvertes en détail par `TODO_GENERAL_ET_CHORD_MODULE.md`. Les éléments marqués comme [RESOLVED], [COMPLETED], ou [PARTIALLY ADDRESSED/IN PROGRESS] ici ou dans `TASKS_PROGRESS.md` doivent être vérifiés.
3.  **`database-schema.md`**: Source de vérité pour la structure de la base de données. Utilisé pour vérifier si les modifications de schéma nécessaires pour certaines fonctionnalités ont été effectuées.

**Processus d'Harmonisation Proposé (itératif) :**

1.  **Revue de `TASKS_PROGRESS.md` (ce document) :**
    *   Identifier chaque tâche marquée comme [DONE] ou ayant une progression significative.
    *   Pour chaque tâche, vérifier si elle correspond à un item dans `TODO_GENERAL_ET_CHORD_MODULE.md` ou `TECHNICAL_DEBT_AND_TODO.md`.
2.  **Mise à jour de `TODO_GENERAL_ET_CHORD_MODULE.md` :**
    *   Pour les items identifiés à l'étape 1, marquer la progression ou la complétion dans `TODO_GENERAL_ET_CHORD_MODULE.md`.
    *   Ajouter des références croisées vers `TASKS_PROGRESS.md` si nécessaire pour plus de détails.
3.  **Mise à jour de `TECHNICAL_DEBT_AND_TODO.md` :**
    *   Pour les items identifiés à l'étape 1, mettre à jour leur statut (e.g., [COMPLETED], [PARTIALLY ADDRESSED/IN PROGRESS], [DB SCHEMA IN PLACE]).
    *   Ajouter des références croisées vers `TASKS_PROGRESS.md` et/ou `TODO_GENERAL_ET_CHORD_MODULE.md`.
    *   Ajouter de nouveaux items à `TECHNICAL_DEBT_AND_TODO.md` si des fonctionnalités de `TODO_GENERAL_ET_CHORD_MODULE.md` ne sont pas encore listées et nécessitent un suivi technique spécifique (e.g., création de tables DB, RPCs, UI complexe).
4.  **Vérification du Code Base (`/app`, `/components`, `/lib/supabase/db_functions`) :**
    *   Pour les fonctionnalités marquées comme [DONE] ou en progression avancée dans les documents, effectuer une recherche dans le code pour confirmer leur implémentation.
    *   Exemples de points à vérifier :
        *   Existence des routes et pages dans `/app`.
        *   Présence des composants UI dans `/components`.
        *   Définition des fonctions RPC dans `db_functions` et leur référencement dans `database-schema.md`.
        *   Utilisation des types et interfaces définis (e.g., pour le module Chord).
5.  **Affinage et Précision :**
    *   S'assurer que les descriptions des tâches dans `TECHNICAL_DEBT_AND_TODO.md` sont précises et reflètent le travail restant ou l'état actuel.
    *   Harmoniser la terminologie entre les documents.

**Exemple d'Application (basé sur les informations actuelles) :**

*   **Tâche dans `TASKS_PROGRESS.md`:** "Harmonisation des Statistiques et Actions sur les pages publiques et d'édition (Song, Album, Playlist, Band) - [EN COURS]"
    *   **Action sur `TODO_GENERAL_ET_CHORD_MODULE.md`:** Vérifier Phase 1 - "Harmoniser la présence et le fonctionnement des boutons Share, Edit, Stats block..." et marquer comme [EN COURS] si ce n'est pas déjà fait.
    *   **Action sur `TECHNICAL_DEBT_AND_TODO.md`:** L'item "UI Harmonization (General)" a déjà été ajouté et marqué comme [Partially Addressed/In Progress] avec référence à `TODO_GENERAL_ET_CHORD_MODULE.md` et `TASKS_PROGRESS.md`. C'est correct.
*   **Tâche dans `TASKS_PROGRESS.md`:** "Playlist Management - Drag & Drop pour réorganiser les morceaux - [DONE]"
    *   **Action sur `TODO_GENERAL_ET_CHORD_MODULE.md`:** Vérifier Phase 4 - "Organisation des morceaux (drag & drop)" et marquer comme [DONE].
    *   **Action sur `TECHNICAL_DEBT_AND_TODO.md`:** L'item "Playlist Edit Page - Song Management" -> "Drag-and-drop reordering of songs" a été marqué comme [COMPLETED]. C'est correct.

**Prochaines Étapes Immédiates (basées sur la demande utilisateur) :**

1.  **Lire `TASKS_PROGRESS.md` en entier.**
2.  **Lire `TODO_GENERAL_ET_CHORD_MODULE.md` en entier (déjà fait en grande partie).**
3.  **Lire `TECHNICAL_DEBT_AND_TODO.md` en entier (déjà fait en grande partie).**
4.  **Commencer le processus d'harmonisation en se basant sur `TASKS_PROGRESS.md` et en mettant à jour `TODO_GENERAL_ET_CHORD_MODULE.md` et `TECHNICAL_DEBT_AND_TODO.md`.**
5.  **Parallèlement, effectuer des recherches ciblées dans le code (`search_codebase`, `search_by_regex`, `view_files`) pour vérifier l'état d'implémentation des fonctionnalités clés.**

---
