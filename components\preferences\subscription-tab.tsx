"use client";

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, CreditCard, Star } from "lucide-react";

interface SubscriptionTabProps {
  currentPlan?: string;
}

export function SubscriptionTab({ currentPlan = "free" }: SubscriptionTabProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>(currentPlan);

  const plans = [
    {
      id: "free",
      name: "Gratuit",
      price: "0€",
      period: "pour toujours",
      description: "Pour découvrir les fonctionnalités de base",
      features: [
        "3 projets musicaux",
        "Stockage limité à 100 Mo",
        "Fonctionnalités d'édition de base",
        "Pas d'export haute qualité",
      ],
      badge: null,
    },
    {
      id: "creator",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      price: "9,99€",
      period: "par mois",
      description: "Pour les musiciens indépendants",
      features: [
        "Projets illimités",
        "Stockage jusqu'à 5 Go",
        "Toutes les fonctionnalités d'édition",
        "Export haute qualité",
        "Collaboration avec 3 artistes",
      ],
      badge: null,
    },
    {
      id: "pro",
      name: "Professionnel",
      price: "19,99€",
      period: "par mois",
      description: "Pour les professionnels et studios",
      features: [
        "Tout ce qui est inclus dans Créateur",
        "Stockage jusqu'à 50 Go",
        "Collaborations illimitées",
        "Support prioritaire",
        "Outils avancés de mixage et mastering",
        "API pour intégrations personnalisées",
      ],
      badge: "Populaire",
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Abonnement</h2>
        <p className="text-muted-foreground">
          Gérez votre abonnement et découvrez nos différentes offres.
        </p>
      </div>

      <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-3">
        {plans.map((plan) => (
          <Card 
            key={plan.id} 
            className={`flex flex-col ${selectedPlan === plan.id ? 'border-primary ring-2 ring-primary' : ''}`}
          >
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                {plan.badge && (
                  <Badge variant="secondary" className="ml-2">
                    {plan.badge}
                  </Badge>
                )}
              </div>
              <div className="flex items-baseline gap-1">
                <span className="text-3xl font-bold">{plan.price}</span>
                <span className="text-sm text-muted-foreground">
                  {plan.period}
                </span>
              </div>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-2">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-primary" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {currentPlan === plan.id ? (
                <Button className="w-full" variant="outline" disabled>
                  Plan actuel
                </Button>
              ) : (
                <Button 
                  className="w-full" 
                  variant={plan.id === "pro" ? "default" : "outline"}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  {plan.id === "free" ? "Rétrograder" : "Passer à " + plan.name}
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      {selectedPlan !== currentPlan && (
        <div className="flex justify-end">
          <Button className="mt-4" onClick={() => alert("Fonctionnalité à venir")}>
            <CreditCard className="mr-2 h-4 w-4" />
            Mettre à jour l'abonnement
          </Button>
        </div>
      )}

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Historique de facturation</CardTitle>
          <CardDescription>Consultez vos factures précédentes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground">
            Aucune facture disponible pour le moment.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
